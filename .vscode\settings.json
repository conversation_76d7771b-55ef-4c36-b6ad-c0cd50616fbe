{
  "i18n-ally.localesPaths": ["src/locales", "src/locales/lang", "public/resource/tinymce/langs"],
  // 控制相关文件嵌套展示
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "*.ts": "$(capture).test.ts, $(capture).test.tsx",
    "*.tsx": "$(capture).test.ts, $(capture).test.tsx",
    "*.env": "$(capture).env.*",
    "CHANGELOG.md": "CHANGELOG*",
    "package.json": ".dockerignore,nginx.conf,Dockerfile,pnpm-lock.yaml,pnpm-workspace.yaml,LICENSE,.gitattributes,.gitignore,.gitpod.yml,CNAME,README*,.npmrc,.browserslistrc,turbo.json,.markdownlint.json",
    ".eslintrc.cjs": ".eslintignore,.prettierignore,.stylelintignore,.prettierrc.js,.stylelintrc.cjs,postcss.config.js,prettier.config.cjs,stylelint.config.js,windi.config.ts,tailwind.config.js,.editorconfig",
    "eslint.config.mjs": ".eslintignore,.prettierignore,.stylelintignore,.commitlintrc.*,.prettierrc.*,stylelint.config.*,.lintstagedrc.mjs,cspell.json"
  },
  "terminal.integrated.scrollback": 10000,
  "cSpell.words": ["CUSTOMMENUTYPE", "cyberplayer", "graphiccode", "jingkai", "undizuo"],
  "cSpell.enableFiletypes": ["shellscript"],
  "typescript.tsdk": "node_modules/typescript/lib",
  "eslint.format.enable": true,
  "prettier.prettierPath": "./node_modules/prettier/index.cjs",
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[react]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
