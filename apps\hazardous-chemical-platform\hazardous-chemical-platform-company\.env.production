# Whether to open mock
VITE_USE_MOCK = false

# public path
VITE_PUBLIC_PATH = /

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VITE_BUILD_COMPRESS = 'gzip'


# Basic interface address SPA
VITE_GLOB_API_URL = /basic-api

# File upload address， optional
# It can be forwarded by nginx or write the actual address directly
VITE_GLOB_UPLOAD_URL = /upload

# Interface prefix
VITE_GLOB_API_URL_PREFIX =

# 预览前缀
VITE_GLOB_FILE_PREFIX = http://**************:9000

VITE_GLOB_KK_FILE_PREFIX = http://**************:8012

VITE_GLOB_MODEL_URL = http://***************/hqmodel
