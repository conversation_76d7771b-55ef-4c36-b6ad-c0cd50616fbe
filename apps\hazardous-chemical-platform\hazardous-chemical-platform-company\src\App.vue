<template>
  <ConfigProvider
    :locale="getAntdLocale"
    :theme="themeConfig"
  >
    <AppProvider>
      <RouterView />
      <div class="back-shadow w-full h-full absolute top-0 pointer-events-none" />
    </AppProvider>
  </ConfigProvider>
</template>

<script lang="ts" setup>
import { AppProvider } from '@/components/Application';
import { useTitle } from '@/hooks/web/useTitle';
import { useLocale } from '@/locales/useLocale';
import { ConfigProvider } from 'ant-design-vue';
import { useDarkModeTheme } from '@/hooks/setting/useDarkModeTheme';
import 'dayjs/locale/zh-cn';
import { computed } from 'vue';

// support Multi-language
const { getAntdLocale } = useLocale();

const { isDark, darkTheme } = useDarkModeTheme();

const themeConfig = computed(() =>
  Object.assign(
    {
      token: {
        colorPrimary: '#0960bd',
        colorSuccess: '#55D187',
        colorWarning: '#EFBD47',
        colorError: '#ED6F6F',
        colorInfo: '#0960bd',
        colorBgContainer: 'transparent',
        colorBorder: '#0C554A',
        colorPrimaryHover: '#00C0C7',
      },
      components: {
        Layout: {
          colorBgBody: 'transparent',
          layoutHeaderColor: 'transparent',
        },
        Table: { tableBg: 'transparent' },
        Tooltip: { colorBgDefault: '#063a39af' },
        // Modal: { modalContentBg: '#063a398c' },
      },
    },
    isDark.value ? darkTheme : {}
  )
);
// Listening to page changes and dynamically changing site titles
useTitle();
</script>

<style lang="less" scoped>
.back-shadow {
  background-image: url('@/assets/images/home-page/background-line-default.png'),
    url('@/assets/images/home-page/background.png');
  background-repeat: no-repeat, no-repeat;
  background-size:
    98% 97%,
    100% 100%;
  background-position: center;
}
</style>
