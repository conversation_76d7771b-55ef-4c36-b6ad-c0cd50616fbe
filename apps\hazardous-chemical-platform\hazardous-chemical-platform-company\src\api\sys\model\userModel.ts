/**
 * @description: Login interface parameters
 */
export interface LoginParams {
  account: string;
  pwd: string;
  verifyCodeId?: string;
  verifyCode?: string;
  systemType?: string;
  device: string;
  loginType: string;
}

export interface RoleInfo {
  roleName: string;
  value: string;
}

/**
 * @description: Login interface return value
 */
export interface LoginResultModel {
  userId: string | number;
  token?: string;
  saToken: string;
  roles: RoleInfo[];
}

/**
 * @description: Get user information return value
 */
export interface GetUserInfoModel {
  roles: RoleInfo[];
  // 用户id
  userId: string | number;
  // 用户名
  username: string;
  // 真实名字
  realName: string;
  // 头像
  avatar: string;
  // 介绍
  desc?: string;
  //
  companyName: string;
  companyId: string;
  token?: string;
  nickname?: string;
  systemType: string;
  userName: string;
  account?: string;
  accountType: string;
}
