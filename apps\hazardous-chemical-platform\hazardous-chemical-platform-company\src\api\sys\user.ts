import { dataCenterHttp, defHttp } from '@/utils/http/axios';
import { LoginParams, LoginResultModel, GetUserInfoModel } from './model/userModel';
import { RouteItem } from './model/menuModel';
import { ErrorMessageMode } from '#/axios';

enum Api {
  Login = '/sysAuthAbout/login',
  Logout = '/sysAuthAbout/logoutCurrent',
  GetUserInfo = '/getUserInfo',
  GetPermCode = '/sysAuthAbout/getMenuListCurrent',
  TestRetry = '/testRetry',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return dataCenterHttp.post<LoginResultModel>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    }
  );
}

/**
 * @description: getUserInfo
 */
export function getUserInfo() {
  return defHttp.get<GetUserInfoModel>({ url: Api.GetUserInfo }, { errorMessageMode: 'none' });
}

export function getPermCode() {
  return dataCenterHttp.get<RouteItem[]>({ url: Api.GetPermCode });
}

export function doLogout() {
  return dataCenterHttp.post({ url: Api.Logout });
}

export function testRetry() {
  return defHttp.get(
    { url: Api.TestRetry },
    {
      retryRequest: {
        isOpenRetry: true,
        count: 5,
        waitTime: 1000,
      },
    }
  );
}
