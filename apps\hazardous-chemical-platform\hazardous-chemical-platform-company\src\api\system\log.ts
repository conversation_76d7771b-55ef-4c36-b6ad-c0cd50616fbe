import { dataCenterHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum OBJ {
  operationLogList = '/queryOperateLog',
  loginAndOutAccessLogList = '/queryLoginAndOutAccessLog',
}

function getApi(url?: string) {
  if (!url) {
    return '/log';
  }
  return '/log' + url;
}

//操作日志查询
export const operationLogList = params => {
  const { pageNum, pageSize } = params;
  return dataCenterHttp.post<BasicResponse>(
    { url: getApi(OBJ.operationLogList) + `?pageNum=${pageNum}&pageSize=${pageSize}`, params },
    {
      isTransformResponse: false,
    }
  );
};
//登录登出日志查询
export const loginAndOutAccessLogList = params => {
  const { pageNum, pageSize } = params;
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.loginAndOutAccessLogList) + `?pageNum=${pageNum}&pageSize=${pageSize}`,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = (autoId: number[] | number) => {
  return dataCenterHttp.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};
