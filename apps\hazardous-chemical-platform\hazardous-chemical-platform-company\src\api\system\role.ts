import { dataCenterHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum OBJ {
  findList = '/findRoleInfoList',
  view = '/getRoleInfo',
  getRoleListByAccount = '/getRoleListByAccount',
  save = '/saveRoleInfo',
  update = '/updateRoleInfo',
  permisstion = '/findRolePermissionList',
  roleEnableOrDisable = '/roleEnableOrDisable',
  deleteRoleInfo = '/deleteRoleInfo',
  findRolePermissionList = '/findRolePermissionList',
}

function getApi(url?: string) {
  if (!url) {
    return '/sysAuthAbout';
  }
  return '/sysAuthAbout' + url;
}

//列表
export const list = (params: Recordable) => {
  return dataCenterHttp.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增
export const save = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.save),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//修改
export const update = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.update),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//view
export const view = (params: Recordable) => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取指定账号的角色信息
export const getRoleListByAccount = (params: Recordable) => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.getRoleListByAccount),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.deleteRoleInfo),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 角色启用和禁用
export const roleEnableOrDisable = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.roleEnableOrDisable),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const roleList = (params: { pageSize: number; userId: string }) => {
  return dataCenterHttp.post<Recordable[]>({
    url: OBJ.findList,
    params,
  });
};

export const permisstion = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.permisstion),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const findRolePermissionList = params => {
  return dataCenterHttp.get<Recordable>({
    url: getApi(OBJ.findRolePermissionList),
    params,
  });
};
