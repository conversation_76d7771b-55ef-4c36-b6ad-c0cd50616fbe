<template>
  <a-input
    v-bind="$attrs"
    :class="prefixCls"
    :size="size"
    :value="state"
  >
    <template #addonAfter>
      <CountButton
        :size="size"
        :count="count"
        :value="state"
        :beforeStartFunc="sendCodeApi"
      />
    </template>
    <template
      #[item]="data"
      v-for="item in Object.keys($slots).filter(k => k !== 'addonAfter')"
    >
      <slot
        :name="item"
        v-bind="data || {}"
      ></slot>
    </template>
  </a-input>
</template>
<script lang="ts" setup>
import { PropType } from 'vue';
import CountButton from './CountButton.vue';
import { useRuleFormItem, useDesign } from '@monorepo-yysz/hooks';

defineOptions({ name: 'CountDownInput', inheritAttrs: false });

const props = defineProps({
  value: { type: String },
  size: { type: String, validator: (v: string) => ['default', 'large', 'small'].includes(v) },
  count: { type: Number, default: 60 },
  sendCodeApi: {
    type: Function as PropType<() => Promise<boolean>>,
    default: null,
  },
});

const { prefixCls } = useDesign('countdown-input');
const [state] = useRuleFormItem(props);
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-countdown-input';

.@{prefix-cls} {
  .ant-input-group-addon {
    padding-right: 0;
    border: none;
    background-color: transparent;

    button {
      font-size: 14px;
    }
  }
}
</style>
