<template>
  <div
    class="w-full h-full"
    :class="$style['cropper-img']"
  >
    <slot name="tip"></slot>
    <div class="w-full h-full img-content">
      <div
        class="ant-upload ant-upload-select ant-upload-select-picture-card !flex justify-center items-center !mr-0 !mb-0 !w-full !h-full overflow-hidden"
        @click="handleUploadappCover"
        v-if="!disabled"
      >
        <img
          v-if="url"
          :src="url"
          alt=""
          :class="{ '!h-full': ifH, '!w-full': ifW }"
        />
        <div
          v-else
          class="flex flex-col items-center"
        >
          <plus-outlined />
          <div class="ant-upload-text !mr-5px">上传图片</div>
        </div>
      </div>
      <Image
        :src="url"
        :fallback="!!errorImg ? errorImg : Error"
        class="!w-full !h-full !inline-block"
        v-else
      />
    </div>
    <CropperModal
      @register="register"
      :circled="false"
      @upload-success="handleUploadSuccess"
      :imgSize="imgSize"
      :upload-api="uploadFile"
      :operateType="operateType"
    />
  </div>
</template>

<script lang="ts" setup>
import CropperModal from './CropperModal.vue';
import { useModal } from '@/components/Modal';
import { Image } from 'ant-design-vue';
import { computed, ref, unref, useAttrs, watch } from 'vue';
import { uploadFile } from '@/api';
import { PlusOutlined } from '@ant-design/icons-vue';
import Error from '@/assets/images/error.png';
import { useGlobSetting } from '@/hooks/setting';

const props = defineProps({
  imgSize: {
    type: Number,
    default: 1,
  },
  operateType: {
    type: Number,
    default: 0,
  },
  value: {
    type: String,
  },
  ifError: {
    type: Boolean,
    default: false,
  },
  errorImg: {
    type: String,
  },
});

const emit = defineEmits(['change']);

const { filePrefix } = useGlobSetting();

//区别本地图片
const ifLocal = computed(() => {
  return props.value?.includes('/src/assets');
});

const ifHttp = computed(() => {
  return props.value?.includes('http');
});
const attrs = useAttrs();

const [register, { openModal, closeModal }] = useModal();

const disabled = computed(() => {
  return attrs.disabled;
});

const ifW = computed(() => {
  return props.imgSize > 1;
});

const ifH = computed(() => {
  return props.imgSize < 1;
});

const url = ref('');

function handleUploadappCover() {
  if (unref(disabled)) {
    return false;
  }

  openModal(true);
}

function handleUploadSuccess({ filePath }) {
  url.value = filePrefix + filePath;

  emit('change', filePath);
  closeModal();
}

watch(
  () => props.value,
  () => {
    url.value = props.value
      ? unref(ifLocal) || unref(ifHttp)
        ? props.value
        : filePrefix + props.value
      : '';
  },
  { deep: true, immediate: true }
);
</script>

<style lang="less" module>
.cropper-img {
  :global {
    .img-content {
      background-color: @app-content-background;
      border: 1px dashed @border-disabled;
    }
    .ant-upload {
      width: 128px !important;
      height: 128px !important;
    }
  }
}
</style>
