@simple-prefix-cls: ~'@{namespace}-simple-menu';
@prefix-cls: ~'@{namespace}-menu';

.@{prefix-cls} {
  &-dark&-vertical .@{simple-prefix-cls}__parent {
    // background-color: @sider-dark-bg-color;
    background-color: transparent;
    > .@{prefix-cls}-submenu-title {
      // background-color: @sider-dark-bg-color;
      background-color: transparent;
    }
  }

  &-dark&-vertical .@{simple-prefix-cls}__children,
  &-dark&-popup .@{simple-prefix-cls}__children {
    // background-color: @sider-dark-lighten-bg-color;
    background-color: transparent;

    > .@{prefix-cls}-submenu-title {
      // background-color: @sider-dark-lighten-bg-color;
      background-color: transparent;
    }
  }

  .collapse-title {
    overflow: hidden;
    font-size: 12px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-item-selected {
    color: @white;
    background: @menu-selected-bg;

    .@{simple-prefix-cls} {
      &-sub-title {
        color: @menu-selected-text;
      }
    }
  }
}

.@{namespace} {
  &-menu-opened > &-menu-submenu-title {
    // background-color: @menu-p-selected !important;
    background: linear-gradient(90deg, #edfbfd 0%, #cbf5fd 100%);
    font-weight: 600;
    color: @menu-selected-text !important;
  }
}

.@{simple-prefix-cls} {
  &-sub-title {
    overflow: hidden;
    transition: all 0.3s;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-tag {
    display: inline-block;
    position: absolute;
    top: calc(50% - 8px);
    right: 30px;
    margin-right: 4px;
    padding: 2px 3px;
    border-radius: 2px;
    color: #fff;
    font-size: 10px;
    line-height: 14px;

    &--collapse {
      top: 6px !important;
      right: 2px;
    }

    &--dot {
      top: calc(50% - 2px);
      width: 6px;
      height: 6px;
      padding: 0;
      border-radius: 50%;
    }

    &--primary {
      background-color: @primary-color;
    }

    &--error {
      background-color: @error-color;
    }

    &--success {
      background-color: @success-color;
    }

    &--warn {
      background-color: @warning-color;
    }
  }
}
