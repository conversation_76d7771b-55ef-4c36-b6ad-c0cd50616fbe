<template>
  <div class="w-full flex justify-center items-center absolute bottom-8 text-[#FFFFFF]">
    <div class="relative">
      <div v-if="VITE_GLOB_APP_MAIN_ONE">
        <label>主办单位：</label>
        <a
          class="cursor-pointer text-[#B4D8FD]"
          :href="VITE_GLOB_APP_MAIN_ONE_URL"
          target="_blank"
          >{{ VITE_GLOB_APP_MAIN_ONE }}</a
        >、
        <a
          class="cursor-pointer text-[#B4D8FD]"
          :href="VITE_GLOB_APP_MAIN_TWO_URL"
          target="_blank"
          >{{ VITE_GLOB_APP_MAIN_TWO }}</a
        >
        <br />
      </div>
      <div class="text-[#216EF0] text-[16px] font-semibold">
        推荐分辨率为 1920*1080 推荐使用 谷歌浏览器 技术支持：<a
          class="cursor-pointer text-[#216EF0] text-[16px] support"
          target="_blank"
          :href="VITE_GLOB_APP_SUPPORT_URL"
          >{{ VITE_GLOB_APP_SUPPORT }}</a
        >
      </div>
    </div>
    <div class="pl-6"><label> </label></div>
  </div>
</template>

<script lang="ts" setup>
import { getAppEnvConfig } from '@/utils/env';

const {
  VITE_GLOB_APP_MAIN_ONE,
  VITE_GLOB_APP_MAIN_TWO,
  VITE_GLOB_APP_SUPPORT,
  VITE_GLOB_APP_SUPPORT_URL,
  VITE_GLOB_APP_MAIN_ONE_URL,
  VITE_GLOB_APP_MAIN_TWO_URL,
} = getAppEnvConfig();
</script>
<style lang="less" scoped>
.support {
  &:hover {
    font-weight: 600;
    text-decoration: underline;
  }
}
</style>
