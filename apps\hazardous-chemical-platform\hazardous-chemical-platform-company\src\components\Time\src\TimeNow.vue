<template>
  <div class="flex justify-center items-center title-style !text-[#00EFFF]">
    {{ dateTime }} {{ dateUtil2.format('dddd') }}
  </div>
</template>

<script lang="ts" setup>
import { useDateFormat, useNow } from '@vueuse/core';
import { dateUtil2 } from '@monorepo-yysz/utils';

const dateTime = useDateFormat(useNow(), 'YYYY-MM-DD HH:mm:ss');
</script>

<style lang="less" scoped>
.title-style {
  font-family: Source Han Sans CN;
  color: @white;
}
</style>
