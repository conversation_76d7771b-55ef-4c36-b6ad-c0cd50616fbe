@import './pagination.less';
@import './input.less';
@import './btn.less';
@import './popconfirm.less';

.ant-image-preview-root {
  img {
    display: unset;
  }
}

.ant-back-top {
  right: 20px;
  bottom: 20px;
}

.collapse-container__body {
  > .ant-descriptions {
    margin-left: 6px;
  }
}

.ant-image-preview-operations {
  background-color: rgb(0 0 0 / 30%);
}

.ant-popover {
  &-content {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }
}

// =================================
// ==============modal message======
// =================================
.modal-icon-warning {
  color: @warning-color !important;
}

.modal-icon-success {
  color: @success-color !important;
}

.modal-icon-error {
  color: @error-color !important;
}

.modal-icon-info {
  color: @primary-color !important;
}

.ant-checkbox-checked .ant-checkbox-inner::after,
.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
  border-top: 0 !important;
  border-left: 0 !important;
}

.ant-form-item-control-input-content {
  > div {
    > div {
      max-width: 100%;
    }
  }
}

// 空
.ant-empty-description {
  color: @text-color-base;
}

// select
.ant-select-multiple {
  .ant-select-selection-overflow-item {
    .ant-select-selection-item {
      border-color: @select-selected-color;
      background-color: @select-selected-color;
    }
  }
}

.ant-select-selection-placeholder {
  color: #d3fff488 !important;
}

.ant-select-dropdown {
  background: fade(@app-content-background-origin, 80);

  .ant-select-tree {
    background-color: transparent;
  }

  .ant-select-item-option-selected,
  .ant-select-tree-node-selected {
    background-color: @select-selected-color !important;
  }
}

// radio
.ant-radio-group {
  .ant-radio-wrapper {
    .ant-radio-checked {
      .ant-radio-inner {
        border-color: @select-selected-color;
        background-color: @select-selected-color;

        &::after {
          background: @radio-inner-color;
        }
      }
    }
  }

  .ant-radio-button-wrapper-checked {
    background: @select-selected-color !important;
    border-color: @select-selected-color !important;

    &::before {
      background: @select-selected-color !important;
    }
  }

  .ant-radio-button-wrapper {
    &:hover {
      color: #d3fff488 !important;
    }
  }
}

// checkbox
// 多选
.ant-checkbox-wrapper {
  .ant-checkbox-checked {
    .ant-checkbox-inner {
      border-color: @select-selected-color;
      background-color: @select-selected-color;
    }
  }
}

// date
.ant-picker {
  .ant-picker-input {
    .ant-picker-suffix {
      color: @text-color-base;
    }
  }
}

.ant-picker-dropdown {
  .ant-picker-panel-container {
    border-color: @select-selected-color;
    background: fade(@app-content-background-origin, 80);

    .ant-picker-cell-selected {
      .ant-picker-cell-inner {
        background-color: @text-color-base;
      }
    }

    .ant-picker-time-panel-cell-selected {
      .ant-picker-time-panel-cell-inner {
        background-color: @text-color-base !important;
      }
    }

    .ant-picker-cell-range-start,
    .ant-picker-cell-range-end,
    .ant-picker-cell-in-range {
      .ant-picker-cell-inner,
      &::before {
        background-color: @text-color-base !important;
      }
    }

    .ant-picker-year-btn,
    .ant-picker-decade-btn,
    .ant-picker-month-btn {
      &:hover {
        color: @text-color-base;
      }
    }

    .ant-picker-ranges {
      .ant-picker-now {
        .ant-picker-now-btn {
          color: @text-color-base;
        }
      }
    }

    .ant-picker-today-btn {
      color: @text-color-base;
    }
  }
}

//notification
.ant-notification {
  .ant-notification-notice {
    background: fade(@app-content-background-origin, 80);
    border: 1px solid @select-selected-color;
  }
}

// message
.ant-message {
  .ant-message-notice {
    .ant-message-notice-content {
      background: fade(@app-content-background-origin, 80);
      border: 1px solid @select-selected-color;
    }
  }
}

// tree

.ant-tree-node-selected {
  background: @select-selected-color !important;

  &:hover {
    background: fade(@app-content-background-origin, 80) !important;
  }
}

.ant-tree-node-content-wrapper {
  &:hover {
    background: fade(@app-content-background-origin, 80) !important;
  }
}

.self-confirm {
  .ant-modal-confirm {
    .ant-modal-content {
      background: fade(@app-content-background-origin, 80);
      border: 1px solid @select-selected-color;
    }
  }
}

//
.ant-col {
  width: 100%;
}

.ant-modal-header {
  background-color: transparent !important;
  color: #fff !important;

  .ant-modal-title {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    /* stylelint-disable-next-line order/order */
    @apply !text-light-200 w-2/3 truncate;
  }
}

.ant-table {
  .ant-table-header {
    .ant-table-thead > tr > th {
      background-color: @table-title-color;
      border: unset !important;
    }
  }

  .ant-table-container {
    border: 1px solid @table-title !important;
    border-radius: 10px;
    border-start-start-radius: 10px !important;
    border-start-end-radius: 10px !important;

    .ant-table-body {
      .ant-table-row-expand-icon {
        border: 1px solid @pagination-border-color !important;
      }

      .ant-table-row-expand-icon-expanded {
        &::before {
          top: 4px !important;
          right: 3px !important;
          left: 3px !important;
          height: 7px !important;
          background: @table-pointer-bg-color;
          clip-path: polygon(0% 0%, 50% 100%, 100% 0%) !important;
        }

        &::after {
          top: 0 !important;
          right: 0 !important;
          left: 0 !important;
          height: 0 !important;
        }
      }

      .ant-table-row-expand-icon-collapsed {
        &::before {
          top: 4px !important;
          right: 3px !important;
          left: 3px !important;
          height: 7px !important;
          background: @table-pointer-bg-color;
          clip-path: polygon(100% 0%, 0% 50%, 100% 100%) !important;
        }

        &::after {
          top: 0 !important;
          right: 0 !important;
          left: 0 !important;
          height: 0 !important;
        }
      }

      .ant-table-tbody > tr.ant-table-row:hover {
        background-image: url('@/assets/images/basic/table-selected.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;

        > td {
          background: transparent !important;
        }
      }

      .ant-table-tbody > tr.ant-table-row > td {
        border: unset !important;
      }
    }

    .ant-table-header {
      .ant-table-thead {
        .ant-table-cell-scrollbar {
          box-shadow: unset !important;
        }
      }
    }
  }

  // .ant-table-cell-fix-right-first::after {
  //   border-inline-end: unset !important;
  // }
}
