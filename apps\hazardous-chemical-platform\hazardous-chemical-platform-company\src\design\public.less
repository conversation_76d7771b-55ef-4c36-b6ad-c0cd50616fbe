@prefix-table-action: ~'@{namespace}-basic-table-action';

#app {
  width: 100%;
  height: 100%;
}

// =================================
// ==============scrollbar==========
// =================================

body {
  scrollbar-color: @select-selected-color rgb(0 0 0 / 5%);

  ::-webkit-scrollbar {
    width: 7px;
    height: 8px;
  }

  // ::-webkit-scrollbar-track {
  //   background: transparent;
  // }

  ::-webkit-scrollbar-track {
    background-color: rgb(0 0 0 / 5%);
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: @select-selected-color;
    box-shadow: inset 0 0 6px rgb(0 0 0 / 20%);
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: @border-color-dark;
  }

  ::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}

// =================================
// ==============nprogress==========
// =================================
#nprogress {
  pointer-events: none;

  .bar {
    position: fixed;
    z-index: 99999;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    opacity: 0.75;
    background-color: @primary-color;
  }
}

.default-tree-class {
  flex-direction: column;

  .default-tree-search {
    width: 100%;
  }

  .default-tree-title {
    @apply font-semibold;
  }
}

.tree-info {
  border-right: 1px solid @default-border-color;
}

.deal-action {
  width: 100% !important;

  .@{prefix-table-action} {
    button {
      position: relative;
      margin: 5px 5px 5px 0;
    }
  }
}

/* 添加其他字体格式和路径（如 woff、woff2）以增强兼容性 */
@font-face {
  font-family: 'Source Han Sans CN';
  src: url('/resource/path/NotoSansSC-Medium.otf') format('truetype');
}

@font-face {
  font-family: 'Source Han Sans CN Regular';
  src: url('/resource/path/NotoSansSC-Regular.otf') format('truetype');
}

@font-face {
  font-family: 'Source Han Sans CN Bold';
  src: url('/resource/path/NotoSansSC-Bold.otf') format('truetype');
}

@font-face {
  /* stylelint-disable-next-line font-family-name-quotes */
  font-family: 'MaShanZheng-Regular';
  src: url('/resource/path/MaShanZheng-Regular.ttf') format('truetype');
}

.default-family {
  font-family: Source Han Sans CN;
}
