.bg-white {
  background-color: var(--component-background-color) !important;
}

html[data-theme='light'] {
  .text-secondary {
    color: rgb(0 0 0 / 45%);
  }

  .ant-alert-success {
    border: 1px solid #b7eb8f;
    background-color: #f6ffed;
  }

  .ant-alert-error {
    border: 1px solid #ffccc7;
    background-color: #fff2f0;
  }

  .ant-alert-warning {
    border: 1px solid #ffe58f;
    background-color: #fffbe6;
  }

  :not(:root):fullscreen::backdrop {
    background-color: @layout-body-background !important;
  }

  .back-transparent {
    .ant-input-affix-wrapper-disabled,
    .ant-input-number-affix-wrapper-disabled,
    .ant-input-number-disabled,
    .ant-input-affix-wrapper,
    .ant-picker-disabled,
    .ant-select-disabled,
    .ant-input-disabled,
    .ant-input-group-addon {
      background: transparent !important;
      cursor: auto !important;
      color: @text-color-call-out !important;
      border: transparent !important;

      textarea {
        resize: none !important;
      }

      div,
      input,
      .ant-select-selector,
      textarea,
      .ant-collapse-item-disabled {
        cursor: auto !important;
        color: @text-color-call-out !important;
        background: transparent !important;
        border: transparent !important;
      }

      .ant-select-arrow,
      .ant-picker-suffix,
      .ant-input-show-count-suffix {
        display: none;
      }

      .ant-picker-range-separator,
      .ant-picker-separator {
        cursor: auto !important;
      }
    }

    .ant-select-selection-item {
      cursor: auto !important;
      color: @text-color-call-out !important;
      background: transparent !important;
    }

    .ant-form-item {
      margin: 0 5px 16px !important;
      height: 100% !important;

      .ant-form-item-label {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 12px 0;

        .ant-form-item-required {
          &::before {
            content: '' !important;
          }
        }
      }

      .ant-form-item-control {
        display: flex;
        justify-content: center;

        .ant-input-textarea-show-count {
          &::after {
            content: '' !important;
          }
        }

        .ant-radio-group {
          label:not(.ant-radio-wrapper-checked) {
            display: none;
          }

          .ant-radio-wrapper-checked {
            cursor: auto !important;

            .ant-radio-checked {
              display: none;
            }

            span {
              color: @text-color-call-out !important;
              cursor: auto !important;
            }
          }
        }

        span {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .ant-form-item-explain-error,
      .ant-select-selection-placeholder {
        display: none !important;
      }

      /* stylelint-disable-next-line selector-no-vendor-prefix */
      input::-webkit-input-placeholder {
        /* WebKit browsers */
        color: transparent !important;
      }

      /* stylelint-disable-next-line selector-no-vendor-prefix */
      input:-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: transparent !important;
      }

      /* stylelint-disable-next-line selector-no-vendor-prefix */
      input::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: transparent !important;
      }

      /* stylelint-disable-next-line selector-no-vendor-prefix */
      input:-ms-input-placeholder {
        /* Internet Explorer 10+ */
        color: transparent !important;
      }

      //upload
      .ant-space {
        .ant-space-item {
          button {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
          }
        }
      }

      //富文本

      .tox-menubar,
      .tox-toolbar-overlord,
      .lt-tinymce-img-upload {
        display: none !important;
      }

      .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-checked) {
        display: none !important;
      }

      .ant-checkbox-wrapper-checked {
        span {
          color: @text-color-call-out !important;
        }

        .ant-checkbox {
          display: none !important;
        }
      }
    }
  }
}

[data-theme='dark'] {
  body {
    color: @text-color-base;
  }

  .ant-btn {
    &[disabled],
    &[disabled]:hover,
    &[disabled]:focus,
    &[disabled]:active {
      border-color: #007d61;
      background: rgb(255 255 255 / 8%);
      color: rgb(255 255 255 / 30%);
    }

    &-success.ant-btn-link.ant-btn-loading,
    &-warning.ant-btn-link.ant-btn-loading,
    &-error.ant-btn-link.ant-btn-loading,
    &-background-ghost.ant-btn-link.ant-btn-loading,
    &.ant-btn-link.ant-btn-loading {
      &::before {
        background: transparent;
      }
    }

    &:not(
        .ant-btn-link,
        .is-disabled,
        .ant-btn-primary,
        .ant-btn-success,
        .ant-btn-warning,
        .ant-btn-error,
        .ant-btn-dangerous
      ) {
      background: transparent;
      color: @text-color-base;

      &:hover {
        color: @button-primary-hover-color;
      }
    }

    &-dangerous.ant-btn-primary {
      &:focus {
        background: @error-color !important;
      }
    }

    &-default.ant-btn-dangerous {
      border-color: @error-color;
      background: transparent !important;
      color: @error-color;

      &:hover,
      &:focus {
        border-color: @button-error-hover-color !important;
        color: @button-error-hover-color !important;
      }
    }

    &-default:not(.ant-btn-background-ghost) {
      border-color: @default-border-color !important;

      &:hover,
      &:focus {
        border-color: @button-cancel-hover-color;
        color: @button-cancel-hover-color;
      }
    }

    &-default.is-disabled {
      &:hover,
      &:focus {
        border-color: #303030;
        color: rgb(255 255 255 / 30%);
      }
    }

    &-success:not(.is-disabled, .ant-btn-link, .ant-btn-background-ghost) {
      &:hover,
      &:focus,
      &:active {
        border-color: @button-success-active-color !important;
        background-color: @button-success-active-color !important;
        color: @white !important;
      }
    }

    &-warning:not(.is-disabled, .ant-btn-link, .ant-btn-background-ghost) {
      &:hover,
      &:focus,
      &:active {
        border-color: @button-warn-active-color !important;
        background-color: @button-warn-active-color !important;
        color: @white !important;
      }
    }

    &-error:not(.is-disabled, .ant-btn-link, .ant-btn-background-ghost) {
      &:hover,
      &:focus,
      &:active {
        border-color: @button-error-active-color !important;
        background-color: @button-error-active-color !important;
        color: @white !important;
      }
    }
  }

  // 处理斑马纹切换有白底
  .ant-table-cell-row-hover {
    background: transparent !important;
  }

  .back-transparent {
    .ant-input-affix-wrapper-disabled,
    .ant-input-number-affix-wrapper-disabled,
    .ant-input-number-disabled,
    .ant-input-affix-wrapper,
    .ant-picker-disabled,
    .ant-select-disabled,
    .ant-input-disabled,
    .ant-input-group-addon {
      background: transparent !important;
      cursor: auto !important;
      color: @text-color-base !important;
      border: transparent !important;

      textarea {
        resize: none !important;
      }

      div,
      input,
      .ant-select-selector,
      textarea,
      .ant-collapse-item-disabled {
        cursor: auto !important;
        color: @text-color-base !important;
        background: transparent !important;
        border: transparent !important;
      }

      .ant-select-arrow,
      .ant-picker-suffix,
      .ant-input-show-count-suffix {
        display: none;
      }

      .ant-picker-range-separator,
      .ant-picker-separator {
        cursor: auto !important;
      }
    }

    .ant-select-selection-item {
      cursor: auto !important;
      color: @text-color-base !important;
      background: transparent !important;
    }

    .ant-form-item {
      margin: 0 5px 16px !important;
      height: 100% !important;

      .ant-form-item-label {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 12px 0;

        .ant-form-item-required {
          &::before {
            content: '' !important;
          }
        }
      }

      .ant-form-item-control {
        display: flex;
        justify-content: center;

        .ant-input-textarea-show-count {
          &::after {
            content: '' !important;
          }
        }

        .ant-radio-group {
          label:not(.ant-radio-wrapper-checked) {
            display: none;
          }

          .ant-radio-wrapper-checked {
            cursor: auto !important;

            .ant-radio-checked {
              display: none;
            }

            span {
              color: @text-color-base !important;
              cursor: auto !important;
            }
          }
        }

        span {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .ant-form-item-explain-error,
      .ant-select-selection-placeholder {
        display: none !important;
      }

      /* stylelint-disable-next-line selector-no-vendor-prefix */
      input::-webkit-input-placeholder {
        /* WebKit browsers */
        color: transparent !important;
      }

      /* stylelint-disable-next-line selector-no-vendor-prefix */
      input:-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: transparent !important;
      }

      /* stylelint-disable-next-line selector-no-vendor-prefix */
      input::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: transparent !important;
      }

      /* stylelint-disable-next-line selector-no-vendor-prefix */
      input:-ms-input-placeholder {
        /* Internet Explorer 10+ */
        color: transparent !important;
      }

      //upload
      .ant-space {
        .ant-space-item {
          button {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
          }
        }
      }

      //富文本

      .tox-menubar,
      .tox-toolbar-overlord,
      .lt-tinymce-img-upload {
        display: none !important;
      }

      .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-checked) {
        display: none !important;
      }

      .ant-checkbox-wrapper-checked {
        span {
          color: @text-color-base !important;
        }

        .ant-checkbox {
          display: none !important;
        }
      }
    }
  }
}
