import type { GlobConfig } from '#/config';

import { getAppEnvConfig } from '@/utils/env';

export const useGlobSetting = (): Readonly<GlobConfig> => {
  const {
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_API_URL,
    VITE_GLOB_API_URL_PREFIX,
    VITE_GLOB_UPLOAD_URL,
    VITE_PUBLIC_PATH,
    VITE_GLOB_BAIDU_SERVICE,
    VITE_GLOB_FILE_PREFIX,
    VITE_GLOB_KK_FILE_PREFIX,
    VITE_GLOB_BUCKET_NAME,
    VITE_GLOB_MODEL_URL,
  } = getAppEnvConfig();

  // Take global configuration
  const glob: Readonly<GlobConfig> = {
    title: VITE_GLOB_APP_TITLE,
    apiUrl: VITE_GLOB_API_URL,
    shortName: VITE_GLOB_APP_TITLE.replace(/\s/g, '_').replace(/-/g, '_'),
    urlPrefix: VITE_GLOB_API_URL_PREFIX,
    uploadUrl: VITE_GLOB_UPLOAD_URL,
    rootUrl: VITE_PUBLIC_PATH,
    map_api: VITE_GLOB_BAIDU_SERVICE,
    filePrefix: VITE_GLOB_FILE_PREFIX,
    KKFilePrefix: VITE_GLOB_KK_FILE_PREFIX,
    bucket_name: VITE_GLOB_BUCKET_NAME,
    modelUrl: VITE_GLOB_MODEL_URL,
  };
  return glob as Readonly<GlobConfig>;
};
