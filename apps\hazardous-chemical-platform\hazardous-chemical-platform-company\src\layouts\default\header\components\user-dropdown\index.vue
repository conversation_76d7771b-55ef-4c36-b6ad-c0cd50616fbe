<template>
  <Dropdown
    placement="bottomLeft"
    :overlayClassName="`${prefixCls}-dropdown-overlay`"
  >
    <span
      :class="[prefixCls, `${prefixCls}--${theme}`]"
      class="flex"
    >
      <img
        :class="`${prefixCls}__header`"
        :src="getUserInfo.avatar"
      />
      <span :class="`${prefixCls}__info md:block`">
        <span
          :class="`${prefixCls}__name`"
          class="truncate text-[#fff]"
        >
          您好{{ getUserInfo.account || '' }}
        </span>
      </span>
      <Icon
        icon="ion:caret-down"
        class="text-[#00DFFF] ml-1"
      />
    </span>

    <template #overlay>
      <Menu @click="handleMenuClick">
        <MenuItem
          key="doc"
          :text="t('layout.header.dropdownItemDoc')"
          icon="ion:document-text-outline"
          v-if="getShowDoc"
        />
        <Menu.Divider v-if="getShowDoc" />
        <MenuItem
          v-if="getShowApi"
          key="api"
          :text="t('layout.header.dropdownChangeApi')"
          icon="ant-design:swap-outlined"
        />
        <MenuItem
          v-if="getUseLockPage"
          key="lock"
          :text="t('layout.header.tooltipLock')"
          icon="ion:lock-closed-outline"
        />
        <MenuItem
          key="logout"
          :text="t('layout.header.dropdownItemLoginOut')"
          icon="ion:power-outline"
        />
      </Menu>
    </template>
  </Dropdown>
</template>
<script lang="ts" setup>
import { Dropdown, Menu } from 'ant-design-vue';
import type { MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
import { computed } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { useHeaderSetting } from '@/hooks/setting/useHeaderSetting';
import { useI18n } from '@/hooks/web/useI18n';
import { useDesign } from '@monorepo-yysz/hooks';
import headerImg from '@/assets/images/pic.png';
import { propTypes } from '@monorepo-yysz/utils';
import { createAsyncComponent } from '@monorepo-yysz/ui';
import { Icon } from '@monorepo-yysz/ui';

type MenuEvent = 'logout' | 'lock';

const MenuItem = createAsyncComponent(() => import('./DropMenuItem.vue'));

defineOptions({ name: 'UserDropdown' });

defineProps({
  theme: propTypes.oneOf(['dark', 'light']),
});

const { prefixCls } = useDesign('header-user-dropdown');
const { t } = useI18n();
const { getShowDoc, getUseLockPage, getShowApi } = useHeaderSetting();
const userStore = useUserStore();

const getUserInfo = computed(() => {
  const { account = '', avatar, desc } = userStore.getUserInfo || {};
  return { account, avatar: avatar || headerImg, desc };
});

// function handleLock() {
//   openModal(true);
// }

//  login out
function handleLoginOut() {
  userStore.confirmLoginOut();
}

function handleMenuClick(e: MenuInfo) {
  switch (e.key as MenuEvent) {
    case 'logout':
      handleLoginOut();
      break;
    case 'lock':
      // handleLock();
      break;
  }
}
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-header-user-dropdown';

.@{prefix-cls} {
  align-items: center;
  height: 60px; //@header-height;
  padding: 0 0 0 10px;
  padding-right: 10px;
  overflow: hidden;
  font-size: 12px;
  cursor: pointer;

  img {
    width: 24px;
    height: 24px;
    margin-right: 12px;
  }

  &__header {
    border-radius: 50%;
  }

  &__name {
    font-size: 14px;
  }

  &--dark {
    // &:hover {
    //   background-color: @header-dark-bg-hover-color;
    // }
  }

  &--light {
    // &:hover {
    //   background-color: @header-light-bg-hover-color;
    // }

    .@{prefix-cls}__name {
      color: @text-color-base;
    }

    .@{prefix-cls}__desc {
      color: @header-light-desc-color;
    }
  }

  &-dropdown-overlay {
    .ant-dropdown-menu {
      background-color: @app-content-background;

      .ant-dropdown-menu-item {
        min-width: 160px;
        color: #fff !important;
        border-bottom: 1px dashed #83b7ff;
      }

      .ant-dropdown-menu-item-active {
        min-width: 160px;
        color: #00eaff !important;
        background: linear-gradient(
          90deg,
          rgba(0, 121, 191, 0) 0%,
          rgba(0, 121, 191, 0.31) 18%,
          rgba(0, 121, 191, 0.99) 50%,
          rgba(0, 121, 191, 0.29) 80%,
          rgba(0, 121, 191, 0) 100%
        );
      }
    }
  }
}
</style>
