<template>
  <div :class="$style['content-tabs']">
    <div
      class="flex items-center w-full h-[50px]"
      :style="{
        backgroundImage: `url(${secondBg})`,
        backgroundSize: '100% 100%',
        backgroundRepeat: 'no-repeat',
      }"
    >
      <div
        class="h-full w-[18.5%] flex justify-center items-center text-[24px] text-[#F3FCFF] default-family"
      >
        {{ currentTabRoute?.meta?.title }}
      </div>
      <div class="w-[81.5%] pl-20 pr-10 second-tabs">
        <a-tabs
          v-model:activeKey="activeKey"
          tab-position="top"
          @tab-click="handleFirstTab"
        >
          <a-tab-pane
            v-for="first in firstTabs"
            :key="first.path"
            :tab="first.meta?.title || ''"
          ></a-tab-pane>
        </a-tabs>
      </div>
    </div>
    <div
      class="h-[50px] pl-4 third-tabs"
      v-if="secondTabs?.length > 0"
    >
      <div
        class="w-full mt-[20px] pr-10 pl-5 relative"
        :style="{
          backgroundImage: `url(${thirdBg})`,
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
        }"
      >
        <a-tabs
          v-model:activeKey="activeChildKey"
          tab-position="top"
          type="card"
          @tab-click="handleSecondTab"
          class="bottom-3 relative"
        >
          <a-tab-pane
            v-for="second in secondTabs"
            :key="second.path"
            :tab="second.meta?.title || ''"
          ></a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, unref } from 'vue';
import { useTabPageContext } from '../hooks/useTabPage';
import secondBg from '@/assets/images/layouts/second-bg.png';
import thirdBg from '@/assets/images/layouts/third-bg.png';
import { usePermissionStore } from '@/store/modules/permission';
import { Menu } from '@/router/types';
import { filter, find, isArray, isEmpty } from 'lodash-es';
import { useGo } from '@/hooks/web/usePage';
import { RouterEnum } from '@monorepo-yysz/enums';
import { useNewTabStore } from '@/store/modules/new-tab';
import { usePermission } from '@/hooks/web/usePermission';

const { tabRouterEmitter } = useTabPageContext();

const permissionStore = usePermissionStore();

const { hasPermission } = usePermission();

const useNewTab = useNewTabStore();

const go = useGo();

const activeKey = ref();

const firstTabs = ref<Menu[]>([]);

const activeChildKey = ref();

const secondTabs = ref<Menu[]>([]);

const currentTabRoute = ref();

function handleSecondTab(key) {
  useNewTab.setSecondTabSelected(key);

  go(key);
}

function handleFirstTab(key) {
  useNewTab.setFirstTabSelected(key);

  hasThirdRouter(key);
}

// 校验三级
function hasThirdRouter(path) {
  let goPath = path;

  const tabRouter = find(permissionStore.getAllPermList || [], (v: Menu) => v.path === path);

  // 过滤子集
  secondTabs.value = filter(
    permissionStore.getAllPermList,
    (v: Recordable) =>
      v.pid === tabRouter?.menuId &&
      v.menuType !== RouterEnum.BUTTON &&
      !v.meta?.hideMenu &&
      hasPermission(v.path)
  ) as Menu[];

  if (isArray(unref(secondTabs)) && !isEmpty(unref(secondTabs))) {
    activeChildKey.value = useNewTab.getSecondTabSelected || unref(secondTabs)?.[0]?.path;

    goPath = unref(activeChildKey);
  }

  go(goPath);
}

onBeforeMount(() => {
  tabRouterEmitter?.on('tab-header-change', function (router: Menu) {
    if (router) {
      currentTabRoute.value = router;

      const routeMap = permissionStore.getRouteMap;

      const children = routeMap.get(router.path) || [];
      console.log(router, children);

      // 二级菜单 pid为当前路由的id，菜单类型不为按钮，不隐藏的 默认本地的路由展示所有下级
      firstTabs.value = router?.meta?.ifLocal
        ? children
        : (filter(
            children,
            (v: Menu) =>
              v.meta?.pid === router.meta?.menuId &&
              v.meta?.menuType !== RouterEnum.BUTTON &&
              !v.meta?.hideMenu &&
              hasPermission(v.path)
          ) as Menu[]);

      if (!isEmpty(unref(firstTabs))) {
        activeKey.value = useNewTab.getFirstTabSelected || unref(firstTabs)?.[0]?.path;

        // 校验是否存在三级路由
        hasThirdRouter(unref(activeKey));
      }
    }
  });
});
</script>

<style lang="less" module>
.content-tabs {
  :global {
    .default-family {
      text-shadow:
        0 0 20px #3bf5bb,
        0 0 20px #3bf5bb;
    }

    .ant-tabs {
      .ant-tabs-nav {
        margin: 0;
        &::before {
          border: unset;
        }

        .ant-tabs-nav-operations {
          .ant-tabs-nav-more {
            cursor: pointer !important;
          }
        }
      }
    }

    .second-tabs {
      .ant-tabs {
        .ant-tabs-nav {
          .ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              color: @table-pointer-bg-color !important;
            }
          }

          .ant-tabs-tab {
            padding-bottom: 0px !important;
            padding-top: 5px !important;

            .ant-tabs-tab-btn {
              color: #dfeef3;
              font-size: 16px;
            }
          }

          .ant-tabs-ink-bar {
            background-color: @table-pointer-bg-color !important;
          }
        }
      }
    }

    .third-tabs {
      .ant-tabs {
        padding-left: 10px;

        .ant-tabs-tab-active {
          border-bottom: unset !important;
          border: unset !important;
          background: rgba(255, 255, 255, 0.04);

          .ant-tabs-tab-btn {
            color: @table-pointer-bg-color !important;
          }
        }

        .ant-tabs-tab {
          border-radius: 0 !important;
          border-bottom: unset !important;
          border: unset !important;
          box-shadow: inset 0 0 20px 0 #238087;

          &:hover {
            color: #00eeff7c !important;
          }
        }
      }
    }
  }
}
</style>
