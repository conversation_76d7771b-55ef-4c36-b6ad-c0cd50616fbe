<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :canFullscreen="false"
    width="833px"
    :bodyStyle="{ height: '50vh' }"
    :showCancelBtn="false"
    :showOkBtn="false"
    :wrapClassName="$style['common-modal']"
    @cancel="handleClose"
  >
    <template #title>
      <div class="header-title">常用功能管理</div>
    </template>

    <div class="px-[20px]">
      <div class="relative">
        <label class="header-title !text-[20px]">常用功能</label>
        <div class="w-full block">
          <div
            class="!text-white !m-1 !p-2 !text-sm relative w-[15.6%] inline-block"
            v-for="element in gridLists"
          >
            <div class="flex flex-col justify-center items-center relative">
              <img :src="icon_dashboard" />
              <div class="text-[#00D6FF]">{{ element.meta?.title }}</div>

              <Icon
                icon="icon-park-twotone:close-one"
                class="absolute top-0 right-[8px] cursor-pointer !text-red-500"
                :size="20"
                @click="handleClick(element, false)"
              />
            </div>
          </div>
        </div>
      </div>
      <div>
        <label class="header-title !text-[20px]">功能列表</label>
        <div class="w-full block">
          <div
            class="!text-white !m-1 !p-2 !text-sm relative w-[15.6%] inline-block"
            v-for="element in gridOthers"
          >
            <div class="flex flex-col justify-center items-center relative">
              <img :src="icon_dashboard" />
              <div class="text-[#00D6FF]"> {{ element.meta?.title }}</div>

              <Icon
                icon="icon-park-twotone:add-one"
                class="absolute top-0 right-[8px] cursor-pointer !text-green-500"
                :size="20"
                @click="handleClick(element, true)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { useModalInner, BasicModal } from '@/components/Modal';
import { differenceWith, remove } from 'lodash-es';
import { ref, unref } from 'vue';
import icon_dashboard from '@/assets/images/home-page/icon_dashboard.png';
import { Icon } from '@monorepo-yysz/ui';

import { MenuSplitTyeEnum } from '@monorepo-yysz/enums';
import { useSplitMenu } from '@/layouts/default/menu/useLayoutMenu';
import { Menu } from '@/router/types';
import dashboard from '@/router/routes/modules/dashboard';
import { useNewTabStore } from '@/store/modules/new-tab';
import mapRouter from '@/router/routes/modules/mapRouter';

const emit = defineEmits(['register', 'cancel']);

const useNewTab = useNewTabStore();

const splitType = ref<MenuSplitTyeEnum>(MenuSplitTyeEnum.NONE);

const gridLists = ref<Menu[]>([]);

const { menusRef } = useSplitMenu(splitType);

const gridOthers = ref<Menu[]>([]);

const [registerModal] = useModalInner(async data => {
  //TODO 掉接口查看

  gridLists.value = data.selectedPaths;

  gridOthers.value = differenceWith(
    [dashboard, mapRouter, ...unref(menusRef)],
    data.selectedPaths,
    (a, b: Menu) => a.path === b.path
  );

  console.log(unref(gridOthers));
});

function handleClick(item, isAdd) {
  if (isAdd) {
    if (unref(gridLists)?.length < 6) {
      gridLists.value.push(item);

      remove(unref(gridOthers), v => v.path === item.path);
    }
  } else {
    remove(unref(gridLists), v => v.path === item.path);
    gridOthers.value.push(item);
  }
  useNewTab.setNormalTab(unref(gridLists));
}

function handleClose() {
  emit('cancel');
}
</script>

<style lang="less" module>
.common-modal {
  :global {
    .header-title {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 24px;
      color: #ffffff;
      line-height: 42px;
      background: linear-gradient(0deg, #00a2ff 0%, #00f0ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .ant-modal-title {
      width: 100%;
      text-align: center;
    }

    .ant-modal-body {
      background-image: url('@/assets/images/basic/modal_bg_02.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .ant-modal-header {
      background-color: transparent !important;
      background-image: url('@/assets/images/basic/modal_bg_01.png');
      background-repeat: no-repeat;
      background-size: 100%;
    }

    .ant-modal-footer {
      background-image: url('@/assets/images/basic/modal_bg_03.png');
      background-repeat: no-repeat;
      background-size: 100% 101%;
      background-position: center;
    }
  }
}
</style>
