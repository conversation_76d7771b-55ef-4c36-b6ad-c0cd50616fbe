<template>
  <Layout.Header :class="[$style.header, '!bg-transparent']">
    <div
      class="w-full h-full flex px-[40px]"
      :style="{
        backgroundImage: `url(${bg_top})`,
        backgroundSize: '100% 100%',
        backgroundRepeat: 'no-repeat',
      }"
    >
      <div
        class="h-full px-2 w-[74px] h-full text-[24px]"
        :style="{
          backgroundImage: `url(${normalBg})`,
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
        }"
      >
        <div class="h-1/2 flex justify-center items-center pt-2">常 用</div>
        <div class="h-1/2 flex justify-center items-center pb-2">功 能</div>
      </div>
      <div class="w-4/5 flex pl-8">
        <div
          v-for="item in menus"
          class="-enter-x flex justify-between items-center flex-col mx-1 cursor-pointer w-[100px] py-2"
          @click="handleClick(item)"
        >
          <img :src="backImg(item)" />
          <div
            class="line-height-none"
            :style="{ fontFamily: 'Source Han Sans CN' }"
            :class="backClazz(item)"
          >
            {{ item?.meta?.title }}
          </div>
        </div>
        <div class="-enter-x flex justify-center items-center ml-20 h-full">
          <img
            :src="icon_editor"
            class="w-[62px] h-[62px] cursor-pointer"
            @click.stop="handleOtherRouter"
          />
        </div>
      </div>

      <Flex class="h-full w-1/5 justify-end items-center">
        <UserDropDown :theme="getHeaderTheme" />
      </Flex>
      <common-modal
        @register="registerModal"
        @cancel="handleCancel"
      />
    </div>
  </Layout.Header>
</template>

<script lang="ts" setup>
import { useHeaderSetting } from '@/hooks/setting/useHeaderSetting';
import { Layout, Flex } from 'ant-design-vue';
import { onBeforeMount, onMounted, ref, unref } from 'vue';
import bg_top from '@/assets/images/dashboard/bg_top.jpg';
import { includes, map } from 'lodash-es';
import normalBg from '@/assets/images/layouts/normal-bg.png';
import { UserDropDown } from '@/layouts/default/header/components';
import { useTabPageContext } from '../hooks/useTabPage';
import icon_dashboard_selected from '@/assets/images/home-page/icon_dashboard_selected.png';
import icon_dashboard from '@/assets/images/home-page/icon_dashboard.png';
import icon_editor from '@/assets/images/home-page/editor.png';
import { useModal } from '@/components/Modal';
import CommonModal from './common-modal.vue';
import { Menu } from '@/router/types';
import { useNewTabStore } from '@/store/modules/new-tab';
import { useGo } from '@/hooks/web/usePage';
import { PageEnum } from '@/enums/pageEnum';

defineOptions({ name: 'LayoutTabHeader' });

const { getHeaderTheme } = useHeaderSetting();

const { tabRouterEmitter } = useTabPageContext();

const useNewTab = useNewTabStore();

const go = useGo();

const [registerModal, { openModal }] = useModal();

const clickPath = ref<string>();

const menus = ref<Menu[]>([]);

// 处理反选
const backImg = item =>
  includes([item.redirect, item.path], unref(clickPath)) ? item.selectedIcon : item.icon;

const backClazz = item =>
  includes([item.redirect, item.path], unref(clickPath))
    ? ' text-[#FFC72C] text-[18px]'
    : ' text-[#00D6FF] text-[16px]';

// 点击
function handleClick(item) {
  // 单独处理首页
  if (item.path === PageEnum.BASE_HOME) {
    go(item.path);
  } else if (unref(clickPath) !== item.path) {
    //TODO 暂时不处理redirect
    // 清除
    useNewTab.setFirstTabSelected(undefined);

    clickPath.value = item.path;
    useNewTab.setHeaderTabSelected(item.path);
    useNewTab.setHeaderTabRouterSelected(item);

    tabRouterEmitter?.emit('tab-header-change', item);
  }
}

function reSetMenu() {
  //TODO 接口交互
  menus.value = map(useNewTab.getNormalTab || [], v => ({
    ...v,
    selectedIcon: icon_dashboard_selected,
    icon: icon_dashboard,
  }));
}

//常用功能
function handleOtherRouter() {
  openModal(true, { selectedPaths: unref(menus) });
}

function handleCancel() {
  reSetMenu();
}

onBeforeMount(() => {
  reSetMenu();
});

onMounted(() => {
  clickPath.value = useNewTab.getHeaderTabSelected; //unref(currentRoute)?.path;

  tabRouterEmitter?.emit('tab-header-change', useNewTab.getHeaderTabRouterSelected as Menu);
});
</script>

<style lang="less" module>
.header {
  :global {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 110px !important;
    background-color: transparent;
    color: @white;
    padding-inline: unset !important;
  }
}
</style>
