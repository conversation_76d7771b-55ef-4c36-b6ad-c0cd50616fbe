import { createContext, useContext } from '@monorepo-yysz/hooks';
import { Menu } from '@/router/types';
import { Emitter } from '@monorepo-yysz/utils';
import { InjectionKey } from 'vue';

export type TabPageEmitterEvents = {
  'tab-header-change': Menu;
  'tab-footer-change': Recordable;
};

export interface TabPageContextProps {
  tabRouterEmitter: Emitter<TabPageEmitterEvents>;
}

const key: InjectionKey<TabPageContextProps> = Symbol();

export function createTabPageContext(context: TabPageContextProps) {
  return createContext<TabPageContextProps>(context, key, { readonly: false, native: true });
}

export function useTabPageContext() {
  return useContext<TabPageContextProps>(key);
}
