<template>
  <Layout
    :class="prefixCls"
    v-bind="lockEvents"
  >
    <LayoutHeader
      v-if="getShowInsetHeaderRef"
      class="z-1"
    />
    <Layout
      :class="[`ant-layout ${prefixCls}-out`]"
      class="z-1"
    >
      <Layout :class="`${prefixCls}-main`">
        <LayoutContentTabs />
        <LayoutTabContent />
      </Layout>
    </Layout>
  </Layout>
</template>

<script lang="ts" setup>
import { useDesign } from '@monorepo-yysz/hooks';
import { useLockPage } from '@/hooks/web/useLockPage';
import { Layout } from 'ant-design-vue';
import { Content as LayoutTabContent } from './components';
import { useHeaderSetting } from '@/hooks/setting/useHeaderSetting';
import LayoutHeader from './components/header/index.vue';
import LayoutContentTabs from './components/content-tabs/index.vue';
import { TabPageEmitterEvents, createTabPageContext } from './components/hooks/useTabPage';
import { mitt } from '@monorepo-yysz/utils';

defineOptions({ name: 'TabLayout' });

const { getShowInsetHeaderRef } = useHeaderSetting();

const { prefixCls } = useDesign('default-layout');

const tabRouterEmitter = mitt<TabPageEmitterEvents>();

// Create a lock screen monitor
const lockEvents = useLockPage();
createTabPageContext({ tabRouterEmitter: tabRouterEmitter });
</script>

<style lang="less">
@prefix-cls: ~'@{namespace}-default-layout';

.@{prefix-cls} {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;
  padding: 25px 20px 20px;

  > .ant-layout {
    min-height: 100%;
  }

  &-main {
    width: 100%;
    // margin-left: 1px;
  }
}

// .@{prefix-cls}-out {
//   &.ant-layout-has-sider {
//     .@{prefix-cls} {
//       &-main {
//         // margin-left: 1px;
//       }
//     }
//   }
// }
</style>
