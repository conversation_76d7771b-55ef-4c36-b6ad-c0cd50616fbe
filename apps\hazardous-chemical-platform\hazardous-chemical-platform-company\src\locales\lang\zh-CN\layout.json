{"footer": {"onlinePreview": "在线预览", "onlineDocument": "在线文档"}, "header": {"dropdownChangeApi": "切换API", "dropdownItemDoc": "文档", "dropdownItemLoginOut": "退出系统", "tooltipErrorLog": "错误日志", "tooltipLock": "锁定屏幕", "tooltipNotify": "消息通知", "tooltipEntryFull": "全屏", "tooltipExitFull": "退出全屏", "lockScreenPassword": "锁屏密码", "lockScreen": "锁定屏幕", "lockScreenBtn": "锁定", "home": "首页"}, "multipleTab": {"reload": "重新加载", "close": "关闭标签页", "closeLeft": "关闭左侧标签页", "closeRight": "关闭右侧标签页", "closeOther": "关闭其它标签页", "closeAll": "关闭全部标签页"}, "setting": {"contentModeFull": "流式", "contentModeFixed": "定宽", "topMenuAlignLeft": "居左", "topMenuAlignRight": "居中", "topMenuAlignCenter": "居右", "menuTriggerNone": "不显示", "menuTriggerBottom": "底部", "menuTriggerTop": "顶部", "menuTypeSidebar": "左侧菜单模式", "menuTypeMixSidebar": "左侧菜单混合模式", "menuTypeMix": "顶部菜单混合模式", "menuTypeTopMenu": "顶部菜单模式", "on": "开", "off": "关", "minute": "分钟", "operatingTitle": "操作成功", "operatingContent": "复制成功,请到 src/settings/projectSetting.ts 中修改配置！", "resetSuccess": "重置成功！", "copyBtn": "拷贝", "clearBtn": "清空缓存并返回登录页", "drawerTitle": "项目配置", "darkMode": "主题", "navMode": "导航栏模式", "interfaceFunction": "界面功能", "interfaceDisplay": "界面显示", "animation": "动画", "splitMenu": "分割菜单", "closeMixSidebarOnChange": "切换页面关闭菜单", "sysTheme": "系统主题", "headerTheme": "顶栏主题", "sidebarTheme": "菜单主题", "menuDrag": "侧边菜单拖拽", "menuSearch": "菜单搜索", "menuAccordion": "侧边菜单手风琴模式", "menuCollapse": "折叠菜单", "collapseMenuDisplayName": "折叠菜单显示名称", "topMenuLayout": "顶部菜单布局", "menuCollapseButton": "菜单折叠按钮", "contentMode": "内容区域宽度", "expandedMenuWidth": "菜单展开宽度", "breadcrumb": "面包屑", "breadcrumbIcon": "面包屑图标", "tabs": "标签页", "tabDetail": "标签详情页", "tabsQuickBtn": "标签页快捷按钮", "tabsRedoBtn": "标签页刷新按钮", "tabsFoldBtn": "标签页折叠按钮", "sidebar": "左侧菜单", "header": "顶栏", "footer": "页脚", "fullContent": "全屏内容", "grayMode": "灰色模式", "colorWeak": "色弱模式", "progress": "顶部进度条", "switchLoading": "切换loading", "switchAnimation": "切换动画", "animationType": "动画类型", "autoScreenLock": "自动锁屏", "notAutoScreenLock": "不自动锁屏", "fixedHeader": "固定header", "fixedSideBar": "固定Sidebar", "mixSidebarTrigger": "混合菜单触发方式", "triggerHover": "悬停", "triggerClick": "点击", "mixSidebarFixed": "固定展开菜单", "autoCollapseTabsInFold": "fold模式下自动收起标签页"}}