<template>
  <span
    class="mars-icon"
    v-html="svgComponent"
  ></span>
</template>
<script lang="ts">
import { computed, useAttrs, defineComponent } from 'vue';
import * as svgModule from '@icon-park/svg';
import { upperFirst, camelCase } from 'lodash-es';
import DOMPurify from 'dompurify';

export default defineComponent({
  name: 'MarsIcon',
  // inheritAttrs: false,
  props: {
    icon: {
      type: String,
    },
    color: {
      type: String,
    },
    width: {
      type: [String, Number],
      default: '14',
    },
  },
  setup(props) {
    const attrs = useAttrs();
    const iconName = computed(() => upperFirst(camelCase(props.icon)));

    const svgComponent = DOMPurify.sanitize(
      svgModule[iconName.value]({
        theme: 'outline',
        fill: props.color,
        size: props.width,
        ...attrs,
      })
    );

    return {
      attrs,
      svgComponent,
    };
  },
});
</script>
<style lang="less" scoped>
.mars-icon {
  line-height: 1;
  vertical-align: middle;
}
</style>
