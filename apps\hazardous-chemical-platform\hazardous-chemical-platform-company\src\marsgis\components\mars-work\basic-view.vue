<template>
  <ConfigProvider :locale="locale">
    <div
      class="mars-main-view"
      id="mars-main-view"
      ref="marsMainView"
    >
      <div
        id="centerDiv"
        class="centerDiv-container"
      >
        <mars-map
          :url="configUrl"
          :options="mapOptions"
          @onload="marsOnload"
        />
      </div>
    </div>
  </ConfigProvider>
</template>

<script setup lang="ts">
/**
 * 渲染主入口
 * @copyright
 * <AUTHOR>
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { provide, ref } from 'vue';
import { ConfigProvider } from 'ant-design-vue';
import MarsMap from '@mars/components/mars-work/mars-map.vue';
import * as mars3d from 'mars3d';
import { useGlobSetting } from '@/hooks/setting';

const emit = defineEmits(['mapLoaded']);

withDefaults(
  defineProps<{
    mapOptions?: any;
  }>(),
  {
    mapOptions: () => ({ mapKey: 'basic' }),
  }
);

const locale = zhCN;

const { rootUrl } = useGlobSetting();

const configUrl = `${rootUrl}config/config.json?time=${new Date().getTime()}`;

const eventTarget = new mars3d.BaseClass();

let mapInstance: Nullable<mars3d.Map> = null;

const marsMainView = ref();

provide('getMapInstance', () => {
  return mapInstance;
});

provide('marsMainView', marsMainView);

let graphicLayer: mars3d.layer.GraphicLayer;

provide('getLayer', () => {
  return graphicLayer;
});

const marsOnload = (map: any) => {
  console.log('map构造完成', map);

  mapInstance = map;

  graphicLayer = new mars3d.layer.GraphicLayer({ id: 'basic-layer' });

  mapInstance?.addLayer(graphicLayer);

  emit('mapLoaded', mapInstance);

  bindLayerContextMenu(graphicLayer);
  bindLayerEvent(graphicLayer);
};

function bindLayerContextMenu(graphicLayer) {
  graphicLayer.bindContextMenu([
    {
      text: '开始编辑对象',
      iconCls: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic || !graphic.startEditing) {
          return false;
        }
        return !graphic.isEditing;
      },
      callback: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphicLayer.startEditing(graphic);
        }
      },
    },
    {
      text: '停止编辑对象',
      iconCls: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        return graphic.isEditing;
      },
      callback: function (e) {
        const graphic = e.graphic;

        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphicLayer.stopEditing(graphic);
        }
      },
    },
  ]);
}

function bindLayerEvent(graphicLayer) {
  // 数据编辑相关事件， 用于属性弹窗的交互
  graphicLayer.on(mars3d.EventType.drawCreated, function (e) {
    eventTarget.fire('graphicEditor-start', e);
  });
  graphicLayer.on(
    [
      mars3d.EventType.editStart,
      mars3d.EventType.editMovePoint,
      mars3d.EventType.editStyle,
      mars3d.EventType.editRemovePoint,
    ],
    function (e) {
      eventTarget.fire('graphicEditor-update', e);
    }
  );
  graphicLayer.on([mars3d.EventType.editStop, mars3d.EventType.removeGraphic], function (e) {
    eventTarget.fire('graphicEditor-stop', e);
  });
}
</script>

<style lang="less" scoped>
.mars-main-view {
  position: relative;
  width: 100%;
  height: 100%;
}

.centerDiv-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
