<template>
  <ConfigProvider :locale="locale">
    <div
      class="mars-main-view"
      id="mars-main-view"
      ref="marsMainView"
    >
      <div
        id="centerDiv"
        class="centerDiv-container"
      >
        <mars-map
          :url="configUrl"
          :options="mapOptions"
          @onload="marsOnload"
        />
      </div>
      <template v-if="loaded">
        <template
          v-for="comp in widgets"
          :key="comp.key"
        >
          <mars-widget
            v-if="openAtStart.includes(comp.name) && comp.visible"
            v-model:visible="comp.visible"
            :widget="comp"
          />
        </template>
      </template>
    </div>
  </ConfigProvider>
</template>

<script setup lang="ts">
/**
 * 渲染主入口
 * @copyright
 * <AUTHOR>
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { computed, provide, ref } from 'vue';
import { ConfigProvider } from 'ant-design-vue';
import { useWidgetStore } from '@mars/common/store/widget';
import MarsMap from '@mars/components/mars-work/mars-map.vue';
import MarsWidget from './widget.vue';
import * as mars3d from 'mars3d';
import { useGlobSetting } from '@/hooks/setting';

const locale = zhCN;

const widgetStore = useWidgetStore();

const { rootUrl } = useGlobSetting();

const widgets = computed(() => widgetStore.state.widgets);

const openAtStart = computed(() => widgetStore.state.openAtStart);

const configUrl = `${rootUrl}config/config.json?time=${new Date().getTime()}`;

const eventTarget = new mars3d.BaseClass();

withDefaults(
  defineProps<{
    mapOptions?: any;
  }>(),
  {
    mapOptions: () => ({}),
  }
);

let mapInstance: any = null;

const marsMainView = ref();

provide('getMapInstance', () => {
  return mapInstance;
});

provide('marsMainView', marsMainView);

let graphicLayer: mars3d.layer.GraphicLayer;

provide('getLayer', () => {
  return graphicLayer;
});

const emit = defineEmits(['mapLoaded']);

const loaded = ref(false);

const marsOnload = (map: any) => {
  console.log('map构造完成', map);

  mapInstance = map;

  graphicLayer = new mars3d.layer.GraphicLayer({ id: 'basic-layer' });

  // // 开场动画
  // map.openFlyAnimation().then(() => {

  // });
  emit('mapLoaded', mapInstance);

  bindLayerContextMenu(graphicLayer);
  bindLayerEvent(graphicLayer);

  loaded.value = true;
};

function bindLayerContextMenu(graphicLayer) {
  graphicLayer.bindContextMenu([
    {
      text: '开始编辑对象',
      iconCls: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic || !graphic.startEditing) {
          return false;
        }
        return !graphic.isEditing;
      },
      callback: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphicLayer.startEditing(graphic);
        }
      },
    },
    {
      text: '停止编辑对象',
      iconCls: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        return graphic.isEditing;
      },
      callback: function (e) {
        const graphic = e.graphic;

        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphicLayer.stopEditing(graphic);
        }
      },
    },
  ]);
}

function bindLayerEvent(graphicLayer) {
  // 数据编辑相关事件， 用于属性弹窗的交互
  graphicLayer.on(mars3d.EventType.drawCreated, function (e) {
    eventTarget.fire('graphicEditor-start', e);
  });
  graphicLayer.on(
    [
      mars3d.EventType.editStart,
      mars3d.EventType.editMovePoint,
      mars3d.EventType.editStyle,
      mars3d.EventType.editRemovePoint,
    ],
    function (e) {
      eventTarget.fire('graphicEditor-update', e);
    }
  );
  graphicLayer.on([mars3d.EventType.editStop, mars3d.EventType.removeGraphic], function (e) {
    eventTarget.fire('graphicEditor-stop', e);
  });
}
</script>

<style lang="less" scoped>
.mars-main-view {
  position: relative;
  width: 100%;
  height: 100%;
}

.centerDiv-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
