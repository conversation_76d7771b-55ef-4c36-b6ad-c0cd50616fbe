/**
 * index页面的widget配置
 * @copyright
 */
import { defineAsyncComponent, markRaw } from 'vue';
import { WidgetState } from '@mars/common/store/widget';
import { StoreOptions } from 'vuex';

const defaultStart = ['visualization']; //'query-poi',

const store: StoreOptions<WidgetState> = {
  state: {
    widgets: [
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/query-poi/index.vue'))
        ),
        name: 'query-poi',
        autoDisable: true,
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/toolbar/index.vue'))
        ),
        name: 'toolbar',
        autoDisable: true,
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/graphic-editor/index.vue'))
        ),
        name: 'graphic-editor',
        meta: {
          props: {
            position: {
              left: 50,
              top: 10,
              bottom: 50,
            },
          },
        },
      },
      //全屏
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/full-screen/index.vue'))
        ),
        name: 'full-screen',
        group: 'manage',
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/manage-basemap/index.vue'))
        ),
        name: 'manage-basemap',
        group: 'manage',
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/manage-layers/index.vue'))
        ),
        name: 'manage-layers',
        group: 'manage',
        disableOther: ['roamLine'],
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/manage-layers/layer-tree.vue'))
        ),
        name: 'layer-tree',
      },
      {
        component: markRaw(
          defineAsyncComponent(
            () => import('@mars/widgets/basic/manage-layers/layer-picture-heatmap.vue')
          )
        ),
        name: 'layer-picture-heatmap',
      },
      {
        component: markRaw(
          defineAsyncComponent(
            () => import('@mars/widgets/basic/manage-layers/layer-picture-guihua.vue')
          )
        ),
        name: 'layer-picture-guihua',
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/location-point/index.vue'))
        ),
        name: 'location-point',
        group: 'tools',
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/command-plot/index.vue'))
        ),
        name: 'command-plot',
        group: 'tools',
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/measure/index.vue'))
        ),
        name: 'measure',
        group: 'tools',
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/isInPoly/index.vue'))
        ),
        name: 'isInPoly',
        group: 'tools',
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/line/index.vue'))
        ),
        name: 'line',
        group: 'tools',
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/buffer/index.vue'))
        ),
        name: 'buffer',
        group: 'tools',
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/location-region/index.vue'))
        ),
        name: 'location-region',
        group: 'tools',
      },
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/widgets/basic/fourAreaDraw/index.vue'))
        ),
        name: 'fourAreaDraw',
        group: 'tools',
      },
      //一张图- 可视化
      {
        component: markRaw(
          defineAsyncComponent(() => import('@mars/views/dashboard/analysis/index.vue'))
        ),
        name: 'visualization',
        autoDisable: true,
        visible: true,
      },
    ],
    openAtStart: defaultStart,
  },
  mutations: {
    SET_OPENATSTART(state, openAtStart) {
      state.openAtStart = openAtStart;
    },
  },
  getters: {
    getOpenAtStart(state) {
      return state.openAtStart;
    },
  },
};

export default store;
