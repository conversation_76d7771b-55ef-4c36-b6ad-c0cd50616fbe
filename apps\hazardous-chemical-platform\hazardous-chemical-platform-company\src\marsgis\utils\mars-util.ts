/**
 * 项目内通用 静态Util方法
 *
 * @copyright
 * <AUTHOR>
import { isObject, isString, merge } from 'lodash-es';
import * as mars3d from 'mars3d';

/**
 * 判断是否 "经度,纬度" 字符串值
 *
 * @export
 * @param {string} text 传入的字符串
 * @return {boolean} 是否 经度,纬度
 */
export function isLonLat(text: string): boolean {
  const reg =
    /^-?((0|1?[0-7]?[0-9]?)(([.][0-9]*)?)|180(([.][0]*)?)),-?((0|[1-8]?[0-9]?)(([.][0-9]*)?)|90(([.][0]*)?))$/;
  return reg.test(text);
}

/**
 * 设置自动高度值
 * @param {function} callback 窗口大小变化时的回调,返回当前计算完成的高度
 * @param {number} [lose=0] 窗口高度基础上减少的值
 * @param {string} [container="sanbox-warpper"] 窗口id
 * @return {void}
 */
export function setAutoHeight(
  callback: (v: number) => void,
  lose = 0,
  container = 'sanbox-warpper'
): void {
  const wapper = document.getElementById(container) || document.body;

  let wapperHeight = wapper?.clientHeight || 0;
  const result = wapperHeight - lose;
  callback(result);

  const resize = () => {
    wapperHeight = wapper?.clientHeight || 0;
    const resizeHeight = wapperHeight - lose;
    callback(resizeHeight);
  };

  window.addEventListener('resize', resize);

  resize();
  // return () => {
  //   window.removeEventListener("resize", resize)
  // }
}

/**
 *  获取URL参数
 *
 * @export
 * @param {string} parameter url值
 * @return {string | null}  参数值
 */
export function getQueryString(parameter: string): string | null {
  return new URL(window.location.href).searchParams.get(parameter);
}

/**
 * 将指定的异步方法转为Promise
 *
 * @param {*} context
 * @param {string} apiName
 * @param {string} [success="success"]
 * @param {string} [error="error"]
 * @return {*} Promise
 */
export function apiToSync(context: any, apiName: string, success = 'success', error = 'error') {
  return apiArrayToSync(context, [apiName], success, error)[0];
}

/**
 * 将指定的多个异步方法转为Promise
 *
 * @param {*} context
 * @param {string[]} apiNames
 * @param {string} [success="success"]
 * @param {string} [error="error"]
 * @return {*} Promise[]
 */
export function apiArrayToSync(
  context: any,
  apiNames: string[],
  success = 'success',
  error = 'error'
) {
  return apiNames.map(name => {
    const apiFunc = context[name];

    return (options: any) =>
      new Promise((resolve, reject) => {
        options[success] = function (result: any) {
          resolve(result);
        };
        options[error] = function (error) {
          reject(error);
        };
        // console.log("zhix", options)
        apiFunc.call(context, options);
      });
  });
}

/**
 * 地图打印，连接打印机，设置参数
 *
 * @param {*} base64 map.expImage方法的回调函数参数
 */
export function printImage(base64: any) {
  const iframe: HTMLIFrameElement = mars3d.DomUtil.create('iframe', '', document.body);
  let doc = null;
  iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:-500px;top:-500px;');
  document.body.appendChild(iframe);
  doc = iframe.contentWindow.document;
  doc.write(`<div><img src="${base64}" style="margin:0" /></div>`);
  doc.close();

  iframe.focus();
  iframe.contentWindow.focus();
  setTimeout(() => {
    iframe.contentWindow.print();
    document.body.removeChild(iframe);
  }, 500);
}

export function drawLine(map, data?: string | Recordable, options?: Recordable) {
  const layer: Recordable = {
    ...options,
  };

  if (data) {
    if (isString(data)) {
      layer.data = JSON.parse(data);
    } else if (isObject(data)) {
      layer.data = data;
    }
  } else {
    layer.url = `/config/lt/bj.json`;
  }

  const line = new mars3d.layer.GeoJsonLayer(
    merge(
      {
        name: `园区线`,
        chinaCRS: mars3d.ChinaCRS.WGS84,
        id: 'lt_boundary',
        symbol: {
          type: 'polyline',
          styleOptions: {
            color: 'rgba(79,234,252,0.82)',
            setHeight: 50,
            width: 2,
            label: {
              text: '{name}',
              position: 'center',
              font_size: 25,
              color: 'yellow',
              font_family: '楷体',
              outline: true,
              outlineColor: '#f1f3f4',
              outlineWidth: 1,
              // 视距的设置
              scaleByDistance: true,
              scaleByDistance_far: 20000000,
              scaleByDistance_farValue: 0.1,
              scaleByDistance_near: 1000,
              scaleByDistance_nearValue: 1,
              clampToGround: true,
            },
            clampToGround: true,
          },
          styleField: 'name',
          styleFieldOptions: {},
          // callback: function (attr, styleOpt) {},
        },
      },
      layer
    )
  );
  // const graphic = new mars3d.graphic.PolylinePrimitive({
  //   positions,
  //   style: {
  //     width: 4,
  //     materialType: mars3d.MaterialType.LineTrail,
  //     materialOptions: {
  //       color: '#ffff00',
  //       speed: 0.5,
  //     },
  //     clampToGround: true,
  //   },
  //   attr: { remark: '' },
  // });
  // graphicLayerDanger.addGraphic(graphic);

  map.addLayer(line);
  return line;
}

/*
 * 平面坐标转换，格式：{ "x": x, "y": y }
 */
// 基准点
const G_Project_Coordinates = {
  // wgs84Location1: { x:84.03355259857717, y: 41.833188167865266 },
  // wgs84Location2: { x:84.03901613491466, y: 41.82949950914455 },
  wgs84Location1: { x: 84.019093, y: 41.82247 },
  wgs84Location2: { x: 84.023913, y: 41.819958 },

  planeLocation1: { x: 0, y: 0 },
  planeLocation2: { x: 451.874611, y: 409.441118 },
};

const CAD_Project_Coordinates = {
  wgs84Location1: { x: 84.03355259857717, y: 41.833188167865266 },
  wgs84Location2: { x: 84.03901613491466, y: 41.82949950914455 },
  // wgs84Location1: { x: 84.019093, y: 41.82247 },
  // wgs84Location2: { x: 84.023913, y: 41.820418 },

  planeLocation1: { x: 0, y: 0 },
  planeLocation2: { x: 451.874611, y: 409.441118 },
};

function projectCoordinateX(pt, original1, original2, target1, target2) {
  const x =
    target1.x - ((original1.x - pt.x) / (original1.x - original2.x)) * (target1.x - target2.x);

  return x;
}

function projectCoordinateY(pt, original1, original2, target1, target2) {
  const y =
    target1.y - ((original1.y - pt.y) / (original1.y - original2.y)) * (target1.y - target2.y);

  return y;
}

// 坐标转平面
export function geoXToRoute(x) {
  return projectCoordinateX(
    { x },
    CAD_Project_Coordinates.wgs84Location1,
    CAD_Project_Coordinates.wgs84Location2,
    CAD_Project_Coordinates.planeLocation1,
    CAD_Project_Coordinates.planeLocation2
  );
}

export function geoYToRoute(y) {
  return projectCoordinateY(
    { y },
    CAD_Project_Coordinates.wgs84Location1,
    CAD_Project_Coordinates.wgs84Location2,
    CAD_Project_Coordinates.planeLocation1,
    CAD_Project_Coordinates.planeLocation2
  );
}

// 平面转坐标

export function cadXToGeo(x) {
  return projectCoordinateX(
    { x },
    G_Project_Coordinates.planeLocation1,
    G_Project_Coordinates.planeLocation2,
    G_Project_Coordinates.wgs84Location1,
    G_Project_Coordinates.wgs84Location2
  );
}

export function cadYToGeo(y) {
  return projectCoordinateY(
    { x: 0, y },
    G_Project_Coordinates.planeLocation1,
    G_Project_Coordinates.planeLocation2,
    G_Project_Coordinates.wgs84Location1,
    G_Project_Coordinates.wgs84Location2
  );
}
