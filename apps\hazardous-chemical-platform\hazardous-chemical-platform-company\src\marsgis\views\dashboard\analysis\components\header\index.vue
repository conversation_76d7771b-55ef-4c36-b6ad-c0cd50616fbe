<template>
  <Layout.Header
    :class="getHeaderClass"
    :style="{
      backgroundImage: `url(${bg_top})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
    }"
  >
    <!-- logo center -->
    <div class="flex w-full justify-center items-center h-2/3">
      <img :src="top_title" />
    </div>
    <!-- action  -->
    <div :class="`${prefixCls}-action absolute right-3 top-3 flex`">
      <div
        class="text-16px"
        v-show="false"
        >{{ titleHeader }}</div
      >

      <AppSearch
        v-if="getShowSearch"
        :class="`${prefixCls}-action__item `"
      />

      <ErrorAction
        v-if="getUseErrorHandle"
        :class="`${prefixCls}-action__item error-action`"
      />

      <Notify
        v-if="getShowNotice"
        :class="`${prefixCls}-action__item notify-item`"
      />

      <FullScreen
        v-if="getShowFullScreen"
        :class="`${prefixCls}-action__item fullscreen-item`"
      />

      <AppLocalePicker
        v-if="getShowLocalePicker"
        :reload="true"
        :showText="false"
        :class="`${prefixCls}-action__item`"
      />
      <!-- 时间 -->
      <TimeNow />

      <UserDropDown :theme="getHeaderTheme" />

      <SettingDrawer
        v-if="getShowSetting"
        :class="`${prefixCls}-action__item`"
      />
    </div>
    <slot name="left-header" />
  </Layout.Header>
</template>

<script lang="ts" setup>
import { Layout } from 'ant-design-vue';
import { computed, unref } from 'vue';
import { AppLocalePicker, AppSearch } from '@/components/Application';
import { SettingButtonPositionEnum } from '@/enums/appEnum';
import { useHeaderSetting } from '@/hooks/setting/useHeaderSetting';
import { useRootSetting } from '@/hooks/setting/useRootSetting';
import { useDesign, useAppInject } from '@monorepo-yysz/hooks';
import { useLocale } from '@/locales/useLocale';
import { createAsyncComponent } from '@monorepo-yysz/ui';
import { propTypes } from '@monorepo-yysz/utils';
import { ErrorAction, FullScreen, Notify, UserDropDown } from '@/layouts/default/header/components';
import bg_top from '@/assets/images/home-page/topbg.png';
import top_title from '@/assets/images/home-page/top-title.png';
import { useGlobSetting } from '@/hooks/setting';
import { useUserStore } from '@/store/modules/user';
import { TimeNow } from '@/components/Time';

const SettingDrawer = createAsyncComponent(() => import('@/layouts/default/setting/index.vue'), {
  loading: true,
});
defineOptions({ name: 'LayoutHeader' });

const props = defineProps({
  fixed: propTypes.bool,
});
const { prefixCls } = useDesign('layout-header');
const { getUseErrorHandle, getShowSettingButton, getSettingButtonPosition } = useRootSetting();

const { title } = useGlobSetting();

const { getHeaderTheme, getShowFullScreen, getShowNotice, getShowHeader, getShowSearch } =
  useHeaderSetting();

const { getShowLocalePicker } = useLocale();

const { getIsMobile } = useAppInject();

const userStore = useUserStore();

const getHeaderClass = computed(() => {
  const theme = unref(getHeaderTheme);
  return [
    prefixCls,
    {
      [`${prefixCls}--fixed`]: props.fixed,
      [`${prefixCls}--mobile`]: unref(getIsMobile),
      [`${prefixCls}--${theme}`]: theme,
    },
  ];
});

const titleHeader = computed(() => {
  return userStore.getCompanyInfo?.companyName || title;
});

const getShowSetting = computed(() => {
  if (!unref(getShowSettingButton)) {
    return false;
  }
  const settingButtonPosition = unref(getSettingButtonPosition);

  if (settingButtonPosition === SettingButtonPositionEnum.AUTO) {
    return unref(getShowHeader);
  }
  return settingButtonPosition === SettingButtonPositionEnum.HEADER;
});
</script>

<style lang="less">
@header-trigger-prefix-cls: ~'@{namespace}-layout-header-trigger';
@header-prefix-cls: ~'@{namespace}-layout-header';
@breadcrumb-prefix-cls: ~'@{namespace}-layout-breadcrumb';
@logo-prefix-cls: ~'@{namespace}-app-logo';

.ant-layout .@{header-prefix-cls} {
  display: flex;
  justify-content: space-between;
  top: 0;
  width: 100%;
  padding: 0;
  position: absolute;
  color: @white;

  &--mobile {
    .@{breadcrumb-prefix-cls},
    .error-action,
    .notify-item,
    .fullscreen-item {
      display: none;
    }

    .@{logo-prefix-cls} {
      min-width: unset;
      padding-right: 0;

      &__title {
        display: none;
      }
    }
    .@{header-trigger-prefix-cls} {
      padding: 0 4px 0 8px !important;
    }
    .@{header-prefix-cls}-action {
      padding-right: 4px;
    }
  }

  &--fixed {
    position: fixed;
    z-index: @layout-header-fixed-z-index;
    top: 0;
    left: 0;
    width: 100%;
  }

  &-logo {
    min-width: 400px;
    height: @header-height;
    padding: 0 10px;
    font-size: 14px;

    img {
      // width: @logo-width;
      // height: @logo-width;
      // margin-right: 2px;
    }
  }

  &-left {
    display: flex;
    align-items: center;
    height: 100%;

    .@{header-trigger-prefix-cls} {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 1px 10px 0;
      cursor: pointer;

      .anticon {
        font-size: 16px;
      }

      &.light {
        &:hover {
          background-color: @header-light-bg-hover-color;
        }

        svg {
          fill: #000;
        }
      }

      &.dark {
        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }

  &-menu {
    flex: 1;
    align-items: center;
    min-width: 0;
    height: 100%;
  }

  &-action {
    display: flex;
    // padding-right: 12px;
    align-items: center;
    // min-width: 180px;

    &__item {
      display: flex !important;
      align-items: center;
      height: @header-height;
      padding: 0 2px;
      font-size: 1.2em;
      cursor: pointer;

      .ant-badge {
        height: @header-height;
        line-height: @header-height;
      }

      .ant-badge-dot {
        top: 14px;
        right: 2px;
      }
    }

    span[role='img'] {
      padding: 0 8px;
    }
  }

  &--light {
    border-bottom: 1px solid @header-light-bottom-border-color;
    // border-left: 1px solid @header-light-bottom-border-color;
    background-color: @white !important;

    .@{header-prefix-cls}-logo {
      color: @text-color-base;

      &:hover {
        background-color: @header-light-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        color: @text-color-base;

        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        &:hover {
          background-color: @header-light-bg-hover-color;
        }
      }

      &-icon,
      span[role='img'] {
        color: @text-color-base;
      }
    }
  }

  &--dark {
    // border-bottom: 1px solid @border-color-base;
    // border-left: 1px solid @border-color-base;
    background-color: @header-dark-bg-color !important;
    .@{header-prefix-cls}-logo {
      &:hover {
        background-color: @header-dark-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        .ant-badge {
          span {
            color: @white;
          }
        }

        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }
}
</style>
