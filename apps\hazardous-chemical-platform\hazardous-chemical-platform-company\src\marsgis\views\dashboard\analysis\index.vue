<template>
  <Layout class="top-0 absolute">
    <LayoutHeader class="absolute justify-between z-[992] !h-[135px] !w-100vw">
      <template #left-header>
        <div class="absolute left-8 top-4">
          <a-button
            type="primary"
            @click="mapWork.showModel(true)"
            class="mr-1"
          >
            室外
          </a-button>
          <a-button
            type="primary"
            @click="mapWork.showModel(false)"
          >
            室内
          </a-button>
        </div>
      </template>
    </LayoutHeader>
    <Footer class="z-[992] !fixed bottom-[22px]" />
  </Layout>
</template>

<script lang="ts" setup>
import * as mapWork from './map';
import useLifecycle from '@mars/common/uses/use-lifecycle';
import { useWidget } from '@mars/common/store/widget';
import { markRaw } from 'vue';
import LayoutHeader from './components/header/index.vue';
import { createAsyncComponent } from '@monorepo-yysz/ui';
import { Layout } from 'ant-design-vue';

const Footer = createAsyncComponent(
  () => import('@/views/dashboard/analysis/components/footer/index.vue')
);

useLifecycle(mapWork);

const { activate, disable, isActivate, updateWidget } = useWidget();

// 属性面板
const showEditor = (e: any) => {
  console.log(e.graphic, 'e.graphic');

  if (!isActivate('graphic-editor')) {
    activate({
      name: 'graphic-editor',
      data: { graphic: markRaw(e.graphic) },
    });
  } else {
    updateWidget('graphic-editor', {
      data: { graphic: markRaw(e.graphic) },
    });
  }
};
mapWork.eventTarget.on('graphicEditor-start', async (e: any) => {
  showEditor(e);
});
// 编辑修改了模型
mapWork.eventTarget.on('graphicEditor-update', async (e: any) => {
  showEditor(e);
});

// 停止编辑修改模型
mapWork.eventTarget.on('graphicEditor-stop', async () => {
  disable('graphic-editor');
});
</script>
