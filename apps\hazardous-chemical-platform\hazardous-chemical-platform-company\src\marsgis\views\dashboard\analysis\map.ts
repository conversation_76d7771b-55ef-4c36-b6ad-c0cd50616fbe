import { useGlobSetting } from '@/hooks/setting';
import * as mars3d from 'mars3d';

const Cesium = mars3d.Cesium;
let map: mars3d.Map; // 地图对象

const basicHeight = 440;

export let tiles3dLayer;

export let tiles3dLayerIn;

export const eventTarget = new mars3d.BaseClass();

// 初始化当前业务
export function onMounted(mapInstance: mars3d.Map): void {
  const { modelUrl } = useGlobSetting();

  map = mapInstance; // 记录map

  tiles3dLayer = new mars3d.layer.TilesetLayer({
    // 是否允许鼠标穿透拾取
    allowDrillPick: function (event) {
      const alpha = event?.pickedObject?.color?.alpha;
      if (Cesium.defined(alpha) && alpha !== 1) {
        return true; // 鼠标不拾取前面遮挡的透明的构件，穿透拾取其后方不透明构件。
      }
      return false;
    },
    name: '环球中心室外',
    id: 'out-model',
    url: `${modelUrl}/hq/tileset.json`,
    maximumScreenSpaceError: 10,
    position: { lat: 30.571271, lng: 104.060584, alt: basicHeight },
    center: { lat: 30.571169, lng: 104.069382, alt: 569.9, heading: 268.6, pitch: -9.5 },
    rotation: { x: 0.01, y: 0.5, z: -1.1 },
    luminanceAtZenith: 0.5,
    show: true,
    // clampToGround: true,
    flyTo: true,
  });
  map.addLayer(tiles3dLayer);

  tiles3dLayerIn = new mars3d.layer.TilesetLayer({
    // 是否允许鼠标穿透拾取
    allowDrillPick: function (event) {
      const alpha = event?.pickedObject?.color?.alpha;

      if (Cesium.defined(alpha) && alpha !== 1) {
        return true; // 鼠标不拾取前面遮挡的透明的构件，穿透拾取其后方不透明构件。
      }
      return false;
    },
    name: '环球中心室内',
    id: 'in-model',
    url: `${modelUrl}/inhq/tileset.json`,
    // maximumScreenSpaceError: 10,
    maximumScreenSpaceError: 16,
    cacheBytes: 1073741824 * 2, // 1024MB = 1024*1024*1024
    maximumCacheOverflowBytes: 2147483648 * 4, // 2048MB = 2048*1024*1024
    position: { lat: 30.571271, lng: 104.060584, alt: basicHeight },
    center: { lat: 30.571169, lng: 104.091382, alt: 3104.7, heading: 268.7, pitch: -32 },
    luminanceAtZenith: 0.5,
    show: false,
    scale: 1,
    preloadWhenHidden: true,
    // clampToGround: true,
    // flyTo: true,
    skipLevelOfDetail: true, // 是Cesium在1.5x 引入的一个优化参数，这个参数在金字塔数据加载中，可以跳过一些级别，这样整体的效率会高一些，数据占用也会小一些。但是带来的异常是：1） 加载过程中闪烁，看起来像是透过去了，数据载入完成后正常。2，有些异常的面片，这个还是因为两级LOD之间数据差异较大，导致的。当这个参数设置false，两级之间的变化更平滑，不会跳跃穿透，但是清晰的数据需要更长，而且还有个致命问题，一旦某一个tile数据无法请求到或者失败，导致一直不清晰。所以我们建议：对于网络条件好，并且数据总量较小的情况下，可以设置false，提升数据显示质量。
    // loadSiblings: true, // 如果为true则不会在已加载完模型后，自动从中心开始超清化模型
    cullRequestsWhileMoving: true,
    cullRequestsWhileMovingMultiplier: 1, // 【重要】 值越小能够更快的剔除
    preferLeaves: true, // 【重要】这个参数默认是false，同等条件下，叶子节点会优先加载。但是Cesium的tile加载优先级有很多考虑条件，这个只是其中之一，如果skipLevelOfDetail=false，这个参数几乎无意义。所以要配合skipLevelOfDetail=true来使用，此时设置preferLeaves=true。这样我们就能最快的看见符合当前视觉精度的块，对于提升大数据以及网络环境不好的前提下有一点点改善意义。
    progressiveResolutionHeightFraction: 0.1, // 【重要】 数值偏于0能够让初始加载变得模糊
    dynamicScreenSpaceError: true, // true时会在真正的全屏加载完之后才清晰化模型
    // shadows: Cesium.ShadowMode.DISABLED,
    // 以下参数可以参考用于3dtiles总数据大，清晰度过高情况下进行性能优化。这不是一个通用的解决方案，但可以以此为参考。
    enableDebugWireframe: true, // 是否可以进行三角网的切换显示
    // 以上为优化的参数
  });
  map.addLayer(tiles3dLayerIn);

  setTimeout(() => {
    showCengByStyle(tiles3dLayerIn);
  }, 500);

  // 测试geoserver发布服务
}

function showCengByStyle(tiles3dLayer) {
  tiles3dLayer.closeHighlight();
  tiles3dLayer.style = new Cesium.Cesium3DTileStyle({
    color: {
      conditions: [
        ["${name} === 'Plane002'", 'rgba(255, 255, 255, 0.00)'],
        ["${name} === 'Line016'", 'rgba(255, 255, 255, 0.00)'],
        ["${name} === 'Line004'", 'rgba(255, 255, 255, 0.00)'],
        ["${name} === 'Line003'", 'rgba(255, 255, 255, 0.00)'],
        ["${name} === 'Line005'", 'rgba(255, 255, 255, 0.00)'],
        ["${name} === 'Line008'", 'rgba(255, 255, 255, 0.00)'],
        ["${name} === 'Line008_1'", 'rgba(255, 255, 255, 0.00)'],
        ["${name} === 'Line014'", 'rgba(255, 255, 255, 0.00)'],
        ["${name} === '对象096'", 'rgba(255, 255, 255, 0.00)'],
        ["${name} === '对象099'", 'rgba(255, 255, 255, 0.00)'],
        ["${name} === '对象074'", 'rgba(255, 255, 255, 0.00)'],
      ],
    },
  });
}

export function showModel(flg) {
  tiles3dLayer.show = flg;
  tiles3dLayerIn.show = !flg;

  // map.flyToPoint([104.091382, 30.571169, basicHeight], {
  //   heading: 268.7,
  //   // radius: 270,
  //   // pitch: 270,
  // });
}
