import * as mars3d from 'mars3d';

let map; // mars3d.Map三维地图对象
let width;
let lastgeojson;

export let graphicLayer; // 矢量图层对象

/**
 * 初始化地图业务，生命周期钩子函数（必须）
 * 框架在地图初始化完成后自动调用该函数
 * @param {mars3d.Map} mapInstance 地图对象
 * @returns {void} 无
 */
export function onMounted(mapInstance) {
  map = mapInstance; // 记录map

  graphicLayer = map.getLayerById('buffer');

  if (!graphicLayer) {
    graphicLayer = new mars3d.layer.GraphicLayer({
      hasEdit: false,
      isAutoEditing: false, // 绘制完成后是否自动激活编辑
      id: 'buffer',
    });
    map.addLayer(graphicLayer);
  }
}

/**
 * 释放当前地图业务的生命周期函数
 * @returns {void} 无
 */
export function onUnmounted() {
  map = null;
}

export function drawPoint() {
  graphicLayer.startDraw({
    type: 'point',
    style: {
      pixelSize: 12,
      color: '#ffff00',
      clampToGround: true,
    },
    success: function (graphic) {
      // 绘制成功之后回调
      updateBuffer(graphic);
    },
  });
}

export function drawPolyline() {
  graphicLayer.startDraw({
    type: 'polyline',
    style: {
      color: '#ffff00',
      width: 3,
      clampToGround: true,
    },
    success: function (graphic) {
      // 绘制成功之后回调
      updateBuffer(graphic);
    },
  });
}

export function drawPolygon() {
  graphicLayer.startDraw({
    type: 'polygon',
    style: {
      color: '#ffff00',
      outline: true,
      outlineColor: '#f0ce22',
      outlineWidth: 2,
      opacity: 0.5,
      clampToGround: true,
    },
    success: function (graphic) {
      // 绘制成功之后回调
      updateBuffer(graphic);
    },
  });
}

export function deleteAll() {
  graphicLayer.clear();
  map.graphicLayer.clear();
  lastgeojson = null;
}

export function radiusChange(val) {
  width = val * 1000; // km
  if (lastgeojson) {
    updateBuffer();
  }
}

function updateBuffer(graphic?: any) {
  let buffere;
  try {
    const geojson = graphic ? graphic.toGeoJSON() : lastgeojson;
    geojson.properties = {};

    buffere = mars3d.PolyUtil.buffer(geojson, width);

    lastgeojson = geojson;
  } catch (e) {
    console.log('缓冲分析异常', e);
  }
  if (!buffere) {
    return;
  }

  const graphicsOptions = mars3d.Util.geoJsonToGraphics(buffere, {
    type: 'polygon',
    style: {
      color: 'rgba(255,0,0,0.4)',
      clampToGround: true,
    },
  });

  map.graphicLayer.addGraphic(graphicsOptions);
}

// 绑定右键菜单
export function bindLayerContextMenu(layer) {
  layer.bindContextMenu([
    {
      text: '开始编辑对象',
      iconCls: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic || !graphic.startEditing) {
          return false;
        }
        return !graphic.isEditing;
      },
      callback: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphicLayer.startEditing(graphic);
        }
      },
    },
    {
      text: '停止编辑对象',
      iconCls: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        return graphic.isEditing;
      },
      callback: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphicLayer.stopEditing(graphic);
        }
      },
    },
    {
      text: '删除对象',
      iconCls: 'fa fa-trash-o',
      show: event => {
        const graphic = event.graphic;
        if (!graphic || graphic.isDestroy) {
          return false;
        } else {
          return true;
        }
      },
      callback: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return;
        }
        graphicLayer.removeGraphic(graphic);
      },
    },
  ]);
}

export function bindEvent() {
  graphicLayer.on(mars3d.EventType.drawCreated, function (e) {
    updateBuffer(e.graphic);
  });

  graphicLayer.on(mars3d.EventType.editMovePoint, function (e) {
    updateBuffer(e.graphic);
  });

  graphicLayer.on(mars3d.EventType.editRemovePoint, function (e) {
    updateBuffer(e.graphic);
  });
}
