<template>
  <mars-dialog
    title="指挥标绘"
    width="366"
    top="60"
    right="10"
    :min-width="500"
  >
    <template #icon>
      <mars-icon
        icon="local"
        width="18"
      />
    </template>
    <div
      class="position-container"
      :class="$style.pointer"
    >
      <a-row>
        <a-col>
          <a-button @click="mapWork.startDrawGraphic()">直线箭头</a-button>
          <a-button @click="mapWork.startDrawDoubleArrow()">钳击箭头</a-button>
          <a-button @click="mapWork.startDrawAttackArrowYW()">攻击箭头</a-button>
          <a-button
            @click="mapWork.clearlayer()"
            class="!text-red-500"
            >清除</a-button
          >
          <a-button
            @click="mapWork.expGeoJSONFile()"
            class="!text-blue-500"
            >保存</a-button
          >
          <a-upload
            :multiple="false"
            name="file"
            accept=".json,.geojson"
            :file-list="fileList"
            :showUploadList="false"
            :supportServerRender="true"
            :beforeUpload="() => false"
            @change="onClickImpFile"
          >
            <mars-button title="打开GeoJSON">
              <mars-icon
                icon="folder-open"
                class="icon-vertical-a"
              />
              打开
            </mars-button>
          </a-upload>
        </a-col>
      </a-row>
    </div>
  </mars-dialog>
</template>

<script lang="ts" setup>
import { ref, markRaw } from 'vue';
import useLifecycle from '@mars/common/uses/use-lifecycle';
import * as mapWork from './map';
import { $message } from '@mars/components/mars-ui';
import { useWidget } from '@mars/common/store/widget';
const { activate, disable, isActivate, updateWidget } = useWidget();

// 数据列表
interface GraphicTableItem {
  key: number;
  name: string;
}
// 启用map.ts生命周期
useLifecycle(mapWork);

const fileList = ref([]);

const graphicDataList = ref<GraphicTableItem[]>([]);
const rowKeys = ref<number[]>([]); // 勾选的row
let graphicIndex = 0;

// 获取map.js中定义的需要管理的图层
function getManagerLayer() {
  if (mapWork.getManagerLayer) {
    return mapWork.getManagerLayer();
  }
  return mapWork.graphicLayer;
}

// 打开geojson
const onClickImpFile = (info: any) => {
  const graphicLayer = getManagerLayer();

  const item = info.file;
  const fileName = item.name;
  const fileType = fileName
    ?.substring(fileName.lastIndexOf('.') + 1, fileName.length)
    .toLowerCase();

  if (fileType === 'json' || fileType === 'geojson') {
    const reader = new FileReader();
    reader.readAsText(item, 'UTF-8');
    reader.onloadend = function (e) {
      const geojson = JSON.parse(this.result as string);

      if (geojson.type === 'graphic' && geojson.data) {
        graphicLayer.addGraphic(geojson.data);
        graphicLayer.flyTo();
      } else {
        graphicLayer.loadGeoJSON(geojson, { flyTo: true });
        initGraphicableData(graphicLayer);
      }
    };
  } else {
    $message('暂不支持 ' + fileType + ' 文件类型的数据！');
  }
};

function initGraphicableData(graphicLayer) {
  const list = graphicLayer.graphics;
  for (let i = list.length - 1; i >= 0; i--) {
    const graphic = list[i];
    if (graphic.isPrivate) {
      continue;
    }

    graphicDataList.value.push({
      key: graphic.id,
      name: getGraphicName(graphic),
    });
    if (graphic.show) {
      rowKeys.value.push(graphic.id);
    }
  }
}

function getGraphicName(graphic) {
  if (graphic?.style?.label?.text) {
    return `${graphic.type} - ${graphic.style.label.text}`;
  }

  if (graphic.name) {
    return `${graphic.type} - ${graphic.name}`;
  }
  if (graphic.attr.remark) {
    return `${graphic.type} - ${graphic.attr.remark}`;
  }

  graphic.name = `未命名${++graphicIndex}`;
  return `${graphic.type} - ${graphic.name}`;
}

// 属性面板
const showEditor = (e: any) => {
  if (!isActivate('graphic-editor')) {
    activate({
      name: 'graphic-editor',
      data: { graphic: markRaw(e.graphic) },
    });
  } else {
    updateWidget('graphic-editor', {
      data: { graphic: markRaw(e.graphic) },
    });
  }
};

mapWork.eventTarget.on('graphicEditor-start', async (e: any) => {
  showEditor(e);
});
// 编辑修改了模型
mapWork.eventTarget.on('graphicEditor-update', async (e: any) => {
  showEditor(e);
});

// 停止编辑修改模型
mapWork.eventTarget.on('graphicEditor-stop', async (e: any) => {
  disable('graphic-editor');
});
</script>

<style lang="less" module>
.pointer {
  :global {
    .ant-btn {
      background-color: transparent;
      color: #fff;
      border: 1px solid #89bceb;

      &:hover {
        background-color: transparent !important;
      }
    }
  }
}
</style>
