import * as mars3d from 'mars3d';
import { $alert as globalAlert } from '@mars/components/mars-ui/index';
import { Cesium } from 'mars3d';
import { $message } from '@mars/components/mars-ui';

export let map; // mars3d.Map三维地图对象
export let graphicLayer; // 矢量图层对象

// 事件对象，用于抛出事件给vue
export const eventTarget = new mars3d.BaseClass();

// 初始化当前业务
export function onMounted(mapInstance: mars3d.Map): void {
  map = mapInstance; // 记录map

  // 创建矢量数据图层
  graphicLayer = map.getLayerById('command-plot');
  if (!graphicLayer) {
    graphicLayer = new mars3d.layer.GraphicLayer({ id: 'command-plot' });
    map.addLayer(graphicLayer);
  }

  bindLayerPopup();
  bindEvent();
  bindLayerContextMenu();
}

// 释放当前业务
export function onUnmounted(): void {
  eventTarget.off();
  map = null;
}

// 在图层绑定Popup弹窗
export function bindLayerPopup() {
  graphicLayer.bindPopup(function (event) {
    // @ts-ignore
    window.saveFn = function () {
      const el = document.getElementById('save-value');
      if (el) {
        // @ts-ignore
        const val = el.value;
        console.log(val);
        console.log(event);
        const { graphic } = event;
        graphic.style = {
          ...graphic.style,
          label: {
            text: val || '',
            font_size: 25,
            color: '#e6e6e6',
            distanceDisplayCondition: true,
            distanceDisplayCondition_far: 500000,
            distanceDisplayCondition_near: 0,
          },
        };
        graphicLayer.closePopup();
      }
    };

    return `<div class="w-300px">
      <div class="w-full h-full"><lable>名称：</label><input class="!caret-light-500 border-hex-89BCEB border-1 bg-transparent !text-light-500 w-4/5 h-25px" id="save-value" /></div>
      <div class="w-full h-full flex justify-center mt-2"><button class="border-hex-89BCEB border-1 px-2" id="save-name" onclick="saveFn()">保存</button></div>
    </div>`;
  });
}

// 绑定右键菜单
export function bindLayerContextMenu() {
  graphicLayer.bindContextMenu([
    {
      text: '开始编辑对象',
      icon: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic || !graphic.hasEdit) {
          return false;
        }
        return !graphic.isEditing;
      },
      callback: e => {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphicLayer.startEditing(graphic);
        }
      },
    },
    {
      text: '停止编辑对象',
      icon: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic || !graphic.hasEdit) {
          return false;
        }
        return graphic.isEditing;
      },
      callback: e => {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphic.stopEditing();
        }
      },
    },
    {
      text: '删除对象',
      icon: 'fa fa-trash-o',
      show: event => {
        const graphic = event.graphic;
        if (!graphic || graphic.isDestroy) {
          return false;
        } else {
          return true;
        }
      },
      callback: e => {
        const graphic = e.graphic;
        if (!graphic) {
          return;
        }
        const parent = graphic.parent; // 右击是编辑点时
        graphicLayer.removeGraphic(graphic);
        if (parent) {
          graphicLayer.removeGraphic(parent);
        }
      },
    },
    {
      text: '计算周长',
      icon: 'fa fa-medium',
      callback: e => {
        const graphic = e.graphic;
        const strDis = mars3d.MeasureUtil.formatDistance(graphic.distance);
        globalAlert('该对象的周长为:' + strDis);
      },
    },
    {
      text: '计算面积',
      icon: 'fa fa-reorder',
      callback: e => {
        const graphic = e.graphic;
        const strArea = mars3d.MeasureUtil.formatArea(graphic.area);
        globalAlert('该对象的面积为:' + strArea);
      },
    },
  ]);
}

// 开始绘制
export function startDrawGraphic() {
  graphicLayer.startDraw({
    type: 'fineArrow',
    style: {
      materialType: mars3d.MaterialType.PolyGradient,
      materialOptions: {
        color: '#ff0000',
        alphaPower: 0.8,
        center: new Cesium.Cartesian2(0.5, 0.0),
      },
      clampToGround: true,
    },
  });
}

// 开始绘制
export function startDrawDoubleArrow() {
  graphicLayer.startDraw({
    type: 'doubleArrow',
    style: {
      materialType: mars3d.MaterialType.PolyGradient,
      materialOptions: {
        color: '#ff0000',
        alphaPower: 0.8,
        center: new Cesium.Cartesian2(0.5, 0.0),
      },
      clampToGround: true,
    },
  });
}

// 开始绘制
export function startDrawAttackArrowYW() {
  graphicLayer.startDraw({
    type: 'attackArrowYW',
    style: {
      materialType: mars3d.MaterialType.PolyGradient,
      materialOptions: {
        color: '#ff0000',
        alphaPower: 0.8,
        center: new Cesium.Cartesian2(0.5, 0.0),
      },
      clampToGround: true,
    },
  });
}

// 清除
export function clearlayer() {
  graphicLayer.clear();
}

// 导出
export const expGeoJSONFile = () => {
  if (graphicLayer.length === 0) {
    $message('当前没有标注任何数据，无需保存！');
    return;
  }
  const geojson = graphicLayer.toGeoJSON();
  mars3d.Util.downloadFile('矢量数据GeoJSON.json', JSON.stringify(geojson));
};

export function getManagerLayer() {
  return graphicLayer;
}

export function bindEvent() {
  // 触发自定义事件 创建完成 标绘事件
  graphicLayer.on(mars3d.EventType.drawCreated, function (e) {
    const graphic = e.graphic;
    eventTarget.fire('graphicEditor-start', { graphic });
  });

  // 触发自定义事件 开始编辑 标绘事件 正在移动鼠标中，正在编辑拖拽修改点中（MOUSE_MOVE） 标绘事件 图上编辑修改了相关style属性 标绘事件
  // 编辑修改了点（LEFT_UP）标绘事件
  graphicLayer.on(
    [
      mars3d.EventType.editStart,
      mars3d.EventType.editMovePoint,
      mars3d.EventType.editStyle,
      mars3d.EventType.editRemovePoint,
    ],
    function (e) {
      const graphic = e.graphic;
      eventTarget.fire('graphicEditor-update', { graphic });
    }
  );

  // 停止编辑 标绘事件
  graphicLayer.on([mars3d.EventType.editStop, mars3d.EventType.removeGraphic], function (e) {
    eventTarget.fire('graphicEditor-stop');
  });
}
