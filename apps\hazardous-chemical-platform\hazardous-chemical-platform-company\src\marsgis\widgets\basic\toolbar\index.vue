<template>
  <mars-dialog
    :draggable="false"
    :customClass="`base-pannel cursor-pointer`"
    right="150"
    top="22"
  >
    <!-- ${$style.toolbar} -->
    <template
      v-for="(item, i) in data"
      :key="`${item.widget}_${i}`"
    >
      <div
        v-if="item.widget && !item.children"
        class="toolbar-item"
        @click="showWidget(item.widget)"
      >
        <mars-icon
          :icon="item.icon"
          width="18"
        />
        <span class="title">{{ item.name }}</span>
      </div>
      <div
        v-if="item.type === 'line'"
        class="toolbar-item"
      >
        <div
          v-for="v in item.others"
          class="mb-2"
          :key="v.name"
        >
          <span class="title">{{ v.name }}</span>
          <a-switch
            v-model:checked="v.checked"
            @change="checked => handleSwitch(checked, v)"
          />
        </div>
      </div>
      <mars-dropdown-menu
        v-if="item.children && !item.widget"
        trigger="hover"
        placement="bottom"
      >
        <div class="toolbar-item">
          <mars-icon
            :icon="item.icon"
            width="18"
          />
          <span class="title">{{ item.name }}</span>
          <mars-icon
            icon="down"
            width="18"
          />
        </div>
        <template #overlay>
          <a-menu @click="clickMenu">
            <a-menu-item
              v-for="child in item.children"
              :key="child.widget"
              :title="child.name"
            >
              <mars-icon
                :icon="child.icon"
                width="18"
              />
              <span>{{ child.name }}</span>
            </a-menu-item>
          </a-menu>
        </template>
      </mars-dropdown-menu>
    </template>
  </mars-dialog>
</template>

<script setup lang="ts">
/**
 * 导航菜单按钮 （右上角）
 */
import { useWidget } from '@mars/common/store/widget';
import { inject, ref, unref } from 'vue';
import { useFullscreen } from '@vueuse/core';

const getLayer = inject<any>('getLayer');

const marsMainView = inject<any>('marsMainView');

const { toggle } = useFullscreen(unref(marsMainView));

interface DataItem {
  [x: string]: any;
  name: string;
  icon?: string;
  children?: {
    name: string;
    icon?: string;
    widget?: string;
  }[];
}

const { activate } = useWidget();

const data = ref<DataItem[]>([
  // { name: '底图', icon: 'international', widget: 'manage-basemap' },
  { name: '全屏', icon: 'full-screen', widget: 'full-screen' },
  { name: '图层', icon: 'layers', widget: 'manage-layers' },
  // {
  //   name: '边界',
  //   type: 'line',
  //   icon: 'switch',
  //   others: [
  //     {
  //       name: '乡镇边界',
  //       icon: 'switch',
  //       type: 'town',
  //       checked: true,
  //     },
  //     { name: '村边界', icon: 'switch', type: 'village', checked: false },
  //     { name: 'POI点', icon: 'switch', type: 'danger', checked: false },
  //   ],
  // },
  {
    name: '工具',
    icon: 'tool',
    children: [
      { name: '图上量算', icon: 'ruler', widget: 'measure' },
      // { name: "空间分析", icon: "analysis", widget: "analysis" },
      { name: '坐标定位', icon: 'local', widget: 'location-point' },
      { name: '指挥标绘', icon: 'send', widget: 'command-plot' },
      { name: '路线导航', icon: 'navigation', widget: 'location-region' },
      { name: '缓冲分析', icon: 'freezing-line-column', widget: 'buffer' },
      // { name: "视角书签", icon: "bookmark", widget: "bookmark" },
      // { name: "地图打印", icon: "printer", widget: "print" },
      { name: '线上近点', icon: 'clue', widget: 'line' },
      { name: '图上标绘', icon: 'hand-painted-plate', widget: 'isInPoly' },
      { name: '四色图标绘', icon: 'color-card', widget: 'fourAreaDraw' },
      // { name: "路线导航", icon: "connection", widget: "query-route" },
      // { name: "卷帘对比", icon: "switch-contrast", widget: "map-split" },
      // { name: "分屏对比", icon: "full-screen-play", widget: "map-compare" }
      // { name: "百度街景", icon: h(City, { theme: "outline", size: "18" }), widget: "street-view" }
    ],
  },
]);

function handleSwitch(checked, item) {
  switch (item.type) {
    case 'town':
      getLayer().show = checked;
      break;
  }
}

const showWidget = (widget: string) => {
  switch (widget) {
    case 'full-screen':
      toggle();
      break;
    default:
      activate(widget);
      break;
  }
};

const clickMenu = ({ key }: any) => {
  showWidget(key);
};
</script>

<style lang="less" scoped>
.base-pannel {
  height: 40px;
  padding: 0 !important;
  border: 1px solid;
  border: none;
  border-radius: 2px !important;
  background-color: var(--mars-bg-base);
  background-image: none !important;

  .toolbar-item {
    display: inline-block;
    height: 100%;
    padding: 6px 12px;
    color: var(--mars-text-color);
    font-size: 15px;

    &:hover {
      background-color: var(--mars-select-bg);
    }
  }

  .mars-icon {
    margin-right: 5px;
    color: var(--mars-text-color);
  }
}
</style>

<!-- <style lang="less" module>
.toolbar {
  :global {
    padding: 0 !important;
    border: unset !important;
    background: unset !important;
    background-image: url('@/assets/images/visualization/tools.png') !important;
    background-repeat: no-repeat !important;
    background-size: 100% 100% !important;
    box-shadow: unset !important;

    .mars-dialog__content {
      display: inline-flex !important;
      background: unset !important;
    }
  }
}
</style> -->
