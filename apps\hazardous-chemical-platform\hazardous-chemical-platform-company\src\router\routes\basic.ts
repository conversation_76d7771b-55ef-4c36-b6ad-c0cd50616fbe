import type { AppRouteRecordRaw } from '@/router/types';
import {
  REDIRECT_NAME,
  EXCEPTION_COMPONENT,
  PAGE_NOT_FOUND_NAME,
  LAYOUT_TAB_PAGE,
} from '@/router/constant';

// 404 on a page
export const PAGE_NOT_FOUND_ROUTE: AppRouteRecordRaw = {
  path: '/:path(.*)*',
  name: PAGE_NOT_FOUND_NAME,
  component: LAYOUT_TAB_PAGE,
  meta: {
    title: '错误页面',
    hideBreadcrumb: true,
    hideMenu: true,
  },
  children: [
    {
      path: '/:path(.*)*',
      name: PAGE_NOT_FOUND_NAME,
      component: EXCEPTION_COMPONENT,
      meta: {
        title: '错误页面',
        hideBreadcrumb: true,
        hideMenu: true,
      },
    },
  ],
};

export const REDIRECT_ROUTE: AppRouteRecordRaw = {
  path: '/redirect',
  component: LAYOUT_TAB_PAGE,
  name: 'RedirectTo',
  meta: {
    title: '重定向页面',
    hideBreadcrumb: true,
    hideMenu: true,
  },
  children: [
    {
      path: '/redirect/:path(.*)/:_redirect_type(.*)/:_origin_params(.*)?',
      name: REDIRECT_NAME,
      component: () => import('@/views/sys/redirect/index.vue'),
      meta: {
        title: '重定向页面',
        hideBreadcrumb: true,
      },
    },
  ],
};
