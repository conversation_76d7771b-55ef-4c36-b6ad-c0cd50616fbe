import type { AppRouteModule } from '@/router/types';

const dashboard: AppRouteModule = {
  path: '/dashboard',
  name: 'Dashboard',
  component: () => import('@/views/dashboard/index.vue'),
  // redirect: '/dashboard/analysis',
  meta: {
    orderNo: 10,
    icon: 'ion:grid-outline',
    title: '首页',
  },
  // children: [
  //   {
  //     path: 'analysis',
  //     name: 'Analysis',
  //     component: () => import('@/views/dashboard/analysis/CommentsListModal.vue'),
  //     meta: {
  //       title: '首页',
  //     },
  //   },
  // ],
};

export default dashboard;
