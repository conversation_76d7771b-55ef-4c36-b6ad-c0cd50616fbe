import { LAYOUT_TAB_PAGE } from '@/router/constant';
import type { AppRouteModule } from '@/router/types';

const mapRouter: AppRouteModule = {
  path: '/mapRouter',
  name: 'MapRouter',
  component: LAYOUT_TAB_PAGE,
  meta: {
    title: '地图相关',
    ifLocal: true,
  },
  children: [
    {
      path: 'model',
      name: 'Model',
      component: () => import('@/views/map/model/index.vue'),
      meta: {
        title: '模型调试',
      },
    },
  ],
};

export default mapRouter;
