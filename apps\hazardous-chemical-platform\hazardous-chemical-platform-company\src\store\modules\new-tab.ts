import {
  LAYOUT_FIRST_TAB_SELECTED,
  LAYOUT_HEADER_ROUTER_SELECTED,
  LAYOUT_HEADER_SELECTED,
  LAYOUT_SECOND_TAB_SELECTED,
  NORMAL_MENU,
} from '@monorepo-yysz/enums';
import { Menu } from '@/router/types';
import { getSessionCache, setSessionCache } from '@/utils/auth';
import { defineStore } from 'pinia';

interface NewTabStore {
  // 顶部切换
  headerTabSelected: string;
  headerTabRouterSelected: Menu | undefined;
  // 记录第一个tab选择的
  firstTabSelected: string | undefined;
  secondTabSelected: string | undefined;
  normalMenu: Menu[] | undefined;
}

export const useNewTabStore = defineStore({
  id: 'new-tab',
  state: (): NewTabStore => ({
    // 选中的header path
    headerTabSelected: '',
    // 选中的header item\router
    headerTabRouterSelected: undefined,
    // 记录第一个tab选择的
    firstTabSelected: '',
    // 记录第二个tab选择的
    secondTabSelected: '',
    // normal-tab
    normalMenu: undefined,
  }),
  getters: {
    getHeaderTabSelected(state): string {
      return state.headerTabSelected || getSessionCache<string>(LAYOUT_HEADER_SELECTED);
    },
    getHeaderTabRouterSelected(state): Menu | undefined {
      return state.headerTabRouterSelected || getSessionCache<Menu>(LAYOUT_HEADER_ROUTER_SELECTED);
    },
    getFirstTabSelected(state): string {
      return state.firstTabSelected || getSessionCache<string>(LAYOUT_FIRST_TAB_SELECTED);
    },
    getSecondTabSelected(state): string {
      return state.secondTabSelected || getSessionCache<string>(LAYOUT_SECOND_TAB_SELECTED);
    },
    getNormalTab(state): Menu[] | undefined {
      return state.normalMenu || getSessionCache<Menu[]>(NORMAL_MENU);
    },
  },
  actions: {
    setHeaderTabSelected(tabSelected: string) {
      this.headerTabSelected = tabSelected;
      setSessionCache(LAYOUT_HEADER_SELECTED, tabSelected);
    },
    setHeaderTabRouterSelected(tabRouterSelected: Menu) {
      this.headerTabRouterSelected = tabRouterSelected;
      setSessionCache(LAYOUT_HEADER_ROUTER_SELECTED, tabRouterSelected);
    },
    setFirstTabSelected(tabSelected: string | undefined) {
      this.firstTabSelected = tabSelected;
      setSessionCache(LAYOUT_FIRST_TAB_SELECTED, tabSelected);
    },
    setSecondTabSelected(tabSelected: string | undefined) {
      this.secondTabSelected = tabSelected;
      setSessionCache(LAYOUT_SECOND_TAB_SELECTED, tabSelected);
    },
    setNormalTab(selectedMenu: Menu[]) {
      this.normalMenu = selectedMenu;
      setSessionCache(NORMAL_MENU, selectedMenu);
    },
  },
});
