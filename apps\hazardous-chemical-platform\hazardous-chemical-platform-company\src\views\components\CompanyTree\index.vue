<template>
  <BasicTree
    :selectedKeys="selectedKeys"
    :loading="loadingTree"
    :expandOnSearch="true"
    :fieldNames="{ children: 'children', title: 'companyName', key: 'companyId' }"
    :search="true"
    :treeData="treeData"
    :showLine="true"
    @select="handleSelect"
    :class="`${$style['tree-info']} !h-auto`"
    ref="treeRef"
    v-bind="$attrs"
  >
    <template #other>
      <div class="text-xl font-bold pb-2">{{ treeTitle ? treeTitle : '企业' }}</div>
    </template>
    <template #title="{ companyName }">
      <Tooltip class="!truncate text-[15px]">
        <template #title>{{ companyName }}</template>
        {{ companyName }}
      </Tooltip>
    </template>
  </BasicTree>
</template>

<script lang="ts" setup>
import { onMounted, ref, unref, watch, nextTick } from 'vue';
import { BasicTree, KeyType, TreeActionType, TreeItem } from '@/components/Tree';
import { Tooltip } from 'ant-design-vue';
import { companyList } from '@/api/system/company';

const emit = defineEmits(['selectInfo', 'getFirstNode', 'update:reload']);

const props = defineProps({
  /**
   * 更新数据
   */
  reload: { type: Boolean },
  selectedKey: { type: Array as PropType<KeyType[]>, default: undefined },
  ifSelected: { type: Boolean },
  treeTitle: { type: String },
  companyType: { type: String },
});

const treeData = ref<TreeItem[]>([]);

const selectedKeys = ref<KeyType[]>([]);

const loadingTree = ref<boolean>(false);

const treeRef = ref<Nullable<TreeActionType>>(null);

function handleSelect(_: any, { node, selected }: Recordable) {
  emit('selectInfo', {
    name: selected ? node.companyName?.el?.innerText : undefined,
    id: selected ? node.companyId : undefined,
    type: selected ? node.companyType : undefined,
  });
}

async function getTreeData() {
  loadingTree.value = true;
  treeData.value = (await companyList({
    pageSize: 0,
  })) as TreeItem[];
  loadingTree.value = false;

  //默认选择
  if (unref(props.ifSelected) && unref(treeData) && unref(treeData)[0]?.companyId) {
    const autoIds = [unref(treeData)[0].companyId];
    const id = props.selectedKey && props.selectedKey.length > 0 ? props.selectedKey : autoIds;
    selectedKeys.value = id;

    emit('getFirstNode', { id: id[0], name: unref(treeData)[0].companyName });
  }
  await nextTick();
  handleLevel();
}

//定义tree
function getTree() {
  const tree = unref(treeRef);
  if (!tree) {
    throw new Error('tree is null!');
  }
  return tree;
}

function handleLevel() {
  getTree().expandAll(true);
}

//初始化树
onMounted(async () => {
  await getTreeData();
});

watch(
  () => props.reload,
  async () => {
    await getTreeData();
  }
);

watch(
  () => props.selectedKey,
  async val => {
    await nextTick();
    selectedKeys.value = props.selectedKey as KeyType[];
    getTree().setSelectedKeys(val || []);
  }
);
</script>

<style lang="less" module>
.tree-info {
  :global {
    background-image: url('@/assets/images/basic/search-bg.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    max-height: 73vh;
    min-height: 585px;

    .ant-tree {
      height: calc(71vh - 73px);
      overflow-x: auto;

      .ant-tree-treenode {
        margin-top: 5px;

        .ant-tree-title {
          font-size: 16px;
          height: 100%;
        }

        svg {
          color: #184564;
        }
      }
    }
  }
}
</style>
