<template>
  <Layout.Footer
    :class="[prefixCls]"
    v-if="getShowLayoutFooter"
    ref="footerRef"
  >
    <div class="flex items-center justify-around w-full h-full cursor-pointer">
      <div
        class="w-[48px] h-[48px] flex-shrink-0 flex items-center"
        @click="handleScroll('left')"
      >
        <img
          :src="leftArrow"
          alt=""
          class="flex-shrink-0 w-full"
          v-if="otherCardScrollLeft"
        />
      </div>
      <div
        class="flex flex-shrink-0 overflow-x-auto overflow-y-hidden otherCard pr-[12px]"
        :style="{ width: 'calc(100% - 96px)' }"
      >
        <div
          v-for="item in menus"
          class="relative flex flex-col items-center h-[95px] flex-shrink-0 -enter-x"
          :style="{ width: `${100 * (1 / 6)}%` }"
          @click="handleRoute(item)"
          @mouseover="selected = item.path"
          :class="selected === item.path ? 'justify-between' : 'justify-around'"
        >
          <img :src="selected === item.path ? item.selectedIcon : item.icon" />
          <div
            class="w-full text-center item-text flex flex-col justify-start px-[2px]"
            :style="{ fontFamily: 'Source Han Sans CN' }"
            :class="
              selected === item.path ? 'text-[#FFC72C] text-[18px]' : 'text-[#00D6FF] text-[16px]'
            "
          >
            {{ item?.meta?.title }}
          </div>
        </div>
      </div>
      <div
        class="w-[48px] h-[48px] flex-shrink-0 flex items-center"
        @click="handleScroll('right')"
      >
        <img
          :src="rightArrow"
          alt=""
          class="flex-shrink-0 w-full"
          v-if="otherCardScrollLeft !== -1"
        />
      </div>
    </div>
  </Layout.Footer>
</template>
<script lang="ts" setup>
import { computed, unref, ref } from 'vue';
import { Layout } from 'ant-design-vue';
import { useRootSetting } from '@/hooks/setting/useRootSetting';
import { useRouter } from 'vue-router';
import { useDesign, useLayoutHeight } from '@monorepo-yysz/hooks';
import { useSplitMenu } from '@/layouts/default/menu/useLayoutMenu';
import { MenuSplitTyeEnum } from '@monorepo-yysz/enums';
import leftArrow from '@/assets/images/home-page/left.png';
import rightArrow from '@/assets/images/home-page/right.png';
import { Links } from '@/enums/pageEnum';
import { useGo } from '@/hooks/web/usePage';
import { isHttpUrl, openWindow } from '@monorepo-yysz/utils';
import { useUserStore } from '@/store/modules/user';
import dashboard from '@/router/routes/modules/dashboard';
import icon_dashboard_selected from '@/assets/images/home-page/icon_dashboard_selected.png';
import icon_dashboard from '@/assets/images/home-page/icon_dashboard.png';
import { map } from 'lodash-es';
import { useNewTabStore } from '@/store/modules/new-tab';
import mapRouter from '@/router/routes/modules/mapRouter';

defineOptions({ name: 'TabLayoutFooter' });

const go = useGo();

const router = useRouter();

const userStore = useUserStore();

const { getShowNewFooter } = useRootSetting();

const { prefixCls } = useDesign('layout-footer');

const { setFooterHeight } = useLayoutHeight();

const useNewTab = useNewTabStore();

const splitType = ref<MenuSplitTyeEnum>(MenuSplitTyeEnum.NONE);

const { menusRef } = useSplitMenu(splitType);

const footerRef = ref<ComponentRef>(null);

const selected = ref<string>('/dashboard');

const otherCardScrollLeft = ref(0);

const menus = computed(() => {
  return map([dashboard, mapRouter, ...unref(menusRef)], v => ({
    ...v,
    selectedIcon: icon_dashboard_selected,
    icon: icon_dashboard,
  })) as Recordable[]; // ...unref(menusRef)];
});

const getShowLayoutFooter = computed(() => {
  if (unref(getShowNewFooter)) {
    const footerEl = unref(footerRef)?.$el;
    setFooterHeight(footerEl?.offsetHeight || 0);
  } else {
    setFooterHeight(0);
  }
  return unref(getShowNewFooter) && !unref(router.currentRoute).meta?.hiddenFooter;
});

function handleRoute(item) {
  const path = item.path;
  const url = path.slice(1, path.length);
  const ifUrl = isHttpUrl(url);

  if (isHttpUrl(path) || ifUrl) {
    let _path = ifUrl ? url : path;
    _path = item.ifToken
      ? _path.includes('?')
        ? `${_path}&token=${userStore.getToken}`
        : `${_path}?token=${userStore.getToken}`
      : _path;
    openWindow(_path);
  } else if (Links.includes(path)) {
    const r = router.resolve({
      path,
    });
    openWindow(r.href);
  } else {
    go(path);
  }
  // 设置选择
  useNewTab.setHeaderTabSelected(item.path);
  useNewTab.setHeaderTabRouterSelected(item);
}

const handleScroll = type => {
  const dom: any = document.querySelector('.otherCard');
  if (type === 'left') {
    dom?.scrollTo({
      top: 0,
      left: dom.scrollLeft - dom?.offsetWidth,
      behavior: 'smooth',
    });
  } else {
    dom?.scrollTo({
      top: 0,
      left: dom.scrollLeft + dom?.offsetWidth,
      behavior: 'smooth',
    });
  }
  setTimeout(() => {
    if (dom.scrollLeft <= 0) {
      otherCardScrollLeft.value = 0;
    } else if (dom.scrollLeft >= dom?.scrollWidth - dom.offsetWidth) {
      // 滚动到底部
      otherCardScrollLeft.value = -1;
    } else {
      otherCardScrollLeft.value = dom.scrollLeft;
    }
  }, 500);
};
</script>

<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-layout-footer';

@normal-color: rgba(0, 0, 0, 0.45);

@hover-color: rgba(0, 0, 0, 0.85);

.@{prefix-cls} {
  // 页脚固定高度
  height: 128px;
  color: @normal-color;
  text-align: center;
  position: absolute;
  bottom: 22px;
  width: 56%;
  left: 50%;
  transform: translateX(-50%);
  background-image: url('@/assets/images/home-page/bottom.png');
  background-repeat: 'no-repeat';
  background-size: 100% 100%;

  /* 针对WebKit浏览器隐藏滚动条 */
  .otherCard::-webkit-scrollbar {
    display: none;
  }

  /* 针对Firefox隐藏滚动条 */
  .otherCard {
    scrollbar-width: none;
    /* Firefox */
  }

  /* 针对IE和Edge隐藏滚动条 */
  .otherCard {
    -ms-overflow-style: none;
    /* IE 10+ */
    scrollbar-width: none;
    /* Firefox */
  }
}
</style>
