<template>
  <div
    class="w-full h-full relative"
    :class="$style.dashboard"
  >
    <main-view />
    <RouterView>
      <template #default="{ Component, route }">
        <transition
          :name="
            getTransitionName({
              route,
              openCache: false,
              enableTransition: getEnableTransition,
              cacheTabs: getCaches,
              def: getBasicTransition,
            })
          "
          mode="out-in"
          appear
        >
          <component
            :is="Component"
            :key="route.fullPath"
          />
        </transition>
      </template>
    </RouterView>
  </div>
</template>

<script lang="ts" setup>
import MainView from '@/marsgis/components/mars-work/main-view.vue';
import { getTransitionName } from '@/layouts/page/transition';
import { useTransitionSetting } from '@/hooks/setting/useTransitionSetting';
import { computed, onMounted } from 'vue';

const { getBasicTransition, getEnableTransition } = useTransitionSetting();

const getCaches = computed((): string[] => {
  return [];
});

onMounted(() => {});
</script>

<style lang="less" module>
.dashboard {
  :global {
  }
}
</style>
