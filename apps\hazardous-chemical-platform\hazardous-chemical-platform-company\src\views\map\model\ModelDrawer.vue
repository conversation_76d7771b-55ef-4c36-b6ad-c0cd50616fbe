<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    showFooter
    :title="title"
    width="80%"
    @ok="handleSubmit"
  >
    <a-row>
      <a-col
        :span="12"
        class="h-83vh overflow-auto"
      >
        <BasicForm
          @register="registerForm"
          :class="disabledClass"
        >
          <template #load-button="{ model }">
            <Button
              type="primary"
              @click="handleModel(model)"
            >
              加载模型
            </Button>
          </template>
          <template #fly-to-button="{ model }">
            <Button
              type="primary"
              @click="locateToModel"
            >
              定位
            </Button>
          </template>
        </BasicForm>
      </a-col>
      <a-col
        :span="12"
        class="h-83vh overflow-hidden relative"
      >
        <BasicView @map-loaded="handleLoad" />
      </a-col>
    </a-row>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref, unref, computed, onUnmounted } from 'vue';
import { useDraw<PERSON><PERSON><PERSON>, BasicDrawer } from '@/components/Drawer';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from './data';
import BasicView from '@/marsgis/components/mars-work/basic-view.vue';
import * as mars3d from 'mars3d';
import { $message } from '@/marsgis/components/mars-ui';
import { Button } from '@/components/Button';

const emit = defineEmits(['register', 'success']);

const eventTarget = new mars3d.BaseClass(); // 事件对象，用于抛出事件到面板中

let tiles3dLayer: mars3d.layer.TilesetLayer | null;

let mapInstance: Nullable<mars3d.Map>;

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.modelName || ''}--详情`
      : `编辑${unref(record)?.modelName || ''}`
    : '新增模型';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem({ updateModel, updateDepthTest });
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerDrawer, { setDrawerProps }] = useDrawerInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
    });
  }

  setProps({ disabled: unref(disabled) });

  setDrawerProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

function handleLoad(mapIns: mars3d.Map) {
  mapInstance = mapIns;
}

//

function removeLayer() {
  if (tiles3dLayer) {
    mapInstance?.removeLayer(tiles3dLayer, true);
    tiles3dLayer = null;
  }
}

// 定位
function locateToModel() {
  if (tiles3dLayer?.tileset?.boundingSphere) {
    mapInstance?.camera.flyToBoundingSphere(tiles3dLayer.tileset.boundingSphere, {
      offset: new mars3d.Cesium.HeadingPitchRange(
        mapInstance?.camera.heading,
        mapInstance?.camera.pitch,
        tiles3dLayer.tileset.boundingSphere.radius * 2
      ),
    });
  } else {
    tiles3dLayer?.position &&
      mapInstance?.flyToPoint(tiles3dLayer.position, {
        radius: (tiles3dLayer?.tileset?.boundingSphere?.radius as number) * 2,
      });
  }
}

// 修改模型
function updateModel(value) {
  if (tiles3dLayer) {
    tiles3dLayer.setOptions({
      position: {
        lat: value.lat,
        lng: value.lng,
        alt: value.alt,
      },
      axis: value.axis,
      rotation: {
        x: value.rotationX,
        y: value.rotationY,
        z: value.rotationZ,
      },
      scale: value.scale,
    });
  }
}

// 深度
const updateDepthTest = values => {
  mapInstance && (mapInstance.scene.globe.depthTestAgainstTerrain = values.depthTestAgainstTerrain);
};

// 加载模型
const handleModel = values => {
  removeLayer();

  if (!values.modelUrl) {
    $message('请输入图层URL！');
    return;
  }

  tiles3dLayer = new mars3d.layer.TilesetLayer({
    name: '模型名称',
    url: values.modelUrl,
    maximumScreenSpaceError: 16,
    maximumMemoryUsage: 1024,
    popup: 'all',
    flyTo: true,
  });
  mapInstance?.addLayer(tiles3dLayer);

  // 加载完成事件
  tiles3dLayer.on(mars3d.EventType.load, function () {
    eventTarget.fire('tiles3dLayerLoad', { layer: tiles3dLayer });
  });

  // 加载完成事件
  tiles3dLayer.on(mars3d.EventType.updatePosition, function () {
    eventTarget.fire('changePoition', {
      center: tiles3dLayer?.center,
      rotation: tiles3dLayer?.rotation,
    });
  });

  tiles3dLayer.bindContextMenu([
    {
      text: '开始编辑',
      icon: 'fa fa-edit',
      show: function () {
        return tiles3dLayer?.hasEdit && !tiles3dLayer?.isEditing;
      },
      callback: () => {
        tiles3dLayer?.startEditing();
      },
    },
  ]);
};

async function handleSubmit() {
  try {
    const values = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setDrawerProps({ confirmLoading: false });
  }
}

onUnmounted(() => {
  mapInstance = null;
  tiles3dLayer = null;
});
</script>
