import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '模型名称',
      dataIndex: 'modelName',
    },
    {
      title: '模型地址',
      dataIndex: '',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'modelName',
      label: '模型名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const modalFormItem = ({ updateModel, updateDepthTest }): FormSchema[] => {
  return [
    {
      field: 'modelName',
      label: '模型名称',
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    { field: '', label: '模型地址URL', component: 'Divider' },
    {
      field: 'modelUrl',
      label: '模型地址',
      component: 'Input',
      colProps: { span: 18 },
      required: true,
      defaultValue: 'http://localhost/hqmodel/inhq/tileset.json',
      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      slot: 'load-button',
    },
    { field: '', label: '模型位置', component: 'Divider' },
    {
      field: 'lng',
      label: '经度',
      required: true,
      colProps: { span: 12 },
      component: 'InputNumber',
      defaultValue: 84.038614,
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          step: 0.000001,
          min: -180,
          max: 180,
          onChange() {
            updateModel?.(formModel);
          },
        };
      },
    },
    {
      field: 'lat',
      label: '纬度',
      required: true,
      colProps: { span: 12 },
      component: 'InputNumber',
      defaultValue: 41.823864,
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          step: 0.000001,
          min: -90,
          max: 90,
          onChange() {
            updateModel?.(formModel);
          },
        };
      },
    },
    {
      field: 'alt',
      label: '高度',
      required: true,
      colProps: { span: 12 },
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      defaultValue: 959,
      componentProps: ({ formModel }) => {
        return {
          step: 0.1,
          onChange() {
            updateModel?.(formModel);
          },
        };
      },
    },
    {
      field: 'depthTestAgainstTerrain',
      label: '深度监测',
      colProps: { span: 6 },
      component: 'Checkbox',
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          onChange() {
            updateDepthTest(formModel);
          },
        };
      },
    },
    {
      field: '',
      label: '',
      colProps: { span: 6 },
      component: 'Checkbox',
      rulesMessageJoinLabel: true,
      slot: 'fly-to-button',
    },
    { field: '', label: '模型方向', component: 'Divider' },
    {
      field: 'axis',
      label: '变换垂直轴',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: '',
      componentProps: ({ formModel }) => {
        return {
          onChange() {
            updateModel?.(formModel);
          },
          options: [
            { value: '', label: '默认' },
            { value: 'Z_UP_TO_X_UP', label: 'Z轴 -> X轴' },
            { value: 'Z_UP_TO_Y_UP', label: 'Z轴 -->Y轴' },
            { value: 'X_UP_TO_Y_UP', label: 'X轴 -->Y轴' },
            { value: 'X_UP_TO_Z_UP', label: 'X轴 -->Z轴' },
            { value: 'Y_UP_TO_X_UP', label: 'Y轴 -->X轴' },
            { value: 'Y_UP_TO_Z_UP', label: 'Y轴 -->Z轴' },
          ],
        };
      },
    },
    {
      field: 'rotationX',
      label: '绕X轴旋转模型',
      colProps: { span: 12 },
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          onChange() {
            updateModel?.(formModel);
          },
        };
      },
    },
    {
      field: 'rotationY',
      label: '绕Y轴旋转模型',
      colProps: { span: 12 },
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          onChange() {
            updateModel?.(formModel);
          },
        };
      },
    },
    {
      field: 'rotationZ',
      label: '绕Z轴旋转模型',
      colProps: { span: 12 },
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          onChange() {
            updateModel?.(formModel);
          },
        };
      },
    },
    { field: '', label: '其他参数', component: 'Divider' },
    {
      field: 'scale',
      label: '缩放比例',
      colProps: { span: 12 },
      component: 'InputNumber',
      defaultValue: 1,
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          onChange() {
            updateModel?.(formModel);
          },
        };
      },
    },
  ];
};
