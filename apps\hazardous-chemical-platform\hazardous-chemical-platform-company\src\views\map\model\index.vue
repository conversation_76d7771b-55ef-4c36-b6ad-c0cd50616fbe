<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增
        </a-button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ModelDrawer
      @register="registerDrawer"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { columns, formSchemas } from './data';
import { useMessage } from '@monorepo-yysz/hooks';
import { useDrawer } from '@/components/Drawer';
import ModelDrawer from './ModelDrawer.vue';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, {}] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  // api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',

    fixed: undefined,
  },
});

const [registerDrawer, { openDrawer }] = useDrawer();

// 新增
function handleClick() {
  openDrawer(true, { isUpdate: false, disabled: false });
}

// 编辑
function handleEdit(record: Recordable<any>) {
  // view({ ...record }).then(({ data }) => {
  openDrawer(true, { isUpdate: true, disabled: false, record });
  // });
}

// 详情
function handleView(record: Recordable<any>) {
  // view({ ...record }).then(({ data }) => {
  openDrawer(true, { isUpdate: true, disabled: true, record });
  // });
}

// 删除
function handleDelete(record: Recordable<any>) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.title}`,
    onOk: function () {},
  });
}

function handleSuccess({ values, isUpdate }: Recordable<any>) {
  // saveOrUpdate(values).then(({ code, message }) => {
  //   if (code === 200) {
  //     createSuccessModal({
  //       content: `${isUpdate ? '编辑' : '新增'}成功`,
  //     });
  //     reload();
  //     closeModal();
  //   } else {
  //     createErrorModal({
  //       content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
  //     });
  //   }
  // });
}
</script>
