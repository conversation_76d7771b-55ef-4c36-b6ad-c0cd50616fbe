<template>
  <LoginFormTitle
    v-show="getShow"
    class="enter-x"
  />
  <Form
    class="p-4 px-[50px] enter-x"
    :class="$style.login"
    :model="formData"
    :rules="getFormRules"
    ref="formRef"
    v-show="getShow"
    @keypress.enter="handleLogin"
  >
    <FormItem
      name="account"
      class="enter-x bg-[#F0F5FA]"
    >
      <Input
        size="large"
        v-model:value="formData.account"
        :placeholder="t('sys.login.accountPlaceholder')"
        class="fix-auto-fill"
      >
        <template #addonBefore>
          <img
            :src="userName"
            class="w-[20px] h-[20px] mr-[10px]"
          />
          <span class="!text-[#D8E2EB] relative">|</span>
        </template>
      </Input>
    </FormItem>

    <FormItem
      name="password"
      class="enter-x bg-[#F0F5FA]"
    >
      <InputPassword
        size="large"
        visibilityToggle
        v-model:value="formData.password"
        :placeholder="t('sys.login.passwordPlaceholder')"
      >
        <template #addonBefore>
          <img
            :src="password"
            class="w-[20px] h-[20px] mr-[10px]"
          />
          <span class="!text-[#D8E2EB] relative">|</span>
        </template>
      </InputPassword>
    </FormItem>

    <FormItem
      name="verifyCode"
      class="enter-x bg-[#F0F5FA]"
    >
      <Input
        size="large"
        v-model:value="formData.verifyCode"
        placeholder="请输入验证码"
      >
        <template #addonBefore>
          <img
            :src="verify"
            class="w-[20px] h-[20px] mr-[10px]"
          />

          <span class="!text-[#D8E2EB] relative">|</span>
        </template>
        <template #suffix>
          <img
            :src="codeUrl"
            @click="handleChangeCode"
            :style="{
              position: 'absolute',
              right: 0,
              cursor: 'pointer',
            }"
            class="!border border-gray-200 border-solid z-10 !h-full verify-code w-[135px]"
          />
        </template>
      </Input>
    </FormItem>
    <!-- <ARow class="enter-x">
      <ACol :span="12">
        <FormItem>
          <Checkbox
            v-model:checked="rememberMe"
            size="small"
          >
            {{ t('sys.login.rememberMe') }}
          </Checkbox>
        </FormItem>
      </ACol>
      <ACol :span="12">
        <FormItem :style="{ 'text-align': 'right' }">
          <Button
            type="link"
            size="small"
            @click="setLoginState(LoginStateEnum.RESET_PASSWORD)"
          >
            {{ t('sys.login.forgetPassword') }}
          </Button>
        </FormItem>
      </ACol>
    </ARow> -->

    <FormItem class="enter-x mb-72px mt-67px">
      <Button
        type="primary"
        size="large"
        html-type="submit"
        block
        @click="handleLogin"
        :loading="loading"
        class="w-full !rounded-lg !bg-gradient-to-b from-[#00C0FF] to-[#017FFF] !text-[16px]"
      >
        {{ t('sys.login.loginButton') }}
      </Button>
    </FormItem>
    <!-- <ARow
      class="enter-x"
      :gutter="[16, 16]"
    >
      <ACol
        :md="8"
        :xs="24"
      >
        <Button
          block
          @click="setLoginState(LoginStateEnum.MOBILE)"
        >
          {{ t('sys.login.mobileSignInFormTitle') }}
        </Button>
      </ACol>
      <ACol
        :md="8"
        :xs="24"
      >
        <Button
          block
          @click="setLoginState(LoginStateEnum.QR_CODE)"
        >
          {{ t('sys.login.qrSignInFormTitle') }}
        </Button>
      </ACol>
      <ACol
        :md="8"
        :xs="24"
      >
        <Button
          block
          @click="setLoginState(LoginStateEnum.REGISTER)"
        >
          {{ t('sys.login.registerButton') }}
        </Button>
      </ACol>
    </ARow> -->

    <!-- <Divider class="enter-x">{{ t('sys.login.otherSignIn') }}</Divider>

    <div
      class="flex justify-evenly enter-x"
      :class="`${prefixCls}-sign-in-way`"
    >
      <GithubFilled />
      <WechatFilled />
      <AlipayCircleFilled />
      <GoogleCircleFilled />
      <TwitterCircleFilled />
    </div> -->
  </Form>
</template>
<script lang="ts" setup>
import { reactive, ref, unref, computed, onMounted } from 'vue';
import { Form, Input, Button } from 'ant-design-vue';
// import {
//   GithubFilled,
//   WechatFilled,
//   AlipayCircleFilled,
//   GoogleCircleFilled,
//   TwitterCircleFilled,
// } from '@ant-design/icons-vue';
import LoginFormTitle from './LoginFormTitle.vue';
import { useI18n } from '@/hooks/web/useI18n';
import { useMessage, useDesign } from '@monorepo-yysz/hooks';
import { useUserStore } from '@/store/modules/user';
import { LoginStateEnum, useLoginState, useFormRules, useFormValid } from './useLogin';
import { generatorWebVerifyCode } from '@/api';
//import { onKeyStroke } from '@vueuse/core';
import userName from '@/assets/images/login/userName.png';
import password from '@/assets/images/login/password.png';
import verify from '@/assets/images/login/verify.png';

const FormItem = Form.Item;
const InputPassword = Input.Password;

const { t } = useI18n();
const { notification, createErrorModal } = useMessage();
const { prefixCls } = useDesign('login');
const userStore = useUserStore();

const codeUrl = ref();

const verifyCodeId = ref();

const { getLoginState } = useLoginState();
const { getFormRules } = useFormRules();

const formRef = ref();
const loading = ref(false);
const rememberMe = ref(false);

const formData = reactive({
  account: undefined,
  password: undefined,
  verifyCode: undefined,
});

const { validForm } = useFormValid(formRef);

//onKeyStroke('Enter', handleLogin);

const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN);

async function handleChangeCode() {
  await getCode();
}

async function getCode() {
  const { imageBase64Data, verifyCodeId: id } = await generatorWebVerifyCode();
  codeUrl.value = imageBase64Data;
  verifyCodeId.value = id;
}

async function handleLogin() {
  const data = await validForm();
  if (!data) return;
  try {
    loading.value = true;
    const userInfo = await userStore.login({
      pwd: data.password,
      account: data.account,
      mode: 'none', //不要默认的错误提示
      verifyCode: data.verifyCode,
      verifyCodeId: unref(verifyCodeId),
      device: 'WEBSITE',
      loginType: 'ACCOUNT',
    });
    if (userInfo) {
      notification.success({
        message: t('sys.login.loginSuccessTitle'),
        description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.nickname || userInfo.account}`,
        duration: 3,
      });
    }
  } catch (error) {
    createErrorModal({
      title: t('sys.api.errorTip'),
      content: (error as unknown as Error).message || t('sys.api.networkExceptionMsg'),
      getContainer: () => document.body.querySelector(`.${prefixCls}`) || document.body,
    });
  } finally {
    loading.value = false;
    getCode();
    formData.verifyCode = undefined;
  }
}

onMounted(async () => {
  await getCode();
});
</script>

<style lang="less" module>
.login {
  :global {
    .title-color {
      background: linear-gradient(0deg, #9ddbff, #e3efff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .ant-input-group-addon,
    .ant-input-affix-wrapper,
    .ant-input-affix-wrapper-focused,
    input {
      border: transparent !important;
      background-color: transparent !important;
      border-inline-end-width: unset !important;
      color: #000000;

      &:autofill {
        background: #e3efff !important; // 支持火狐
      }
      // 支持chrome
      &:-webkit-autofill,
      &:-webkit-autofill:hover,
      &:-webkit-autofill:focus,
      &:-webkit-autofill:active {
        transition: background-color 500000000s ease-in-out 0s; //背景色透明  生效时长  过渡效果  启用时延迟的时间
        -webkit-text-fill-color: #000000 !important;
      }

      &::-webkit-input-placeholder {
        --tw-placeholder-opacity: 1;
        color: rgba(107, 114, 128, var(--tw-placeholder-opacity));
      }
      &::-moz-placeholder {
        --tw-placeholder-opacity: 1;
        color: rgba(107, 114, 128, var(--tw-placeholder-opacity));
      }
      &:-ms-input-placeholder {
        --tw-placeholder-opacity: 1;
        color: rgba(107, 114, 128, var(--tw-placeholder-opacity));
      }
      &::-ms-input-placeholder {
        --tw-placeholder-opacity: 1;
        color: rgba(107, 114, 128, var(--tw-placeholder-opacity));
      }
      &::placeholder {
        --tw-placeholder-opacity: 1;
        color: rgba(107, 114, 128, var(--tw-placeholder-opacity));
      }

      &:focus {
        box-shadow: unset !important;
      }
    }
  }
}
</style>
