<template>
  <BasicModal
    @register="registerModal"
    @ok="handleOk"
    :can-fullscreen="false"
    :title="title"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
      <template #pid="{ model, field }">
        <ApiTreeSelect
          v-model:value="model[field]"
          show-search
          :tree-data="categoryData"
          placeholder="请选择父级"
          tree-default-expand-all
          allow-clear
          labelField="deptName"
          valueField="deptId"
          :getPopupContainer="container"
          :filterTreeNode="
            (input: string, option: any) =>
              option.deptName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          "
        />
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { modalFormSchema } from './data';
import { useForm, BasicForm, ApiTreeSelect } from '@/components/Form';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useUserStore } from '@/store/modules/user';
import { list } from '@/api/system/dept';
import { AccountTypeEnum } from '@monorepo-yysz/enums';

const emit = defineEmits(['register', 'success']);

const categoryData = ref<Recordable[]>([]);

const container = () => document.body;

const userStore = useUserStore();

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const record = ref<Recordable>();

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.deptName || ''}--详情`
      : `编辑${unref(record)?.deptName || ''}`
    : '新增部门';
});

const isUpdate = ref<boolean>(false);

const disabled = ref<boolean>(false);

const form = computed(() => {
  return modalFormSchema();
});

const [registerModal, { setModalProps }] = useModalInner(async function (data) {
  await resetFields();

  isUpdate.value = !!data.isUpdate;

  disabled.value = !!data.disabled;

  record.value = data.record;

  const { data: da } = await list({
    companyId:
      userStore.getUserInfo.accountType === AccountTypeEnum.ADMIN
        ? '1'
        : userStore.getUserInfo.companyId,
  });

  categoryData.value = da as Recordable[];

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
    });
  }

  setProps({
    disabled: unref(disabled),
  });
});

const [registerForm, { validate, setFieldsValue, setProps, resetFields }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

async function handleOk() {
  setModalProps({ confirmLoading: true });

  try {
    const { companyId, pid, ...values } = await validate();

    emit('success', {
      params: {
        ...unref(record),
        ...values,
        pid: pid ? pid : 0,
        companyId: companyId ? companyId : userStore.getUserInfo.companyId,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
