<template>
  <div>
    <BasicTable @register="register">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleAdd(undefined)"
          >新增部门
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'fa6-solid:pen-to-square',
                label: '修改',
                type: 'primary',
                onClick: handleModify.bind(null, record),
              },
              {
                icon: 'ep:delete',
                label: '删除',
                danger: true,
                type: 'primary',
                onClick: handleDelete.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <DeptModal
      @register="registerModal"
      width="40%"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { columns, formSchemas } from './data';
import { saveByDTO, updateDeptInfo, getDeptList, deleteLine, view } from '@/api/system/dept';
import { useModal } from '@/components/Modal';
import { useTable, TableAction, BasicTable } from '@/components/Table';
import DeptModal from './DeptModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';

const { createErrorModal, createSuccessModal, createConfirm } = useMessage();

const [register, { reload }] = useTable({
  rowKey: 'deptId',
  columns: columns(),
  api: getDeptList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  requiredParamsKeys: ['companyId'],
  isTreeTable: true,
  useSearchForm: true,
  bordered: true,
  pagination: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    width: 300,
    fixed: undefined,
  },
});

const [registerModal, { closeModal, openModal }] = useModal();

function handleAdd(record: any) {
  openModal(true, { isUpdate: false, disabled: false, record, addChild: true });
}

function handleModify(record: any) {
  view({ deptId: record.deptId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确定是否删除部门名称:【${record.deptName}】的数据?`,
    onOk: function () {
      deleteLine(record.deptId).then(res => {
        const { code, message } = res;
        if (code === 200) {
          createSuccessModal({ content: '删除成功！' });
          reload();
        } else {
          createErrorModal({
            content: `删除失败！${message} `,
          });
        }
      });
    },
  });
}

function handleSuccess({ params, isUpdate }: Recordable) {
  const api = isUpdate ? updateDeptInfo : saveByDTO;

  api({ ...params }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({ content: `${isUpdate ? '修改' : '新增'}成功！` });
      reload();
      closeModal();
    } else {
      createErrorModal({ content: `${isUpdate ? '修改' : '新增'}失败！${message}。` });
    }
  });
}
</script>
