import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import dayjs from 'dayjs';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '登录账号',
      dataIndex: 'account',
    },
    {
      title: '登录人',
      dataIndex: 'nickname',
      customRender({ text }) {
        const name = text ? text : '--';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '登录设备',
      dataIndex: 'device',
    },
    {
      title: '登录类型',
      dataIndex: 'operateType',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`logOperateType_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '登录时间',
      dataIndex: 'createTime',
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  return [
    {
      field: 'account',
      label: '登录账号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'operateType',
      label: '登录类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('logOperateType'),
        };
      },
    },
    {
      field: 'queryTime',
      label: '登录时间',
      component: 'RangePicker',
      colProps: { span: 8 },
      defaultValue: [
        dayjs(dayjs().startOf('month').hour(0).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
        dayjs(dayjs().endOf('month').hour(23).minute(59).second(59).format('YYYY-MM-DD HH:mm:ss')),
      ],
      componentProps: {
        showTime: {
          hideDisabledOptions: true,
          defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
        },
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DDTHH:mm:ss',
        allowClear: false,
      },
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();

  return [
    {
      field: 'account',
      label: '登录账号',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'nickname',
      label: '登录人',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'device',
      label: '登录设备',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'clientIp',
      label: '客户端id',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 30,
      },
    },
    {
      field: 'operateType',
      label: '操作类型',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('logOperateType'),
        };
      },
    },
    {
      field: 'createTime',
      label: '登录时间',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'resultCode',
      label: '响应code',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'resultMsg',
      label: '响应结果描述',
      component: 'InputTextArea',
      colProps: { span: 24 },
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 30,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    {
      field: 'loginVO',
      label: '响应成功时,返回数据',
      component: 'InputTextArea',
      colProps: { span: 24 },
      labelWidth: 160,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 30,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
  ];
};
