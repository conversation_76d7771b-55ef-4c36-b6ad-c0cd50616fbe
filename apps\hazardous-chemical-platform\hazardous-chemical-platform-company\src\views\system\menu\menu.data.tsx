import { useDictionary } from '@/store/modules/dictionary';
import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import type { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group.d';
import { getMenuList } from '@/api/sys/menu';
import { Tinymce } from '@/components/Tinymce';

function sortMenuData(menuData) {
  // 使用 JavaScript 的数组排序方法，按照 orderNum 字段升序排序
  menuData.sort((a, b) => {
    return a.orderNum - b.orderNum;
  });
  // 如果每个菜单项还有子菜单，需要对子菜单递归排序
  menuData.forEach(menu => {
    if (menu.children && menu.children.length > 0) {
      menu.children = sortMenuData(menu.children);
    }
  });
  return menuData;
}
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '菜单名称',
      dataIndex: 'title',
      align: 'left',
    },
    {
      title: '路由地址\n(权限标识)',
      dataIndex: 'path',
    },
    {
      title: '组件',
      dataIndex: 'component',
    },
    {
      title: '排序',
      dataIndex: 'orderNum',
    },
    {
      title: '状态',
      dataIndex: 'state',
      customRender: ({ record }) => {
        const state = record.state;
        const color = dictionary.getDictionaryMap.get(`commonStatus_${state}`)?.remark;
        const text = dictionary.getDictionaryMap.get(`commonStatus_${state}`)?.dictName;
        return h(Tag, { color: color }, () => text);
      },
    },
  ];
};

const isDir = (type: string) => type === 'MENU';
const isMenu = (type: string) => type === 'CATALOGUE';
const isButton = (type: string) => type === 'BUTTON';

export const searchFormSchema = (): FormSchema[] => {
  return [
    {
      field: 'title',
      label: '菜单名称',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
  ];
};

export const formSchema = (isLineCreate: boolean): FormSchema[] => {
  const dictionary = useDictionary();

  return [
    {
      field: 'menuType',
      label: '菜单类型',
      component: 'RadioButtonGroup',
      defaultValue: 'MENU',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('menuType') as RadioGroupChildOption[],
      },
      colProps: { lg: 24, md: 24 },
    },
    {
      field: 'title',
      label: '菜单名称',
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: true,
    },
    {
      field: 'pid',
      label: '上级菜单',
      component: 'ApiTreeSelect',
      ifShow: ({ values }) => !isDir(values.menuType) && !isLineCreate, //({ values }) => values?.pid !== 0, //
      rulesMessageJoinLabel: true,
      componentProps() {
        return {
          api: getMenuList,
          labelField: 'title',
          valueField: 'menuId',
          showSearch: true,
          treeDefaultExpandAll: true,
          filterTreeNode(input: string, option: any) {
            return option.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          getPopupContainer: () => document.body,
          afterFetch: res => {
            return sortMenuData(res || []);
          },
        };
      },
    },
    {
      field: 'ifTop',
      label: '是否最顶级',
      rulesMessageJoinLabel: true,
      component: 'RadioGroup',
      defaultValue: false,
      componentProps: {
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
    },
    {
      field: 'orderNum',
      label: '排序',
      rulesMessageJoinLabel: true,
      component: 'InputNumber',
      required: true,
      className: '!w-full',
    },
    {
      field: 'icon',
      label: '图标',
      component: 'IconPicker',
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => !isButton(values.menuType),
    },
    {
      field: 'path',
      label: '路由地址(权限标识)',
      helpMessage: '不能相同且以斜杠开头',
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: true,
    },
    {
      field: 'component',
      label: '组件路径',
      rulesMessageJoinLabel: true,
      helpMessage: '不能相同(LAYOUT_TAB_PAGE，IFRAME，OUTPAGE，自定义的)',
      component: 'Input',
      required: true,
      defaultValue: 'LAYOUT_TAB_PAGE',
      ifShow: ({ values }) => ['MENU', 'CATALOGUE'].includes(values.menuType),
    },
    {
      field: 'component',
      label: '组件路径',
      rulesMessageJoinLabel: true,
      helpMessage: '不能相同(LAYOUT_TAB_PAGE，IFRAME，OUTPAGE，自定义的)',
      component: 'Input',
      defaultValue: 'LAYOUT_TAB_PAGE',
      ifShow: ({ values }) => ['BUTTON'].includes(values.menuType),
      required: false,
    },
    {
      field: 'redirect',
      label: '重定向路由',
      rulesMessageJoinLabel: true,
      helpMessage: '请填写现有的路由菜单',
      component: 'Input',
    },
    {
      field: 'state',
      label: '状态',
      component: 'RadioButtonGroup',
      defaultValue: 'NORMAL',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('commonStatus') as RadioGroupChildOption[],
      },
    },
    // {
    //   field: 'isExt',
    //   label: '是否外链',
    //   component: 'RadioButtonGroup',
    //   defaultValue: '0',
    //   componentProps: {
    //     options: [
    //       { label: '否', value: '0' },
    //       { label: '是', value: '1' },
    //     ],
    //   },
    //   ifShow: ({ values }) => !isButton(values.menuType),
    // },

    {
      field: 'hidden',
      label: '是否显示',
      component: 'RadioButtonGroup',
      defaultValue: false,
      componentProps: {
        options: [
          { label: '是', value: false },
          { label: '否', value: true },
        ],
      },
    },
    {
      field: 'hiddenChildren',
      label: '是否显示下级',
      component: 'RadioButtonGroup',
      defaultValue: false,
      componentProps: {
        options: [
          { label: '是', value: false },
          { label: '否', value: true },
        ],
      },
    },
    {
      field: 'takeToken',
      label: '是否携带token',
      component: 'RadioButtonGroup',
      defaultValue: 'n',
      componentProps: {
        options: [
          { label: '是', value: 'y' },
          { label: '否', value: 'n' },
        ],
      },
    },
    {
      field: 'frameSrc',
      label: '内嵌地址',
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'menuDesc',
      label: '菜单介绍',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 24, lg: 24 },
      ifShow: ({ values }) => isDir(values.menuType),
      render({ model, field, disabled }) {
        return (
          <Tinymce
            value={model[field]}
            options={{ readonly: !!disabled }}
            onChange={value => {
              model[field] = value;
            }}
          />
        );
      },
    },
  ];
};

export const modalFormCopy = (): FormSchema[] => {
  return [
    {
      field: 'title',
      label: '菜单名称',
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'path',
      label: '路由地址(权限标识)',
      helpMessage: '不能相同且以斜杠开头',
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'component',
      label: '组件路径',
      rulesMessageJoinLabel: true,
      helpMessage: '不能相同(LAYOUT_TAB_PAGE，IFRAME，OUTPAGE，自定义的)',
      component: 'Input',
      colProps: { span: 12 },
    },
    {
      field: 'orderNum',
      label: '排序',
      rulesMessageJoinLabel: true,
      component: 'InputNumber',
      className: '!w-full',
      colProps: { span: 12 },
    },
  ];
};
