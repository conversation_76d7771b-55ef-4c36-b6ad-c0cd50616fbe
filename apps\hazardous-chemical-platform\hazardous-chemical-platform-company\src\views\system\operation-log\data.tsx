import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import dayjs from 'dayjs';

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '操作标题',
      dataIndex: 'title',
    },
    {
      title: '请求uri',
      dataIndex: 'uri',
    },
    {
      title: '操作账号',
      dataIndex: 'account',
    },
    {
      title: '操作人',
      dataIndex: 'nickname',
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'title',
      label: '操作标题',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'account',
      label: '操作账号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'queryTime',
      label: '操作时间',
      component: 'RangePicker',
      colProps: { span: 8 },
      defaultValue: [
        dayjs(dayjs().startOf('month').hour(0).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
        dayjs(dayjs().endOf('month').hour(23).minute(59).second(59).format('YYYY-MM-DD HH:mm:ss')),
      ],
      componentProps: {
        showTime: {
          hideDisabledOptions: true,
          defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
        },
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DDTHH:mm:ss',
        allowClear: false,
      },
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'title',
      label: '操作标题',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'uri',
      label: '请求uri',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'account',
      label: '操作账号',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'nickname',
      label: '操作人姓名',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'createTime',
      label: '操作时间',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
      },
    },
    {
      field: 'requestParams',
      label: '请求params',
      component: 'InputTextArea',
      colProps: { span: 24 },
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 30,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    {
      field: 'requestBody',
      label: '请求body',
      component: 'InputTextArea',
      colProps: { span: 24 },
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 30,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    {
      field: 'resultCode',
      label: '响应code',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'resultMsg',
      label: '响应结果描述',
      component: 'InputTextArea',
      colProps: { span: 24 },
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 30,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
  ];
};
