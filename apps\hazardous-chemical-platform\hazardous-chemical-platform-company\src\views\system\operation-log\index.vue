<template>
  <div class="w-full h-full">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <OperationLogModel
      @register="registerModal"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import OperationLogModel from './operationLogModel.vue';
import { operationLogList } from '@/api/system/log';

const [registerTable] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: operationLogList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
    actionColOptions: { span: 3 },
  },
  beforeFetch: params => {
    return params;
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal }] = useModal();

//详情
function handleView(record) {
  const { requestParams, requestBody } = record;
  openModal(true, {
    isUpdate: true,
    disabled: true,
    record: {
      ...record,
      requestParams: requestParams ? JSON.stringify(requestParams) : '--',
      requestBody: requestBody ? JSON.stringify(requestBody) : '--',
    },
  });
}
</script>
