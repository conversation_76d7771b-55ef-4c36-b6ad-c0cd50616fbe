<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <!-- auth="/management/add" -->
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增角色
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
              },
              // {
              //   icon: 'fluent:delete-16-filled',
              //   label: '删除',
              //   type: 'primary',
              //   ifShow: record.createType !== CreateType.SYS,
              //   danger: true,
              //   onClick: handleDelete.bind(null, record),
              // },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <RoleModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import RoleModal from './RoleModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { list, deleteLine, save, update, findRolePermissionList } from '@/api/system/role';
import { useUserStore } from '@/store/modules/user';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const userStore = useUserStore();

const [registerTable, { reload }] = useTable({
  rowKey: 'roleId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  beforeFetch(params) {
    params.companyId = params.companyId ? params.companyId : userStore.getUserInfo.companyId;
    return params;
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    width: 220,
    title: '操作',
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record: Recordable<any>) {
  findRolePermissionList({ roleId: record.roleId }).then(data => {
    openModal(true, { isUpdate: true, disabled: false, record: { ...record, ...data } });
  });
}

//详情
function handleView(record: Recordable<any>) {
  findRolePermissionList({ roleId: record.roleId }).then(data => {
    openModal(true, { isUpdate: true, disabled: true, record: { ...record, ...data } });
  });
}

// 删除
function handleDelete(record: Recordable<any>) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除角色名称:【${record.roleName}】的数据？`,
    onOk: function () {
      deleteLine({ todoList: [record.roleId] }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功！` });
          reload();
        } else {
          createErrorModal({ content: `删除失败！${message}` });
        }
      });
    },
  });
}

function handleSuccess({ values, isUpdate }: Recordable<any>) {
  const api = isUpdate ? update : save;

  api({ ...values }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({ content: `${isUpdate ? '修改' : '新增'}成功！` });
      reload();
      closeModal();
    } else {
      createErrorModal({ content: `${isUpdate ? '修改' : '新增'}失败！${message}。` });
    }
  });
}
</script>
