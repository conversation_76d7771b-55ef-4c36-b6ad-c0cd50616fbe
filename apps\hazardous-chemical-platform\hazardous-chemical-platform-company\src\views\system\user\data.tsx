import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { AccountTypeEnum, CreateType } from '@monorepo-yysz/enums';
import { Switch, Tag } from 'ant-design-vue';
import { accountEnableOrDisable } from '@/api/system/user';
import { useMessage } from '@monorepo-yysz/hooks';
import { includes, map } from 'lodash-es';
import { list } from '@/api/system/dept';
import { list as companyList } from '@/api/system/company';
import { list as roleList } from '@/api/system/role';
import { validatePhone } from '@monorepo-yysz/utils';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '帐号',
      dataIndex: 'account',
    },
    {
      title: '所属企业',
      dataIndex: 'companyName',
    },
    {
      dataIndex: 'deptNameList',
      title: '所属部门',
      customRender({ record }) {
        return (
          <div>
            {map(record?.deptNameList || [], v => (
              <div className={`inline-block p-1`}>
                <Tag>{v}</Tag>
              </div>
            ))}
          </div>
        );
      },
    },
    {
      title: '帐号类型',
      dataIndex: 'accountType',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`accountType_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
    },
    {
      dataIndex: 'accountState',
      title: '状态',
      customRender: ({ value, record }) => {
        const { createConfirm, createSuccessModal, createErrorModal } = useMessage();
        const name = dictionary.getDictionaryMap.get(`commonStatus_${value}`)?.dictName;
        const color = dictionary.getDictionaryMap.get(`commonStatus_${value}`)?.remark;

        const flg = value === 'NORMAL';
        const agree = userStore.getUserInfo.account === record.account;
        const el = (
          <div>
            <Switch
              checked-children={name}
              unCheckedChildren={name}
              disabled={'ADMIN' === record.accountType}
              checked={flg}
              class={`${agree ? '' : 'cursor-pointer'} `}
              style={{ backgroundColor: color }}
              onClick={() => {
                if (agree) {
                  return false;
                }
                const stateName = value === 'NORMAL' ? '禁用' : '启用';
                const accountState = value === 'NORMAL' ? 'BAN' : 'NORMAL';
                const text = `是否${stateName}${record.account}`;

                createConfirm({
                  iconType: 'warning',
                  content: text,
                  onOk: () => {
                    accountEnableOrDisable({
                      account: record.account,
                      operateState: accountState === 'NORMAL',
                    }).then(({ code, message }) => {
                      if (code === 200) {
                        createSuccessModal({ content: `${stateName}成功` });
                        record.accountState = accountState;
                      } else {
                        createErrorModal({ content: `${stateName}失败，${message}` });
                      }
                    });
                  },
                });
              }}
            >
              {name}
            </Switch>
          </div>
        );
        return el;
      },
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'account',
      label: '帐号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const modalFormItem = (isUpdate: boolean): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: 'accountType',
      label: '帐号类型',
      colProps: { span: 24 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      show: userStore.getUserInfo.accountType === AccountTypeEnum.ADMIN,
      defaultValue:
        userStore.getUserInfo.accountType === AccountTypeEnum.ADMIN
          ? AccountTypeEnum.MANAGE
          : AccountTypeEnum.NORMAL,
      componentProps: {
        options: (
          dictionary.getDictionaryOpt.get('accountType') as RadioGroupChildOption[]
        )?.filter(item => item.value !== 'ADMIN'),
      },
    },
    {
      field: 'account',
      label: '帐号',
      colProps: { span: 24 },
      component: 'Input',
      // required: true,
      rules: [{ required: true, validator: validatePhone }],
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return values.accountType === AccountTypeEnum.NORMAL;
      },
    },
    {
      field: 'account',
      label: '帐号',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return values.accountType !== AccountTypeEnum.NORMAL;
      },
    },
    {
      field: 'pwd',
      label: '密码',
      // required: true,
      required: ({ values }) => {
        return values.accountType !== AccountTypeEnum.NORMAL;
      },
      colProps: { span: 24 },
      component: 'InputPassword',
      ifShow: !isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'nickname',
      label: '昵称',
      required: true,
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      required: true,
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },

    {
      field: 'companyId',
      label: '所属企业',
      colProps: { span: 24 },
      component: 'ApiTreeSelect',
      required: true,
      show: ({ values }) =>
        includes([AccountTypeEnum.MANAGE, AccountTypeEnum.NORMAL], values.accountType),
      ifShow: userStore.getUserInfo.accountType === AccountTypeEnum.ADMIN,
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          api: companyList,
          labelField: 'companyName',
          valueField: 'companyId',
          resultField: 'data',
          params: { pageSize: 0 },
          showSearch: true,
          treeDefaultExpandAll: true,
          filterTreeNode(input: string, option: any) {
            return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          getPopupContainer: () => document.body,
          onChange: () => {
            formModel['roleIdList'] = undefined;
            formModel['deptIdList'] = undefined;
          },
        };
      },
    },
    {
      field: 'deptIdList',
      label: '所属部门',
      colProps: { span: 24 },
      component: 'ApiTreeSelect',
      ifShow: ({ values }) =>
        includes([AccountTypeEnum.MANAGE, AccountTypeEnum.NORMAL], values.accountType),
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          api: list,
          labelField: 'deptName',
          valueField: 'deptId',
          resultField: 'data',
          params: {
            companyId: formModel['companyId']
              ? formModel['companyId']
              : userStore.getUserInfo.companyId,
          },
          multiple: true,
          immediate: userStore.getUserInfo.accountType !== AccountTypeEnum.ADMIN,
          alwaysLoad: true,
          showSearch: true,
          treeDefaultExpandAll: true,
          filterTreeNode(input: string, option: any) {
            return option.deptName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          getPopupContainer: () => document.body,
        };
      },
    },
    {
      field: 'roleIdList',
      label: '相关角色',
      colProps: { span: 24 },
      component: 'ApiTreeSelect',
      required: false,
      ifShow: ({ values }) =>
        includes([AccountTypeEnum.MANAGE, AccountTypeEnum.NORMAL], values.accountType),
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          api: roleList,
          labelField: 'roleName',
          valueField: 'roleId',
          resultField: 'data',
          params: {
            companyId: formModel['companyId']
              ? formModel['companyId']
              : userStore.getUserInfo.companyId,
            pageSize: 0,
            createType: CreateType.CUSTOM,
          },
          immediate: userStore.getUserInfo.accountType !== AccountTypeEnum.ADMIN,
          multiple: true,
          alwaysLoad: true,
          showSearch: true,
          treeDefaultExpandAll: true,
          filterTreeNode(input: string, option: any) {
            return option.roleName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          // getPopupContainer: () => document.body,
        };
      },
    },
  ];
};
