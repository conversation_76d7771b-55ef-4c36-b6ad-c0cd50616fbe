<template>
  <div
    :class="$style['user-info']"
    class="flex w-full h-full"
  >
    <div class="w-1/5">
      <CompanyTree
        @select-info="handleSelect"
        @get-first-node="handleSelect"
        :ifSelected="true"
      />
    </div>
    <div :class="`w-4/5`">
      <BasicTable @register="registerTable">
        <template #toolbar>
          <a-button
            type="primary"
            @click="handleClick"
          >
            新增帐号
          </a-button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  icon: 'carbon:task-view',
                  label: '详情',
                  type: 'default',
                  onClick: handleView.bind(null, record),
                },
                {
                  icon: 'fa6-solid:pen-to-square',
                  label: '编辑',
                  type: 'primary',
                  ifShow: ifRoot(record) && record.account !== userStore.getUserInfo.account,
                  onClick: handleEdit.bind(null, record),
                },
                {
                  icon: 'carbon:password',
                  label: '重置密码',
                  type: 'primary',
                  ifShow: ifResetPwd && record.account !== userStore.getUserInfo.account,
                  onClick: handlePwd.bind(null, record),
                },
                // {
                //   icon: 'fluent:delete-16-filled',
                //   label: '删除',
                //   type: 'primary',
                //   danger: true,
                //   ifShow: ifRoot(record) && record.account !== userStore.getUserInfo.account,
                //   onClick: handleDelete.bind(null, record),
                // },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>

    <UserModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
    <ModifyPwd
      @register="registerPwd"
      width="40%"
      @success="handlePwdSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import UserModal from './UserModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  list,
  view,
  changePassword,
  deleteLine,
  saveApi,
  updateApi,
  getDepListByAccount,
  getRoleListByAccount,
} from '@/api/system/user';
import { useUserStore } from '@/store/modules/user';
import { AccountTypeEnum } from '@monorepo-yysz/enums';
import ModifyPwd from '@/views/components/ModifyPwd/index.vue';
import { computed, ref, unref } from 'vue';
import CompanyTree from '@/views/components/CompanyTree/index.vue';
import { includes, map } from 'lodash-es';

const userStore = useUserStore();

const companyName = ref<string>();

const companyId = ref<string>();

const ifResetPwd = computed(() => {
  return includes(
    [AccountTypeEnum.ADMIN, AccountTypeEnum.MANAGE],
    userStore.getUserInfo.accountType
  );
});

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  beforeFetch(params) {
    params.companyId = unref(ifAdmin) ? unref(companyId) : userStore.getUserInfo.companyId;
    return params;
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 250,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

const ifRoot = (item: Recordable) => item.accountType !== AccountTypeEnum.ADMIN;

const [registerPwd, { openModal: openPwd, closeModal: closePwd }] = useModal();

function handleSelect({ id, name }: Recordable) {
  companyName.value = name;
  companyId.value = id;
  reload();
}

// 重置密码
function handlePwd(record: any) {
  openPwd(true, { record });
}

// 新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

function initView(isUpdate, disabled, lineRecord) {
  Promise.all([
    view({ account: lineRecord.account }),
    getDepListByAccount({ account: lineRecord.account }),
    getRoleListByAccount({ account: lineRecord.account }),
  ]).then(([viewInfo, deptIdList, roleIdList]) => {
    const record = {
      ...((viewInfo?.data as Recordable) || {}),
      deptIdList: map(deptIdList || [], v => v.deptId),
      roleIdList: map(roleIdList || [], v => v.roleId),
    };

    openModal(true, { isUpdate, disabled, record });
  });
}

// 编辑
function handleEdit(record: Recordable<any>) {
  initView(true, false, record);
}

// 详情
function handleView(record: Recordable<any>) {
  initView(true, true, record);
}

// 删除
function handleDelete(record: Recordable<any>) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.account || '当前数据'}？`,
    onOk: function () {
      deleteLine(record.account).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功！` });
          reload();
        } else {
          createErrorModal({ content: `删除失败！${message}。` });
        }
      });
    },
  });
}

// 新增修改
function handleSuccess({ values, isUpdate }: Recordable<any>) {
  const api = isUpdate ? updateApi : saveApi;
  api(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败！${message}。`,
      });
    }
  });
}

// 修改密码
function handlePwdSuccess({ values }) {
  changePassword({
    account: values.account,
    pwd: values.pwd,
  }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({ content: `修改成功！` });
      reload();
      closePwd();
    } else {
      createErrorModal({ content: `修改失败！${message}。` });
    }
  });
}
</script>

<style lang="less" module>
.user-info {
  :global {
    .ant-upload-list {
      display: none;
    }

    .ant-form {
      padding: 0 6px 0 6px;
      @apply px-6px;
    }

    .ant-page-header {
      padding: 0 0 0 36px;
    }
  }
}
</style>
