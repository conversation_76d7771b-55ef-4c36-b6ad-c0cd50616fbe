import { FormSchema } from '@/components/Form';
import { useDictionary } from '@/store/modules/dictionary';

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();

  return [
    {
      field: 'test',
      label: '测试',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'accountType',
      label: '帐号类型',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('accountType'),
          mode: 'multiple',
        };
      },
    },
    {
      field: 'accountType',
      label: '帐号类型',
      required: true,
      colProps: { span: 12 },
      component: 'TreeSelect',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          treeData: dictionary.getDictionaryOpt.get('accountType'),
          multiple: true,
        };
      },
    },
    {
      field: 'accountType',
      label: '帐号类型',
      required: true,
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('accountType'),
        };
      },
    },
    {
      field: 'accountType',
      label: '帐号类型',
      required: true,
      colProps: { span: 12 },
      component: 'Checkbox',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('accountType'),
        };
      },
    },
    {
      field: 'accountType',
      label: '帐号类型',
      required: true,
      colProps: { span: 12 },
      component: 'CheckboxGroup',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('accountType'),
        };
      },
    },
    {
      field: 'accountType',
      label: '时间',
      required: true,
      colProps: { span: 12 },
      component: 'DatePicker',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {};
      },
    },
    {
      field: 'accountType',
      label: '时间',
      required: true,
      colProps: { span: 12 },
      component: 'RangePicker',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          showTime: true,
        };
      },
    },
    {
      field: 'accountType',
      label: '时间',
      required: true,
      colProps: { span: 12 },
      component: 'TimePicker',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          showTime: true,
        };
      },
    },
  ];
};
