<template>
  <div class="w-full h-full">
    <a-divider>列表</a-divider>
    <BasicTable @register="registerTable"></BasicTable>
    <a-divider>弹窗</a-divider>
    <a-button
      type="primary"
      @click="openModal(true)"
      >弹窗</a-button
    >
    <BasicModal @register="registerModal" />
    <a-divider>表单</a-divider>
    <BasicForm @register="registerForm"> </BasicForm>
    <a-divider>通知提示</a-divider>
    <a-button
      type="primary"
      @click="openNotification"
      >通知提示</a-button
    >
    <a-divider>message</a-divider>

    <context-holder />
    <a-button
      type="primary"
      @click="info"
      >提示</a-button
    >
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable } from '@/components/Table';
import { columns, formSchemas } from '@/views/system/company/data';
import { BasicModal, useModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { computed, unref } from 'vue';
import { modalFormItem } from './data';
import { notification, message } from 'ant-design-vue';

const [messageApi, contextHolder] = message.useMessage();

const info = () => {
  messageApi.info('Hello, Ant Design Vue!', 101010);
};

const openNotification = () => {
  notification.open({
    message: 'Notification Title',
    description:
      'I will never close automatically. I will be close automatically. I will never close automatically.',
    duration: 0,
  });
};

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 250,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal }] = useModal();

const form = computed(() => {
  return modalFormItem();
});
const [registerForm, {}] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});
</script>
