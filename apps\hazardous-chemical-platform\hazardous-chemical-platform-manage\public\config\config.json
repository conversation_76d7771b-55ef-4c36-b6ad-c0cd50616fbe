﻿{
  "map3d": {
    "scene": {
      "center": {
        "lat": 30.572352,
        "lng": 104.0771,
        "alt": 15800,
        "heading": 268.2,
        "pitch": -9.5
      },
      "scene3DOnly": true,
      "shadows": false,
      "removeDblClick": true,
      "sceneMode": 3,
      "showSun": false,
      "showMoon": false,
      "showSkyBox": true,
      "showSkyAtmosphere": true,
      "fog": false,
      "fxaa": true,
      "requestRenderMode": true,
      "globe": {
        "depthTestAgainstTerrain": false,
        "baseColor": "#546a53",
        "showGroundAtmosphere": false,
        "enableLighting": false
      },
      "contextOptions": {
        "webgl": {
          "preserveDrawingBuffer": true
        }
      },
      "clock": {
        "currentTime": "2024-05-01 12:00:00"
      },
      "cameraController": {
        "zoomFactor": 5.0,
        "minimumZoomDistance": 1,
        "maximumZoomDistance": 50000000,
        "enableRotate": true,
        "enableTranslate": true,
        "enableTilt": true,
        "enableZoom": true,
        "enableCollisionDetection": true,
        "minimumCollisionTerrainHeight": 15000
      }
    },
    "mouse": {
      "pickLimit": 99
    },
    "control": {
      "homeButton": false,
      "baseLayerPicker": false,
      "sceneModePicker": false,
      "vrButton": false,
      "fullscreenButton": false,
      "navigationHelpButton": false,
      "animation": false,
      "timeline": false,
      "infoBox": false,
      "geocoder": false,
      "selectionIndicator": false,
      "showRenderLoopErrors": true,
      "contextmenu": { "hasDefault": true },
      "mouseDownView": true,
      "zoom": { "enabled": false, "insertIndex": 1 },
      "compass": { "enabled": false, "bottom": "toolbar", "left": "5px" },
      "distanceLegend": { "left": "100px", "bottom": "2px" },
      "locationBar": {
        "crs": "CGCS2000_GK_Zone_3",
        "crsDecimal": 0,
        "template": "<div>经度:{lng}</div> <div>纬度:{lat}</div> <div class='hide1000'>横{crsx}  纵{crsy}</div> <div>海拔：{alt}米</div> <div class='hide700'>层级：{level}</div><div>方向：{heading}°</div> <div>俯仰角：{pitch}°</div><div class='hide700'>视高：{cameraHeight}米</div>"
      }
    },
    "templateValues": {
      "dataServer": "//data.mars3d.cn",
      "gltfServerUrl": "//data.mars3d.cn/gltf"
    },
    "terrain": {
      "url": "//data.mars3d.cn/terrain",
      "show": true,
      "clip": true
    },
    "basemaps": [
      { "id": 10, "name": "地图底图", "type": "group" },
      {
        "pid": 10,
        "name": "天地图影像",
        "icon": "img/basemaps/tdt_img.png",
        "type": "group",
        "layers": [
          { "name": "底图", "type": "tdt", "layer": "img_d" },
          { "name": "注记", "type": "tdt", "layer": "img_z" }
        ],
        "show": true
      }
    ],
    "layers": []
  }
}
