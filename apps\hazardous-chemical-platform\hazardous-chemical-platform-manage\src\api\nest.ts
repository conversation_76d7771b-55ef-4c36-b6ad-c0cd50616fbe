import { BasicResponse } from '@monorepo-yysz/types';
import { nestHttp } from '/@/utils/http/axios';

// 列表
export const nestHello = () => {
  return nestHttp.get<BasicResponse>(
    { url: '/nest-hello/hello-world' },
    {
      isTransformResponse: false,
    }
  );
};

export const nestHello2 = () => {
  return nestHttp.get<BasicResponse>(
    { url: '/nest-hello/hello-world2' },
    {
      isTransformResponse: false,
    }
  );
};
