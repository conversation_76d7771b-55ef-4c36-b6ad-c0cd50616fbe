import { dataCenterHttp, defHttp } from '@/utils/http/axios';
import { CompanyInfo } from '#/store';
import { BasicResponse } from '@monorepo-yysz/types';

enum OBJ {
  findList = '/companyFindList',
  view = '/getCompanyInfo',
  saveOrUpdate = '/initCompanyInfo',
  updateCompanyInfo = '/updateCompanyInfo',
  companyEnableOrDisable = '/companyEnableOrDisable',
}

function getApi(url?: string) {
  if (!url) {
    return '/sysAuthAbout';
  }
  return '/sysAuthAbout' + url;
}

// 列表
export const list = (params: Recordable) => {
  return dataCenterHttp.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    }
  );
};
// 列表
export const companyList = (params: Recordable) => {
  return dataCenterHttp.get<Recordable[]>({ url: getApi(OBJ.findList), params });
};

// 新增
export const saveApi = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//修改
export const updateApi = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.updateCompanyInfo),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const companyEnableOrDisable = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.companyEnableOrDisable),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//view
export const view = (params: Recordable) => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const companyInfoApi = (params: Recordable) => {
  return dataCenterHttp.get<CompanyInfo>({
    url: getApi(OBJ.view),
    params,
  });
};

//删除
export const deleteLine = (autoId: number[] | number) => {
  return defHttp.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};
