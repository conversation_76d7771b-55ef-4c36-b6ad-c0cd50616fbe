import { dataCenterHttp } from '@/utils/http/axios';

export interface Dictionary {
  groupCode?: string;
  pageSize?: 0;
}

export interface DictionaryModal {
  autoId?: number;
  groupCode?: string;
  groupName?: string;
  dictCode?: string;
  dictName?: string;
  dictColor?: string;
  remark?: string;
}

export const queryDictionary = (params?: Dictionary) => {
  return dataCenterHttp.get<DictionaryModal[]>({
    url: '/sysCommon/dictionaryList',
    params: {
      ...params,
      pageSize: 0,
    },
  });
};
