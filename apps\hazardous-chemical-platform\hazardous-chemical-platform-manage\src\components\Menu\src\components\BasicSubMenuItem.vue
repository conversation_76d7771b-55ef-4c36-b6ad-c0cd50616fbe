<template>
  <BasicMenuItem
    v-if="!menuHasChildren(item) && getShowMenu"
    v-bind="$props"
  />
  <Menu.SubMenu
    v-if="menuHasChildren(item) && getShowMenu"
    :class="[theme]"
    :key="`submenu-${item.path}`"
    popupClassName="app-top-menu-popup"
    @title-click="handleClick"
  >
    <template #title>
      <MenuItemContent
        v-bind="$props"
        :item="item"
      />
    </template>

    <template
      v-for="childrenItem in item.children || []"
      :key="childrenItem.path"
    >
      <BasicSubMenuItem
        v-bind="$props"
        :item="childrenItem"
      />
    </template>
  </Menu.SubMenu>
</template>
<script lang="ts" setup>
import type { Menu as MenuType } from '@/router/types';
import { computed } from 'vue';
import { Menu } from 'ant-design-vue';
import { itemProps } from '../props';
import BasicMenuItem from './BasicMenuItem.vue';
import MenuItemContent from './MenuItemContent.vue';
import { Persistent } from '@/utils/cache/persistent';
import { useRouter } from 'vue-router';
import { MENUTYPE, COMMON_RECORD } from '@monorepo-yysz/enums';

defineOptions({ name: 'BasicSubMenuItem', isSubMenu: true });

const props = defineProps(itemProps);

const router = useRouter();

const getShowMenu = computed(() => !props.item.meta?.hideMenu);
function menuHasChildren(menuTreeItem: MenuType): boolean {
  return (
    !menuTreeItem.meta?.hideChildrenInMenu &&
    Reflect.has(menuTreeItem, 'children') &&
    !!menuTreeItem.children &&
    menuTreeItem.children.length > 0
  );
}

function handleClick() {
  //将主菜单介绍
  if (props.item?.menuDesc && props.item?.menuType === MENUTYPE.MENU) {
    Persistent.setLocal(COMMON_RECORD, props.item);
    router.push({ path: props.item.path });
  }
}
</script>
