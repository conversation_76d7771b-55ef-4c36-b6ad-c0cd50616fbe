// Any plugins you want to setting has to be imported
// Detail plugins list see https://www.tinymce.com/docs/plugins/
// Custom builds see https://www.tinymce.com/download/custom-builds/
// colorpicker/contextmenu/textcolor plugin is now built in to the core editor, please remove it from your editor configuration

export const plugins = [
  'advlist anchor autolink autosave directionality fullscreen hr insertdatetime link lists media nonbreaking noneditable pagebreak paste preview print save searchreplace  tabfocus  template  textpattern visualblocks visualchars wordcount image formatpainter upfile code codesample indent2em ', //indent2em tpIndent2em
];

export const toolbar = [
  'fontsizeselect | fontselect | lineheight indent2em removeformat formatpainter alignleft aligncenter alignright alignjustify outdent indent | blockquote undo redo subscript superscript',
  'searchreplace bold italic underline strikethrough hr bullist numlist link preview anchor pagebreak insertdatetime media forecolor backcolor image upfile code ', //code indent2em tpIndent2em
];

export const fontFamilyFormats =
  "宋体='宋体';仿宋='仿宋';微软雅黑='微软雅黑';楷体='楷体';隶书='隶书';幼圆='幼圆';无衬线体=sans serif;Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings";

export const fileTypes = '.mp3, .mp4, .webm, .ogg, .wav, .doc, .docx, .xls, .xlsx, .pdf';

export const fileType = '.doc, .docx, .xls, .xlsx, .pdf';
