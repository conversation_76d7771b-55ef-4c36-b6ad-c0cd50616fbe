<template>
  <Dropdown
    placement="bottomLeft"
    :overlayClassName="`${prefixCls}-dropdown-overlay`"
  >
    <span
      :class="[prefixCls, `${prefixCls}--${theme}`]"
      class="flex"
    >
      <img
        :class="`${prefixCls}__header`"
        :src="getUserInfo.avatar"
      />
      <span :class="`${prefixCls}__info md:block`">
        <span
          :class="`${prefixCls}__name`"
          class="truncate"
        >
          您好{{ getUserInfo.account || '' }}
        </span>
      </span>
      <Icon
        icon="ion:caret-down"
        class="text-[#00DFFF] ml-1"
      />
    </span>

    <template #overlay>
      <Menu @click="handleMenuClick">
        <MenuItem
          key="doc"
          :text="t('layout.header.dropdownItemDoc')"
          icon="ion:document-text-outline"
          v-if="getShowDoc"
        />
        <Menu.Divider v-if="getShowDoc" />
        <MenuItem
          v-if="getShowApi"
          key="api"
          :text="t('layout.header.dropdownChangeApi')"
          icon="ant-design:swap-outlined"
        />
        <MenuItem
          v-if="getUseLockPage"
          key="lock"
          :text="t('layout.header.tooltipLock')"
          icon="ion:lock-closed-outline"
        />
        <MenuItem
          key="logout"
          :text="t('layout.header.dropdownItemLoginOut')"
          icon="ion:power-outline"
        />
      </Menu>
    </template>
  </Dropdown>
  <LockAction @register="register" />
  <ChangeApi @register="registerApi" />
</template>
<script lang="ts" setup>
import { Dropdown, Menu } from 'ant-design-vue';
import type { MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
import { computed } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { useHeaderSetting } from '@/hooks/setting/useHeaderSetting';
import { useI18n } from '@/hooks/web/useI18n';
import { useDesign } from '@monorepo-yysz/hooks';
import { useModal } from '@/components/Modal';
import headerImg from '@/assets/images/pic.png';
import { propTypes } from '@monorepo-yysz/utils';
import { createAsyncComponent } from '@monorepo-yysz/ui';
import { Icon } from '@monorepo-yysz/ui';

type MenuEvent = 'logout' | 'lock' | 'api';

const MenuItem = createAsyncComponent(() => import('./DropMenuItem.vue'));
const LockAction = createAsyncComponent(() => import('../lock/LockModal.vue'));
const ChangeApi = createAsyncComponent(() => import('../ChangeApi/index.vue'));

defineOptions({ name: 'UserDropdown' });

defineProps({
  theme: propTypes.oneOf(['dark', 'light']),
});

const { prefixCls } = useDesign('header-user-dropdown');
const { t } = useI18n();
const { getShowDoc, getUseLockPage, getShowApi } = useHeaderSetting();
const userStore = useUserStore();

const getUserInfo = computed(() => {
  const { account = '', avatar, desc } = userStore.getUserInfo || {};
  return { account, avatar: avatar || headerImg, desc };
});

const [register, { openModal }] = useModal();
const [registerApi, { openModal: openApiModal }] = useModal();

function handleLock() {
  openModal(true);
}

function handleApi() {
  openApiModal(true, {});
}

//  login out
function handleLoginOut() {
  userStore.confirmLoginOut();
}

function handleMenuClick(e: MenuInfo) {
  switch (e.key as MenuEvent) {
    case 'logout':
      handleLoginOut();
      break;
    case 'lock':
      handleLock();
      break;
    case 'api':
      handleApi();
      break;
  }
}
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-header-user-dropdown';

.@{prefix-cls} {
  align-items: center;
  height: @header-height;
  padding: 0 0 0 10px;
  padding-right: 10px;
  overflow: hidden;
  font-size: 12px;
  cursor: pointer;

  img {
    width: 24px;
    height: 24px;
    margin-right: 12px;
  }

  &__header {
    border-radius: 50%;
  }

  &__name {
    font-size: 14px;
  }

  &--dark {
    &:hover {
      background-color: @header-dark-bg-hover-color;
    }
  }

  &--light {
    &:hover {
      background-color: @header-light-bg-hover-color;
    }

    .@{prefix-cls}__name {
      color: @text-color-base;
    }

    .@{prefix-cls}__desc {
      color: @header-light-desc-color;
    }
  }

  &-dropdown-overlay {
    .ant-dropdown-menu-item {
      min-width: 160px;
    }
  }
}
</style>
