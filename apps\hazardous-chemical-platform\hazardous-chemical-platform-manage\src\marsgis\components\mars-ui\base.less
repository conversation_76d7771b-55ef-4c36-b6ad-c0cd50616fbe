// 提示组件背景
.mars-msg-bg {
  background: var(--mars-msg-content-bg);
  background-size: 100% 100%;
  color: var(--mars-msg-title-color);
}

// 提示组件标题样式
.mars-msg-title {
  height: 41px;
  padding-left: 13px;
  background: var(--mars-msg-title-bg);
  background-size: 10px 44px;
  color: var(--mars-alert-title-color);
  font-size: 18px;
  line-height: 41px;

  &::before {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 50px;
    height: 35px;
    border-top: 1px solid var(--mars-primary-color);
    border-left: 1px solid var(--mars-primary-color);
  }

  &::after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    width: 70px;
    border-top: 1px solid var(--mars-primary-color);
  }
}

// 下拉样式
.mars-drop-bg {
  border-right: 1px solid #008aff70;
  border-bottom: 1px solid #008aff70;
  border-left: 1px solid #008aff70;
  border-radius: 0;
  background:
    linear-gradient(to left, var(--mars-base-color), var(--mars-base-color)) left bottom no-repeat,
    linear-gradient(to bottom, var(--mars-base-color), var(--mars-base-color)) left bottom no-repeat,
    linear-gradient(to left, var(--mars-base-color), var(--mars-base-color)) right bottom no-repeat,
    linear-gradient(to left, var(--mars-base-color), var(--mars-base-color)) right bottom no-repeat;
  background-color: var(--mars-bg-base);
  background-size:
    1px 10px,
    10px 1px,
    1px 10px,
    10px 1px;
  box-shadow: 0 4px 15px 1px rgb(2 33 59 / 70%);
}

.mars-hover-bg {
  // background: var(--mars-title-active);
  background-color: var(--mars-select-bg);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mars-main-view {
  color: var(--mars-text-color);

  // collapse 折叠面板相关样式
  .ant-collapse,
  .ant-collapse-item,
  .ant-collapse-content {
    border: none;
    background: none !important;
  }

  .ant-collapse-content-box {
    padding: 5px 5px 10px;
  }

  .ant-collapse-header {
    margin-bottom: 10px;
    padding: 5px 15px !important;
    background: var(--mars-collapse-title-bg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    color: #fff !important;
    font-size: 18px;
  }

  .ant-collapse-content {
    color: var(--mars-base-color) !important;
  }

  .ant-collapse-extra {
    .mars-icon {
      font-size: 16px;
      line-height: 16px;
      vertical-align: middle;
    }
  }

  // 切换按钮
  .ant-radio-button-wrapper {
    background: rgb(32 160 255 / 20%);
    box-shadow: none !important;
    color: #fff;
  }

  // 选中按钮
  .ant-radio-button-checked {
    background-color: #1890ff;
  }

  // 表单元素
  .ant-form {
    color: rgb(255 255 255 / 92.5%);

    .ant-form-item-extra {
      color: var(--mars-text-color);
    }
  }

  .ant-form-item {
    margin-bottom: 10px !important;
    color: var(--mars-text-color);

    .ant-form-item-label > label {
      color: var(--mars-sub-title-color);
      font-size: 16px;
    }
  }

  .ant-form-item:nth-last-child(1) {
    margin-bottom: 0 !important;
  }

  /* 卡片 */
  .mars-ant-card {
    border: 1px solid var(--mars-base-border-color);
    background: none;
    color: var(--mars-text-color);

    .ant-card-head {
      min-height: auto;
      padding: 0 10px;
      border-color: var(--mars-base-border-color);
      color: var(--mars-text-color);

      .ant-card-head-title {
        padding: 8px 0;
      }
    }

    .ant-card-body {
      padding: 10px;
    }
  }

  // 文件选择
  .ant-upload {
    color: var(--mars-text-color);
  }

  // 多选
  .ant-checkbox-wrapper {
    color: var(--mars-text-color);

    .ant-checkbox-inner {
      border-color: var(--mars-base-border-color);
      background-color: var(--mars-bg-base);
    }

    .ant-checkbox-checked {
      background: var(--mars-primary-color);

      .ant-checkbox-inner {
        border-color: var(--mars-primary-color);
        background-color: var(--mars-primary-color);
      }
    }
  }

  // 单选
  .ant-radio-wrapper {
    color: var(--mars-text-color);

    .ant-radio-inner {
      border-color: var(--mars-base-border-color);
      outline: none !important;
      background-color: var(--mars-bg-base);
      box-shadow: none !important;
    }

    .ant-radio-checked {
      .ant-radio-inner {
        border-color: var(--mars-primary-color);
      }
    }

    .ant-radio-input {
      outline: none !important;
      box-shadow: none !important;
    }
  }

  // 表格
  .ant-table,
  .ant-table-thead > tr > th {
    background: none !important;
    color: var(--mars-sub-title-color);
  }

  .ant-table-sticky-holder {
    background: none;
  }

  .ant-table-row:nth-of-type(even) {
    background-color: var(--mars-bg-base);
  }

  .ant-table-row:nth-of-type(odd) {
    background-color: var(--mars-odd-table-bg);
  }

  .ant-table .ant-table-thead {
    background-color: var(--mars-select-bg);
  }

  .ant-table-tbody > tr.ant-table-placeholder > td {
    background: #141414;
  }

  .ant-table-row:hover > td,
  .ant-table-row-selected > td {
    background-color: transparent !important;
  }

  .ant-table-cell {
    border: none !important;
    background: transparent !important;
    color: var(--mars-text-color);
    font-size: 16px;
  }

  .ant-table-container {
    border: 1px solid var(--mars-primary-color) !important;
    background: transparent !important;
  }

  .ant-table-placeholder > td {
    background: var(--mars-bg-base) !important;
  }

  .ant-table-row:nth-last-child(1) .ant-table-cell {
    border-bottom: none;
  }

  .ant-cascader-dropdown {
    padding: 0 !important;
    .mars-drop-bg();

    .ant-cascader-menu-item {
      color: var(--mars-text-color);

      &:hover {
        background: var(--mars-list-active);
      }
    }

    .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled) {
      background: var(--mars-list-active);
    }

    .ant-cascader-menu-item-expand-icon {
      color: var(--mars-text-color);
    }
  }

  // 标签页的标签文字颜色
  .ant-tabs-tab {
    color: var(--mars-text-color);
  }

  // 分页
  .ant-pagination {
    * {
      color: var(--mars-text-color) !important;
    }

    .ant-pagination-item,
    .ant-pagination-prev,
    .ant-pagination-next {
      background: none;
      background-color: transparent !important;
    }

    .ant-pagination-simple-pager {
      input {
        background: none;
        background-color: transparent !important;
      }
    }

    .ant-select {
      background: none;
      background-color: transparent !important;
      color: var(--mars-text-color);

      .ant-select-selector {
        border-color: var(--mars-base-border-color) !important;
        background: none;
        background-color: transparent !important;

        &:hover,
        &:focus {
          border-color: #4db3ff !important;
        }
      }

      .ant-select-arrow {
        color: var(--mars-base-color) !important;
      }
    }

    .ant-select-dropdown {
      padding: 0 !important;
      .mars-drop-bg();

      .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
        background: var(--mars-list-active);
      }

      .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
        background: var(--mars-list-active);
        font-weight: 700;
      }

      .ant-select-item {
        transition: none;
        color: var(--mars-base-color) !important;
        text-align: center;
      }
    }
  }

  // 列表
  .ant-list-items {
    * {
      color: var(--mars-text-color);
    }

    .ant-list-item {
      border-bottom: 1px solid #303030;
    }

    .ant-list-item-meta-description {
      color: hsl(0deg 0% 100% / 45%);
    }
  }

  // 分割线
  .ant-divider-vertical {
    border-color: var(--mars-text-color);
  }

  //table表格滚动条
  .ant-table-body {
    &::-webkit-scrollbar {
      width: 0;
      //整体样式
      height: 0;
    }
  }

  .ant-tabs {
    width: 100%;

    .ant-tabs-nav {
      margin-top: 0;

      &::before {
        border-color: var(--mars-base-border-color);
      }
    }

    .ant-tabs-tab {
      color: var(--mars-base-color) !important;
    }

    .ant-tabs-nav-wrap {
      line-height: 1;
    }

    &.ant-tabs-card {
      .ant-tabs-nav .ant-tabs-tab-active {
        background: #20a0ff33 !important;
      }

      .ant-tabs-tab {
        border-color: var(--mars-base-border-color);
        background: #20a0ff33 !important;
      }
    }

    .ant-tabs-tab-active {
      border-top: none !important;
    }
  }
}

.mars-cascader-dropdown {
  padding: 0 !important;
  background-color: var(--mars-select-bg) !important;
  .mars-drop-bg();

  .ant-cascader-menu-item {
    color: var(--mars-text-color);

    &:hover {
      background: var(--mars-list-active);
    }
  }

  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled) {
    background: var(--mars-list-active);
  }

  .ant-cascader-menu-item-expand-icon {
    color: var(--mars-text-color);
  }
}

// 下拉选择
.mars-select-dropdown {
  padding: 0 !important;
  color: var(--mars-text-color) !important;
  .mars-drop-bg();

  .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
    background: var(--mars-list-active);
  }

  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background: var(--mars-list-active);
    font-weight: 700;
  }

  .ant-select-item {
    transition: none;
    color: var(--mars-text-color) !important;
    text-align: center;
  }

  .ant-cascader-menu-item {
    color: var(--mars-text-color);

    &:hover {
      background: var(--mars-list-active);
    }
  }

  .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled) {
    background: var(--mars-list-active);
  }

  .ant-cascader-menu-item-expand-icon {
    color: var(--mars-text-color);
  }
}

// 滚动条
::-webkit-scrollbar-button {
  display: none;
  width: 0;
  height: 0;
}

::-webkit-scrollbar-track {
  background: var(--mars-scrollbar-track);
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb {
  border: 0;
}

::-webkit-scrollbar {
  width: 4px;
  height: 10px;
  border-radius: 4px;
  background: var(--mars-scrollbar-thumb);
}

::-webkit-scrollbar-thumb {
  min-height: 28px;
  padding-top: 100px;
  border-radius: 4px;
  background-clip: padding-box;
  background-color: var(--mars-scrollbar-thumb);
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb {
  border: 0;
}
