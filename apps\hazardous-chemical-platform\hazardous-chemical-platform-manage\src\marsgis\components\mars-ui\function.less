/**
 * 通用 class
 *
 * @copyright 
 * <AUTHOR>

.w-full {
  width: 100%;
}

.f-pt {
  padding-top: 10px;
}

.f-mt {
  margin-top: 10px;
}

.f-mb {
  margin-bottom: 10px;
}

.f-ml {
  margin-left: 5px;
}

.f-mr {
  margin-right: 5px;
}

.f-ib {
  display: inline-block;
}

.f-cb::after,
.f-cbli li::after {
  content: '.';
  display: block;
  visibility: hidden;
  height: 0;
  clear: both;
  overflow: hidden;
}

.f-cb,
.f-cbli li {
  zoom: 1;
}

.f-dn {
  display: none;
}

.f-db {
  display: block;
}

.f-fl {
  float: left;
}

.f-fr {
  float: right;
}

.f-pr {
  position: relative;
}

.f-prz {
  position: relative;
  zoom: 1;
}

.f-oh {
  overflow: hidden;
}

.f-ff0 {
  font-family: arial, '\5b8b\4f53';
}

.f-ff1 {
  font-family: 'Microsoft YaHei', '\5fae\8f6f\96c5\9ed1', arial, '\5b8b\4f53';
}

.f-fs12 {
  font-size: 12px;
}

.f-fs14 {
  font-size: 14px;
}

.f-fs16 {
  font-size: 16px;
}

.f-fs18 {
  font-size: 18px;
}

.f-fwn {
  font-weight: normal;
}

.f-fwb {
  font-weight: bold;
}

.f-tal {
  text-align: left;
}

.f-tac {
  text-align: center;
}

.f-tar {
  text-align: right;
}

.f-taj {
  text-align: justify;
  text-justify: inter-ideograph;
}

.f-vam,
.f-vama * {
  vertical-align: middle;
}

.f-wsn {
  word-wrap: normal;
  white-space: nowrap;
}

.f-pre {
  overflow: hidden;
  text-align: left;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
}

.f-wwb {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

.f-ti {
  overflow: hidden;
  text-indent: -30000px;
}

.f-ti2 {
  text-indent: 2em;
}

.f-lhn {
  line-height: normal;
}

.f-tdu,
.f-tdu:hover {
  text-decoration: underline;
}

.f-tdn,
.f-tdn:hover {
  text-decoration: none;
}

.f-toe {
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: normal;
  white-space: nowrap;
}

.f-csp {
  cursor: pointer;
}

.f-csd {
  cursor: default;
}

.f-csh {
  cursor: help;
}

.f-csm {
  cursor: move;
}

.f-usn {
  user-select: none;
}

.f-push-5-b {
  margin-bottom: 5px !important;
}

.f-push-5-t {
  margin-top: 5px !important;
}

.f-push-5-r {
  margin-right: 5px !important;
}

.f-push-5-l {
  margin-left: 5px !important;
}

.f-push-10-b {
  margin-bottom: 10px !important;
}

.f-push-10-t {
  margin-top: 10px !important;
  color: var(--mars-text-color);
}

.f-push-10-r {
  margin-right: 10px !important;
}

.f-push-10-l {
  margin-left: 10px !important;
}

.f-push-15-b {
  margin-bottom: 15px !important;
}

.f-push-15-t {
  margin-top: 15px !important;
}

.f-push-15-r {
  margin-right: 15px !important;
}

.f-push-15-l {
  margin-left: 15px !important;
}

.f-push-20-b {
  margin-bottom: 20px !important;
}

.f-push-20-t {
  margin-top: 20px !important;
}

.f-push-20-r {
  margin-right: 20px !important;
}

.f-push-20-l {
  margin-left: 20px !important;
}

.f-push-30-b {
  margin-bottom: 30px !important;
}

.f-push-30-t {
  margin-top: 30px !important;
}

.f-push-30-r {
  margin-right: 30px !important;
}

.f-push-30-l {
  margin-left: 30px !important;
}

.f-push-40-b {
  margin-bottom: 40px !important;
}

.f-push-40-t {
  margin-top: 40px !important;
}

.f-push-40-r {
  margin-right: 40px !important;
}

.f-push-40-l {
  margin-left: 40px !important;
}

.f-push-50-b {
  margin-bottom: 50px !important;
}

.f-push-50-t {
  margin-top: 50px !important;
}

.f-push-50-r {
  margin-right: 50px !important;
}

.f-push-50-l {
  margin-left: 50px !important;
}

.f-pdg-5-b {
  padding-bottom: 5px !important;
}

.f-pdg-5-t {
  padding-top: 5px !important;
}

.f-pdg-5-r {
  padding-right: 5px !important;
}

.f-pdg-5-l {
  padding-left: 5px !important;
}

.f-pdg-10-b {
  padding-bottom: 10px !important;
}

.f-pdg-10-t {
  padding-top: 10px !important;
}

.f-pdg-10-r {
  padding-right: 10px !important;
}

.f-pdg-10-l {
  padding-left: 10px !important;
}

.f-pdg-15-b {
  padding-bottom: 15px !important;
}

.f-pdg-15-t {
  padding-top: 15px !important;
}

.f-pdg-15-r {
  padding-right: 15px !important;
}

.f-pdg-15-l {
  padding-left: 15px !important;
}

.f-pdg-20-b {
  padding-bottom: 20px !important;
}

.f-pdg-20-t {
  padding-top: 20px !important;
}

.f-pdg-20-r {
  padding-right: 20px !important;
}

.f-pdg-20-l {
  padding-left: 20px !important;
}

.f-pdg-30-b {
  padding-bottom: 30px !important;
}

.f-pdg-30-t {
  padding-top: 30px !important;
}

.f-pdg-30-r {
  padding-right: 30px !important;
}

.f-pdg-30-l {
  padding-left: 30px !important;
}

.f-pdg-40-b {
  padding-bottom: 40px !important;
}

.f-pdg-40-t {
  padding-top: 40px !important;
}

.f-pdg-40-r {
  padding-right: 40px !important;
}

.f-pdg-40-l {
  padding-left: 40px !important;
}

.f-pdg-50-b {
  padding-bottom: 50px !important;
}

.f-pdg-50-t {
  padding-top: 50px !important;
}

.f-pdg-50-r {
  padding-right: 50px !important;
}

.f-pdg-50-l {
  padding-left: 50px !important;
}

.pad-none {
  padding: 0 !important;
}

.mars-pannel-item-label {
  display: block;
  min-width: 60px;
  text-align: right;
}
