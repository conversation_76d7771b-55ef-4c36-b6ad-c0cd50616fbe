/**
 * 所有页面全局公共CSS
 *
 * @copyright 
 * <AUTHOR>

/* 清除内外边距 */
body,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
blockquote,
/* structural elements 结构元素 */
dl,
dt,
dd,
ul,
ol,
li,
/* list elements 列表元素 */
pre,
/* text formatting elements 文本格式元素 */
fieldset,
lengend,
button,
input,
textarea,
/* form elements 表单元素 */
th,
td {
  /* table elements 表格元素 */
  margin: 0;
  padding: 0;
  list-style: none; /* 火狐 */ /* webkit浏览器 */ /* IE10 */ /* 早期浏览器 */
  user-select: none;
}

#root,
#app {
  height: 100%;
}

body,
html {
  // min-width: 900px;
  // background-color: var(--mars-bg-base) !important;
  // color: var(--mars-base-color) !important;
  // font-size: 14px;

  * {
    scrollbar-base-color: #f4f7fc;
    scrollbar-track-color: #f4f7fc;
    scrollbar-face-color: #797979;
    scrollbar-arrow-color: #f4f7fc;
    scrollbar-shadow-color: #f4f7fc;
    scrollbar-3dlight-color: #f4f7fc;
    scrollbar-highlight-color: #f4f7fc;
    scrollbar-darkshadow-color: #f4f7fc;
    // font-size: 14px;
    scrollbar-width: thin;
  }
}

.mars-primary-table {
  width: 100%;
  border-color: var(--mars-base-border-color);

  tr td:nth-of-type(1) {
    width: 100px;
  }

  td {
    padding: 5px;
    color: var(--mars-text-color);
  }
}
