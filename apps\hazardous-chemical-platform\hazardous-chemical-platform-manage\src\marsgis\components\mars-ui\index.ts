/**
 * 统一导出公共组件, 按需初始化ant-design-vue
 * @copyright
 * <AUTHOR>
import { App } from 'vue';
import MarsSelect from './mars-select/index.vue';
import MarsButton from './mars-button/index.vue';
import MarsInput from './mars-input/index.vue';
import MarsTextarea from './mars-textarea/index.vue';
import MarsInputNumber from './mars-input-number/index.vue';
import MarsInputGroup from './mars-input-group/index.vue';
import MarsDatePicker from './mars-date-picker/index.vue';
import MarsRangePicker from './mars-range-picker/index.vue';
import MarsColorPicker from './mars-color-picker';
import MarsColor from './mars-color/index.vue';
import MarsIcon from './mars-icon/index.vue';
import MarsSwitch from './mars-switch/index.vue';
import MarsDialog from './mars-dialog/index.vue';
import MarsSlider from './mars-slider/index.vue';
import MarsDropDown from './mars-dropdown';
import MarsGui from './mars-gui/index.vue';
import MarsTable from './mars-table/index.vue';
import MarsMessage, { $message as marsMessage } from './mars-message';
import MarsAlert, { $alert as marsAlert } from './mars-alert/';
import MarsNotify, { $notify as marsNotify } from './mars-notify';
import MarsLoading, {
  $hideLoading as marsHideLoading,
  $showLoading as marsShowLoading,
} from './mars-loading';
import MarsTree from './mars-tree';
import './mars-echarts';
import './themes';
import './index.less';
import './function.less';

export const $alert = marsAlert;
export const $notify = marsNotify;
export const $message = marsMessage;
export const $hideLoading = marsHideLoading;
export const $showLoading = marsShowLoading;

const components = [
  MarsSelect,
  MarsButton,
  MarsInput,
  MarsInputGroup,
  MarsInputNumber,
  MarsDatePicker,
  MarsRangePicker,
  MarsColorPicker,
  MarsTree,
  MarsDropDown,
  MarsIcon,
  MarsDialog,
  MarsTextarea,
  MarsSwitch,
  MarsSlider,
  MarsGui,
  MarsTable,
  MarsColor,
];

let marsUIConfig: Record<string, any>;

export const getConfig = () => {
  return marsUIConfig;
};

export default function (app: App, config: Record<string, any> = {}): App {
  marsUIConfig = config;

  components.forEach(comp => {
    app.component(comp.name as string, comp);
  });
  MarsMessage(app);
  MarsAlert(app);
  MarsNotify(app);
  MarsLoading(app);
  return app;
}
