/**
 * 颜色选择器
 *
 * @copyright 
 * <AUTHOR>
.hu-color-picker {
  z-index: 1;
  padding: 10px;
  border-radius: 4px;
  background: var(--mars-bg-base);
  box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.16);
}
.hu-color-picker {
  width: 220px !important;
  background-color: var(--mars-bg-base) !important;
  box-shadow: none;
}
.hu-color-picker.light {
  background: #f7f8f9;
}
.hu-color-picker.light .color-show .sucker {
  background: #eceef0;
}
.hu-color-picker.light .color-type .name {
  background: #e7e8e9;
}
.hu-color-picker.light .color-type .value {
  background: #eceef0;
  color: #666;
}
.hu-color-picker.light .colors.history {
  border-top: 1px solid #eee;
}
.hu-color-picker canvas {
  vertical-align: top;
}
.hu-color-picker .color-set {
  display: flex;
}
.hu-color-picker .color-show {
  display: flex;
  margin-top: 8px;
}
.saturation {
  position: relative;
  cursor: pointer;
}
.saturation .slide {
  position: absolute;
  top: 0;
  left: 100px;
  width: 10px;
  height: 10px;
  border: 1px solid #fff;
  border-radius: 50%;
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.3);
  pointer-events: none;
}
.color-alpha {
  position: relative;
  margin-left: 8px;
  cursor: pointer;
}
.color-alpha .slide {
  position: absolute;
  top: 100px;
  left: 0;
  width: 100%;
  height: 4px;
  background: #fff;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.3);
  pointer-events: none;
}
.sucker {
  width: 30px;
  transition: all 0.3s;
  background: #2e333a;
  fill: #9099a4;
  cursor: pointer;
}
.sucker.active,
.sucker:hover {
  fill: #1593ff;
}
.hue {
  position: relative;
  margin-left: 8px;
  cursor: pointer;
}
.hue .slide {
  position: absolute;
  top: 100px;
  left: 0;
  width: 100%;
  height: 4px;
  background: #fff;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.3);
  pointer-events: none;
}
.colors {
  margin: 0;
  padding: 0;
}
.colors.history {
  margin-top: 10px;
  border-top: 1px solid #2e333a;
}
.colors .item {
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  margin: 10px 0 0 10px;
  transition: all 0.1s;
  border-radius: 3px;
  vertical-align: top;
  cursor: pointer;
}
.colors .item:nth-child(8n + 1) {
  margin-left: 0;
}
.colors .item:hover {
  transform: scale(1.4);
}
.colors .item .alpha {
  height: 100%;
  border-radius: 4px;
}
.colors .item .color {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
}
.color-type {
  display: flex;
  margin-top: 8px;
  font-size: 12px;
}
.color-type .name {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 30px;
  float: left;
  background: #252930;
  color: #999;
}
.color-type .value {
  box-sizing: border-box;
  flex: 1;
  min-width: 100px;
  height: 30px;
  padding: 0 12px;
  border: 0;
  background: #2e333a;
  color: #fff;
}
.marsColorView {
  display: inline-block;
  width: 80px;
  height: 20px;
  cursor: pointer;
}
.ml5 {
  margin-left: 5px;
}
.overlayClassName .color-alpha {
  display: none;
}
.overlayClassName .hue {
  margin-left: 18px;
}
.overlayClassName .hu-color-picker .color-type:nth-last-child(2) {
  display: none;
}
