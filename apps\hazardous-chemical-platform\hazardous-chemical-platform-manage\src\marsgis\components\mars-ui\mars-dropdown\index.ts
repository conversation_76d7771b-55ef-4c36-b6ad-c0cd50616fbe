import { Dropdown } from 'ant-design-vue';
import { App, defineComponent, h } from 'vue';
import './dropdown.less';

/**
 * 下拉菜单
 *
 * @copyright
 * <AUTHOR>

const MarsDropdown = defineComponent({
  name: 'MarsDropdownMenu',
  inheritAttrs: false,
  setup(props, context) {
    return () =>
      h(
        Dropdown,
        { ...context.attrs, ...props, overlayClassName: 'mars-dropdown-menu' },
        context.slots
      );
  },
});

export function install(app: App): App {
  app.component(MarsDropdown.name as string, MarsDropdown);
  return app;
}
export default MarsDropdown;
