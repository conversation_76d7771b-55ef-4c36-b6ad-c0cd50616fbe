@import url('./zhts.less');

:root[data-theme='light'] {
  --mars-primary-color: rgb(0, 138, 255);
  --mars-primary-half-color: rgb(0, 138, 255);
  --mars-sub-color: #26ddff;
  --mars-danger-color: #f75165;
  --mars-warning-color: #ff8041;
  --mars-success-color: #18af92;
  --mars-base-color: #ffffff;
  --mars-content-color: #cdcdcd;
  --mars-text-color: #19212f;
  --mars-hover-btn-bg: #3ea6ff;
  --mars-click-btn-bg: #015dab;
  --mars-disable-btn-bg: #cdcdcd;
  --mars-base-border: #e9ecf3;
  --mars-select-bg: rgba(0, 138, 255, 0.2);
  --mars-bg-base: #ffffff;
  --mars-odd-table-bg: #f4faff; // 奇数表格背景
  --mars-bg-title: rgb(0, 138, 255); // 普通的头部背景色
  --mars-base-border-color: #b9b9b9;
  --mars-list-active: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0) 98%,
    rgb(0, 138, 255) 100%
  );

  --mars-title-active: rgba(255, 255, 255, 0.8);
  --mars-title-text-active: #ffffff;

  --mars-menu-emb: url(../assets/images/sub-menu-emb-light.png);

  --mars-msg-title-bg: #008aff;
  --mars-msg-title-color: #1995ff;
  --mars-msg-content-bg: #ffffff;

  --mars-alert-title-color: #ffffff;
  --mars-sub-title-color: #707782; // 小标题的颜色

  --mars-scrollbar-thumb: rgba(0, 138, 255, 0.4);
  --mars-scrollbar-track: rgb(236, 236, 236);

  --mars-collapse-title-bg: #008aff;
}
