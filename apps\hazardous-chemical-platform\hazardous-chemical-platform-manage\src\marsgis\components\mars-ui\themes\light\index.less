@import url('./zhts.less');

:root[data-theme='light'] {
  --mars-primary-color: rgba(0, 138, 255, 0.9);
  --mars-primary-half-color: rgba(0, 138, 255, 0.5);
  --mars-sub-color: #26ddff;
  --mars-danger-color: #f75165;
  --mars-warning-color: #ff8041;
  --mars-success-color: #18af92;
  --mars-base-color: #ffffff;
  --mars-content-color: #cdcdcd;
  --mars-text-color: #ffffff;
  --mars-hover-btn-bg: #3ea6ff;
  --mars-click-btn-bg: #015dab;
  --mars-disable-btn-bg: #cdcdcd;
  --mars-base-border: #ffffff;
  --mars-select-bg: rgba(0, 138, 255, 0.2);
  --mars-bg-base: rgba(23, 49, 71, 0.8);
  --mars-odd-table-bg: rgba(0, 138, 255, 0.06); // 奇数表格背景
  --mars-bg-title: #0f3453; // 普通的头部背景色
  --mars-base-border-color: #b9b9b9;
  --mars-list-active: linear-gradient(
    90deg,
    rgba(0, 138, 255, 0) 0%,
    rgba(0, 138, 255, 0.58) 53%,
    rgba(0, 138, 255, 0.02) 100%
  );

  --mars-title-active: linear-gradient(0deg, rgba(0, 138, 255, 0.6) 0%, rgba(0, 138, 255, 0) 100%);
  --mars-title-text-active: #ffffff;

  --mars-menu-emb: url(../assets/images/sub-menu-emb.png);

  --mars-msg-title-bg: url(../assets/images/msg-title-bg.png);
  --mars-msg-title-color: #c7d3dd;
  --mars-msg-content-bg: url('../assets/images/border-image-base.png');

  --mars-alert-title-color: #0089fe;

  --mars-scrollbar-thumb: #134875;
  --mars-scrollbar-track: #173147;

  --mars-sub-title-color: #cdcdcd; // 小标题的颜色

  --mars-collapse-title-bg: url('@mars/components/mars-ui/assets/images/tab-title.png');
}
