<template>
  <mars-dialog
    title="缓冲分析"
    width="366"
    top="60"
    right="10"
    :min-width="500"
  >
    <template #icon>
      <mars-icon
        icon="local"
        width="18"
      />
    </template>
    <div
      class="position-container"
      :class="$style.pointer"
    >
      <div class="f-mb">
        <a-space>
          <span class="mars-pannel-item-label">缓冲半径:</span>
          <mars-input-number
            class="radius"
            @change="radiusChange"
            v-model:value="radiusVal"
            :min="1"
            :step="1"
            :max="999"
          />公里
        </a-space>
      </div>
      <a-space>
        <span class="mars-pannel-item-label">绘制:</span>
        <mars-button @click="drawPoint">点</mars-button>
        <mars-button @click="drawPolyline">线</mars-button>
        <mars-button @click="drawPolygon">面</mars-button>
        <a-button
          @click="mapWork.deleteAll"
          class="!text-red-500"
          >清除</a-button
        >
      </a-space>
    </div>
  </mars-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import * as mapWork from './map.js';
import useLifecycle from '@mars/common/uses/use-lifecycle';

// 启用map.ts生命周期
useLifecycle(mapWork);

const radiusVal = ref<number>(1);

// 点
const drawPoint = () => {
  mapWork.drawPoint();
};
// 线
const drawPolyline = () => {
  mapWork.drawPolyline();
};

// 面
const drawPolygon = () => {
  mapWork.drawPolygon();
};

const radiusChange = () => {
  mapWork.radiusChange(radiusVal.value);
};

radiusChange();
</script>
<style scoped lang="less">
.mars-pannel-item-label {
  width: 55px;
}

.ant-input-number {
  width: 120px !important;
}
</style>

<style lang="less" module>
.pointer {
  :global {
    .ant-btn {
      background-color: transparent;
      color: #fff;
      border: 1px solid #89bceb;

      &:hover {
        background-color: transparent !important;
      }
    }
  }
}
</style>
