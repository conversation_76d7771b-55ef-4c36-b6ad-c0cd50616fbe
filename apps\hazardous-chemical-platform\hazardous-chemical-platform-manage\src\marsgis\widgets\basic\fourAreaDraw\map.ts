import * as mars3d from 'mars3d';
import { map as mapForeach } from 'lodash-es';

let map; // mars3d.Map三维地图对象
export let graphicLayer;

export const eventTarget = new mars3d.BaseClass();

/**
 * 初始化地图业务，生命周期钩子函数（必须）
 * 框架在地图初始化完成后自动调用该函数
 * @param {mars3d.Map} mapInstance 地图对象
 * @returns {void} 无
 */
export function onMounted(mapInstance) {
  map = mapInstance; // 记录map

  // 创建矢量数据图层
  graphicLayer = map.getLayerById('command-plot');

  if (!graphicLayer) {
    graphicLayer = new mars3d.layer.GraphicLayer({ id: 'isInPoly' });
    map.addLayer(graphicLayer);
  }

  bindLayerEvent(); // 对图层绑定相关事件
  // bindLayerPopup()
  bindLayerContextMenu();
}

export function drawPolygon(color) {
  graphicLayer.startDraw({
    type: 'polygon',
    style: {
      color: color || '#ffff00',
      opacity: 0.2,
      outlineWidth: 2,
      outlineColor: '#fff',
      clampToGround: true,
    },
    success: function (graphic) {
      updateSelect(graphic);
    },
  });
}

// 在范围内的改变图标为红色
function updateSelect(drawGraphic) {
  // graphicLayer.eachGraphic((graphic) => {
  //   const position = graphic.positionShow
  //   const isInArea = drawGraphic.isInPoly(position)
  //   if (isInArea) {
  //     graphic.setStyle({
  //       image: "img/marker/mark1.png"
  //     })
  //     selectGraphic.push(graphic)
  //   }
  // })
}

export function removeAll() {
  graphicLayer.clear();

  // for (let i = 0; i < selectGraphic.length; i++) {
  //   const graphic = selectGraphic[i]
  //   graphic.setStyle({
  //     image: "img/marker/mark3.png"
  //   })
  // }
  // selectGraphic = []
}

// 在图层级处理一些事物
function bindLayerEvent() {
  console.log(graphicLayer);

  // 数据编辑相关事件， 用于属性弹窗的交互
  graphicLayer.on(mars3d.EventType.drawCreated, function (e) {
    eventTarget.fire('graphicEditor-start', e);
  });
  graphicLayer.on(
    [
      mars3d.EventType.editStart,
      mars3d.EventType.editMovePoint,
      mars3d.EventType.editStyle,
      mars3d.EventType.editRemovePoint,
    ],
    function (e) {
      eventTarget.fire('graphicEditor-update', e);
    }
  );
  graphicLayer.on([mars3d.EventType.editStop, mars3d.EventType.removeGraphic], function (e) {
    eventTarget.fire('graphicEditor-stop', e);
  });
}

// 绑定右键菜单
export function bindLayerContextMenu() {
  graphicLayer.bindContextMenu([
    {
      text: '开始编辑对象',
      iconCls: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic || !graphic.startEditing) {
          return false;
        }
        return !graphic.isEditing;
      },
      callback: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphicLayer.startEditing(graphic);
        }
      },
    },
    {
      text: '停止编辑对象',
      iconCls: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        return graphic.isEditing;
      },
      callback: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphicLayer.stopEditing(graphic);
        }
      },
    },
    {
      text: '删除对象',
      iconCls: 'fa fa-trash-o',
      show: event => {
        const graphic = event.graphic;
        if (!graphic || graphic.isDestroy) {
          return false;
        } else {
          return true;
        }
      },
      callback: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return;
        }
        graphicLayer.removeGraphic(graphic);
      },
    },
  ]);
}

// 在图层绑定Popup弹窗
export function bindLayerPopup() {
  graphicLayer.bindPopup(function (event) {
    // @ts-ignore
    window.saveFn = function () {
      const el = document.getElementById('save-value');
      if (el) {
        // @ts-ignore
        const val = el.value;
        console.log(val);
        console.log(event);
        const { graphic } = event;
        graphic.style = {
          ...graphic.style,
          label: {
            text: val || '',
            font_size: 25,
            color: '#e6e6e6',
            distanceDisplayCondition: true,
            distanceDisplayCondition_far: 500000,
            distanceDisplayCondition_near: 0,
          },
        };
        graphicLayer.closePopup();
      }
    };

    return `<div class="w-300px">
      <div class="w-full h-full"><lable>名称：</label><input class="!caret-light-500 border-hex-89BCEB border-1 bg-transparent !text-light-500 w-4/5 h-25px" id="save-value" /></div>
      <div class="w-full h-full flex justify-center mt-2"><button class="border-hex-89BCEB border-1 px-2" id="save-name" onclick="saveFn()">保存</button></div>
    </div>`;
  });
}
