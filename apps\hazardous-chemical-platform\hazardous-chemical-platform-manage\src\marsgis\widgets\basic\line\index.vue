<template>
  <mars-dialog
    title="线上近点"
    width="366"
    top="60"
    right="10"
    :min-width="500"
  >
    <template #icon>
      <mars-icon
        icon="local"
        width="18"
      />
    </template>
    <div
      class="position-container"
      :class="$style.pointer"
    >
      <a-space>
        <mars-button @click="drawLine">绘制线</mars-button>
        <mars-button @click="nearPoint">计算最近点</mars-button>
        <mars-button @click="clearAll">清除</mars-button>
      </a-space>
    </div>
  </mars-dialog>
</template>

<script setup lang="ts">
import * as mapWork from './map.js';
import useLifecycle from '@mars/common/uses/use-lifecycle';
import { useWidget } from '@mars/common/store/widget';
import { markRaw } from 'vue';

const { activate, disable, isActivate, updateWidget } = useWidget();

// 启用map.ts生命周期
useLifecycle(mapWork);
// 绘制线
const drawLine = () => {
  mapWork.drawLine();
};
// 计算最近点
const nearPoint = () => {
  mapWork.drawPoint();
};
// 清除
const clearAll = () => {
  mapWork.clearLayer();
};

// 属性面板
const showEditor = (e: any) => {
  if (!isActivate('graphic-editor')) {
    activate({
      name: 'graphic-editor',
      data: { graphic: markRaw(e.graphic) },
    });
  } else {
    updateWidget('graphic-editor', {
      data: { graphic: markRaw(e.graphic) },
    });
  }
};
mapWork.eventTarget.on('graphicEditor-start', async (e: any) => {
  showEditor(e);
});
// 编辑修改了模型
mapWork.eventTarget.on('graphicEditor-update', async (e: any) => {
  showEditor(e);
});

// 停止编辑修改模型
mapWork.eventTarget.on('graphicEditor-stop', async (e: any) => {
  disable('graphic-editor');
});
</script>

<style lang="less" module>
.pointer {
  :global {
    .ant-btn {
      background-color: transparent;
      color: #fff;
      border: 1px solid #89bceb;

      &:hover {
        background-color: transparent !important;
      }
    }
  }
}
</style>
