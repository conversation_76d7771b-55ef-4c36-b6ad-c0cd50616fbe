import * as mars3d from 'mars3d';
import * as turf from '@turf/turf';

let map; // mars3d.Map三维地图对象
let graphicLayer; // 矢量图层对象
let pointLayer;

export const eventTarget = new mars3d.BaseClass();

export const mapOptions = {
  scene: {
    center: { lat: 31.871794, lng: 116.800468, alt: 57020, heading: 90, pitch: -51 },
    fxaa: true,
  },
};

/**
 * 初始化地图业务，生命周期钩子函数（必须）
 * 框架在地图初始化完成后自动调用该函数
 * @param {mars3d.Map} mapInstance 地图对象
 * @returns {void} 无
 */
export function onMounted(mapInstance) {
  map = mapInstance; // 记录map

  // 创建矢量数据图层
  graphicLayer = map.getLayerById('command-plot');
  if (!graphicLayer) {
    graphicLayer = new mars3d.layer.GraphicLayer({ id: 'line_main' });
    map.addLayer(graphicLayer);
  }

  bindLayerEvent(graphicLayer);
  bindLayerPopup(graphicLayer);
  bindLayerContextMenu(graphicLayer);

  // 点矢量数据图层
  pointLayer = map.getLayerById('command-plot');
  if (!pointLayer) {
    pointLayer = new mars3d.layer.GraphicLayer({ id: 'line_point' });
    map.addLayer(pointLayer);
  }

  bindLayerEvent(pointLayer);
  bindLayerPopup(pointLayer);
  bindLayerContextMenu(pointLayer);
}

/**
 * 释放当前地图业务的生命周期函数
 * @returns {void} 无
 */
export function onUnmounted() {
  map = null;
}

// 绘制线
export function drawLine() {
  graphicLayer.startDraw({
    type: 'polyline',
    style: {
      color: '#55ff33',
      width: 3,
      clampToGround: true,
    },
    success: function () {
      // 绘制成功之后回调
    },
  });
}

// 绘制点
export function drawPoint() {
  pointLayer.startDraw({
    type: 'point',
    style: {
      pixelSize: 10,
      color: 'red',
    },
    success: function () {
      nearPoint();
    },
  });
}

// 最近点计算
function nearPoint() {
  const lineLayer = graphicLayer.getGraphics();
  const point = pointLayer.getGraphics();

  if (lineLayer.length < 1 || point.length < 1) {
    return;
  }

  const line = lineLayer[0].toGeoJSON();
  const pt = point[0].toGeoJSON();

  const snapped = turf.nearestPointOnLine(line, pt, { units: 'miles' });
  const position = snapped.geometry.coordinates;

  // 最近点（图标点）
  const primitive = new mars3d.graphic.BillboardPrimitive({
    position: position,
    style: {
      image: 'img/marker/mark-blue.png',
      scale: 1.5,
      horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
      clampToGround: true,
    },
  });
  pointLayer.addGraphic(primitive);
}

// 清除数据
export function clearLayer() {
  graphicLayer.clear();
  pointLayer.clear();
}

// 绑定右键菜单
export function bindLayerContextMenu(layer) {
  layer.bindContextMenu([
    {
      text: '开始编辑对象',
      iconCls: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic || !graphic.startEditing) {
          return false;
        }
        return !graphic.isEditing;
      },
      callback: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphicLayer.startEditing(graphic);
        }
      },
    },
    {
      text: '停止编辑对象',
      iconCls: 'fa fa-edit',
      show: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        return graphic.isEditing;
      },
      callback: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return false;
        }
        if (graphic) {
          graphicLayer.stopEditing(graphic);
        }
      },
    },
    {
      text: '删除对象',
      iconCls: 'fa fa-trash-o',
      show: event => {
        const graphic = event.graphic;
        if (!graphic || graphic.isDestroy) {
          return false;
        } else {
          return true;
        }
      },
      callback: function (e) {
        const graphic = e.graphic;
        if (!graphic) {
          return;
        }
        graphicLayer.removeGraphic(graphic);
      },
    },
  ]);
}

// 在图层级处理一些事物
function bindLayerEvent(layer) {
  // 数据编辑相关事件， 用于属性弹窗的交互
  layer.on(mars3d.EventType.drawCreated, function (e) {
    eventTarget.fire('graphicEditor-start', e);
  });
  layer.on(
    [
      mars3d.EventType.editStart,
      mars3d.EventType.editMovePoint,
      mars3d.EventType.editStyle,
      mars3d.EventType.editRemovePoint,
    ],
    function (e) {
      eventTarget.fire('graphicEditor-update', e);
    }
  );
  layer.on([mars3d.EventType.editStop, mars3d.EventType.removeGraphic], function (e) {
    eventTarget.fire('graphicEditor-stop', e);
  });
}

// 在图层绑定Popup弹窗
export function bindLayerPopup(layer) {
  layer.bindPopup(function (event) {
    // @ts-ignore
    window.saveFn = function () {
      const el = document.getElementById('save-value');
      if (el) {
        // @ts-ignore
        const val = el.value;
        console.log(val);
        console.log(event);
        const { graphic } = event;
        graphic.style = {
          ...graphic.style,
          label: {
            text: val || '',
            font_size: 25,
            color: '#e6e6e6',
            distanceDisplayCondition: true,
            distanceDisplayCondition_far: 500000,
            distanceDisplayCondition_near: 0,
          },
        };
        layer.closePopup();
      }
    };

    return `<div class="w-300px">
      <div class="w-full h-full"><lable>名称：</label><input class="!caret-light-500 border-hex-89BCEB border-1 bg-transparent !text-light-500 w-4/5 h-25px" id="save-value" /></div>
      <div class="w-full h-full flex justify-center mt-2"><button class="border-hex-89BCEB border-1 px-2" id="save-name" onclick="saveFn()">保存</button></div>
    </div>`;
  });
}
