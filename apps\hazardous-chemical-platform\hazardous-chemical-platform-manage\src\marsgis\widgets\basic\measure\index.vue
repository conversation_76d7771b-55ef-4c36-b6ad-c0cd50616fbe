<template>
  <mars-dialog
    title="图上量算"
    width="366"
    top="60"
    right="10"
    :min-width="500"
  >
    <template #icon>
      <mars-icon
        icon="ruler"
        width="18"
      />
    </template>
    <div
      class="position-container"
      :class="$style.pointer"
    >
      <a-space class="!mb-1">
        <mars-button @click="mapWork.measureSurfaceLength">贴地距离</mars-button>
        <mars-button @click="mapWork.measureSurfaceeArea">贴地面积</mars-button>
        <mars-button @click="mapWork.measureAngle">方位角</mars-button>
      </a-space>
      <a-space>
        <mars-button @click="mapWork.measureTriangleHeight">三角测量</mars-button>
        <mars-button @click="mapWork.measurePoint">坐标测量</mars-button>
        <mars-button @click="mapWork.measureHeight">高度差</mars-button>
        <mars-button
          @click="mapWork.removeAll"
          class="!text-red-500"
          >清除</mars-button
        >
      </a-space>
    </div>
  </mars-dialog>
</template>

<script lang="ts" setup>
import useLifecycle from '@mars/common/uses/use-lifecycle';
import * as mapWork from './map';

// 启用map.ts生命周期
useLifecycle(mapWork);
</script>

<style lang="less" module>
.pointer {
  :global {
    .ant-btn {
      background-color: transparent;
      color: #fff;
      border: 1px solid #89bceb;

      &:hover {
        background-color: transparent !important;
      }
    }
  }
}
</style>
