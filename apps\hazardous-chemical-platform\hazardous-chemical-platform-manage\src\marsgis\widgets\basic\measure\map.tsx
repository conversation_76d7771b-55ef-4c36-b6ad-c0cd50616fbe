import * as mars3d from 'mars3d';

let map; // mars3d.Map三维地图对象
let measure;

export const eventTarget = new mars3d.BaseClass();

/**
 * 初始化地图业务，生命周期钩子函数（必须）
 * 框架在地图初始化完成后自动调用该函数
 * @param {mars3d.Map} mapInstance 地图对象
 * @returns {void} 无
 */
export function onMounted(mapInstance) {
  map = mapInstance; // 记录map

  addMeasure();
}

/**
 * 释放当前地图业务的生命周期函数
 * @returns {void} 无
 */
export function onUnmounted() {
  map = null;
}

function addMeasure() {
  measure = new mars3d.thing.Measure({
    label: {
      color: '#ffffff',
      font_family: '楷体',
      font_size: 20,
      background: false,
    },
  });
  map.addThing(measure);
}

export function removeAll() {
  measure.clear();
}

// 空间距离
export function measureLength() {
  measure.distance({
    showAddText: true,
  });
}
// 贴地距离
export function measureSurfaceLength() {
  measure.distanceSurface({
    showAddText: true,
  });
}
// 水平面积
export function measureArea() {
  measure.area({});
}
// 贴地面积
export function measureSurfaceeArea() {
  measure.areaSurface({
    style: {
      color: '#ffff00',
    },
    splitNum: 10, // step插值分割的个数
  });
}
// 高度差
export function measureHeight() {
  measure.height();
}

// 三角测量
export function measureTriangleHeight() {
  measure.heightTriangle();
}

// 方位角
export function measureAngle() {
  measure.angle();
}

// 坐标测量
export function measurePoint() {
  measure.point();
}
