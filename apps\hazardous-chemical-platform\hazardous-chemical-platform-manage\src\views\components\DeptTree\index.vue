<template>
  <BasicTree
    :selectedKeys="selectedKeys"
    :loading="loadingTree"
    expandOnSearch
    :fieldNames="{ children: 'children', title: 'deptName', key: 'autoId' }"
    :search="true"
    :treeData="treeData"
    :showLine="true"
    @select="handleSelect"
    :class="`${$style['user-tree']}`"
    placeholderSearch="请输入部门名称"
    ref="treeRef"
    v-bind="$attrs"
    title="部门列表"
  >
    <template #title="{ deptName }">
      <Tooltip class="!truncate text-[16px]">
        <template #title>{{ deptName }}</template>
        {{ deptName }}
      </Tooltip>
    </template>
  </BasicTree>
</template>

<script lang="ts" setup>
import { onMounted, ref, unref, watch, nextTick } from 'vue';
import { list as getDeptInfoTreeList } from '@/api/system/dept';
import { BasicTree, KeyType, TreeActionType } from '@/components/Tree';
import { useUserStore } from '@/store/modules/user';
import { Tooltip } from 'ant-design-vue';

const emit = defineEmits(['selectInfo', 'getFirstNode', 'update:reload']);

const props = defineProps({
  /**
   * 更新数据
   */
  reload: { type: Boolean },
  selectedKey: { type: Array as PropType<KeyType[]>, default: undefined },
  clickCompanyId: { type: String },
});

const userStore = useUserStore();

const treeData = ref<Recordable[]>([]);

const selectedKeys = ref<KeyType[]>([]);

const loadingTree = ref<boolean>(false);

const treeRef = ref<Nullable<TreeActionType>>(null);

function handleSelect(_, { selected, node }: Recordable) {
  emit('selectInfo', {
    name: selected ? node.deptName?.el?.innerText : undefined,
    id: selected ? node.autoId : undefined,
  });
}

async function getTreeData() {
  loadingTree.value = true;
  const { data }: Recordable[] = (await getDeptInfoTreeList({})) || [];

  treeData.value = data;

  loadingTree.value = false;
  await nextTick();
  handleLevel();
  // if (unref(treeData) && unref(treeData)[0]?.autoId) {
  //   const autoIds = [unref(treeData)[0].autoId];
  //   const id = props.selectedKey && props.selectedKey.length > 0 ? props.selectedKey : autoIds;
  //   selectedKeys.value = id;

  //   emit('getFirstNode', { id: id[0], name: unref(treeData)[0].deptName });
  // }
}

//定义tree
function getTree() {
  const tree = unref(treeRef);
  if (!tree) {
    throw new Error('tree is null!');
  }
  return tree;
}

function handleLevel() {
  getTree().expandAll(true);
}

//初始化树
onMounted(async () => {
  await getTreeData();
});

watch(
  () => props.reload,
  async () => {
    await getTreeData();
  }
);

watch(
  () => props.selectedKey,
  async val => {
    await nextTick();
    selectedKeys.value = props.selectedKey as KeyType[];
    getTree().setSelectedKeys(val || []);
  }
);
</script>

<style lang="less" module>
.user-tree {
  :global {
    .scrollbar__bar {
      display: none;
    }
    .scrollbar__wrap {
      overflow: hidden;
    }

    .ant-tree {
      overflow: auto;
      max-height: 53vh;
    }
  }
}
</style>
