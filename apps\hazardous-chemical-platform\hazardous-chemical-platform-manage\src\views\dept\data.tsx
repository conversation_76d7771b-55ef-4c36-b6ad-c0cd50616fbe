import { toNumber } from 'lodash-es';
import { BasicColumn, FormSchema } from '@/components/Table';
import { list as companyList } from '@/api/system/company';
import { AccountTypeEnum } from '@monorepo-yysz/enums';
import { useUserStore } from '@/store/modules/user';

const userStore = useUserStore();

export function columns(): BasicColumn[] {
  return [
    {
      title: '排序号',
      dataIndex: 'sort',
      align: 'left',
      class: '!text-center',
      width: 120,
    },
    {
      dataIndex: 'deptName',
      title: '部门名称',
      width: 200,
    },
    // {
    //   dataIndex: 'companyName',
    //   title: '所属单位',
    //   width: 200,
    // },
    {
      dataIndex: 'deptAccount',
      title: '部长',
      width: 200,
    },
    {
      dataIndex: 'deptDescribe',
      title: '备注',
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
      width: 150,
    },
  ];
}

export function formSchemas(): FormSchema[] {
  return [
    {
      field: 'deptName',
      label: '部门名称',
      component: 'Input',
      colProps: {
        span: 6,
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyId',
      label: '所属单位',
      colProps: { span: 6 },
      component: 'ApiTreeSelect',
      ifShow: userStore.getUserInfo.accountType === AccountTypeEnum.ADMIN,
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          allowClear: false,
          api: companyList,
          labelField: 'companyName',
          valueField: 'companyId',
          resultField: 'data',
          showSearch: true,
          treeDefaultExpandAll: true,
          filterTreeNode(input: string, option: any) {
            return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          getPopupContainer: () => document.body,
          onOptionsChange(treeData) {
            formModel['companyId'] = treeData?.[0]?.companyId;
          },
        };
      },
    },
  ];
}

export function modalFormSchema(): FormSchema[] {
  return [
    {
      field: 'deptName',
      component: 'Input',
      label: '部门名称',
      required: true,
      componentProps: {
        placeholder: '请输入部门名称',
        autocomplete: 'off',
        showCount: true,
        maxlength: 40,
      },
    },
    {
      field: 'companyId',
      label: '所属单位',
      required: true,
      colProps: { span: 24 },
      component: 'ApiTreeSelect',
      rulesMessageJoinLabel: true,
      ifShow: () => userStore.getUserInfo.accountType === AccountTypeEnum.ADMIN,
      componentProps() {
        return {
          api: companyList,
          labelField: 'companyName',
          valueField: 'companyId',
          resultField: 'data',
          params: { pageSize: 0 },
          showSearch: true,
          treeDefaultExpandAll: true,
          filterTreeNode(input: string, option: any) {
            return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          getPopupContainer: () => document.body,
        };
      },
    },
    {
      field: 'pid',
      label: '父级',
      component: 'TreeSelect',
      rulesMessageJoinLabel: true,
      show: ({ values }) => toNumber(values.pid) !== 0,
      slot: 'pid',
    },
    {
      field: 'deptAccount',
      component: 'Input',
      label: '部长',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'sort',
      label: '排序号(展示按照序号从小到大排列)',
      component: 'InputNumber',
      className: '!w-full',
      labelWidth: 240,
      componentProps: {
        min: 0,
        placeholder: '请输入排序号,展示按照序号从小到大排列',
      },
    },
    {
      field: 'deptDescribe',
      component: 'InputTextArea',
      label: '部门描述',
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 200,
      },
    },
  ];
}
