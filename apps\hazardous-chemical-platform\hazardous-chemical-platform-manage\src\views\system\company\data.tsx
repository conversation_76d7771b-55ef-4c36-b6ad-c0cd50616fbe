import { Switch } from 'ant-design-vue';
import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { validatePhone } from '@monorepo-yysz/utils';
import { companyEnableOrDisable } from '@/api/system/company';
import { useMessage } from '@monorepo-yysz/hooks';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

const dictionary = useDictionary();

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '企业名称',
      dataIndex: 'companyName',
    },
    {
      title: '地址',
      dataIndex: 'address',
    },
    {
      title: '状态',
      dataIndex: 'companyState',
      customRender: ({ value, record }) => {
        const { createConfirm, createSuccessModal, createErrorModal } = useMessage();
        const name = dictionary.getDictionaryMap.get(`state_${value}`)?.dictName;
        const color = dictionary.getDictionaryMap.get(`state_${value}`)?.remark;

        const flg = value === 'NORMAL';
        const el = (
          <div>
            <Switch
              checked-children={name}
              unCheckedChildren={name}
              checked={flg}
              class={`cursor-pointer`}
              style={{ backgroundColor: color }}
              onClick={() => {
                const stateName = value === 'NORMAL' ? '禁用' : '启用';
                const companyState = value === 'NORMAL' ? 'BAN' : 'NORMAL';
                const text = `是否${stateName}${record.companyName}`;

                createConfirm({
                  iconType: 'warning',
                  content: text,
                  onOk: () => {
                    companyEnableOrDisable({
                      companyId: record.companyId,
                      operateState: companyState === 'NORMAL',
                    }).then(({ code, message }) => {
                      if (code === 200) {
                        createSuccessModal({ content: `${stateName}成功` });
                        record.companyState = companyState;
                      } else {
                        createErrorModal({ content: `${stateName}失败，${message}` });
                      }
                    });
                  },
                });
              }}
            >
              {name}
            </Switch>
          </div>
        );
        return el;
      },
    },
    {
      title: '联系人',
      dataIndex: 'contractName',
    },
    {
      title: '联系人电话',
      dataIndex: 'contractPhone',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'companyName',
      label: '企业名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const modalFormItem = (ifUpdate: boolean): FormSchema[] => {
  return [
    {
      field: 'companyName',
      label: '企业名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },

    {
      field: 'address',
      label: '地址',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'addressCoordinate',
      label: '坐标',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'contractName',
      label: '联系人姓名',
      colProps: { span: 24 },
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'contractPhone',
      label: '联系人电话',
      colProps: { span: 24 },
      component: 'Input',
      rules: [{ required: true, validator: validatePhone }],
      rulesMessageJoinLabel: true,
    },
    {
      field: 'industryId',
      label: '所属行业',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyType',
      label: '单位类型',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'state',
      label: '状态',
      component: 'RadioButtonGroup',
      defaultValue: 'NORMAL',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('commonStatus') as RadioGroupChildOption[],
      },
    },
    {
      field: 'initManageFlag',
      label: '是否初始化管理员',
      colProps: { span: 24 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      ifShow: !ifUpdate,
      defaultValue: false,
      componentProps: {
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
    },
    {
      field: 'manageAccount',
      label: '管理员账号',
      required: true,
      colProps: { span: 24 },
      ifShow: ({ values }) => !ifUpdate && values.initManageFlag,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'managePwd',
      label: '管理员密码',
      required: true,
      colProps: { span: 24 },
      ifShow: ({ values }) => !ifUpdate && values.initManageFlag,
      component: 'InputPassword',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'manageNickname',
      label: '管理员昵称',
      colProps: { span: 24 },
      ifShow: ({ values }) => !ifUpdate && values.initManageFlag,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    // {
    //   field: 'companyIcon',
    //   label: '单位系统Icon',
    //   colProps: { span: 24 },
    //   component: 'CropperForm',
    //   rulesMessageJoinLabel: true,
    //   renderComponentContent() {
    //     return {
    //       tip: () => (
    //         <div class="text-sm leading-7">
    //           注:图标规格大小为(<span class="text-red-500">368 * 60</span>)以内
    //         </div>
    //       ),
    //     };
    //   },
    //   componentProps: function () {
    //     return {
    //       imgSize: 368 / 60,
    //       operateType: 1,
    //     };
    //   },
    // },
  ];
};
