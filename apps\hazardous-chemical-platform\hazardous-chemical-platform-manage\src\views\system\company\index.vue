<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增企业
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
              },
              // {
              //   icon: 'fluent:delete-16-filled',
              //   label: '删除',
              //   type: 'primary',
              //   danger: true,
              //   onClick: handleDelete.bind(null, record),
              // },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <CompanyModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="45%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import CompanyModal from './CompanyModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { list, view, deleteLine, saveApi, updateApi } from '@/api/system/company';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record: Recordable<any>) {
  view({ companyId: record.companyId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

//详情
function handleView(record: Recordable<any>) {
  view({ companyId: record.companyId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

// 删除
function handleDelete(record: Recordable<any>) {
  // createConfirm({
  //   iconType: 'warning',
  //   content: `请确认要删除${record.companyName}？`,
  //   onOk: function () {
  //     deleteLine(record.autoId).then(({ code, message }) => {
  //       if (code === 200) {
  //         createSuccessModal({ content: `删除成功` });
  //         reload();
  //       } else {
  //         createErrorModal({ content: `删除失败，${message}` });
  //       }
  //     });
  //   },
  // });
}

function handleSuccess({ values, isUpdate }: Recordable<any>) {
  const api = isUpdate ? updateApi : saveApi;

  api(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败！ ${message}`,
      });
    }
  });
}
</script>
