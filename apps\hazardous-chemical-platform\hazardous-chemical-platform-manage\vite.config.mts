import { defineApplicationConfig } from '@monorepo-yysz/vite-config';
import { generateModifyVars } from './build/modifyVars';
import path from 'path';
// 2.11.4
export default defineApplicationConfig({
  overrides: {
    optimizeDeps: {
      include: [
        'qrcode',
        '@iconify/iconify',
        'ant-design-vue/es/locale/zh_CN',
        'ant-design-vue/es/locale/en_US',
      ],
    },
    server: {
      host: true,
      port: 3601,
      proxy: {
        '/basic-api': {
          target: 'http://**************:32000',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/basic-api`), ''),
          // only https
          // secure: false
        },
        '/nest-api': {
          target: 'http://***************:3000',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/nest-api`), ''),
        },
        '/upload': {
          target: 'http://**************:32000',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/upload`), ''),
        },
        '/map-api': {
          target: 'https://api.map.baidu.com',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/map-api`), ''),
        },
      },
      open: false, // 项目启动后，自动打开
      warmup: {
        clientFiles: ['./index.html', './src/{views,components}/*'],
      },
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          additionalData: `@import "${path.resolve(__dirname, 'src/marsgis/components/mars-ui/base.less')}";`,
          modifyVars: generateModifyVars(),
        },
      },
    },
  },
});
