//配置项相关接口
import { defHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum Api {
  list = '/field/findVOList',
  view = '/field/getExpandInfo',
  saveByDTO = '/field/saveByDTO',
  updateByDTO = '/field/updateByDTO',
  remove = '/field/deleteById',
}

export const list = params => {
  return defHttp.get<BasicResponse>(
    {
      url: Api.list,
      params,
    },
    { isTransformResponse: false }
  );
};
export const view = params => {
  return defHttp.get<BasicResponse>(
    {
      url: Api.view,
      params,
    },
    { isTransformResponse: false }
  );
};
export const remove = id => {
  return defHttp.delete<BasicResponse>(
    {
      url: Api.remove + `?fieldId=${id}`,
    },
    { isTransformResponse: false }
  );
};
export const saveByDTO = params => {
  return defHttp.post<BasicResponse>(
    {
      url: Api.saveByDTO,
      params,
    },
    { isTransformResponse: false }
  );
};
export const updateByDTO = params => {
  return defHttp.post<BasicResponse>(
    {
      url: Api.updateByDTO,
      params,
    },
    { isTransformResponse: false }
  );
};

