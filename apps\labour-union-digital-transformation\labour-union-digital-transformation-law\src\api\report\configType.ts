//配置项类型相关接口
import { defHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum Api {
  list = '/fieldCategory/findVOList',
  view = '/fieldCategory/selectInfoById',
  saveByDTO = '/fieldCategory/saveByDTO',
  updateByDTO = '/fieldCategory/updateByDTO',
  remove = '/fieldCategory/deleteById',
  enableOrDisable = '/fieldCategory/enableOrDisable',
  findList = '/fieldCategory/findList'
}

export const list = params => {
  return defHttp.get<BasicResponse>(
    {
      url: Api.list,
      params,
    },
    { isTransformResponse: false }
  );
};
export const view = params => {
  return defHttp.get<BasicResponse>(
    {
      url: Api.view,
      params,
    },
    { isTransformResponse: false }
  );
};
export const remove = id => {
  return defHttp.delete<BasicResponse>(
    {
      url: Api.remove + `?fieldCategoryId=${id}`,
    },
    { isTransformResponse: false }
  );
};
export const saveByDTO = params => {
  return defHttp.post<BasicResponse>(
    {
      url: Api.saveByDTO,
      params,
    },
    { isTransformResponse: false }
  );
};
export const updateByDTO = params => {
  return defHttp.post<BasicResponse>(
    {
      url: Api.updateByDTO,
      params,
    },
    { isTransformResponse: false }
  );
};
export const enableOrDisable = params => {
  return defHttp.post<BasicResponse>(
    {
      url: Api.enableOrDisable,
      params,
    },
    { isTransformResponse: false }
  );
};

export const findList = params => {
  return defHttp.get<BasicResponse>(
    {
      url: Api.findList,
      params,
    },
    { isTransformResponse: false }
  );
};