//信息填报相关接口
import { defHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum Api {
  list = '/submit/findVOList',
  recordList = '/submit/findList',
  view = '/submit/selectById',
  saveByDTO = '/submit/saveByDTO',
  updateByDTO = '/submit/updateByDTO',
  remove = '/submit/deleteById',
  reporting = '/submit/reporting',
  submitExport = '/submit/submitExport',
}

export const list = params => {
  return defHttp.get<BasicResponse>(
    {
      url: Api.list,
      params,
    },
    { isTransformResponse: false }
  );
};
export const recordList = params => {
  return defHttp.get<BasicResponse>(
    {
      url: Api.recordList,
      params,
    },
    { isTransformResponse: false }
  );
};
export const view = params => {
  return defHttp.get<BasicResponse>(
    {
      url: Api.view,
      params,
    },
    { isTransformResponse: false }
  );
};
export const remove = id => {
  return defHttp.delete<BasicResponse>(
    {
      url: Api.remove + `?submitId=${id}`,
    },
    { isTransformResponse: false }
  );
};
export const saveByDTO = params => {
  return defHttp.post<BasicResponse>(
    {
      url: Api.saveByDTO,
      params,
    },
    { isTransformResponse: false }
  );
};
export const updateByDTO = params => {
  return defHttp.post<BasicResponse>(
    {
      url: Api.updateByDTO,
      params,
    },
    { isTransformResponse: false }
  );
};
export const reporting = params => {
  return defHttp.post<BasicResponse>(
    {
      url: Api.reporting,
      params,
    },
    { isTransformResponse: false }
  );
};
export const submitExport = params => {
  return defHttp.get<BasicResponse>(
    {
      url: Api.submitExport,
      params,
      responseType: 'blob'
    },
    { isReturnNativeResponse: true }
  );
};

