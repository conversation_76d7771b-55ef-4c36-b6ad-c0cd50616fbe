import { UploadApiResult } from './model/uploadModel';
import { defHttp, otherHttp, fileHttp } from '@/utils/http/axios';
import { UploadFileParams } from '#/axios';
import { AxiosProgressEvent } from 'axios';

/**
 * @description: Upload interface
 */
export function uploadApi(
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void
) {
  return fileHttp.uploadFile<UploadApiResult>(
    {
      url: '/v2/uploadFileFormData',
      onUploadProgress,
    },
    params
  );
}

export function download(params) {
  return otherHttp.post<any>(
    {
      url: '/file/minio/download',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}
