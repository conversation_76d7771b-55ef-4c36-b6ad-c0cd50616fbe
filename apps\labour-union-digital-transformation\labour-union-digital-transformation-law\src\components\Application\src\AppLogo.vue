<!--
 * @Description: logo component
-->
<template>
  <div
    class="anticon"
    :class="getAppLogoClass"
    @click="goHome"
  >
    <img class="w-45px h-45px" src="@/assets/images/logo.png" />
    <div
      class="ml-2 truncate md:opacity-100"
      :class="getTitleClass"
      v-show="showNextTitle"
      :title="topTitle"
    >
      四川省总工会—— 法律工作平台
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { useGo } from '@/hooks/web/usePage';

import { useDesign } from '@monorepo-yysz/hooks';
import { PageEnum } from '@/enums/pageEnum';
import { useUserStore } from '@/store/modules/user';

const props = defineProps({
  /**
   * The theme of the current parent component
   */
  theme: { type: String, validator: (v: string) => ['light', 'dark'].includes(v) },
  /**
   * Whether to show title
   */
  showTitle: { type: Boolean, default: true },
  /**
   * The title is also displayed when the menu is collapsed
   */
  alwaysShowTitle: { type: Boolean },
});

const { prefixCls } = useDesign('app-logo');

// const { getCollapsedShowTitle } = useMenuSetting();
const userStore = useUserStore();
const go = useGo();

const getAppLogoClass = computed(() => [
  prefixCls,
  props.theme,
  // { 'collapsed-show-title': unref(getCollapsedShowTitle) },
]);

const getTitleClass = computed(() => [
  `${prefixCls}__title`,
  {
    'xs:opacity-0': !props.alwaysShowTitle,
  },
]);

const topTitle = computed(() => {
  return ` ${userStore.getUserInfo.companyName || ''}`;
});

const showNextTitle = computed(() => {
  return (
    props.showTitle && useUserStore().getUserInfo.companyId !== '6650f8e054af46e7a415be50597a99d5'
  );
});

function goHome() {
  go(userStore.getUserInfo.homePath || PageEnum.BASE_HOME);
}
</script>
<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-app-logo';

.@{prefix-cls} {
  display: flex;
  align-items: center;
  padding-left: 7px;
  cursor: pointer;
  transition: all 0.2s ease;

  &.light {
    border-bottom: 1px solid @border-color-base;
  }

  &.collapsed-show-title {
    padding-left: 20px;
  }

  &.light &__title {
    color: @primary-color;
  }

  &.dark &__title {
    color: @white;
  }

  &__title {
    font-size: 18px;
    font-weight: 500;
    transition: all 0.5s;
    line-height: normal;
    color: #ffffff !important;
    font-family: Source Han Sans CN;
  }
}
</style>
