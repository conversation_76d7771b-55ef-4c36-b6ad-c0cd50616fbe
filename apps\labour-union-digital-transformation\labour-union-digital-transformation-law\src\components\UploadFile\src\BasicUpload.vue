<template>
  <div class="!w-full">
    <Upload v-model:file-list="fileList" :accept="getStringAccept" :multiple="!!maxNumber" :before-upload="beforeUpload"
      class="upload-modal-toolbar__btn">
      <div class="btns">
        <a-button type="primary" preIcon="carbon:cloud-upload" :disabled="disabled" v-if="!hideUpload">
          {{ t('component.upload.upload') }}
        </a-button>
        <span class="tips">*{{ getHelpText }}</span>
      </div>
    </Upload>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, unref, computed, useAttrs, toRefs } from 'vue';
import { Recordable } from '@monorepo-yysz/types';
import { Icon } from '@monorepo-yysz/ui';
import { Tooltip, Space, Upload } from 'ant-design-vue';
import { useModal } from '@/components/Modal';
import { omit } from 'lodash-es';
import { useI18n } from '@/hooks/web/useI18n';
import { buildUUID, isArray } from '@monorepo-yysz/utils';
import { uploadContainerProps } from './props.ts';
import { useUploadType } from './hooks/useUpload';
import { useMessage } from '@monorepo-yysz/hooks';
import { checkImgType, getBase64WithFile } from '@/components/Upload/src/helper';
import { FileItem } from '@/components/Upload/src/types/typing';

defineOptions({ name: 'BasicUpload' });

const props = defineProps(uploadContainerProps);
const emit = defineEmits(['change', 'delete', 'preview-delete', 'update:value']);
const { createMessage } = useMessage();
const attrs = useAttrs();
const { t } = useI18n();


const fileList = ref([]);
const fileListRef = ref<FileItem[]>([]);
const { accept, helpText, maxNumber, maxSize } = toRefs(props);
const { getStringAccept, getHelpText } = useUploadType({
  acceptRef: accept,
  helpTextRef: helpText,
  maxNumberRef: maxNumber,
  maxSizeRef: maxSize,
});

// 上传前校验
function beforeUpload(file: File) {
  const { size, name } = file;
  const { maxSize } = props;

  // 设置最大值，则判断
  if (maxSize && file.size / 1024 / 1024 >= maxSize) {
    createMessage.error(t('component.upload.maxSizeMultiple', [maxSize]));
    return false;
  }

  const commonItem = {
    uuid: buildUUID(),
    file,
    size,
    name,
    percent: 0,
    type: name.split('.').pop(),
  };
  // 生成图片缩略图
  if (checkImgType(file)) {
    // beforeUpload，如果异步会调用自带上传方法
    // file.thumbUrl = await getBase64(file);
    getBase64WithFile(file).then(({ result: thumbUrl }) => {
      fileListRef.value = [
        ...unref(fileListRef),
        {
          thumbUrl,
          ...commonItem,
        },
      ];
    });
  } else {
    fileListRef.value = [...unref(fileListRef), commonItem];
  }
  return false;
}

</script>
<style lang="less" scoped>
.border {
  border: 1px solid red;
}

.upload-modal-toolbar__btn {
  width: 100%;

  display: inline-block;
  text-align: left;

  :deep(.ant-upload) {
    width: 100%;
    border: 1px solid blue;

    .btns {}
  }

}
</style>
