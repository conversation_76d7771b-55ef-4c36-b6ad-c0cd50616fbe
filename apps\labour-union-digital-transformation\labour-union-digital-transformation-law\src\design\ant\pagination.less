html[data-theme='dark'] {
  .ant-pagination {
    &.mini {
      .ant-pagination-prev,
      .ant-pagination-next,
      .ant-pagination-item {
        background-color: rgb(255 255 255 / 4%) !important;

        a {
          color: #8b949e !important;
        }
      }

      .ant-select-arrow {
        color: @text-color-secondary !important;
      }

      .ant-pagination-item-active {
        border: none;
        border-radius: none !important;
        background-color: @primary-color !important;

        a {
          color: @white !important;
        }
      }
    }
  }
}

.ant-pagination {
  &.mini {
    .ant-pagination-prev,
    .ant-pagination-next {
      border: 1px solid;
      color: @text-color-base;
      font-size: 12px;
    }

    .ant-pagination-prev:hover,
    .ant-pagination-next:hover,
    .ant-pagination-item:focus,
    .ant-pagination-item:hover {
      a {
        color: @primary-color;
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next,
    .ant-pagination-item {
      margin: 0 4px !important;
      border: none;
      border-radius: none !important;
      background-color: #f4f4f5 !important;

      a {
        margin-top: 1px;
        color: #606266;
      }

      &:last-child {
        margin-right: 0 !important;
      }
    }

    .ant-pagination-item-active {
      border: none;
      border-radius: none !important;
      background-color: @primary-color !important;

      a {
        color: @white !important;
      }
    }

    .ant-pagination-options {
      margin-left: 12px;
    }

    .ant-pagination-options-quick-jumper input {
      height: 22px;
      margin: 0 6px;
      line-height: 22px;
      text-align: center;
    }

    .ant-select-arrow {
      color: @border-color-shallow-dark;
    }
  }

  &-disabled {
    display: none !important;
  }
}
