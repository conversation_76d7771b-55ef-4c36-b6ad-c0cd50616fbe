@import 'color.less';
@import 'transition/index.less';
@import 'var/index.less';
@import 'public.less';
@import 'ant/index.less';
@import 'theme.less';
@import 'entry.css';
@import 'font.css';

input:-webkit-autofill {
  box-shadow: 0 0 0 1000px transparent inset;
  -webkit-text-fill-color: @text-color-base;
}

:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s !important;
}

html {
  overflow: hidden;
  text-size-adjust: 100%;
}

html,
body {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  &.color-weak {
    filter: invert(80%);
  }

  &.gray-mode {
    filter: grayscale(100%);
    filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
  }

  * {
    user-select: text !important;
  }
}

a:focus,
a:active,
button,
div,
svg,
span {
  outline: none;
}

// 保持 和 windi 一样的全局样式，减少升级带来的影响
ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
