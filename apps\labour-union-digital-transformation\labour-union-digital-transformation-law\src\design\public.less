@prefix-table-action: ~'@{namespace}-basic-table-action';

#app {
  width: 100%;
  height: 100%;
}

// =================================
// ==============scrollbar==========
// =================================

::-webkit-scrollbar {
  width: 7px;
  height: 8px;
}

// ::-webkit-scrollbar-track {
//   background: transparent;
// }

::-webkit-scrollbar-track {
  background-color: rgb(0 0 0 / 5%);
}

::-webkit-scrollbar-thumb {
  // background-color: rgba(144, 147, 153, 0.3);
  border-radius: 2px;
  // background: rgba(0, 0, 0, 0.6);
  background-color: rgb(144 147 153 / 30%);
  box-shadow: inset 0 0 6px rgb(0 0 0 / 20%);
}

::-webkit-scrollbar-thumb:hover {
  background-color: @border-color-dark;
}

::-webkit-scrollbar-corner {
  background-color: transparent;
}

// =================================
// ==============nprogress==========
// =================================
#nprogress {
  pointer-events: none;

  .bar {
    position: fixed;
    z-index: 99999;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    opacity: 0.75;
    background-color: @primary-color;
  }
}

.back-transparent {
  .ant-input-affix-wrapper-disabled,
  .ant-input-number-affix-wrapper-disabled,
  .ant-input-number-disabled,
  .ant-input-affix-wrapper,
  .ant-picker-disabled,
  .ant-select-disabled,
  .ant-input-disabled,
  .ant-input-group-addon {
    background: transparent !important;
    cursor: auto !important;
    color: @text-color-call-out !important;
    border: transparent !important;

    textarea {
      resize: none !important;
    }

    div,
    input,
    .ant-select-selector,
    textarea,
    .ant-collapse-item-disabled {
      cursor: auto !important;
      color: @text-color-call-out !important;
      background: transparent !important;
      border: transparent !important;
    }

    .ant-select-arrow,
    .ant-picker-suffix,
    .ant-input-show-count-suffix {
      display: none;
    }

    .ant-picker-range-separator,
    .ant-picker-separator {
      cursor: auto !important;
    }
  }

  .ant-select-selection-item {
    cursor: auto !important;
    color: @text-color-call-out !important;
    background: transparent !important;
  }

  .ant-form-item {
    // margin: 0 5px 16px !important;
    height: 100% !important;

    .ant-form-item-label {
      display: flex;
      justify-content: center;
      align-items: center;
      // margin: 12px 0;

      .ant-form-item-required {
        &::before {
          content: '' !important;
        }
      }
    }

    .ant-form-item-control {
      display: flex;
      justify-content: center;

      .ant-input-textarea-show-count {
        &::after {
          content: '' !important;
        }
      }

      .ant-radio-group {
        label:not(.ant-radio-wrapper-checked) {
          display: none;
        }

        .ant-radio-wrapper-checked {
          cursor: auto !important;

          .ant-radio-checked {
            display: none;
          }

          span {
            color: @text-color-call-out !important;
            cursor: auto !important;
          }
        }
      }

      span {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .ant-form-item-explain-error,
    .ant-select-selection-placeholder {
      display: none !important;
    }

    /* stylelint-disable-next-line selector-no-vendor-prefix */
    input::-webkit-input-placeholder {
      /* WebKit browsers */
      color: transparent !important;
    }

    /* stylelint-disable-next-line selector-no-vendor-prefix */
    input:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: transparent !important;
    }

    /* stylelint-disable-next-line selector-no-vendor-prefix */
    input::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: transparent !important;
    }

    /* stylelint-disable-next-line selector-no-vendor-prefix */
    input:-ms-input-placeholder {
      /* Internet Explorer 10+ */
      color: transparent !important;
    }

    //upload
    .ant-space {
      .ant-space-item {
        button {
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
        }
      }
    }

    //富文本

    .tox-menubar,
    .tox-toolbar-overlord,
    .lt-tinymce-img-upload {
      display: none !important;
    }

    .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-checked) {
      display: none !important;
    }

    .ant-checkbox-wrapper-checked {
      span {
        color: @text-color-call-out !important;
      }

      .ant-checkbox {
        display: none !important;
      }
    }
  }
}

.default-tree-class {
  flex-direction: column;

  .default-tree-search {
    width: 100%;
  }

  .default-tree-title {
    @apply font-semibold;
  }
}

.tree-info {
  border-right: 1px solid #dbdbdb;
}

.deal-action {
  width: 100% !important;

  .@{prefix-table-action} {
    button {
      position: relative;
      margin: 5px 0 5px 0;
    }
  }
}

.full-modal-self {
  overflow-y: auto;

  .ant-modal {
    top: 60px !important;
    height: calc(100vh - 60px);

    .ant-modal-content {
      height: 100%;

      .ant-modal-footer {
        padding: 10px !important;
      }
    }
  }

  .scroll-container {
    padding: 10px !important;

    .scrollbar__view {
      overflow: hidden;

      & > div {
        max-height: 740px !important;
      }

      .ant-input-number,
      .ant-input-number-group-wrapper {
        width: 100%;
      }
    }
  }

  .ant-modal-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
  }
}

.basic-border {
  border: 1px solid @form-border-color;
}

/* 添加其他字体格式和路径（如 woff、woff2）以增强兼容性 */
@font-face {
  font-family: 'Source Han Sans CN';
  src: url('/resource/path/SOURCEHANSANSCN-NORMAL.OTF') format('truetype');
}

@font-face {
  font-family: 'Source Han Sans CN MEDIUM';
  src: url('/resource/path/SOURCEHANSANSCN-MEDIUM.OTF') format('truetype');
}
