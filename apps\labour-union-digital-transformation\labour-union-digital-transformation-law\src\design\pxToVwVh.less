// 默认宽度
@design-width:1920;
@design-height:1080;

.px-to-vw(@property, @px, @base-width: @design-width) {
    @{property}: calc( (@px / @base-width) * 100vw ) !important;
}

// px转vh混入
.px-to-vh(@property, @px, @base-height: @design-height) {
  @{property}: calc( (@px / @base-height) * 100vh ) !important;
}

// 动态循环生成margin和padding
@directions: top, right, bottom, left;
// 生成 margin/padding 通用循环函数
.generate-classes(@type, @property) {
  .loop(@i: 1) when (@i <= 100) {
    .@{type}-@{i} when ((@property=height) or (@property = top) or (@property=bottom)){
        @{property}: calc((@i / @design-height) * 100vh );
    }
    .@{type}-@{i} when not ((@property=height) or (@property = top) or (@property=bottom)) {
      @{property}: calc((@i / @design-width) * 100vw );
    }

    // 判断是否 margin/padding才循环方向
    * when ((@property = margin) or (@property = padding)){
        each(@directions, {
            @dir: @value;
            // less 判断方向是不是top或者bottom，如果是则使用vh单位
            .@{type}-@{dir}-@{i} when ((@dir = top) or (@dir = bottom)) {
                @{property}-@{dir}: calc((@i / @design-height) * 100vh ) !important;
            }
            // 其余方向使用vw单位
            .@{type}-@{dir}-@{i} when not ((@dir = top) or (@dir = bottom)) {
                @{property}-@{dir}: calc((@i / @design-width) * 100vw ) !important;
            }
        })        
    }
    .loop(@i + 1); // 递归调用
  }
  .loop(1); // 启动循环
}

// 调用生成器
.generate-classes(m, margin);
.generate-classes(p, padding);
.generate-classes(fontSize, font-size);
.generate-classes(height, height);
.generate-classes(width, width);
.generate-classes(top, top);
.generate-classes(bottom, bottom);
.generate-classes(left, left);
.generate-classes(right, right);

