import { computed, createVNode, onUnmounted, unref, watchEffect } from 'vue';
import { useThrottleFn } from '@vueuse/core';
import { useAppStore } from '@/store/modules/app';
import { useLockStore } from '@/store/modules/lock';
import { useUserStore } from '@/store/modules/user';
import { useRootSetting } from '../setting/useRootSetting';
import { Modal } from 'ant-design-vue';
import { CloseCircleFilled } from '@ant-design/icons-vue';
import { useGlobSetting } from '../setting';
import { PageEnum } from '@/enums/pageEnum';
import { useRouter } from 'vue-router';

export function useLockPage() {
  const { getLockTime, getTokenTime } = useRootSetting();
  const lockStore = useLockStore();
  const userStore = useUserStore();
  const appStore = useAppStore();
  const { main_login, ifLogin } = useGlobSetting();
  const router = useRouter();

  let timeId: TimeoutHandle;

  function clear(): void {
    window.clearTimeout(timeId);
  }

  function resetCalcLockTimeout(): void {
    // not login
    if (!userStore.getToken) {
      clear();
      return;
    }
    const lockTime = appStore.getProjectConfig.lockTime;
    if (!lockTime || lockTime < 1) {
      clear();
      return;
    }
    clear();

    timeId = setTimeout(
      () => {
        lockPage();
      },
      lockTime * 60 * 1000
    );
  }

  function resetTokenTimeout(): void {
    if (!userStore.getToken) {
      clear();
      return;
    }

    const tokenTime = appStore.getProjectConfig.tokenTime;

    if (!tokenTime || tokenTime < 0) {
      clear();
      return;
    }
    clear();

    timeId = setTimeout(
      async () => {
        reLogin();
        userStore.resetState();
      },
      tokenTime * 60 * 1000
    );
  }

  function lockPage(): void {
    lockStore.setLockInfo({
      isLock: true,
      pwd: undefined,
    });
  }

  function reLogin(): void {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '长时间未操作，请重新登录',
      okText: '确认',
      onOk: () => {
        if (ifLogin === '1') {
          window.open(main_login, '_self'); //工会登录页
        } else {
          router.push(PageEnum.BASE_LOGIN);
        }
      },
      closable: false,
      keyboard: false,
    });
  }

  watchEffect(onClean => {
    if (userStore.getToken) {
      resetCalcLockTimeout();
      resetTokenTimeout();
    } else {
      clear();
    }
    onClean(() => {
      clear();
    });
  });

  onUnmounted(() => {
    clear();
  });

  const keyupFn = useThrottleFn(resetCalcLockTimeout, 2000);
  const keyupFnToken = useThrottleFn(resetTokenTimeout, 2000);

  return computed(() => {
    if (unref(getLockTime)) {
      return { onKeyup: keyupFn, onMousemove: keyupFn };
    } else if (unref(getTokenTime)) {
      return { onKeyup: keyupFnToken, onMousemove: keyupFnToken };
    } else {
      clear();
      return {};
    }
  });
}
