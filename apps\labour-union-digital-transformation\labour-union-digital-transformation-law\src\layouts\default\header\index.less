@header-trigger-prefix-cls: ~'@{namespace}-layout-header-trigger';
@header-prefix-cls: ~'@{namespace}-layout-header';
@breadcrumb-prefix-cls: ~'@{namespace}-layout-breadcrumb';
@logo-prefix-cls: ~'@{namespace}-app-logo';

.ant-layout .@{header-prefix-cls} {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: @header-height;
  // margin-left: -1px;
  padding: 0;
  background-color: @white;
  color: @white;
  line-height: @header-height;

  &--mobile {
    .@{breadcrumb-prefix-cls},
    .error-action,
    .notify-item,
    .fullscreen-item {
      display: none;
    }

    .@{logo-prefix-cls} {
      min-width: unset;
      padding-right: 0;

      &__title {
        display: none;
      }
    }
    .@{header-trigger-prefix-cls} {
      padding: 0 4px 0 8px !important;
    }
    .@{header-prefix-cls}-action {
      padding-right: 4px;
    }
  }

  &--fixed {
    position: fixed;
    z-index: @layout-header-fixed-z-index;
    top: 0;
    left: 0;
    width: 100%;
  }

  &-logo {
    min-width: 200px;
    height: @header-height;
    padding: 0 10px;
    font-size: 14px;

    img {
      // width: @logo-width;
      // height: @logo-width;
      // margin-right: 2px;
    }
  }

  &-left {
    display: flex;
    align-items: center;
    height: 100%;

    .@{header-trigger-prefix-cls} {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 1px 10px 0;
      cursor: pointer;

      .anticon {
        font-size: 16px;
      }

      &.light {
        &:hover {
          background-color: @header-light-bg-hover-color;
        }

        svg {
          fill: #000;
        }
      }

      &.dark {
        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }

  &-menu {
    flex: 1;
    align-items: center;
    min-width: 0;
    height: 100%;
  }

  &-action {
    display: flex;
    // padding-right: 12px;
    align-items: center;
    // min-width: 180px;

    &__item {
      display: flex !important;
      align-items: center;
      height: @header-height;
      padding: 0 2px;
      font-size: 1.2em;
      cursor: pointer;

      .ant-badge {
        height: @header-height;
        line-height: @header-height;
      }

      .ant-badge-dot {
        top: 14px;
        right: 2px;
      }
    }

    span[role='img'] {
      padding: 0 8px;
    }
  }

  &--light {
    border-bottom: 1px solid @header-light-bottom-border-color;
    // border-left: 1px solid @header-light-bottom-border-color;
    background-color: @white !important;

    .@{header-prefix-cls}-logo {
      color: @text-color-base;

      &:hover {
        background-color: @header-light-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        color: @text-color-base;

        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        &:hover {
          background-color: @header-light-bg-hover-color;
        }
      }

      &-icon,
      span[role='img'] {
        color: @text-color-base;
      }
    }
  }

  &--dark {
    // border-bottom: 1px solid @border-color-base;
    // border-left: 1px solid @border-color-base;
    background-color: @header-dark-bg-color !important;
    .@{header-prefix-cls}-logo {
      &:hover {
        background-color: @header-dark-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        .ant-badge {
          span {
            color: @white;
          }
        }

        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }
}
