<template>
  <div class="flex justify-start items-center w-full h-full flex-col pt-30">
    <div class="flex flex-col justify-center items-center">
      <img
        :src="getUserInfo.avatar"
        class="w-[50px] h-[50px]"
      />
      <div class="text-[#fff] text-[16px]">
        <span class="truncate">
          {{ getUserInfo.account || '' }}
        </span>
      </div>
      <div class="cursor-pointer text-[#183a66]">
        <!-- 编辑
        <Icon
          icon="ion:caret-down"
          class="text-[#00DFFF] ml-1"
        /> -->
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user';
import { computed } from 'vue';
import headerImg from '@/assets/images/pic.png';
import { Icon } from '@monorepo-yysz/ui';

const userStore = useUserStore();

const getUserInfo = computed(() => {
  const { account = '', avatar, desc } = userStore.getUserInfo || {};
  return { account, avatar: avatar || headerImg, desc };
});
</script>
