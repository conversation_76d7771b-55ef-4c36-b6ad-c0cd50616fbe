import type { Router, RouteRecordRaw } from 'vue-router';
import { usePermissionStoreWithOut } from '@/store/modules/permission';
import { PageEnum } from '@/enums/pageEnum';
import { useUserStoreWithOut } from '@/store/modules/user';
import { PAGE_NOT_FOUND_ROUTE } from '@/router/routes/basic';
import { RootRoute } from '@/router/routes';
import { useGlobSetting } from '@/hooks/setting';
import { adminLoginBack } from '.';
import { isDevMode } from '@/utils/env';

const LOGIN_PATH = PageEnum.BASE_LOGIN;

const ROOT_PATH = RootRoute.path;

const whitePathList: PageEnum[] = [];

export function createPermissionGuard(router: Router) {
  const userStore = useUserStoreWithOut();
  const permissionStore = usePermissionStoreWithOut();

  const { ifLogin } = useGlobSetting();

  router.beforeEach(async (to, from, next) => {
    if (
      from.path === ROOT_PATH &&
      to.path === PageEnum.BASE_HOME &&
      userStore.getUserInfo.homePath &&
      userStore.getUserInfo.homePath !== PageEnum.BASE_HOME
    ) {
      next(userStore.getUserInfo.homePath);
      return;
    }
    let token;
    if (isDevMode()) {//开发模式 直接登录
      token = 'JTdiJTIydHlwJTIyJTNhJTIySldUJTIyJTJjJTIyYWxnJTIyJTNhJTIySFMyNTYlMjIlN2Q=.JTdiJTIydW4lMjIlM2ElMjIlZTUlOWIlOWIlZTUlYjclOWQlZTclOWMlODElZTYlODAlYmIlZTUlYjclYTUlZTQlYmMlOWElMjIlMmMlMjJ1aWQlMjIlM2ElMjIwNDNhYzBkODIxZjM0MTUzOGI1MTA2NDFiY2MxODdlNiUyMiUyYyUyMnVhaWQlMjIlM2ElMjJiM2Y2NWFmZTUzNGY0MjI5YjU1ZmQ3MmFiZWI4YzMzZCUyMiUyYyUyMnVzZXJpZCUyMiUzYSUyMiUyMiUyYyUyMnBzJTIyJTNhJTIyYTRmNmU3Y2Y0MzU3NGJlNjhhODI0YjEwNTE2YzI4YzUlMjIlMmMlMjJuayUyMiUzYSUyMiVlNSU5MSVhOCVlOSU5NCVhZSUyMiUyYyUyMmR0JTIyJTNhJTIyMjAyMy0wOS0yMysxNiUzYTAxJTNhNDAlMjIlMmMlMjJ1cmwlMjIlM2ElMjIlMjIlMmMlMjJpc2FkbWluJTIyJTNhZmFsc2UlMmMlMjJsYSUyMiUzYSUyMjE4ODAyODAzMzY5JTIyJTJjJTIyYW4lMjIlM2ElMjIlZTUlOWIlOWIlZTUlYjclOWQlZTclOWMlODElMjIlMmMlMjJhYyUyMiUzYSUyMjUxMDAwMCUyMiUyYyUyMmNzJTIyJTNhZmFsc2UlMmMlMjJtaWQlMjIlM2ElMjIlMjIlMmMlMjJtciUyMiUzYSUyMiUyMiUyYyUyMnN1YiUyMiUzYSUyMmIzZjY1YWZlNTM0ZjQyMjliNTVmZDcyYWJlYjhjMzNkJTIyJTdk.53a3a4c8895240dfb12dffb5753c8056ede51b3936973b0efbff1ceb2b0fa815';
      // token = "JTdiJTIydHlwJTIyJTNhJTIySldUJTIyJTJjJTIyYWxnJTIyJTNhJTIySFMyNTYlMjIlN2Q=.JTdiJTIydW4lMjIlM2ElMjIlZTUlOWIlOWIlZTUlYjclOWQlZTclOWMlODElZTYlODAlYmIlZTUlYjclYTUlZTQlYmMlOWElMjIlMmMlMjJ1aWQlMjIlM2ElMjIwNDNhYzBkODIxZjM0MTUzOGI1MTA2NDFiY2MxODdlNiUyMiUyYyUyMnVhaWQlMjIlM2ElMjI0MDk3ZmE4OC1mNDM1LTRmMWItOTdiNi0yNzJiNDdjZDE0M2MlMjIlMmMlMjJ1c2VyaWQlMjIlM2ElMjIlMjIlMmMlMjJwcyUyMiUzYSUyMmE0ZjZlN2NmNDM1NzRiZTY4YTgyNGIxMDUxNmMyOGM1JTIyJTJjJTIybmslMjIlM2ElMjJzY3MuLi56emglMjIlMmMlMjJkdCUyMiUzYSUyMjIwMjUtMDctMTErMDklM2ExNSUzYTI3JTIyJTJjJTIydXJsJTIyJTNhJTIyJTIyJTJjJTIyaXNhZG1pbiUyMiUzYXRydWUlMmMlMjJsYSUyMiUzYSUyMnNjc3pnaHp6aCUyMiUyYyUyMmFuJTIyJTNhJTIyJWU1JTliJTliJWU1JWI3JTlkJWU3JTljJTgxJTIyJTJjJTIyYWMlMjIlM2ElMjI1MTAwMDAlMjIlMmMlMjJjcyUyMiUzYWZhbHNlJTJjJTIybWlkJTIyJTNhJTIyOTAwYjVjOTgtNTdkZi00ZjRlLWExNWQtOTAyZTM1NzkzODA0JTIyJTJjJTIybXIlMjIlM2ElMjJBZGQlMmNFZGl0JTJjRGVsZXRlJTJjVmlld0RldGlhbCUyMiUyYyUyMmNpZCUyMiUzYSUyMiUyMiUyYyUyMnN1YiUyMiUzYSUyMjQwOTdmYTg4LWY0MzUtNGYxYi05N2I2LTI3MmI0N2NkMTQzYyUyMiU3ZA==.c7fbd0922f480496e3b5d96c179bc47b0820fbcf1628241c130ad4e32ad8108c"
    } else {
      token = to.query?.token;
    }

    // 检验是省总跳过来的路由是否携带token，如果有就走token解析用户流程 没有就重新登录
    if (!token) {
      // You can access without permission. You need to set the routing meta.ignoreAuth to true您可以在未经许可的情况下访问。您需要将路由meta.ignoreAuth设置为true
      if (to.meta.ignoreAuth) {
        next();
        return;
      }

      //首先没有token 校验是否是省总过来 有没有用户名称 校验用户名称是否在管理平台有效 有效生成token 跳转到首页 无效跳转登录省总
      let path = PageEnum.BASE_LOGIN;

      if (ifLogin === '1') {
        path = PageEnum.BASE_HOME;

        const userInfo = await adminLoginBack(to);
        console.log('没有token', userInfo);
        if (!userInfo) {
          // 返回
          userStore.dymLogout(true);
          // next();
          return;
        }
      }
      const redirectData: { path: string; replace: boolean; query?: Recordable<string> } = {
        path,
        replace: true,
      };
      if (to.path) {
        redirectData.query = {
          ...redirectData.query,
          redirect: to.path,
        };
      }
      next(redirectData);
      // next();
      return;
    } else {//解析用户信息
      const userInfo = await adminLoginBack(to);//登录
      next();
    }

    // get userinfo while last fetch time is empty
    if (userStore.getLastUpdateTime === 0) {
      try {
        await userStore.getUserInfoAction();
      } catch (err) {
        next();
        return;
      }
    }

    // 动态路由加载（首次）
    if (!permissionStore.getIsDynamicAddedRoute) {

      const routes = await permissionStore.buildRoutesAction();
      [...routes, PAGE_NOT_FOUND_ROUTE].forEach(route => {
        router.addRoute(route as unknown as RouteRecordRaw);
      });
      // 记录动态路由加载完成
      permissionStore.setDynamicAddedRoute(true);

      // 现在的to动态路由加载之前的，可能为PAGE_NOT_FOUND_ROUTE（例如，登陆后，刷新的时候）
      // 此处应当重定向到fullPath，否则会加载404页面内容
      // next({ path: to.fullPath, replace: true, query: to.query });
      next();
      return;
    }

    if (to.name === PAGE_NOT_FOUND_ROUTE.name) {
      // 遇到不存在页面，后续逻辑不再处理redirect（阻止下面else逻辑）
      from.query.redirect = '';

      if (
        from.path === LOGIN_PATH &&
        to.fullPath !== (userStore.getUserInfo.homePath || PageEnum.BASE_HOME)
      ) {
        // 登陆重定向不存在路由，转去“首页”
        next({ path: userStore.getUserInfo.homePath || PageEnum.BASE_HOME, replace: true });
      } else {
        // 正常前往“404”页面
        next();
      }
    } else if (from.query.redirect) {
      // 存在redirect
      const redirect = decodeURIComponent((from.query.redirect as string) || '');

      // 只处理一次 from.query.redirect
      // 也避免某场景（指向路由定义了 redirect）下的死循环
      from.query.redirect = '';

      if (redirect === to.fullPath) {
        // 已经被redirect
        next();
      } else {
        // 指向redirect
        next({ path: redirect, replace: true });
      }
    } else if (to.path === from.path && to.query.redirect === from.path) {
      // 自己跳自己并且重定向还是自己的直接调from
      next(from.path);
    } else {
      // 正常访问
      next();
    }
  });
}
