import type { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';

const dashboard: AppRouteModule = {
  path: '/dashboard',
  name: 'Dashboard',
  component: LAYOUT,
  redirect: '/dashboard/analysis',
  meta: {
    orderNo: 10,
    // icon: 'ion:grid-outline',
    title: '首页',
    hideChildrenInMenu: true,
    dynamicLevel: 1,
  },
  children: [
    {
      path: 'analysis',
      name: 'Analysis',
      component: () => import('@/views/dashboard/analysis/index.vue'),
      meta: {
        affix: true,
        title: '首页',
        dynamicLevel: 1,
        hideMenu: false,
        hideBreadcrumb: true,
      },
    },
  ],
};

export default dashboard;
