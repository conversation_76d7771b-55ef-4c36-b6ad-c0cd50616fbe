import { AppRouteRecordRaw } from '@/router/types';
import { LAYOUT } from '../../constant'
import { isDevMode } from '@/utils/env';

const obj: AppRouteRecordRaw = {
  path: '/statisticsReport',
  name: 'StatisticsReport',
  component: LAYOUT,
  meta: {
    title: '统计报送',
  },
  children: [
    {
      path: 'reportConfig',
      name: 'ReportConfig',
      component: () => import('/@/views/statistics/reportConfig/index.vue'),
      meta: {
        title: '填报项设置',
      },
    },
    {
      path: 'reportType',
      name: 'ReportType',
      component: () => import('/@/views/statistics/reportType/index.vue'),
      meta: {
        title: '报送通知',
      },
    },
    // {
    //   path: 'example',
    //   name: 'example',
    //   component: () => import('/@/views/example/DraggableFormExample.vue'),
    //   meta: {
    //     title: '示例',
    //   },
    // },
    {
      path: 'contacts',
      name: 'Contacts',
      component: () => import('/@/views/statistics/contacts/index.vue'),
      meta: {
        title: '联系人管理',
      },
    },
    {
      path: 'reportList',
      name: 'ReportList',
      component: () => import('/@/views/statistics/reportList/index.vue'),
      meta: {
        title: '信息填报',
      },
    },
    {
      path: 'recordList',
      name: 'RecordList',
      component: () => import('/@/views/statistics/recordList/index.vue'),
      meta: {
        title: '填报记录',
      },
    },
  ],
}

const list = [
  {
    path: '/statisticsReport/reportConfig',
    name: 'ReportConfig',
    component: () => import('/@/views/statistics/reportConfig/index.vue'),
    meta: {
      title: '填报项设置',
    },
  },

  {
    path: '/statisticsReport/reportType',
    name: 'ReportType',
    component: () => import('/@/views/statistics/reportType/index.vue'),
    meta: {
      title: '报送通知',
    },
  },
  // {
  //   path: 'example',
  //   name: 'example',
  //   component: () => import('/@/views/example/DraggableFormExample.vue'),
  //   meta: {
  //     title: '示例',
  //   },
  // },
  {
    path: '/statisticsReport/contacts',
    name: 'Contacts',
    component: () => import('/@/views/statistics/contacts/index.vue'),
    meta: {
      title: '联系人管理',
    },
  },
  {
    path: '/statisticsReport/reportList',
    name: 'ReportList',
    component: () => import('/@/views/statistics/reportList/index.vue'),
    meta: {
      title: '信息填报',
    },
  },
  {
    path: '/statisticsReport/recordList',
    name: 'RecordList',
    component: () => import('/@/views/statistics/recordList/index.vue'),
    meta: {
      title: '填报记录',
    },
  },
]

export default isDevMode() ? obj : list;