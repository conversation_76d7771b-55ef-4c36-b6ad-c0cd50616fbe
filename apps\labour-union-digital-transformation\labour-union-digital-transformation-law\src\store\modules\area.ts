import { defineStore } from 'pinia';
import { getArea } from '@/api';
import { map } from 'lodash-es';

interface AreaOption {
  label: string;
  value: string | number | boolean;
  key?: string;
  children?: AreaStore[];
}

interface AreaStore {
  areaOptions: AreaOption[];
}

export const useArea = defineStore({
  id: 'area',
  state: (): AreaStore => ({
    areaOptions: [],
  }),
  getters: {
    getAreaOpt(state): AreaOption[] {
      return state.areaOptions;
    },
  },
  actions: {
    setAreaOpt(arr: AreaOption[]) {
      this.areaOptions = arr;
    },
    async setArea(userInfo) {
      //获取当前登录用户area
      const areaCode = userInfo.areaCode;
      const res = await getArea({ pcode: areaCode });
      const { data: originData } = res?.data;
      this.setAreaOpt(
        map(originData, v => {
          return {
            label: v.areaName,
            value: v.areaName,
          };
        })
      );
    },
  },
});
