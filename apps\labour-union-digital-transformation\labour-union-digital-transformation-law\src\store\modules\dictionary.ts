import { defineStore } from 'pinia';
import { DictionaryModal, queryDictionary } from '@/api/system/dictionary';

interface DictionaryStore {
  dictionaryMap: Map<string, DictionaryModal>;
  dictionaryOBJmap: Map<string, DictionaryModal[]>;
  dictionaryOpt: Map<string, Recordable[]>;
}

export const useDictionary = defineStore({
  id: 'dictionary',
  state: (): DictionaryStore => ({
    dictionaryMap: new Map<string, DictionaryModal>(),
    dictionaryOBJmap: new Map<string, DictionaryModal[]>(),
    dictionaryOpt: new Map<string, Recordable[]>(),
  }),
  getters: {
    getDictionaryMap(state): Map<string, DictionaryModal> {
      return state.dictionaryMap;
    },
    getDictionaryOBJMap(state): Map<string, DictionaryModal[]> {
      return state.dictionaryOBJmap;
    },
    getDictionaryOpt(state): Map<string, Recordable[]> {
      console.log(state.dictionaryOpt, 'dictionaryOpt');

      return state.dictionaryOpt;
    },
  },
  actions: {
    setDictionaryMap(map: Map<string, DictionaryModal>) {
      this.dictionaryMap = map;
    },
    setDictionaryOBJMap(map: Map<string, DictionaryModal[]>) {
      this.dictionaryOBJmap = map;
    },
    setDictionaryOptMap(map: Map<string, Recordable[]>) {
      this.dictionaryOpt = map;
    },
    async setDictionary(): Promise<void> {
      //避免重复请求
      if (this.dictionaryOpt.size && this.dictionaryOBJmap.size) return;
      queryDictionary({ groupCode: '', groupName: '', dictCode: '', dictName: '', }).then(res => {
        const map = new Map<string, DictionaryModal>();
        const objMap = new Map<string, DictionaryModal[]>();
        const optMap = new Map<string, Recordable[]>();
        res.map(v => {
          map.set(v.groupCode + '_' + v.dictCode, v);
          if (objMap.has(v.groupCode as string)) {
            objMap.get(v.groupCode as string)?.push(v);
            //选择框
            optMap.get(v.groupCode as string)?.push({
              label: v.dictName,
              value: v.dictCode,
            });
          } else {
            const arr: DictionaryModal[] = [];
            const optArr: Recordable[] = [];
            arr.push(v);
            optArr.push({
              label: v.dictName,
              value: v.dictCode,
            });
            objMap.set(v.groupCode as string, arr);
            optMap.set(v.groupCode as string, optArr);
          }
        });
        this.setDictionaryMap(map);
        this.setDictionaryOBJMap(objMap);
        this.setDictionaryOptMap(optMap);
      });
    },
  },
});
