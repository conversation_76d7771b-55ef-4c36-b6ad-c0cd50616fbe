import { TreeItem } from '@/components/Tree';
import { defineStore } from 'pinia';
import { unionNextLevel } from '@/api';
import { map } from 'lodash-es';

export interface UnionInfo extends TreeItem {
  id: string; //UUID
  unionName?: string; //名称
  title?: string;
  areaName?: string; //所在区域
  level?: string; //层级等级
  provinceId?: string; //省id
  cityId?: string; //市id
  areaId?: string; //县级id
  ifDelete?: boolean | number; //是否删除
  name?: string; //单位名称
  phone?: string; //负责人联系电话
  key: string;
  children?: UnionInfo[];
}

interface UnionStore {
  unionOpt: UnionInfo[];
}

export const useUnionNextLevel = defineStore({
  id: 'union-next-level',
  state: (): UnionStore => ({
    unionOpt: [],
  }),
  getters: {
    getUnionOpt(state): UnionInfo[] {
      return state.unionOpt;
    },
  },
  actions: {
    setUnionOpt(arr: UnionInfo[]) {
      this.unionOpt = arr;
    },
    setUnion(userInfo) {
      const user = userInfo;
      if (this.unionOpt.length) return;
      unionNextLevel({ unionId: userInfo.companyId }).then(({ data }) => {
        const { data: orginData } = data as { data: [] };
        const children = map(orginData, v => {
          return {
            id: v['id'],
            key: v['id'],
            unionName: v['c0100'],
            title: v['c0100'],
            areaName: v['c0102'],
            level: v['c0107'],
            provinceId: v['c0108'],
            cityId: v['c0109'],
            areaId: v['c0110'],
            ifDelete: v['c0114'],
            name: v['c0215'],
            phone: v['c0216'],
            unionCategory: v['c0217'],
            checkable: true,
          };
        });
        this.setUnionOpt([
          {
            id: user.companyId,
            key: user.companyId,
            unionName: user.companyName,
            title: user.companyName,
            checkable: true,
            children,
          },
        ]);
      });
    },
  },
});
