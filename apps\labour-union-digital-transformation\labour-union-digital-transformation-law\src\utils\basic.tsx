import {
  FileOutlined,
  PlaySquareOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  FileZipOutlined,
  FileWordOutlined,
} from '@ant-design/icons-vue';
import { isImgTypeByName } from '@/components/Upload/src/helper';
import { useUserStore } from '../store/modules/user';

/**
 * 
 * @param type
     '.xls',
      '.doc',
      '.docx',
      '.xlsx',
      '.csv',
      '.zip',
      '.rar',
      '.mp3',
      '.mp4',
 * @returns 
 */

export function TypeCom(type, url?: string) {
  const isImg = isImgTypeByName(`.${type}`);

  const userStore = useUserStore();

  if (isImg) {
    return (
      <img
        src={`${userStore.getPrefix}${url}`}
        class="w-full h-full"
      />
    );
  }
  switch (type) {
    case 'mp4':
      return <PlaySquareOutlined class={`!text-[30px]`} />;
    case 'pdf':
      return <FilePdfOutlined class={`!text-[30px]`} />;
    case 'txt':
      return <FileTextOutlined class={`!text-[30px]`} />;
    case 'zip':
    case 'rar':
      return <FileZipOutlined class={`!text-[30px]`} />;
    case 'doc':
    case 'docx':
      return <FileWordOutlined class={`!text-[30px]`} />;
    // case 'mp3':
    //   return (
    //     <IconFont
    //       type={`icon-audio`}
    //       class={`!text-[30px]`}
    //     />
    //   );
    default:
      return <FileOutlined class={`!text-[30px]`} />;
  }
}
