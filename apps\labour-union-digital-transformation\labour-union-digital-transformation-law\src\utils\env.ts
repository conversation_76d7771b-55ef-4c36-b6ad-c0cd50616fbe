import type { GlobEnvConfig } from '#/config';
import pkg from '../../package.json';
import { API_ADDRESS } from '@monorepo-yysz/enums';

export function getCommonStoragePrefix() {
  const { VITE_GLOB_APP_TITLE } = getAppEnvConfig();
  return `${VITE_GLOB_APP_TITLE.replace(/\s/g, '_')}__${getEnv()}`.toUpperCase();
}

// Generate cache key according to version
export function getStorageShortName() {
  return `${getCommonStoragePrefix()}${`__${pkg.version}`}__`.toUpperCase();
}

const getVariableName = (title: string) => {
  function strToHex(str: string) {
    const result: string[] = [];
    for (let i = 0; i < str.length; ++i) {
      const hex = str.charCodeAt(i).toString(16);
      result.push(('000' + hex).slice(-4));
    }
    return result.join('').toUpperCase();
  }
  return `__PRODUCTION__${strToHex(title) || '__APP'}__CONF__`.toUpperCase().replace(/\s/g, '');
};

export function getAppEnvConfig() {
  const ENV_NAME = getVariableName(import.meta.env.VITE_GLOB_APP_TITLE);
  const ENV = import.meta.env.DEV
    ? // Get the global configuration (the configuration will be extracted independently when packaging)
      (import.meta.env as unknown as GlobEnvConfig)
    : (window[ENV_NAME] as unknown as GlobEnvConfig);
  const {
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_API_URL_PREFIX,
    VITE_GLOB_UPLOAD_URL,
    VITE_PUBLIC_PATH,
    VITE_GLOB_BAIDU_SERVICE,
    VITE_GLOB_BUCKET_NAME,
    VITE_GLOB_APP_MAIN_ONE,
    VITE_GLOB_APP_MAIN_TWO,
    VITE_GLOB_APP_SUPPORT,
    VITE_GLOB_APP_SUPPORT_URL,
    VITE_GLOB_APP_MAIN_ONE_URL,
    VITE_GLOB_APP_MAIN_TWO_URL,
    VITE_GLOB_IF_LOGIN,
    VITE_GLOB_APP_MAIN_LOGIN,

  } = ENV;
  let { VITE_GLOB_API_URL } = ENV;
  if (localStorage.getItem(API_ADDRESS)) {
    const address = JSON.parse(localStorage.getItem(API_ADDRESS) || '{}');
    if (address?.key) VITE_GLOB_API_URL = address?.val;
  }
  return {
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_API_URL,
    VITE_GLOB_API_URL_PREFIX,
    VITE_GLOB_UPLOAD_URL,
    VITE_PUBLIC_PATH,
    VITE_GLOB_BAIDU_SERVICE,
    VITE_GLOB_BUCKET_NAME,
    VITE_GLOB_APP_MAIN_ONE,
    VITE_GLOB_APP_MAIN_TWO,
    VITE_GLOB_APP_SUPPORT,
    VITE_GLOB_APP_SUPPORT_URL,
    VITE_GLOB_APP_MAIN_ONE_URL,
    VITE_GLOB_APP_MAIN_TWO_URL,
    VITE_GLOB_IF_LOGIN,
    VITE_GLOB_APP_MAIN_LOGIN,

  };
}

/**
 * @description: Development mode
 */
export const devMode = 'development';

/**
 * @description: Production mode
 */
export const prodMode = 'production';

/**
 * @description: Get environment variables
 * @returns:
 * @example:
 */
export function getEnv(): string {
  return import.meta.env.MODE;
}

/**
 * @description: Is it a development mode
 * @returns:
 * @example:
 */
export function isDevMode(): boolean {
  return import.meta.env.DEV;
}

/**
 * @description: Is it a production mode
 * @returns:
 * @example:
 */
export function isProdMode(): boolean {
  return import.meta.env.PROD;
}
