import { cloneDeep, filter } from 'lodash-es';
import { useDictionary } from '../store/modules/dictionary';
import { useUserStore } from '../store/modules/user';
import { FormSchema } from '../components/Form';

const dictionary = useDictionary();
const userStore = useUserStore();
export function searchNextUnionForm(): FormSchema[] {
  return [
    {
      field: 'queryCompanyId',
      label: '下级工会',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      ifShow: userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5',
      componentProps: function () {
        return {
          options: filter(
            cloneDeep(dictionary.getDictionaryOpt.get(`unionsInfo`)),
            v => v.value !== '6650f8e054af46e7a415be50597a99d5'
          ),
        };
      },
    },
    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: { span: 3 },
      defaultValue: true,
    },
  ];
}
