<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" @ok="handleSubmit">
    <BasicForm @register="registerForm" :class="disabledClass"> </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
import { defineComponent, ref, computed, unref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { modalForm } from './data'
import { Select } from 'ant-design-vue'

const emit = defineEmits(['success', 'register'])
const isUpdate = ref(true)

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.userName || ''}--详情`
    : unref(isUpdate)
      ? `编辑--${unref(record)?.userName || ''}`
      : '新增白名单'
})
const disabled = ref(false)

const record = ref<Recordable>()

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})

const form = computed(() => {
  return modalForm(unref(record))
})

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields()

  isUpdate.value = !!data?.isUpdate

  disabled.value = !!data?.disabled

  record.value = data.record

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
    })
  } else {

  }
  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  })

  setProps({ disabled: unref(disabled) })
})

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 100,
  schemas: form,
  showActionButtonGroup: false,
})

async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true })
    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...unref(record),
        ...values,
      },
    })
  } finally {
    setModalProps({ confirmLoading: false })
  }
}

</script>
