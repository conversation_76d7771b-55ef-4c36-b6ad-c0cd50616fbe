<template>
  <BasicModal
    @register="registerModal"
    :can-fullscreen="false"
    maskTransitionName="fade"
    transitionName="fade"
    :title="title"
    :show-ok-btn="false"
  >
    <BasicTable @register="registerTable"> </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useModalInner, BasicModal } from '/@/components/Modal'
import { useTable, BasicTable } from '/@/components/Table'
import { recordList } from '/@/api/system/whiteList'
import { recordColumns } from './data'

defineEmits(['register'])

const title = ref('')

const account = ref('')

const recordObj = ref<Recordable>({})

const [registerModal, {}] = useModalInner(async function (data) {
  title.value = `${data.record.userName}--访问记录`

  recordObj.value = data.record

  account.value = data.record.userMobile

  reload()
})

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  api: recordList,
  columns: recordColumns(),
  beforeFetch: params => {
    params.sortType = 'desc'
    params.orderBy = 'create_time'
    params.account = account.value
    return params
  },
  immediate: false,
  useSearchForm: false,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: false,
  maxHeight: 500,
})
</script>
