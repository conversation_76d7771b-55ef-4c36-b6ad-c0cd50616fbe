import { Tooltip } from 'ant-design-vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { validatePhone } from '@monorepo-yysz/utils';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary()
  return [
    {
      title: '案件名称',
      dataIndex: 'userName',
      width: 200,
    },
    {
      title: '案件类别',
      dataIndex: 'userName',
      width: 200,
    },
    {
      title: '涉案金额',
      dataIndex: 'userName',
      width: 200,
    },
    {
      title: '涉案人员',
      dataIndex: 'userName',
      width: 200,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 200,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'userMobile',
      label: '手机号',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
      },
    },
    {
      field: 'enableType',
      label: '是否启用',
      colProps: { span: 6 },
      component: 'Select',
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('YesOrNo'),
          placeholder: '请选择是否禁用',
        }
      },
    },
    {
      field: 'whiteType',
      label: '白名单类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('whiteType'),
        }
      },
    },
  ]
}

export const modalForm = (record?: Recordable): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'userMobile',
      label: '手机号',
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      rules: [{ required: true, validator: validatePhone, trigger: ['change', 'blur'] }],
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 11,
      },
    },
    {
      field: 'whiteType',
      label: '白名单类型',
      required: true,
      colProps: { span: 24 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('whiteType'),
        }
      },
    },
    {
      field: 'userName',
      label: '姓名',
      component: 'Input',
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 40,
      },
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 400,
      },
    },
    {
      field: 'enableType',
      label: '是否启用',
      required: true,
      component: 'RadioGroup',
      defaultValue: 'y',
      colProps: { span: 24 },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo'),
      },
    },
  ]
}

export const recordColumns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'userNickName',
      width: 200,
    },
    {
      title: '工会名称',
      dataIndex: 'companyName',
      width: 200,
    },
    {
      title: '手机号',
      dataIndex: 'account',
      width: 200,
    },
    {
      title: '注册区域',
      dataIndex: 'registerArea',
      width: 200,
    },

    {
      title: '访问时间',
      dataIndex: 'createTime',
      width: 200,
    },
  ]
}
