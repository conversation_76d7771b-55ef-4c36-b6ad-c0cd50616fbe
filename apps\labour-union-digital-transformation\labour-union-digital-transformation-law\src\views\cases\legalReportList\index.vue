<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleClick" >
          新增案件
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              auth: '/system/whiteList/view',
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              onClick: handleEdit.bind(null, record),
              auth: '/system/whiteList/update',
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '访问记录',
              type: 'primary',
              onClick: handleOpenRecord.bind(null, record),
              // auth: '/system/whiteList/update',
            },
            {
              icon: 'fluent:delete-20-filled',
              label: '删除',
              type: 'primary',
              onClick: handleDelete.bind(null, record),
              danger: true,
              auth: '/system/whiteList/delete',
            },
          ]"
        />
      </template>
         </template>
    </BasicTable>
    <WhiteListModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="50%"
    />
    <RecordTable
      @register="registerRecordModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="60%"
    />
  </div>
</template>

<script lang="ts" setup>
import { createVNode } from 'vue'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import { columns, formSchemas } from './data'
import { Modal } from 'ant-design-vue'
import { CloseCircleFilled, CheckCircleOutlined } from '@ant-design/icons-vue'
import WhiteListModal from './WhiteListModal.vue'
import RecordTable from './RecordTable.vue'
import { useModal } from '/@/components/Modal'
import { list, saveOrUpdate, deleteLine } from '/@/api/system/whiteList'
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage()

const [registerTable, { reload, updateTableDataRecord }] = useTable({
  rowKey: 'categoryId',
  columns: columns(),
  showIndexColumn: false,
  // authInfo: ['/system/whiteList/add'],
  api: list,
  formConfig: {
    labelWidth: 200,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  pagination: false,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',
    fixed: undefined,
    // auth: ['/system/whiteList/update', '/system/whiteList/view', '/system/whiteList/delete'],
  },
})

const [registerModal, { openModal, closeModal }] = useModal()
const [registerRecordModal, { openModal: openRecordModal }] = useModal()

//新增
function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  })
}

//访问记录
function handleOpenRecord(record) {
  openRecordModal(true, {
    record: record,
    isUpdate: true,
    disabled: false,
  })
}
//编辑
function handleEdit(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: false,
  })
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确定删除${record?.userName || ''}`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: '删除成功' })
          reload()
        } else {
          createErrorModal({ content: `删除失败，${message}` })
        }
      })
    },
  })
}

//详情
function handleView(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: true,
  })
}

//提交表单
function handleSuccess({ isUpdate, values }) {
  if (isUpdate) {
    saveOrUpdate(values).then(res => {
      const { code, message } = res
      if (code === 200) {
        Modal.success({
          title: '提示',
          icon: createVNode(CheckCircleOutlined),
          content: '编辑成功!' || message,
          okText: '确认',
          closable: true,
        })
        // 刷新编辑数据的缓存值
        updateTableDataRecord(values.autoId, values)
        closeModal()
        reload()
      } else {
        Modal.error({
          title: '提示',
          icon: createVNode(CloseCircleFilled),
          content: `编辑失败!${message}`,
          okText: '确认',
          closable: true,
        })
      }
    })
  } else {
    saveOrUpdate(values).then(res => {
      const { code, message } = res
      if (code === 200) {
        closeModal()
        Modal.success({
          title: '提示',
          icon: createVNode(CheckCircleOutlined),
          content: '新增成功!' || message,
          okText: '确认',
          closable: true,
        })
        reload()
      } else {
        Modal.error({
          title: '提示',
          icon: createVNode(CloseCircleFilled),
          content: `新增失败!${message}`,
          okText: '确认',
          closable: true,
        })
      }
    })
  }
}
</script>
./data
