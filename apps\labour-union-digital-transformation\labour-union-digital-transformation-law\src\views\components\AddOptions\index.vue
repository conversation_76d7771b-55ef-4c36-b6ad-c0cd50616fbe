<template>
  <div class="main">
    <BasicTable @register="registerTable" :dataSource="dataSource">
      <template #toolbar v-if="!disabled">
        <Button type="primary" @click="handleClick()" :icon="h(PlusOutlined)">
          新增
        </Button>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'label'">
          <Input v-model:value="record.label" :readonly="disabled" allow-clear placeholder="请输入选项名称"></Input>
        </template>
        <template v-if="column.dataIndex === 'value'">
          <Input v-model:value="record.value" :readonly="disabled" allow-clear placeholder="请输入选项值"></Input>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: 'material-symbols:note-add',
              onClick: handleCreate.bind(null, record),
              tooltip: '新增下级',
              ifShow: isTree
            },
            {
              icon: 'fluent:delete-20-filled',
              tooltip: '删除',
              onClick: handleDelete.bind(null, record),
              danger: true,
              // auth: '/system/whiteList/delete',
            },
          ]" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
import { ref, h, watch, nextTick } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { Input, Button } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  options: {// 关联数据信息
    type: Array,
    default: () => [
      // { label: '选项1', value: 1, autoId: 1, },
      // { label: '选项2', value: 2, autoId: 2, },
      // { label: '选项3', value: 3, autoId: 3, },
    ],
  },
  disabled: {// 是否禁用编辑
    type: Boolean,
    default: false,
  },
  isTree: {// 是否树类型
    type: Boolean,
    default: false,
  },

});

const emit = defineEmits(['update:options', 'change']);
const dataSource = ref(props.options) as any;

const [registerTable, { setTableData, getDataSource, insertTableDataRecord, deleteTableDataRecord, setProps, }] = useTable({
  rowKey: 'autoId',
  columns: [
    {
      title: '选项名称',
      dataIndex: 'label',
      width: props.isTree ? 250 : undefined,
    },
    {
      title: '选项值',
      dataIndex: 'value',
      width: props.isTree ? 150 : undefined,
    }
  ],
  isTreeTable: props.isTree,
  expandRowByClick: props.isTree,
  showIndexColumn: false,
  useSearchForm: false,
  bordered: false,
  pagination: false,
  showTableSetting: false,
  maxHeight: 250,
  actionColumn: {
    title: '操作',
    width: 100,
    dataIndex: 'action',
    fixed: undefined,
    // auth: ['/system/whiteList/update', '/system/whiteList/view', '/system/whiteList/delete'],
  },
})

function handleClick(pid = 0) {
  const data = getDataSource()
  let autoId = new Date().getTime();
  let label = {
    label: '',
    value: '',
    autoId: autoId,
  }
  if (props.isTree) {
    label.pid = pid;
    label.children = []
  }
  insertTableDataRecord(label)
  emit('update:options', [...data])
  emit('change', [...data])
}

function handleCreate(item) {
  const data = getDataSource();
  let autoId = new Date().getTime();
  let label = {
    label: '',
    value: '',
    autoId: autoId,
    pid: item.autoId,
    children: []
  }
  if (item.children && item.children.length > 0) {
    item.children.push(label);
    item.expanded = true;
  } else {
    item.children = [label]
  }
  setProps({ isTreeTable: true, expandRowByClick: true });
  emit('update:options', [...data]);
  emit('change', [...data])

}

function handleDelete(record: any) {
  record.autoId && deleteTableDataRecord(record.autoId);
  const data = getDataSource()
  emit('update:options', [...data])
  emit('change', [...data])
}
watch(() => props.options, (val) => {
  dataSource.value = val;
}, { deep: true, immediate: true })

watch(() => props.disabled, (val) => {
  setProps({
    actionColumn: val ? undefined : {
      title: '操作',
      width: 100,
      dataIndex: 'action',
      fixed: undefined,
    }
  })
}, { deep: true, })

</script>

<style lang="less" scoped>
.main {
  :deep(.ant-input-affix-wrapper) {
    width: 70% !important;
  }
}
</style>
