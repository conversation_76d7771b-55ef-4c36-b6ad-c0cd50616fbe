<template>
  <BasicTree :selectedKeys="selectedKeys" :loading="loadingTree" expandOnSearch
    :fieldNames="{ children: 'children', title: 'fieldCategoryName', key: 'fieldCategoryBizId' }" search
    :treeData="treeData" showLine @select="handleSelect" :class="`tree-info ${$style['tree-info']}`" ref="treeRef"
    v-bind="$attrs">
    <template #headerTitle>
      <div class="text-xl text-[#005090] font-bold pb-2">报送通知类型</div>
    </template>
    <template #title="{ fieldCategoryName }">
      <Tooltip class="!truncate text-[15px]">
        <template #title>{{ fieldCategoryName }}</template>
        {{ fieldCategoryName }}
      </Tooltip>
    </template>
  </BasicTree>
</template>

<script lang="ts" setup>
import { onMounted, ref, unref, watch, nextTick } from 'vue'
import { BasicTree, TreeItem, KeyType, TreeActionType } from '/@/components/Tree'
import { Tooltip } from 'ant-design-vue'
import { list, } from '/@/api/report/configType';

const emit = defineEmits(['selectInfo', 'getFirstNode', 'update:reload'])

const props = defineProps({
  /**
   * 更新数据
   */
  reload: { type: Boolean },
  selectedKey: { type: Array as PropType<KeyType[]>, default: undefined },
  ifSelected: { type: Boolean },
  treeTitle: { type: String },
  companyType: { type: String },
})

const treeData = ref<TreeItem[]>([])

const selectedKeys = ref<KeyType[]>([])

const loadingTree = ref<boolean>(false)

const treeRef = ref<Nullable<TreeActionType>>(null)

function handleSelect(_: any, { node, selected }: Recordable) {
  emit('selectInfo', {
    name: selected ? node.fieldCategoryName?.el?.innerText : undefined,
    id: selected ? node.fieldCategoryBizId : undefined,
    fieldCategoryId: selected ? node.fieldCategoryId : undefined,
  })
}

async function getTreeData() {
  loadingTree.value = true
  let res = await list({ pageSize: 0 });
  if (res.code === 200) treeData.value = res.data || [];
  loadingTree.value = false

  //默认选择
  if (unref(props.ifSelected) && unref(treeData) && unref(treeData)[0]?.fieldCategoryBizId) {
    const autoIds = [unref(treeData)[0].fieldCategoryBizId]
    const id = props.selectedKey && props.selectedKey.length > 0 ? props.selectedKey : autoIds
    selectedKeys.value = id

    emit('getFirstNode', { id: id[0], name: unref(treeData)[0].fieldCategoryName, fieldCategoryId: unref(treeData)[0].fieldCategoryId })
  }
  await nextTick()
  handleLevel()
}

//定义tree
function getTree() {
  const tree = unref(treeRef)
  if (!tree) {
    throw new Error('tree is null!')
  }
  return tree
}

function handleLevel() {
  getTree().expandAll(true)
}

//初始化树
onMounted(async () => {
  await getTreeData()
})

watch(
  () => props.reload || props.companyType,
  async () => {
    await getTreeData()
  }
)

watch(
  () => props.selectedKey,
  async val => {
    await nextTick()
    selectedKeys.value = props.selectedKey as KeyType[]
    getTree().setSelectedKeys(val || [])
  }
)
</script>

<style lang="less" module>
.tree-info {
  :global {
    .ant-tree {
      height: 100%;
      overflow-y: auto;
      .ant-tree-treenode {
        margin-top: 5px;

        .ant-tree-title {
          font-size: 16px;
          height: 100%;
        }

        svg {
          color: #184564;
        }
      }
    }
  }
}
</style>
