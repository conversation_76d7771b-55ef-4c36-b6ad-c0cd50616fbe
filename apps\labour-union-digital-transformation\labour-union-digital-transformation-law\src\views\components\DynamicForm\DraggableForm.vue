<template>
  <Form :model="formState" ref="formRef" :disabled="disabled">
    <draggable :list="formItems" item-key="key" :class="{ 'draggable-list': isFlex }" :animation="100"
      :forceFallback="true" ghost-class="ghost" chosen-class="chosenClass" :sort="!disabled" @end="onDragEnd"
      @start="onDragStart" @change="onMoveCallback">
      <template #item="{ element }">
        <div class="draggable-item" :class="{ 'w-1/2': formItems.length > 1 }">
          <FormItem :name="element.field" :label="element.label" :rules="element.rules" :required="element.required">
            <div class="flex items-center">
              <component :is="getComponent(element.component)" v-model:value="formState[element.field]"
                v-bind="element.componentProps || {}" @change="changeFiled" />
            </div>
          </FormItem>
        </div>
      </template>
    </draggable>
  </Form>
</template>

<script lang="ts" setup>
import { ref, watch, computed, unref } from 'vue';
import { Form, FormItem, Input, InputNumber, Select, TreeSelect, Radio, Checkbox, Upload, DatePicker, RangePicker, TimePicker, TimeRangePicker } from 'ant-design-vue';
import draggable from 'vuedraggable';

const componentMap = {
  Input: Input,
  InputTextArea: Input.TextArea,
  InputNumber: InputNumber,
  Select: Select,
  TreeSelect: TreeSelect,
  RadioGroup: Radio.Group,
  CheckboxGroup: Checkbox.Group,
  Upload: Upload,
  DatePicker: DatePicker,
  TimePicker: TimePicker,
  TimeRangePicker: TimeRangePicker,
  RangePicker: RangePicker,
};

interface FormItem {
  key: string;
  name: string;
  label: string;
  component?: string;
  required?: boolean;
  componentProps?: Record<string, any>;
}

const props = defineProps({
  formItems: {
    type: Array as () => any[],
    required: true
  },
  formState: {
    type: Object,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:formState', 'change']);

const formRef = ref();
const formItems = ref();
const formState = ref();
const isFlex = ref(false);

// const formState = computed(() => {
//   console.log('有变化吗');

//   let obj = {};
//   if (!unref(formItems) || !unref(formItems).length) return obj;
//   unref(formItems).forEach(item => {
//     obj[item.field] = item.fieldDefaultValue;
//   });
//   return obj;
// });

function getComponent(type?: string) {
  return componentMap[type as keyof typeof componentMap] || Input;
}

// 拖拽结束后可以在这里处理额外逻辑
function onDragEnd(e) {
  let newFormItems = handleValueChange();
  emit('change', newFormItems);
  emit('update:formState', formState.value);
}

function onDragStart(e) {
  // console.log('拖拽开始', e);
  // 拖拽结束后可以在这里处理额外逻辑
}
function onMoveCallback(e) {
  // console.log('拖拽变化', e);
  // 拖拽结束后可以在这里处理额外逻辑
}
function changeFiled(e) {
  let newFormItems = handleValueChange();
  emit('change', newFormItems);
  emit('update:formState', formState.value);
}

//处理表单值变化，更新动态表单配置
function handleValueChange() {
  if (!formItems.value.length) return [];
  return formItems.value.map(item => {
    let fieldDefaultValue = formState.value[item.field];
    return {
      ...item,
      fieldDefaultValue: fieldDefaultValue
    }
  });
}

watch(() => props.formItems, (val) => {
  formState.value = {};
  formItems.value = val;
  if (unref(formItems).length) {
    unref(formItems).forEach(item => {
      formState.value[item.field] = item.fieldDefaultValue;
    });
  }
  isFlex.value = val.length > 1;
}, { deep: true });

</script>

<style scoped lang="less">
.ghost {
  border: solid 1px red;
}

.chosenClass {
  background-color: #afc6e7;
}

.draggable-list {
  display: flex;
  flex-wrap: wrap;
}

.draggable-item {
  // background: #fff;
  // padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  transition: all 0.3s;
  position: relative;
  cursor: grab;
  user-select: none;

  :deep(.ant-form-item) {
    // padding: 15px 10px !important;
    margin-bottom: 0 !important;

    .ant-col {
      width: auto !important;
    }

    .ant-form-item-label {
      width: 160px !important;
      user-select: none !important;
      background-color: @form-table-title-color;
      border: 1px solid #f0f0f0;

      label {
        user-select: none !important;
      }
    }


  }
}

.draggable-item:active {
  cursor: grabbing;
}

.draggable-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
