<template>
  <div class="main">
    <div v-if="isdrag">
      <DraggableForm :formItems="formItems" v-model:formState="formState" :disabled="disabled"
        @change="handleFormChange" />
    </div>
    <div v-show="!isdrag">
      <BasicForm @register="registerForm" :calss="disabledClass"></BasicForm>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { BasicForm, useForm } from '/@/components/Form';
import DraggableForm from './DraggableForm.vue';
import dayjs from 'dayjs';
import { isValidJSON } from '@/utils/tool';
import { isArray } from 'lodash-es';
import { uploadApi } from '@/api/sys/upload';


const emit = defineEmits(['change', 'success']);

const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  type: {// 表单类型,modal表单，field单个字段
    type: String,
    default: 'field',
  },
  isdrag: {//表单是否开启拖拽
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const formState = ref<Record<string, any>>({});//拖拽表单需要的字段集合
const formItems = computed(() => {
  if (!props.isdrag) return handleOptions(props.options);
  return handleOptions(props.options).map((item, index) => ({
    ...item,
    key: index,
  }));
});
const disabledClass = computed(() => {
  return props.disabled ? 'back-transparent' : '';
})

const [registerForm, { setFieldsValue, resetFields, validate, setProps, getFieldsValue, resetSchema }] = useForm({
  labelWidth: 160,
  schemas: formItems,
  showActionButtonGroup: false,
})

defineExpose({
  validate: () => validate(),
  getFieldsValue: () => getFieldsValue(),
  resetFields: () => resetFields(),
});

function handleOptions(options: any[] = []) {
  if (!options.length) return [];

  return options.map((item) => {
    let componentProps = {} as any;
    let componentType = item.fieldType;
    let fieldDefaultValue = item.fieldDefaultValue;
    if (item.fieldType == 'Input' || item.fieldType == 'InputTextArea') {
      componentProps.showCount = !!item.fieldMaxValue;
      componentProps.maxlength = item.fieldMaxValue;
    } else if (item.fieldType == 'InputNumber') {
      componentProps.min = item.fieldMinValue;
      componentProps.max = item.fieldMaxValue;
    } else if (item.fieldType == 'Upload') {
      componentProps.maxSize = item.fieldMaxValue;
      componentProps.maxNumber = item.fieldMinValue || 1;
      componentProps.api = uploadApi;
    } else if (item.fieldType == 'Select' || item.fieldType == 'TreeSelect' || item.fieldType == 'RadioGroup' || item.fieldType == 'CheckboxGroup') {
      componentProps.options = item.fieldOptions;
      componentProps.treeData = item.fieldOptions;

      if (item.fieldType == 'Select') componentProps.mode = item.fieldMultiple == 'y' && 'multiple';
      if (item.fieldType == 'TreeSelect') componentProps.multiple = item.fieldMultiple == 'y' && item.fieldType == 'TreeSelect';

    } else if (item.fieldType == 'TimePicker') {
      item.fieldDefaultValue = item.fieldDefaultValue || dayjs().format('YYYY-MM-DD HH:mm:ss');
      componentProps.valueFormat = 'HH:mm:ss';
      componentProps.format = 'HH:mm:ss';
      if (item.fieldMultiple == 'y') {
        componentProps.placeholder = ['开始时间', '结束时间'];
        item.fieldDefaultValue = item.fieldDefaultValue || [dayjs().format('YYYY-MM-DD 00:00:00'), item.fieldDefaultValue]
        componentType = 'TimeRangePicker'
      }
    } else if (item.fieldType == 'DatePicker') {
      item.fieldDefaultValue = item.fieldDefaultValue || dayjs().format('YYYY-MM-DD HH:mm:ss');
      componentProps.valueFormat = 'YYYY-MM-DD HH:mm:ss';
      componentProps.format = 'YYYY-MM-DD HH:mm:ss';

      if (item.fieldMultiple == 'y') {
        componentProps.placeholder = ['开始时间', '结束时间'];
        item.fieldDefaultValue = item.fieldDefaultValue || [dayjs().format('YYYY-MM-DD 00:00:00'), item.fieldDefaultValue]
        componentType = 'RangePicker'
      }
    }

    //新增和编辑选填时逻辑
    if (item.fieldDefaultValue) {
      if (typeof item.fieldDefaultValue == 'string') {
        fieldDefaultValue = item.fieldDefaultValue.indexOf('[') > -1 ? JSON.parse(item.fieldDefaultValue) : item.fieldDefaultValue;
      }
      //设置默认值
      let obj = {}
      obj[item.fieldBizId || 'value'] = fieldDefaultValue;
      setFieldsValue(obj)
    }

    return {
      ...item,
      fieldDefaultValue: fieldDefaultValue,
      field: item.fieldBizId || 'value',
      label: item.fieldTitle,
      required: item.fieldRequire == 'y' ? true : false,
      component: componentType,
      colProps: { span: options.length == 1 || item.fieldType == 'InputTextArea' ? 24 : 12 },
      // defaultValue: item.fieldDefaultValue,
      componentProps: componentProps,
      rulesMessageJoinLabel: true,
    };
  });
}


//动态表单顺序变化时触发的事件
function handleFormChange(value: any[]) {

  let newOptions = value.map((item) => {
    let fieldDefaultValue = isArray(item.fieldDefaultValue) ? JSON.stringify(item.fieldDefaultValue) : item.fieldDefaultValue;
    return { ...item, fieldDefaultValue: fieldDefaultValue }
  });

  emit('change', newOptions);
}


// 监听options变化，更新表单字段
watch(() => props.disabled, () => {

  setProps({ disabled: props.disabled });
}, { deep: true, });

</script>

<style lang="less" scoped>
.main {
  :deep(.ant-form-item) {
    margin: 16px 5px !important;
  }
}
</style>
