<template>
  <div class="main">
    <BasicForm @register="registerForm"></BasicForm>
  </div>
</template>

<script lang="ts" setup>
import { Input, Button } from 'ant-design-vue';
import { useModal } from '/@/components/Modal';
import { computed, onMounted, ref, unref, watch } from 'vue';
import { RowSelectionType } from 'ant-design-vue/es/table/interface';
import { isArray, join } from 'lodash-es';
import { BasicForm, useForm } from '/@/components/Form';

const emit = defineEmits(['change']);

const props = defineProps({
  options: {// 表单配置信息
    type: Array,
    default: () => [],
  },
  type: {// 表单类型,modal表单，field单个字段
    type: String,
    default: 'field',
  },
});

const form = computed(() => {
  return handleOptions(props.options)
})

const [registerForm, { setFieldsValue, resetFields, validate, setProps, getFieldsValue, resetSchema }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
})

function handleOptions(options: any[] = []) {
  if (!options.length) return []

  return options.map((item) => {
    let componentProps = {} as any;
    if (item.fieldType == 'Input' || item.fieldType == 'InputTextArea') {
      componentProps.showCount = !!item.fieldMaxValue
      componentProps.maxlength = item.fieldMaxValue
    } else if (item.fieldType == 'InputNumber') {
      componentProps.min = item.fieldMinValue
      componentProps.max = item.fieldMaxValue
    } else if (item.fieldType == 'Upload') {
      componentProps.maxSize = item.fieldMaxValue
      componentProps.maxNumber = item.fieldMinValue||1
    } else if (item.fieldType == 'Select' || item.fieldType == 'RadioGroup' || item.fieldType == 'CheckboxGroup') {
      componentProps.options = item.fieldOptions
    }

    //新增和编辑选填时逻辑
    if (props.type == 'field') {
      //设置默认值
      let obj = {}
      obj[item.fieldBizId || 'value'] = item.fieldDefaultValue
      setFieldsValue(obj)
    }

    return {
      field: item.fieldBizId || 'value',
      label: item.fieldTitle,
      required: item.fieldRequire == 'y' ? true : false,
      component: item.fieldType,
      colProps: { span: options.length == 1 || item.fieldType == 'InputTextArea' ? 24 : 12 },
      defaultValue: item.fieldDefaultValue,
      componentProps: componentProps,
    }
  })
}

// 获取表单数据
function getFormData() {
   let values = getFieldsValue();
   for (let key in values) {
     if (values[key] == null || values[key] == '') {
       delete values[key];
     }
     console.log(key,values[key],'key');
   }
}

//监听表单变化时触发,jxs写法，表单数据变化时触发
function handleFormChange() {
  let obj = getFieldsValue();
  let values=validate();
  if(obj===values){
    for (let key in obj){
      if (values[key]==null||values[key]==''){
        values[key] = obj[key];
        delete obj[key];
      }
    }
  }
  emit('change', values);
}


watch(() => props.options, () => {

}, { deep: true, immediate: true })


</script>

<style lang="less" scoped>
.main {}
</style>
