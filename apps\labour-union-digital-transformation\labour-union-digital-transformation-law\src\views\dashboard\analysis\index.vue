<template>
  <div :class="`${$style.dashboard} h-full`">
    首页统计
  </div>
</template>

<script lang="ts" setup>
import { onMounted, provide, ref } from 'vue';
import { userUnionCode } from '@/api';
import { useDictionary } from '@/store/modules/dictionary';

const loading = ref(true);


onMounted(async () => {

});

setTimeout(() => {
  loading.value = false;
}, 1500);
</script>

<style lang="less" module>
.dashboard {
  :global {
    padding: 13px 13px 10px 13px;

  }
}
</style>
