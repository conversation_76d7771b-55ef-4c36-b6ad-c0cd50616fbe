<template>
  <div class="p-4 w-full">
    <AddOptions :istree="true" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Button } from 'ant-design-vue';
import AddOptions from '/@/views/components/AddOptions/index.vue';
import { BasicForm ,useForm} from '/@/components/Form';
// import DraggableForm from '@/views/components/DynamicForm/DraggableForm.vue';

const draggableForm = ref();
const formValues = ref({});

const [registerForm,{setProps,resetFields}]= useForm({
  labelWidth: 100,
  schemas:[{
    field:'name',
    component:'Input',
    label:'姓名'
  }],
});

function handleFinish() {
  console.log('finish');
}

function submitForm() {
  formValues.value = draggableForm.value?.formState;
}

function resetForm() {
  draggableForm.value?.formRef.resetFields();
}
</script>
