<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" @ok="handleSubmit">
    <BasicForm @register="registerForm" :class="disabledClass">
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
import { defineComponent, ref, computed, unref, watch } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { modalForm } from './data'


const emit = defineEmits(['success', 'register'])
const isUpdate = ref(true)
const disabled = ref(false)

const record = ref<Recordable>();
const configInfo = ref<Recordable[]>([]);//动态表单配置信息



const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.contactMemberName || ''}--详情`
    : unref(isUpdate)
      ? `编辑--${unref(record)?.contactMemberName || ''}`
      : '新增联系人'
})
const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})

const form = computed(() => {
  return modalForm()
})

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields()
  configInfo.value = [];
  isUpdate.value = !!data?.isUpdate
  disabled.value = !!data?.disabled

  record.value = data.record;

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
    })
  }

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  })

  setProps({ disabled: unref(disabled) })
})

const [registerForm, { setFieldsValue, resetFields, validate, setProps, getFieldsValue }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
})



async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true })


    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...unref(record),
        ...values,
      },
    })
  } finally {
    setModalProps({ confirmLoading: false })
  }
}

// watch(
//   () => labelOptions,
//   val => {
//     configInfo.value = [{ ...getFieldsValue(), fieldOptions: val }];
//   },
//   { deep: true, }
// )

</script>
<style lang="less" scoped>
.main {
  .title {
    border-bottom: 1px solid #5A9EFB;
  }
}
</style>
