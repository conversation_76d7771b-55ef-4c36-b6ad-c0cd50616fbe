import { Tooltip } from 'ant-design-vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { useUnionNextLevel } from '/@/store/modules/unionNextLevel';
import { validatePhone } from '@monorepo-yysz/utils';
import { useUserStore } from '@/store/modules/user';

const dictionary = useDictionary()
const unionNextLevel = useUnionNextLevel()
const userStore = useUserStore();

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '联系人',
      dataIndex: 'contactMemberName',

    },
    {
      title: '联系电话',
      dataIndex: 'contactMemberPhone',
    },
    {
      title: '所属单位',
      dataIndex: 'companyName',
    },

    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 200,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'contactMemberName',
      label: '联系人',
      component: 'Input',
      colProps: { span: 5 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'contactMemberPhone',
      label: '联系人',
      component: 'Input',
      colProps: { span: 5 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyId',
      label: '所属工会',
      component: 'TreeSelect',
      colProps: { span: 5 },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          treeData: unionNextLevel.getUnionOpt,
          showSearch: true,
          allowClear: true,
          treeDefaultExpandAll: true,
          fieldNames: { label: 'unionName', value: 'id' },
          filterTreeNode(input: string, option: any) {
            return option.unionName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
        }
      },
    },
    {
      field: 'tenantChildFlag',
      label: '是否包含下级工会',
      component: 'Switch',
      labelWidth: 140,
      colProps: { span: 5 },
      defaultValue: true,
      componentProps: {
        checkedChildren: '是',
        unCheckedChildren: '否',
        checkedValue: true,
        unCheckedValue: false,
      },
    },
  ]
}

export const modalForm = (): FormSchema[] => {
  return [
    {
      field: 'contactMemberName',
      label: '联系人',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 40,
      },
    },
    {
      field: 'contactMemberPhone',
      label: '联系电话',
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      rules: [{ required: true, validator: validatePhone, trigger: ['change', 'blur'] }],
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 11,
      },
    },
    {
      field: 'companyId',
      label: '所属工会',
      component: 'TreeSelect',
      colProps: { span: 24 },
      required: true,
      defaultValue: userStore.getUserInfo.companyId,
      componentProps({ formModel }) {
        return {
          treeData: unionNextLevel.getUnionOpt,
          showSearch: true,
          allowClear: true,
          treeDefaultExpandAll: true,
          fieldNames: { label: 'unionName', value: 'id' },
          filterTreeNode(input: string, option: any) {
            return option.unionName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          onChange: (value, label) => {
            if (label) {
              formModel['companyName'] = label[0];
            }
          },
        }
      },
    },
    {
      field: 'companyName',
      label: '工会名称',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      defaultValue: userStore.getUserInfo.companyName,
      show: false,
    },
  ]
}

