<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleClick">
          新增联系人
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              // auth: '/system/whiteList/view',
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              onClick: handleEdit.bind(null, record),
              // auth: '/system/whiteList/update',
            },
            {
              icon: 'fluent:delete-20-filled',
              label: '删除',
              type: 'primary',
              danger: true,
              popConfirm: {
                title: `请确定删除${record?.contactMemberName || ''}吗?`,
                confirm: handleDelete.bind(null, record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <ItemModal @register="registerModal" @success="handleSuccess" :canFullscreen="false" width="50%" />

  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { columns, formSchemas } from './data'
import ItemModal from './ItemModal.vue'
import { useModal } from '/@/components/Modal';
import { list, saveByDTO, updateByDTO, remove, view } from '/@/api/report/contacts';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createMessage } = useMessage()

const [registerTable, { reload, updateTableDataRecord }] = useTable({
  rowKey: 'companyContactMemberId',
  columns: columns(),
  showIndexColumn: false,
  // authInfo: ['/system/whiteList/add'],
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    actionColOptions: { span: 4 },
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',
    fixed: undefined,
    // auth: ['/system/whiteList/update', '/system/whiteList/view', '/system/whiteList/delete'],
  },
})

const [registerModal, { openModal, closeModal }] = useModal()

//新增
function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  })
}

//编辑
function handleEdit(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: false,
  })
}

//删除
function handleDelete(record) {
  remove(record.companyContactMemberId).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success('删除成功')
      reload()
    } else {
      createErrorModal({ content: `删除失败，${message}` })
    }
  })
}

//详情
function handleView(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: true,
  })
}

//提交表单
function handleSuccess({ isUpdate, values }) {
  const api = isUpdate ? updateByDTO : saveByDTO
  api(values).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success(`${isUpdate ? '编辑' : '新增'}成功!`)
      closeModal()
      reload()

    } else {
      createErrorModal({ content: `${isUpdate ? '编辑' : '新增'}失败，${message}` })
    }
  })
}
</script>
