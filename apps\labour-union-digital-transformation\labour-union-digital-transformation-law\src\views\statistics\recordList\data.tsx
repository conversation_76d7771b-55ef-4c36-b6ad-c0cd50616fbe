import { Tag } from 'ant-design-vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { useUnionNextLevel } from '/@/store/modules/unionNextLevel';
import { list } from '@/api/report/configType'
import { queryDictionary } from '@/api/system/dictionary';

const dictionary = useDictionary()
const unionNextLevel = useUnionNextLevel()

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '上报类型名称',
      dataIndex: 'fieldCategoryName',
    },
    {
      title: '上报单位',
      dataIndex: 'submitCompanyName',
      width: 200,
    },
    {
      title: '上报人员',
      dataIndex: 'submitUserName',
      width: 200,
    },
    {
      title: '上报人员电话',
      dataIndex: 'submitUserPhone',
      width: 200,
    },
    {
      title: '上报状态',
      dataIndex: 'submitStatus',
      width: 200,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`subumit_status_${text}`)?.dictName;
        const color = dictionary.getDictionaryMap.get(`subumit_status_${text}`)?.remark || ''
        return <Tag color={color}>{name}</Tag>
      },
    },
    {
      title: '上报时间',
      dataIndex: 'submitTime',
      width: 200,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'fieldCategoryName',
      label: '上报类型',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          api: list,
          resultField: 'data',
          showSearch: true,
          params: {
            pageSize: 0,
            status: 'y',
          },
          fieldNames: { label: 'fieldCategoryName', value: 'fieldCategoryName' },
          filterOption(input: string, option: any) {
            return option.fieldCategoryName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          getPopupContainer: () => document.body,
        }
      },
    },
    {
      field: 'submitStatus',
      label: '上报状态',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          api: queryDictionary,
          resultField: 'data',
          showSearch: true,
          params: {
            pageSize: 0,
            groupCode: 'subumit_status',
          },
          fieldNames: { label: 'dictName', value: 'dictCode' },
          filterOption(input: string, option: any) {
            return option.dictName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          getPopupContainer: () => document.body,
        }
      },
    },
    // {
    //   field: 'submitStatus',
    //   label: '上报状态',
    //   component: 'Select',
    //   colProps: { span: 6 },
    //   rulesMessageJoinLabel: true,
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get('subumit_status') as any,
    //   },
    // },

    {
      field: 'submitCompanyId',
      label: '上报单位',
      component: 'TreeSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          treeData: unionNextLevel.getUnionOpt,
          showSearch: true,
          allowClear: true,
          treeDefaultExpandAll: true,
          fieldNames: { label: 'unionName', value: 'id' },
          filterTreeNode(input: string, option: any) {
            return option.unionName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
        }
      },
    },
    {
      field: 'time',
      label: '上报时间',
      component: 'RangePicker',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          format: 'YYYY-MM-DD ',
          valueFormat: 'YYYY-MM-DD',
        }
      },
    },
    {
      field: 'tenantChildFlag',
      label: '是否包含下级工会',
      component: 'Switch',
      labelWidth: 140,
      colProps: { span: 6 },
      defaultValue: true,
      componentProps: {
        checkedChildren: '是',
        unCheckedChildren: '否',
        checkedValue: true,
        unCheckedValue: false,
      },
    },
  ]
}

export const modalForm = (disabled): FormSchema[] => {

  return [
    {
      field: 'fieldCategoryBizId',
      label: '上报类型',
      component: 'ApiSelect',
      colProps: { span: 8 },
      required: true,
      slot: 'fieldCategorySlot',
      // componentProps({ formModel }) {
      //   return {
      //     api: list,
      //     resultField: 'data',
      //     showSearch: true,
      //     params: {
      //       pageSize: 0,
      //     },
      //     fieldNames: { label: 'fieldCategoryName', value: 'fieldCategoryId' },
      //     filterOption(input: string, option: any) {
      //       return option.fieldCategoryName.toLowerCase().indexOf(input.toLowerCase()) >= 0
      //     },
      //     getPopupContainer: () => document.body,
      //   }
      // },
    },
    {
      field: 'submitStatus',
      label: '上报状态',
      component: 'Select',
      colProps: { span: 8 },
      ifShow: disabled,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('subumit_status') as any,
      },
    },
    {
      field: 'submitCompanyName',
      label: '上报单位',
      component: 'Input',
      colProps: { span: 8 },
      ifShow: disabled,
    },
    {
      field: 'submitUserName',
      label: '上报人员',
      component: 'Input',
      colProps: { span: 8 },
      ifShow: disabled,
    },
    {
      field: 'submitUserPhone',
      label: '上报人员电话',
      component: 'Input',
      colProps: { span: 8 },
      ifShow: disabled,
    },
    {
      field: 'submitTime',
      label: '上报时间',
      component: 'Input',
      colProps: { span: 8 },
      ifShow: disabled,
    },
    {
      field: 'returnRemark',
      label: '退回原因',
      component: 'Input',
      colProps: { span: 8 },
      ifShow: ({ values }) => values.returnRemark,
    },
    {
      field: 'dataVersion',
      label: '版本号',
      component: 'Input',
      colProps: { span: 8 },
      ifShow: false,
    },
  ]
}


export const returnModalForm = (disabled): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'submitId',
      label: '填报id',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow: false
    },
    {
      field: 'returnRemark',
      label: '退回原因',
      // required: function ({ values }) {
      //   if (values.auditStatus == 'refuse') {
      //     return true;
      //   } else {
      //     return false;
      //   }
      // },
      rulesMessageJoinLabel: true,
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 200,
      },
    },
  ];
};
