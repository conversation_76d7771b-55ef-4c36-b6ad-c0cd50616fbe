<template>
  <div class="w-full h-full">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" :loading="loading" @click="handleExport">
          {{ loading ? '导出中' : '导出' }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              // auth: '/system/whiteList/view',
            },
            {
              icon: 'mingcute:sort-descending-fill',
              label: '退回',
              type: 'primary',
              onClick: handleReturn.bind(null, record),
              disabled: record.submitStatus != 'had_submit',
              //disabled: record.submitStatus == 'had_submit' || record.submitCompanyId !== userInfo.companyId,
              // auth: '/system/whiteList/update',
            },
            // {
            //   icon: 'fa6-solid:pen-to-square',
            //   label: '编辑',
            //   type: 'primary',
            //   onClick: handleEdit.bind(null, record),
            //   disabled:
            //     record.submitStatus == 'had_submit' ||
            //     record.submitCompanyId !== userInfo.companyId,
            //   // auth: '/system/whiteList/update',
            // },
            // {
            //   icon: 'ix:upload-document-note',
            //   label: '上报',
            //   type: 'primary',
            //   disabled:
            //     record.submitStatus == 'had_submit' ||
            //     record.submitCompanyId !== userInfo.companyId,
            //   onClick: handleReport.bind(null, record),
            //   // auth: '/system/whiteList/update',
            // },
            // {
            //   icon: 'fluent:delete-20-filled',
            //   label: '删除',
            //   type: 'primary',
            //   onClick: handleDelete.bind(null, record),
            //   danger: true,
            //   disabled:
            //     record.submitStatus == 'had_submit' ||
            //     record.submitCompanyId !== userInfo.companyId,
            //   // auth: '/system/whiteList/delete',
            // },
          ]" />
        </template>
      </template>
    </BasicTable>

    <ItemModal @register="registerModal" @success="handleSuccess" :canFullscreen="false" width="70%" />
    <ReturnModal @register="registerReturnModal" :canFullscreen="false" width="35%" @success="handleReturnModel">
    </ReturnModal>
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { columns, formSchemas } from './data';
import ItemModal from './ItemModal.vue';
import { useModal } from '/@/components/Modal';
import { recordList, saveByDTO, updateByDTO, submitExport, view, reporting } from '/@/api/report/index';
import { useMessage } from '@monorepo-yysz/hooks';
import { ref, unref } from 'vue';
import ReturnModal from './returnModal.vue';
import dayjs from 'dayjs';
import { downloadByUrl } from '@monorepo-yysz/utils';

const { createConfirm, createErrorModal, createMessage } = useMessage();
const loading = ref(false);
const searchParams = ref({});
const [registerTable, { reload }] = useTable({
  rowKey: 'submitId',
  columns: columns(),
  showIndexColumn: false,
  // authInfo: ['/system/whiteList/add'],
  api: recordList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    actionColOptions: { span: 4 },
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  beforeFetch: async params => {
    let { time } = params;
    if (time && time.length) {
      params.startTime = time[0] + ' 00:00:00';
      params.endTime = time[1] + ' 23:59:59';
      delete params.time;
    }
    searchParams.value = params;
    return params;
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 280,
    dataIndex: 'action',
    fixed: 'right',
    // auth: ['/system/whiteList/update', '/system/whiteList/view', '/system/whiteList/delete'],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();
const [registerReturnModal, { openModal: openReturnModal, closeModal: closeReturnModal }] =
  useModal();


//退回
function handleReturn(record) {
  openReturnModal(true, { isUpdate: true, record });
}

//详情
function handleView(record) {
  view({ submitId: record.submitId }).then(({ data, code, message }) => {
    if (code !== 200) return createErrorModal({ content: `查询失败，${message}` });
    openModal(true, {
      record: data,
      isUpdate: true,
      disabled: true,
    });
  });
}

function handleExport() {
  loading.value = true;
  try {
    submitExport(unref(searchParams)).then(res => {
      loading.value = false;
      const url = window.URL.createObjectURL(res?.data)
      const day = dayjs().format('YYYY-MM-DD HH:mm')
      downloadByUrl({
        url,
        fileName: '填报记录' + day + '.xlsx',
      })
    })
  } catch (error) {
    loading.value = false;
  }

}


//提交表单
function handleSuccess({ isUpdate, values }) {
  const api = isUpdate ? updateByDTO : saveByDTO;
  api(values).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success(`${isUpdate ? '编辑' : '新增'}成功!`);
      closeModal();
      reload();
    } else {
      createErrorModal({ content: `${isUpdate ? '编辑' : '新增'}失败，${message}` });
    }
  });
}

//退回
function handleReturnModel(record) {
  console.log(record);
  reporting(record.values).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success('退回成功');
      closeReturnModal();
      reload();
    } else {
      createErrorModal({ content: `退回失败，${message}` });
    }
  });
}
</script>
