<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { returnModalForm } from './data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const title = computed(() => {
  return `退回`;
});

const form = computed(() => {
  return returnModalForm();
});

const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  const { evidentiaryMaterial } = data.record;
  setFieldsValue({
    ...data.record,
    evidentiaryMaterial: evidentiaryMaterial ? evidentiaryMaterial.split(',') : [],
  });

  setModalProps({ confirmLoading: false });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    const { returnRemark } = values;
    emit('success', {
      values: {
        submitId: unref(record)?.submitId,
        submitStatus:'had_returned',
        returnRemark,
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
