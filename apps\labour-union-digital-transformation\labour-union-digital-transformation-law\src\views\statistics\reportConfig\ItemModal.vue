<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" @ok="handleSubmit">
    <div class="w-full flex main">
      <div class="left box-border pr-10px">
        <BasicForm @register="registerForm" :class="disabledClass">
          <template #fieldTitleSlot="{ model, field }">
            <Input v-model:value="model[field]" :maxlength="40" :showCount="true" allowClear @change="changefile"
              placeholder="请输入填报项名称" />
          </template>
          <template #fieldTypeSlot="{ model, field }">
            <Select v-model:value="model[field]" :options="typeOption" @change="changeType" placeholder="请选择填报项类型" />
          </template>
          <template #fieldRequireSlot="{ model, field }">
            <RadioGroup v-model:value="model[field]" :options="plainOptions" @change="changefile"
              placeholder="请选择是否必填" />
          </template>
          <template #fieldMultipleSlot="{ model, field }">
            <RadioGroup v-model:value="model[field]" :options="plainOptions" @change="changefile" />
          </template>
          <template #fieldDefaultValueSlot="{ model, field }">
            <Select v-model:value="model[field]" v-if="showOptions" :options="labelOptions" @change="changefile"
              placeholder="请选择填报项默认值" />
            <Input v-model:value="model[field]" v-else :maxlength="40" :showCount="true" allowClear @change="changefile"
              placeholder="请输入填报项默认值" />
          </template>
          <template #fieldMinValueSlot="{ model, field }">
            <InputNumber v-model:value="model[field]" allowClear @change="changefile"
              :placeholder="model['fieldType'] == 'InputNumber' ? '请输入最小值' : '请输入文件上传个数限制'"
              :min="model['fieldType'] != 'InputNumber' ? 1 : undefined"
              :max="model['fieldType'] != 'InputNumber' ? undefined : model['fieldMaxValue']" />
          </template>
          <template #fieldMaxValueSlot="{ model, field }">
            <InputNumber v-model:value="model[field]" allowClear @change="changefile"
              :placeholder="model['fieldType'] == 'InputNumber' ? '请输入最大值' : model['fieldType'] == 'Input' || model['fieldType'] == 'InputTextArea' ? '请输入最大字数' : '请输入单个文件大小限制'"
              :min="model['fieldType'] != 'InputNumber' ? 1 : model['fieldMinValue']" />
          </template>
        </BasicForm>
      </div>
      <div class="w-1/2 box-border px-10px" v-show="configInfo.length">
        <div class="text-16px font-bold pb-2 text-center title ">填报项预览</div>
        <div class="w-full my-10px">
          <div class="text-[#999] my-10px" v-show="!disabled">提示：可输入/选择默认值</div>
          <DynamicForm :options="configInfo" type="field" ref="dynamicFormRef" :disabled="disabled" />
        </div>
        <div class="w-full" v-show="showOptions">
          <div class="text-16px font-bold pb-2 ">填报项数据:</div>
          <AddOptions v-model:options="labelOptions" :disabled="disabled" :is-tree="isTree" @change="changeAddOpt" />
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import { defineComponent, ref, computed, unref, watch } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { modalForm } from './data'
import { Select, Input, Divider, RadioGroup, InputNumber, TimePicker, DatePicker } from 'ant-design-vue'
import { useDictionary } from '/@/store/modules/dictionary'
import DynamicForm from '/@/views/components/DynamicForm/index.vue'
import AddOptions from '/@/views/components/AddOptions/index.vue'
import { useMessage } from '@monorepo-yysz/hooks'
import { isArray } from 'lodash-es'


const dictionary = useDictionary()
const dynamicFormRef = ref();
const emit = defineEmits(['success', 'register'])
const { createErrorModal } = useMessage()
const isUpdate = ref(true)
const disabled = ref(false)
const showOptions = ref(false)
const isTree = ref(false);

const record = ref<Recordable>();
const configInfo = ref<Recordable[]>([]);//动态表单配置信息
const typeOption = ref<Recordable[]>([])
const plainOptions = ref<Recordable[]>([])
let labelOptions = ref([
  { label: '选项1', value: "value1", autoId: new Date().getTime() },
])

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.fieldTitle || ''}--详情`
    : unref(isUpdate)
      ? `编辑--${unref(record)?.fieldTitle || ''}`
      : '新增填报项'
})
const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})

const form = computed(() => {
  return modalForm(unref(record))
})

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields()
  configInfo.value = [];
  isTree.value = false;
  isUpdate.value = !!data?.isUpdate
  disabled.value = !!data?.disabled
  typeOption.value = dictionary.getDictionaryOpt.get('field_type') as any[];
  plainOptions.value = dictionary.getDictionaryOpt.get('yes_no') as any[];

  showOptions.value = false;
  labelOptions.value = [{ label: '选项1', value: "value1", autoId: new Date().getTime() },];
  record.value = data.record


  if (unref(isUpdate)) {

    if (unref(record)?.fieldType == 'Select' || unref(record)?.fieldType == 'TreeSelect' || unref(record)?.fieldType == 'RadioGroup' || unref(record)?.fieldType == 'CheckboxGroup') {
      showOptions.value = true;
      isTree.value = unref(record)?.fieldType == 'TreeSelect' ? true : false;
    }

    if (unref(record)?.fieldDict) {
      labelOptions.value = JSON.parse(data.record.fieldDict);
    }

    configInfo.value = [{ ...record.value, fieldOptions: unref(labelOptions) }];

    setFieldsValue({
      ...data.record,
    })
  }

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  })

  setProps({ disabled: unref(disabled) })
})

const [registerForm, { setFieldsValue, resetFields, validate, setProps, getFieldsValue }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
})

function changefile(e) {
  dynamicFormRef.value?.resetFields();
  configInfo.value = [{ ...getFieldsValue(), fieldDefaultValue: '', fieldOptions: unref(labelOptions) }];

}
function changeType(e) {
  dynamicFormRef.value?.resetFields();
  if (e == 'Select' || e == 'TreeSelect' || e == 'RadioGroup' || e == 'CheckboxGroup') {
    showOptions.value = true;
    isTree.value = e == 'TreeSelect' ? true : false;
  } else {
    showOptions.value = false;
    isTree.value = false;
  }
  configInfo.value = [{ ...getFieldsValue(), fieldDefaultValue: '', fieldOptions: unref(labelOptions) }];
}
function changeAddOpt(opt) {
  configInfo.value[0].fieldOptions = opt;
}

async function handleSubmit() {
  try {
    const values = await validate();
    const dynamicFormValid = await dynamicFormRef.value?.getFieldsValue();
    values.fieldDefaultValue = !unref(isUpdate) ? dynamicFormValid.value : dynamicFormValid[unref(record)?.fieldBizId];

    values.fieldDefaultValue = isArray(values.fieldDefaultValue) ? JSON.stringify(values.fieldDefaultValue) : values.fieldDefaultValue;

    setModalProps({ confirmLoading: true })
    if (!unref(labelOptions).length && (values.fieldType == 'Select' || values.fieldType == 'RadioGroup' || values.fieldType == 'CheckboxGroup')) {
      return createErrorModal({ content: '请添加选填项关联数据' });
    }

    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...unref(record),
        ...values,
        fieldDict: unref(labelOptions).length && JSON.stringify(unref(labelOptions)),
      },
    })
  } finally {
    setModalProps({ confirmLoading: false })
  }
}


</script>
<style lang="less" scoped>
.main {
  .left {
    flex: 1;
  }

  .title {
    border-bottom: 1px solid #5A9EFB;
  }
}
</style>
