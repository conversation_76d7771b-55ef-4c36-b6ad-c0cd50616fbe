import { Tooltip } from 'ant-design-vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { uploadApi } from '@/api/sys/upload';
import { validatePhone } from '@monorepo-yysz/utils';
import dayjs from 'dayjs';
import { queryDictionary } from '@/api/system/dictionary';
const dictionary = useDictionary()

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '填报项名称',
      dataIndex: 'fieldTitle',

    },
    {
      title: '填报项类型',
      dataIndex: 'fieldType',
      width: 200,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`field_type_${text}`)?.dictName
        return name
      },
    },
    {
      title: '是否必填',
      dataIndex: 'fieldRequire',
      width: 200,
      customRender: ({ text }) => {
        const name = text == 'y' ? '是' : '否'
        const color = text == 'y' ? 'green' : ''
        return <span style={{ color: color }}>{name}</span>
      },
    },
    // {
    //   title: '版本号',
    //   dataIndex: 'dataVersion',
    //   width: 200,
    // },
    // {
    //   title: '备注',
    //   dataIndex: 'fieldPrecautions',
    //   width: 200,
    // },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 200,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'fieldTitle',
      label: '填报项名称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
    // {
    //   field: 'fieldType',
    //   label: '填报项类型',
    //   colProps: { span: 6 },
    //   component: 'Select',
    //   componentProps: function () {
    //     return {
    //       options: dictionary.getDictionaryOpt.get('field_type'),
    //       placeholder: '请选择填报项类型',
    //       getPopupContainer: () => document.body,
    //     }
    //   },
    // },
    {
      field: 'fieldType',
      label: '填报项类型',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          api: queryDictionary,
          resultField: 'data',
          showSearch: true,
          params: {
            pageSize: 0,
            groupCode: 'field_type',
          },
          fieldNames: { label: 'dictName', value: 'dictCode' },
          filterOption(input: string, option: any) {
            return option.dictName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          getPopupContainer: () => document.body,
        }
      },
    },
    {
      field: 'fieldRequire',
      label: '是否必填',
      component: 'RadioGroup',
      colProps: { span: 6 },
      defaultValue: '',
      componentProps() {
        let options = dictionary.getDictionaryOpt.get('yes_no') as any
        return {
          options: [
            { label: '全部', value: '' },
            ...options,
          ],
        }
      },
    },
  ]
}

export const modalForm = (record?: Recordable): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'fieldTitle',
      label: '填报项名称',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: true,
      slot: 'fieldTitleSlot',
      // componentProps: {
      //   autocomplete: 'off',
      //   showCount: true,
      //   maxlength: 40,
      // },
    },

    {
      field: 'fieldType',
      label: '填报项类型',
      colProps: { span: 24 },
      component: 'Select',
      required: true,
      // componentProps: function () {
      //   return {
      //     options: dictionary.getDictionaryOpt.get('field_type'),
      //     placeholder: '请选择填报项类型',
      //   }
      // },
      slot: 'fieldTypeSlot',
    },
    {
      field: 'fieldRequire',
      label: '是否必填',
      component: 'RadioGroup',
      defaultValue: 'y',
      colProps: { span: 24 },
      // componentProps: {
      //   options: dictionary.getDictionaryOpt.get('yes_no') as any,
      // },
      slot: 'fieldRequireSlot',
    },
    {
      field: 'fieldMultiple',
      label: ({ values }) => values?.fieldType == 'Select' || values?.fieldType == 'TreeSelect' ? '是否多选' : '是否时间范围选择',
      component: 'RadioGroup',
      defaultValue: 'n',
      colProps: { span: 24 },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('yes_no') as any,
      },
      ifShow: ({ values }) => values?.fieldType == 'Select' || values?.fieldType == 'TreeSelect' || values?.fieldType == 'TimePicker' || values?.fieldType == 'DatePicker',
      slot: 'fieldMultipleSlot',
    },
    // {
    //   field: 'fieldDefaultValue',
    //   label: '填报项默认值',
    //   colProps: { span: 24 },
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    //   ifShow: ({ values }) => values?.fieldType != 'TimePicker' && values?.fieldType != 'DatePicker',
    //   // componentProps: {
    //   //   autocomplete: 'off',
    //   //   showCount: true,
    //   //   maxlength: 40,
    //   // },
    //   slot: 'fieldDefaultValueSlot',
    // },
    {
      field: 'fieldMinValue',
      label: ({ values }) => values.fieldType == 'InputNumber' ? '最小值' : '文件上传个数限制',
      component: 'InputNumber',
      defaultValue: 1,
      colProps: { span: 24 },
      ifShow: ({ values }) => values.fieldType == 'InputNumber' || values.fieldType == 'Upload',
      slot: 'fieldMinValueSlot',
      // componentProps({ formModel }) {
      //   return {
      //     min: formModel['fieldType'] !== 'InputNumber' ? 1 : undefined,
      //     max: formModel['fieldType'] !== 'InputNumber' ? undefined : formModel['fieldMaxValue'],
      //   }
      // }
    },
    {
      field: 'fieldMaxValue',
      label: ({ values }) => values.fieldType == 'InputNumber' ? '最大值' : values.fieldType == 'Input' || values.fieldType == 'InputTextArea' ? '最大字数' : values.fieldType == 'Upload' ? '文件大小限制' : '',
      component: 'InputNumber',
      defaultValue: 99,
      colProps: { span: 24 },
      ifShow: ({ values }) => values.fieldType == 'InputNumber' || values.fieldType == 'Input' || values.fieldType == 'Upload' || values.fieldType == 'InputTextArea',
      // componentProps({ formModel }) {
      //   return {
      //     min: formModel['fieldMinValue'],
      //   }
      // },
      slot: 'fieldMaxValueSlot',
    },
    {
      field: 'fieldPrecautions',
      label: '备注',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 400,
      },
    },
    {
      field: 'fieldDict',
      label: '填报项数据',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      show: false,
    },
    // {
    //   field: 'rectifyBeforePhotoFile',
    //   label: '隐患图片',
    //   colProps: { span: 24 },
    //   component: 'UploadFile',
    //   required: true,
    //   pStyle: 'width:100%',
    //   rulesMessageJoinLabel: true,
    //   componentProps: {
    //     operateType: 9999,
    //     api: uploadApi,
    //     maxSize: 3,
    //     maxNumber: 3,
    //   },
    // },
    // {
    //   field: 'rectifyBeforePhotoFile1',
    //   label: '隐患图片',
    //   colProps: { span: 12 },
    //   component: 'Upload',
    //   required: true,

    //   rulesMessageJoinLabel: true,
    //   componentProps: {
    //     operateType: 9999,
    //     api: uploadApi,
    //     maxSize: 3,
    //     maxNumber: 3,
    //   },
    // },
  ]
}

