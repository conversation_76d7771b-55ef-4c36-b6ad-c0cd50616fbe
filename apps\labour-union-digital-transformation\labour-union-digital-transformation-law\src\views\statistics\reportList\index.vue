<template>
  <div class="w-full h-full">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleClick">
          信息填报
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
            },

            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              ifShow: getShow(record),
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ix:upload-document-note',
              label: '上报',
              type: 'success',
              ifShow: getShow(record),
              popConfirm: {
                title: `确定上报此填报数据吗?`,
                confirm: handleReport.bind(null, record),
              },
            },
            {
              icon: 'fluent:delete-20-filled',
              label: '删除',
              type: 'primary',
              ifShow: getShow(record),
              danger: true,
              popConfirm: {
                title: `确定删除此填报数据吗?`,
                confirm: handleDelete.bind(null, record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <ItemModal @register="registerModal" @success="handleSuccess" :canFullscreen="false" width="70%" />
    <ReturnModal @register="registerReturnModal" :canFullscreen="false" width="35%" @success="handleReturnModel" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { columns, formSchemas } from './data'
import ItemModal from './ItemModal.vue'
import { list, saveByDTO, updateByDTO, remove, view, reporting } from '/@/api/report/index';
import { useMessage } from '@monorepo-yysz/hooks';
import ReturnModal from '../recordList/returnModal.vue';

const { createConfirm, createErrorModal, createMessage } = useMessage()
const fieldCategoryName = ref('');
const fieldCategoryBizId = ref('');
const fieldCategoryId = ref('');
const [registerTable, { reload }] = useTable({
  rowKey: 'submitId',
  columns: columns(),
  showIndexColumn: false,
  // authInfo: ['/system/whiteList/add'],
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    actionColOptions: { span: 4 },
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 350,
    dataIndex: 'action',
    fixed: 'right',
    // auth: ['/system/whiteList/update', '/system/whiteList/view', '/system/whiteList/delete'],
  },
})

const [registerModal, { openModal, closeModal }] = useModal()
const [registerReturnModal, { openModal: openReturnModal, closeModal: closeReturnModal }] =
  useModal();

function getShow(record) {
  if (!record?.isOwn) return false
  return record.submitStatus !== 'had_submit';
}

//新增
function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
    record: {
      fieldCategoryBizId: fieldCategoryBizId.value,
      fieldCategoryName: fieldCategoryName.value,
      fieldCategoryId: fieldCategoryId.value
    },
  })
}

//编辑
function handleEdit(record) {
  view({ submitId: record.submitId }).then(({ data, code, message }) => {
    if (code !== 200) return createErrorModal({ content: `查询失败，${message}` })
    openModal(true, {
      record: data,
      isUpdate: true,
      disabled: false,
    })
  })
}

//详情
function handleView(record) {
  view({ submitId: record.submitId }).then(({ data, code, message }) => {
    if (code !== 200) return createErrorModal({ content: `查询失败，${message}` })
    openModal(true, {
      record: data,
      isUpdate: true,
      disabled: true,
    })
  })
}

//删除
function handleDelete(record) {
  remove(record.submitId).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success('删除成功')
      reload()
    } else {
      createErrorModal({ content: `删除失败，${message}` })
    }
  })
}

//上报
async function handleReport(record) {
  reporting({ submitId: record.submitId, submitStatus: 'had_submit' }).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success(`上报成功`)
      reload()
    } else {
      createErrorModal({ content: `上报失败，${message}` })
    }
  })
}

//退回
function handleReportReturn(record) {
  openReturnModal(true, { isUpdate: true, record });
}

//提交表单
function handleSuccess({ isUpdate, values }) {
  const api = isUpdate ? updateByDTO : saveByDTO;
  api(values).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success(`${isUpdate ? '编辑' : '新增'}成功!`)
      closeModal()
      reload()

    } else {
      createErrorModal({ content: `${isUpdate ? '编辑' : '新增'}失败，${message}` })
    }
  })
}

function handleReturnModel(record) {
  reporting(record.values).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success('退回成功');
      closeReturnModal();
      reload();
    } else {
      createErrorModal({ content: `退回失败，${message}` });
    }
  });
}
</script>
