<template>
  <div class="flex">
    <div class="w-1/5">
      <ConfigTypeTree @selectInfo="changeType" />
    </div>
    <div>
      <BasicTable @register="registerTable">
        <!-- <template #toolbar>
          <a-button type="primary" @click="handleClick">
            信息填报
          </a-button>
        </template> -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                // {
                //   icon: 'carbon:task-view',
                //   label: '详情',
                //   type: 'default',
                //   onClick: handleView.bind(null, record),
                //   // auth: '/system/whiteList/view',
                // },
                {
                  icon: 'ix:upload-document-note',
                  label: '信息填报',
                  type: 'primary',
                  disabled: record.submitStatus == 'had_submit',
                  onClick: handleReport.bind(null, record),
                  // auth: '/system/whiteList/update',
                },
                {
                  icon: 'ant-design:container-twotone',
                  label: '填报记录',
                  type: 'primary',
                  onClick: handleRecord.bind(null, record),
                  // auth: '/system/whiteList/delete',
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <ItemModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="70%"
    />
    <RecordList
      @register="registerRecordModal"
      @success="handleRecordSuccess"
      :canFullscreen="false"
      width="70%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { columns, formSchemas } from './data';
import ItemModal from './ItemModal.vue';
import { useModal } from '/@/components/Modal';
import { findList, view } from '/@/api/report/configType';
import { saveByDTO, updateByDTO, remove } from '/@/api/report/index';
import { useMessage } from '@monorepo-yysz/hooks';
import { ref } from 'vue';
import RecordList from './record/index.vue';

const { createConfirm, createErrorModal, createMessage } = useMessage();
const fieldCategoryName = ref('');
const fieldCategoryBizId = ref('');
const fieldCategoryId = ref('');
const [registerTable, { reload, updateTableDataRecord }] = useTable({
  rowKey: 'submitId',
  columns: columns(),
  showIndexColumn: false,
  // authInfo: ['/system/whiteList/add'],
  api: findList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    actionColOptions: { span: 4 },
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  beforeFetch: async params => {
    // params.fieldCategoryName = fieldCategoryName.value;
    return params;
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 380,
    dataIndex: 'action',
    fixed: 'right',
    // auth: ['/system/whiteList/update', '/system/whiteList/view', '/system/whiteList/delete'],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();
const [registerRecordModal, { openModal:openRecordModal }] = useModal();
function changeType(params: any) {
  fieldCategoryName.value = params?.name || "";
  fieldCategoryBizId.value = params?.id || "";
  fieldCategoryId.value = params?.fieldCategoryId || "";
  reload();
}

//新增
function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
    record: {
      fieldCategoryBizId: fieldCategoryBizId.value,
      fieldCategoryName: fieldCategoryName.value,
      fieldCategoryId: fieldCategoryId.value,
    },
  });
}

//编辑
function handleEdit(record) {
  view({ submitId: record.submitId }).then(({ data, code, message }) => {
    if (code !== 200) return createErrorModal({ content: `查询失败，${message}` });
    openModal(true, {
      record: data,
      isUpdate: true,
      disabled: false,
    });
  });
}

//详情
function handleView(record) {
  view({ submitId: record.submitId }).then(({ data, code, message }) => {
    if (code !== 200) return createErrorModal({ content: `查询失败，${message}` });
    openModal(true, {
      record: data,
      isUpdate: true,
      disabled: true,
    });
  });
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确定删除此填报数据？`,
    onOk: function () {
      remove(record.submitId).then(({ code, message }) => {
        if (code === 200) {
          createMessage.success('删除成功');
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

//上报
function handleReport(record) {
  openModal(true, {
    isUpdate: false,
    disabled: false,
    record: {
      fieldCategoryBizId: record.fieldCategoryBizId,
      fieldCategoryName: record.fieldCategoryName,
      fieldCategoryId: record.fieldCategoryId,
    },
  });
}

//填报记录
function handleRecord(record) {
  openRecordModal(true, { isUpdate: true, record });
}
//提交表单
function handleSuccess({ isUpdate, values }) {
  const api = isUpdate ? updateByDTO : saveByDTO;
  api(values).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success(`${isUpdate ? '编辑' : '新增'}成功!`);
      closeModal();
      reload();
    } else {
      createErrorModal({ content: `${isUpdate ? '编辑' : '新增'}失败，${message}` });
    }
  });
}
</script>
