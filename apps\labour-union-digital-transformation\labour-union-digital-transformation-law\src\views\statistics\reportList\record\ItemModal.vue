<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" @ok="handleSubmit">
    <div class="w-full ">
      <BasicForm @register="registerForm" :class="disabledClass">
        <template #fieldCategorySlot="{ model, field }">
          <Select v-model:value="model[field]" :options="configOption" @change="changeType" placeholder="请选择填报项类型"
            :fieldNames="{ label: 'fieldCategoryName', value: 'fieldCategoryBizId' }" />
        </template>
      </BasicForm>
      <div class="box-border pt-10px" v-show="fieldDTOList.length">
        <div class="text-16px font-bold pb-2 text-center title">数据填报</div>
        <DynamicForm ref="dynamicFormRef" :options="fieldDTOList" :disabled="disabled" />
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref, computed, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { modalForm } from './data';
import { list, view } from '/@/api/report/configType';
import { Select } from 'ant-design-vue'
import DynamicForm from '/@/views/components/DynamicForm/index.vue'

const emit = defineEmits(['success', 'register'])
const isUpdate = ref(true);
const dynamicFormRef = ref();
const disabled = ref(false);

const record = ref<Recordable>();
const fieldDTOList = ref<Recordable[]>([]);//动态表单配置信息
const configOption = ref([]);//所有填报项类型项数据

const title = computed(() => {
  return unref(disabled)
    ? `填报数据详情`
    : unref(isUpdate)
      ? `编辑填报数据`
      : '新增填报'
})

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})

const form = computed(() => {
  return modalForm(unref(disabled))
})

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields()
  fieldDTOList.value = [];
  isUpdate.value = !!data?.isUpdate
  disabled.value = !!data?.disabled
  record.value = data.record

  await getConfigOption()

  if (unref(isUpdate)) {
    let { fieldVOList, submitDataVOList } = data.record;
    if (fieldVOList && fieldVOList.length) {
      fieldDTOList.value = fieldVOList.map(el => {
        let value = submitDataVOList.find(item => item.fieldBizId == el.fieldBizId)?.fieldValue;
        return {
          ...el,
          fieldDefaultValue: value,
          fieldOptions: el.fieldDict ? JSON.parse(el.fieldDict) : []
        }
      });
    }
  } else {
    data.record?.fieldCategoryId && changeType('', data.record);
  }

  setFieldsValue({
    ...data.record,
  })
  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  })

  setProps({ disabled: unref(disabled) })
})

const [registerForm, { setFieldsValue, resetFields, validate, setProps, getFieldsValue }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
})

async function getConfigOption() {
  const res = await list({ pageSize: 0 });
  if (res.code == 200) {
    configOption.value = res.data.map(item => {
      if (item.fieldDict) item.fieldOptions = JSON.parse(item.fieldDict);
      return { ...item, label: item.fieldCategoryName, value: item.fieldCategoryId }
    });
  }
}

async function changeType(value, option) {
  let { fieldCategoryId } = option;
  view({ fieldCategoryId: fieldCategoryId }).then(({ code, data }) => {
    if (code == 200 && data) {
      setFieldsValue({ dataVersion: data.dataVersion || '' });
      fieldDTOList.value = data.fieldVOList?.map(item => {
        return {
          ...item,
          fieldDefaultValue: item.fieldDefaultValue && JSON.parse(item.fieldDefaultValue),
          fieldOptions: item.fieldDict ? JSON.parse(item.fieldDict) : []
        }
      });
    }
  });
}

//处理填报动态表单提交数据
function handleSubmitData(obj) {
  if (!obj) return [];
  let submitDataDTOList = [] as any[];
  for (let key in obj) {
    submitDataDTOList.push({
      fieldBizId: key,
      fieldValue: obj[key]
    })
  }
  return submitDataDTOList;
}

//处理接口所需要动态表单的信息
async function handleSubmit() {
  try {
    // 先验证DynamicForm
    const dynamicFormValid = await dynamicFormRef.value?.validate();
    if (!dynamicFormValid) return;

    // 再验证BasicForm
    const values = await validate();
    setModalProps({ confirmLoading: true });

    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...unref(record),
        ...values,
        submitDataDTOList: handleSubmitData(dynamicFormValid),
      },
    })
  } finally {
    setModalProps({ confirmLoading: false })
  }
}

</script>
<style lang="less" scoped>
.main {
  .left {
    flex: 1
  }

  .title {
    border-bottom: 1px solid #5A9EFB;
  }
}
</style>
