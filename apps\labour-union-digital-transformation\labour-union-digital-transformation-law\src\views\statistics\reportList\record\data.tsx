import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'

const dictionary = useDictionary()

export const columns = (): BasicColumn[] => {
  return [
    // {
    //   title: '类型名称',
    //   dataIndex: 'fieldCategoryName',
    // },
    {
      title: '上报单位',
      dataIndex: 'submitCompanyName',
      width: 200,
    },
    {
      title: '上报人员',
      dataIndex: 'submitUserName',
      width: 200,
    },
    {
      title: '上报人员电话',
      dataIndex: 'submitUserPhone',
      width: 200,
    },
    {
      title: '上报状态',
      dataIndex: 'submitStatus',
      width: 200,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`subumit_status_${text}`)?.dictName;
        const color = text == 'had_submit' ? 'green' : text == 'had_returned' ? 'red' :''
        return <span style={{ color: color }}>{name}</span>
      },
    },
    {
      title: '上报时间',
      dataIndex: 'submitTime',
      width: 200,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'fieldCategoryName',
      label: '类型名称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'submitStatus',
      label: '上报状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('subumit_status') as any,
      },
    },
    // {
    //   field: 'tenantChildFlag',
    //   label: '是否包含下级工会',
    //   component: 'Switch',
    //   labelWidth: 140,
    //   colProps: { span: 6 },
    //   componentProps: {
    //     checkedChildren: '是',
    //     unCheckedChildren: '否',
    //     checkedValue: true,
    //     unCheckedValue: false,
    //   },
    // },
  ]
}

export const modalForm = (disabled): FormSchema[] => {

  return [
    {
      field: 'fieldCategoryBizId',
      label: '上报类型',
      component: 'ApiSelect',
      colProps: { span: 12 },
      required: true,
      slot: 'fieldCategorySlot',
      // componentProps({ formModel }) {
      //   return {
      //     api: list,
      //     resultField: 'data',
      //     showSearch: true,
      //     params: {
      //       pageSize: 0,
      //     },
      //     fieldNames: { label: 'fieldCategoryName', value: 'fieldCategoryId' },
      //     filterOption(input: string, option: any) {
      //       return option.fieldCategoryName.toLowerCase().indexOf(input.toLowerCase()) >= 0
      //     },
      //     getPopupContainer: () => document.body,
      //   }
      // },
    },
    {
      field: 'submitStatus',
      label: '上报状态',
      component: 'Select',
      colProps: { span: 12 },
      ifShow: disabled,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('subumit_status') as any,
      },
    },
    {
      field: 'submitCompanyName',
      label: '上报单位',
      component: 'Input',
      colProps: { span: 12 },
      ifShow: disabled,
    },
    {
      field: 'submitUserName',
      label: '上报人员',
      component: 'Input',
      colProps: { span: 12 },
      ifShow: disabled,
    },
    {
      field: 'submitUserPhone',
      label: '上报人员电话',
      component: 'Input',
      colProps: { span: 12 },
      ifShow: disabled,
    },
    {
      field: 'submitTime',
      label: '上报时间',
      component: 'Input',
      colProps: { span: 12 },
      ifShow: disabled,
    },
    // {
    //   field: 'returnRemrk',
    //   label: '退回原因',
    //   component: 'Input',
    //   colProps: { span: 12 },
    //   ifShow: ({values}) => !values.returnRemrk &&  disabled,
    // },
  ]
}

