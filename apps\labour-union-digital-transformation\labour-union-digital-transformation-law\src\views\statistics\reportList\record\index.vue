<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
      <BasicTable @register="registerTable">
        <template #toolbar>
          <a-button type="primary" @click="handleClick">
            信息填报
          </a-button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                // auth: '/system/whiteList/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                disabled: record.submitStatus == 'had_submit',
                // auth: '/system/whiteList/update',
              },
              {
                icon: 'ix:upload-document-note',
                label: '上报',
                type: 'primary',
                disabled: record.submitStatus == 'had_submit',
                onClick: handleReport.bind(null, record),
                // auth: '/system/whiteList/update',
              },
              {
                icon: 'fluent:delete-20-filled',
                label: '删除',
                type: 'primary',
                onClick: handleDelete.bind(null, record),
                disabled: record.submitStatus == 'had_submit',
                danger: true,
                // auth: '/system/whiteList/delete',
              },
            ]" />
          </template>
        </template>
      </BasicTable>
    </BasicModal>
    <ItemModal @register="itemModal" @success="handleSuccess" :canFullscreen="false" width="70%" />
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { columns, formSchemas } from './data'
import ItemModal from './ItemModal.vue'
import { useModal,useModalInner,BasicModal } from '/@/components/Modal';
import { list, saveByDTO, updateByDTO, remove, view,reporting } from '/@/api/report/index';
import { useMessage } from '@monorepo-yysz/hooks';
import { ref,computed,unref,nextTick } from 'vue';

const { createConfirm, createErrorModal, createMessage } = useMessage()
const fieldCategoryName = ref('');
const fieldCategoryBizId = ref('');
const fieldCategoryId = ref('');


const title = computed(() => {
  return `${unref(fieldCategoryName)}的填报记录`;
});

const [registerModal, {}] = useModalInner(async data => {
  fieldCategoryBizId.value = data.record.fieldCategoryBizId;
  fieldCategoryId.value = data.record.fieldCategoryId;
  fieldCategoryName.value = data.record.fieldCategoryName;
  await reload();
  await nextTick();
  // await clearSelectedRowKeys();
  console.log(data);  
});

const [registerTable, { reload, clearSelectedRowKeys }] = useTable({
  rowKey: 'submitId',
  columns: columns(),
  showIndexColumn: false,
  // authInfo: ['/system/whiteList/add'],
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    actionColOptions: { span: 4 },
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  beforeFetch: async (params) => {
    params.fieldCategoryBizId = fieldCategoryBizId.value;
    return params;
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 380,
    dataIndex: 'action',
    fixed: 'right',
    // auth: ['/system/whiteList/update', '/system/whiteList/view', '/system/whiteList/delete'],
  },
})

const [itemModal, { openModal: openItemModal, closeModal: closeItemModal }] = useModal()

// function changeType(params: any) {
//   fieldCategoryName.value = params?.name || "";
//   fieldCategoryBizId.value = params?.id || "";
//   fieldCategoryId.value = params?.fieldCategoryId || "";
//   reload();
// }

//新增
function handleClick() {
  openItemModal(true, {
    isUpdate: false,
    disabled: false,
    record: {
      fieldCategoryBizId: fieldCategoryBizId.value,
      fieldCategoryName: fieldCategoryName.value,
      fieldCategoryId:fieldCategoryId.value
    },
  })
}

//编辑
function handleEdit(record) {
  view({ submitId: record.submitId }).then(({ data, code, message }) => {
    if (code !== 200) return createErrorModal({ content: `查询失败，${message}` })
    openItemModal(true, {
      record: data,
      isUpdate: true,
      disabled: false,
    })
  })
}

//详情
function handleView(record) {
  view({ submitId: record.submitId }).then(({ data, code, message }) => {
    if (code !== 200) return createErrorModal({ content: `查询失败，${message}` })
    openItemModal(true, {
      record: data,
      isUpdate: true,
      disabled: true,
    })
  })
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确定删除此填报数据？`,
    onOk: function () {
      remove(record.submitId).then(({ code, message }) => {
        if (code === 200) {
          createMessage.success('删除成功')
          reload()
        } else {
          createErrorModal({ content: `删除失败，${message}` })
        }
      })
    },
  })
}

//上报
async function handleReport(record) {
  console.log(record.submitId);
  reporting({ submitId: record.submitId ,submitStatus:'had_submit' }).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success('上报成功')
      reload()
    } else {
      createErrorModal({ content: `上报失败，${message}` })
    }
  })
}


//提交表单
function handleSuccess({ isUpdate, values }) {
  const api = isUpdate ? updateByDTO : saveByDTO;
  api(values).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success(`${isUpdate ? '编辑' : '新增'}成功!`)
      closeItemModal()
      reload()

    } else {
      createErrorModal({ content: `${isUpdate ? '编辑' : '新增'}失败，${message}` })
    }
  })
}

</script>
