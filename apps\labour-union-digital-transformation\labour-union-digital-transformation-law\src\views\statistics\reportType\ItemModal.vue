<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" @ok="handleSubmit">
    <div class="w-full flex main">
      <div class="left box-border pr-10px">
        <BasicForm @register="registerForm" :class="disabledClass">
          <template #typeSlot="{ model, field }">
            <Select v-model:value="model[field]" placeholder="可快速选择历史类型配置项内容" :options="allTypes"
              :fieldNames="{ label: 'fieldCategoryName', value: 'fieldCategoryId' }" @change="changeType"></Select>
          </template>
          <template #fieldDTOListSlot="{ model, field }">
            <div class="w-full">
              <TypeList v-model:selectedKeys="selectedKeys" :disabled="disabled" @change="changeField" />
            </div>
            <!-- <CheckboxGroup v-model:value="model[field]" :options="configOption" @change="changeField" /> -->
          </template>
        </BasicForm>
      </div>
      <div class="w-1/2 box-border pl-10px" v-show="fieldDTOList.length">
        <div class="text-16px font-bold pb-2 text-center title">表单预览</div>
        <div class="text-[#ed6f6f] my-10px" v-show="!disabled">可长按拖拽改变表单位置</div>
        <DynamicForm :options="fieldDTOList" :isdrag="true" :disabled="disabled" type="modal" @change="changeOpt" />
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref, computed, unref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm, ApiSelect } from '/@/components/Form'
import { modalForm } from './data'
import { list, } from '/@/api/report/config';
import { list as typeList, view } from '/@/api/report/configType';
import { CheckboxGroup, Select } from 'ant-design-vue'
import { useDictionary } from '/@/store/modules/dictionary'
import DynamicForm from '/@/views/components/DynamicForm/index.vue'
import { useMessage } from '@monorepo-yysz/hooks'
import { isArray } from '@monorepo-yysz/utils';
import TypeList from './typeList.vue';


const dictionary = useDictionary()
const emit = defineEmits(['success', 'register'])
const { createErrorModal } = useMessage()
const isUpdate = ref(true)
const disabled = ref(false)

const record = ref<Recordable>();
const allTypes = ref<Recordable[]>([])
const selectedKeys = ref<string[]>([])
const fieldDTOList = ref<Recordable[]>([]);//动态表单配置信息

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.fieldCategoryName || ''}--详情`
    : unref(isUpdate)
      ? `编辑--${unref(record)?.fieldCategoryName || ''}`
      : '新增上报类型'
})

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})



const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields()
  fieldDTOList.value = [];
  selectedKeys.value = [];
  isUpdate.value = !!data?.isUpdate
  disabled.value = !!data?.disabled
  record.value = data.record

  if (unref(isUpdate)) {
    let { fieldVOList } = data.record;
    if (fieldVOList && fieldVOList.length) {
      changeField(fieldVOList);
      selectedKeys.value = unref(fieldDTOList).map(item => item.fieldBizId)
    }

    setFieldsValue({
      ...data.record,
      fieldDTO: selectedKeys.value,
    })
  } else {
    getAllType()
  }

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  })

  setProps({ disabled: unref(disabled) })
})
const form = computed(() => {
  return modalForm(unref(isUpdate))
})
const [registerForm, { setFieldsValue, resetFields, validate, setProps, getFieldsValue }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
})


async function getAllType() {
  const res = await typeList({ pageSize: 0 });
  if (res.code == 200) {
    allTypes.value = res.data;
  }
}

function changeField(value) {
  let arr = [...fieldDTOList.value]
  fieldDTOList.value = value.map(el => {
    let obj = arr.find(item => item.fieldBizId == el.fieldBizId);
    return {
      ...el,
      ...obj,
      fieldOptions: el.fieldDict ? JSON.parse(el.fieldDict) : []
    }
  });

  setFieldsValue({
    fieldDTO: selectedKeys.value,
  })
}

async function changeType(value, opt) {
  view({ fieldCategoryId: opt.fieldCategoryId }).then(({ data, code, message }) => {
    if (code == 200) {
      let { fieldVOList } = data;
      selectedKeys.value = fieldVOList.map(item => item.fieldBizId);
      changeField(fieldVOList);
      setFieldsValue({
        fillingFrequency: data?.fillingFrequency || 'ls',
        notificationMethod: data?.notificationMethod || 'SMS',
        fieldCategoryPrecautions: data?.fieldCategoryPrecautions || '',
        fieldDTO: selectedKeys.value,
      })
    }
  })
}

//获取动态表单调整后的配置
function changeOpt(value) {
  fieldDTOList.value = value;
}

//处理接口所需要动态表单的信息
async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true });
    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...unref(record),
        ...values,
        fieldDTOList: unref(fieldDTOList),
      },
    })
  } finally {
    setModalProps({ confirmLoading: false })
  }
}



</script>
<style lang="less" scoped>
.main {
  .left {
    flex: 1
  }

  .border {
    border: #5A9EFB solid 1px;
  }

  .title {
    border-bottom: 1px solid #5A9EFB;
  }
}
</style>
