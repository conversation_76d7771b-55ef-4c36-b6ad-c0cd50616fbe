import { Switch } from 'ant-design-vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { enableOrDisable, list } from '/@/api/report/configType';
import { useMessage } from '@monorepo-yysz/hooks';
import { queryDictionary } from '@/api/system/dictionary';


const dictionary = useDictionary()

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '类型名称',
      dataIndex: 'fieldCategoryName',
    },
    {
      title: '填报频率',
      dataIndex: 'fillingFrequency',
      width: 200,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`submission_frequency_${text}`)?.dictName
        return name
      },
    },
    {
      title: '通知方式',
      dataIndex: 'notificationMethod',
      width: 200,
      customRender: ({ text }) => {
        // const nameList = text.split(',');
        // const result = nameList
        //   .map(e => {
        //     const trimmed = e.trim(); // 防止有空格干扰
        //     if (!trimmed) return '';
        //     return dictionary.getDictionaryMap.get('notice_type_' + trimmed)?.dictName || '';
        //   })
        //   .join(',');
        // return result;
        return dictionary.getDictionaryMap.get(`notice_type_${text}`)?.dictName;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      customRender: ({ value, record }) => {
        const { createConfirm, createSuccessModal, createErrorModal } = useMessage();
        const name = value === 'y' ? '启用' : '禁用';
        let flg = value === 'y';
        const el = (
          <div>
            <Switch
              checked-children={name}
              unCheckedChildren={name}
              checked={flg}
              class={`${'cursor-pointer'}`}
              // style={{ backgroundColor: color }}
              onClick={() => {
                const stateName = value === 'n' ? '启用' : '禁用'
                const status = value === 'y' ? 'n' : 'y'
                const text = `是否${stateName}${record.fieldCategoryName}类型?`
                createConfirm({
                  iconType: 'warning',
                  content: text,
                  onOk: () => {
                    enableOrDisable({
                      ...record,
                      status,
                    }).then(({ code, message }) => {
                      if (code === 200) {
                        createSuccessModal({ content: `${stateName}成功` })
                        record.status = status
                      } else {
                        createErrorModal({ content: `${stateName}失败，${message}` })
                      }
                    })
                  },
                })
              }}
            >
            </Switch>
          </div>
        )
        return el
      },
      width: 150,
    },
    // {
    //   title: '版本号',
    //   dataIndex: 'dataVersion',
    //   width: 200,
    // },
    // {
    //   title: '备注',
    //   dataIndex: 'fieldCategoryPrecautions',
    //   width: 200,
    // },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 200,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'fieldCategoryName',
      label: '类型名称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      // componentProps({ formModel }) {
      //   return {
      //     api: list,
      //     resultField: 'data',
      //     showSearch: true,
      //     params: {
      //       pageSize: 0,
      //     },
      //     fieldNames: { label: 'fieldCategoryName', value: 'fieldCategoryName' },
      //     filterOption(input: string, option: any) {
      //       return option.fieldCategoryName.toLowerCase().indexOf(input.toLowerCase()) >= 0
      //     },
      //     getPopupContainer: () => document.body,
      //   }
      // },
    },
    // {
    //   field: 'fillingFrequency',
    //   label: '填报项类型',
    //   colProps: { span: 6 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   componentProps: function () {
    //     return {
    //       options: dictionary.getDictionaryOpt.get('submission_frequency'),
    //     }
    //   },
    // },
    {
      field: 'fillingFrequency',
      label: '填报项类型',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          api: queryDictionary,
          resultField: 'data',
          showSearch: true,
          params: {
            pageSize: 0,
            groupCode: 'submission_frequency',
          },
          fieldNames: { label: 'dictName', value: 'dictCode' },
          filterOption(input: string, option: any) {
            return option.dictName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          getPopupContainer: () => document.body,
        }
      },
    },
    // {
    //   field: 'notificationMethod',
    //   label: '通知方式',
    //   component: 'Select',
    //   colProps: { span: 6 },
    //   rulesMessageJoinLabel: true,
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get('notice_type') as any,
    //   },
    // },
    {
      field: 'notificationMethod',
      label: '通知方式',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          api: queryDictionary,
          resultField: 'data',
          showSearch: true,
          params: {
            pageSize: 0,
            groupCode: 'notice_type',
          },
          fieldNames: { label: 'dictName', value: 'dictCode' },
          filterOption(input: string, option: any) {
            return option.dictName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          getPopupContainer: () => document.body,
        }
      },
    },
  ]
}

export const modalForm = (isUpdate): FormSchema[] => {

  let helpMessage = '临时报送：立即发送填报通知。'

  return [

    {
      field: 'fieldCategoryName',
      label: '类型名称',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 40,
      },
    },
    {
      field: 'type',
      label: '历史类型配置',
      component: 'ApiSelect',
      colProps: { span: 24 },
      slot: 'typeSlot',
      ifShow: !isUpdate,
      // componentProps({ formModel }) {
      //   return {
      //     api: list,
      //     resultField: 'data',
      //     showSearch: true,
      //     params: {
      //       pageSize: 0,
      //     },
      //     fieldNames: { label: 'fieldCategoryName', value: 'fieldCategoryName' },
      //     filterOption(input: string, option: any) {
      //       return option.fieldCategoryName.toLowerCase().indexOf(input.toLowerCase()) >= 0
      //     },
      //     getPopupContainer: () => document.body,
      //     onChange(_, option: any) {
      //       console.log('option', option);
      //     }
      //   }
      // },
    },
    {
      field: 'fillingFrequency',
      label: '报送频率',
      colProps: { span: 24 },
      component: 'Select',
      required: true,
      defaultValue: 'ls',
      helpMessage: () => helpMessage,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('submission_frequency'),
          onChange: (value: string) => {
            const msg = getHelpMessage(value)
            helpMessage = msg || ''
            return helpMessage
          },
        }
      },
    },
    {
      field: 'notificationMethod',
      label: '通知方式',
      component: 'RadioGroup',
      defaultValue: 'SMS',
      colProps: { span: 24 },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('notice_type') as any,
      },
    },

    {
      field: 'fieldDTO',
      label: '填报项',
      component: 'CheckboxGroup',
      required: true,
      colProps: { span: 24 },
      pStyle: 'width: 100%',
      slot: 'fieldDTOListSlot',
    },
    {
      field: 'fieldCategoryPrecautions',
      label: '备注',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      colProps: { span: 24 },
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 400,
      },
    },

  ]
}

const getHelpMessage = (value: string): string => {
  switch (value) {
    case 'ls':
      return '临时报送：立即发送填报通知。'
    case 'month':
      return '每月报送：在每月1号发送填报通知'
    case 'quarterly':
      return '每季度报送：如：1,4,7,10月1号发送填报通知'
    case 'halfYear':
      return '每半年报送：如：1,7月1号发送填报通知'
    default:
      return '请选择一种通知方式以获取更多信息。'
  }
}