<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleClick">
          新增上报类型
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              // auth: '/system/whiteList/view',
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              onClick: handleEdit.bind(null, record),
              // auth: '/system/whiteList/update',
            },
            {
              icon: 'ant-design:container-twotone',
              label: '删除',
              type: 'primary',
              danger: true,
              popConfirm: {
                title: `确定删除${record?.fieldCategoryName || ''}吗?`,
                confirm: handleDelete.bind(null, record),
              },
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <ItemModal @register="registerModal" @success="handleSuccess" :canFullscreen="false" width="70%" />

  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { columns, formSchemas } from './data'
import ItemModal from './ItemModal.vue'
import { useModal } from '/@/components/Modal';
import { list, saveByDTO, updateByDTO, remove, view } from '/@/api/report/configType';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createMessage } = useMessage()

const [registerTable, { reload }] = useTable({
  rowKey: 'fieldCategoryId',
  columns: columns(),
  showIndexColumn: false,
  // authInfo: ['/system/whiteList/add'],
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    actionColOptions: { span: 4 },
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',
    fixed: undefined,
    // auth: ['/system/whiteList/update', '/system/whiteList/view', '/system/whiteList/delete'],
  },
})

const [registerModal, { openModal, closeModal }] = useModal()

//新增
function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  })
}

//编辑
function handleEdit(record) {
  view({ fieldCategoryId: record.fieldCategoryId }).then(({ data, code, message }) => {
    if (code !== 200) return createErrorModal({ content: `查询失败，${message}` })
    openModal(true, {
      record: data,
      isUpdate: true,
      disabled: false,
    })
  })
}

//详情
function handleView(record) {
  view({ fieldCategoryId: record.fieldCategoryId }).then(({ data, code, message }) => {
    if (code !== 200) return createErrorModal({ content: `查询失败，${message}` })
    openModal(true, {
      record: data,
      isUpdate: true,
      disabled: true,
    })
  })

}
//删除
function handleDelete(record) {
  remove(record.fieldCategoryId).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success('删除成功')
      reload()
    } else {
      createErrorModal({ content: `删除失败，${message}` })
    }
  })
}


//提交表单
function handleSuccess({ isUpdate, values }) {
  const api = isUpdate ? updateByDTO : saveByDTO
  api(values).then(({ code, message }) => {
    if (code === 200) {
      createMessage.success(`${isUpdate ? '编辑' : '新增'}成功!`)
      closeModal()
      reload()

    } else {
      createErrorModal({ content: `${isUpdate ? '编辑' : '新增'}失败，${message}` })
    }
  })
}
</script>
