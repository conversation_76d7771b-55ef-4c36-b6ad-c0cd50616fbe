<template>
  <div class="main !w-full ">
    <div v-if="disabled" class="w-full flex">
      <div class="tag !text-[#606266]" v-for="v, i in dataList" :key="i">{{ v.fieldTitle }}</div>
    </div>
    <div v-else class="w-full">
      <div class="w-full" v-for="v, i in dataList" :key="i">
        <div class="type-title">类型：{{ v.name }} <span class="text-[#579dff]">({{ v.data.length || 0 }}项)</span></div>
        <div class="w-full flex content">
          <div class="tag cursor-pointer" :class="{ 'success': item.isChosed, }" v-for="item, index in v.data"
            :key="index" @click="handelChose(item)">{{ item.fieldTitle }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, h, watch, nextTick, onMounted, computed } from 'vue';
import { list, } from '/@/api/report/config';
import { useDictionary } from '/@/store/modules/dictionary';
import { Tag } from 'ant-design-vue';

const props = defineProps({
  selectedKeys: {// 已选中的数据
    type: Array,
    default: () => [],
  },
  disabled: {// 是否禁用编辑
    type: Boolean,
    default: false,
  },
});

const dictionary = useDictionary()
const emit = defineEmits(['update:selectedKeys', 'change']);
const dataSource = ref([]);

const dataList = computed(() => {
  if (!dataSource.value.length) return [];
  if (props.disabled) return dataSource.value.filter(v => v.isChosed);
  const result = [] as any[];
  dataSource.value.forEach(item => {
    let found = false;//用于判断是否已经添加过
    for (let i = 0; i < result.length; i++) {
      if (result[i].type === item.fieldType) {
        result[i].data.push(item);
        found = true;
        break;
      }
    }
    if (!found) {
      result.push({
        type: item.fieldType,
        name: dictionary.getDictionaryMap.get(`field_type_${item.fieldType}`)?.dictName,
        data: [item],
      })
    }
  })
  return result;
});


async function getConfigOption() {
  const res = await list({ pageSize: 0 });
  if (res.code == 200) {
    dataSource.value = res.data.map(v => ({ ...v, isChosed: props.selectedKeys.includes(v.fieldBizId) }))
  }
}

function handelChose(item) {
  if (props.disabled) return;
  item.isChosed = !item.isChosed;
  let selectedKeys = props.selectedKeys;
  if (item.isChosed) {
    selectedKeys.push(item.fieldBizId)
  } else {
    selectedKeys = selectedKeys.filter(v => v !== item.fieldBizId)
  }
  let selectedlist = selectedKeys.map(v => dataSource.value.find(v2 => v2.fieldBizId === v))
  emit('update:selectedKeys', selectedKeys);
  emit('change', selectedlist);
}

watch(() => props.selectedKeys, () => {
  if (dataSource.value.length) {
    dataSource.value.forEach(v => {
      v.isChosed = props.selectedKeys.includes(v.fieldBizId)
    })
  }
}, { deep: true, immediate: false })

onMounted(() => {
  getConfigOption()
})

</script>

<style lang="less" scoped>
.main {
  max-height: 300px;
  overflow-y: auto;

  .tag {
    padding: 3px 8px;
    margin: 5px;
    color: rgba(0, 0, 0, 0.88);
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;


    &.success {
      color: #fff;
      background-color: #87d068;
      border-color: #87d068;
      cursor: pointer;
    }

    // &.nochosed {
    //   background: rgba(0, 0, 0, 0.02);
    //   border: 1px solid #d9d9d9;
    //   cursor: pointer;
    // }

  }

  .type-title {
    font-size: 14px;
    font-weight: 600;
    color: #606266;
    margin: 5px;
  }

  .content {
    flex-wrap: wrap;
  }
}
</style>
