<template>
  <div
    :class="$style.tokenUser"
    class="relative"
  >
    <div class="flex justify-center items-center relative top-[-20px]">
      <div
        class="w-[349px] h-[59px] flex justify-center items-center"
        :style="{
          backgroundImage: `url(${loginTitleBg})`,
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
        }"
      >
        <img
          :src="loginTitle"
          class="h-[33px]"
        />
      </div>
    </div>
    <Form
      class="p-4 enter-x h-[calc(100%-59px)] relative"
      ref="formRef"
      @keypress.enter="handleLogin"
    >
      <FormItem
        name="account"
        class="enter-x"
      >
        <Select
          :options="options"
          placeholder="请选择登录账号"
          v-model:value="value"
        />
      </FormItem>
      <FormItem class="enter-x absolute bottom-2 w-full">
        <Button
          size="large"
          class="!w-1/2 !bg-[#68ABFF] !text-[#fff] !rounded-[30px] left-1/2 translate-x--1/2"
          block
          @click="handleLogin"
          :loading="loading"
        >
          {{ t('sys.login.loginButton') }}
        </Button>
      </FormItem>
    </Form>
  </div>
</template>

<script lang="ts" setup>
import { Form, Button, Select } from 'ant-design-vue';
import { ref, unref } from 'vue';
import { useI18n } from 'vue-i18n';
import { adminLoginBack } from '/@/router/guard';
import { LoginStateEnum, useLoginState } from './useLogin';
import { find } from 'lodash-es';
import loginTitleBg from '@/assets/images/login/login-title-bg.png';
import loginTitle from '@/assets/images/login/login-title.png';

const { setLoginState } = useLoginState();

setLoginState(LoginStateEnum.LOGIN);
const value = ref();

const options = [
  {
    label: '南充市总工会',
    value: '南充市总工会',
  },
  {
    label: '南充干部18784702293',
    value: '南充干部18784702293',
  },
  {
    label: '南充市下基层工会-18280424917',
    value: '南充市下基层工会-18280424917',
  },
  {
    label: '南部县总工会',
    value: '南部县总工会',
  },
  {
    label: '南部县干部***********',
    value: '南部县干部***********',
  },
  {
    label: '南部县下基层工会***********',
    value: '南部县下基层工会***********',
  },
];

const { t } = useI18n();

const FormItem = Form.Item;

const loading = ref(false);

function handleLogin() {
  try {
    loading.value = true;
    if (!unref(value)) {
      return false;
    }
    adminLoginBack({ query: { token: unref(value) } } as any, {
      account: find(options, v => v.value === unref(value))?.label,
      pwd: 'nanchong@123456',
    });
  } catch (error) {
  } finally {
    loading.value = false;
  }
}
</script>

<style lang="less" module>
.tokenUser {
  :global {
    background-image: url('@/assets/images/login/login-form-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 651px;
    height: 381px;
    .ant-radio-wrapper {
      color: rgb(73, 170, 255) !important;
    }
  }
}
</style>
