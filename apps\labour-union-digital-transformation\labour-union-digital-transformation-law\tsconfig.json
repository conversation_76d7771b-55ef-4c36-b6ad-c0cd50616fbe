{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@monorepo-yysz/ts-config/vue-app.json", "compilerOptions": {"baseUrl": ".", "declaration": false, "types": ["vite/client"], "paths": {"@/*": ["src/*"], "#/*": ["types/*"], "@mars/*": ["src/marsgis/*"], "/@/*": ["src/*"], "/#/*": ["types/*"]}}, "include": ["tests/**/*.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "vite.config.mts"], "exclude": ["node_modules", "tests/server/**/*.ts", "dist", "**/*.js"]}