import { defineApplicationConfig } from '@monorepo-yysz/vite-config';
import { generateModifyVars } from './build/modifyVars';
import path from 'path';
export default defineApplicationConfig({
  overrides: {
    optimizeDeps: {
      include: [
        'qrcode',
        '@iconify/iconify',
        'ant-design-vue/es/locale/zh_CN',
        'ant-design-vue/es/locale/en_US',
        'lottie-web',
        'sortablejs',
        'cropperjs',
      ],
    },
    build: {
      outDir: 'lawdist',
    },
    server: {
      host: true,
      port: 3900,
      proxy: {
        '/basic-api': {
          // target: 'https://lawworkerstat.sc12351.com/basic-api',
          target: 'http://**************:33000',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/basic-api`), ''),
          // only https
          // secure: false
        },
        '/upload': {
          // target: 'https://lawworkerstat.sc12351.com/basic-api',
          target: 'http://**************:33001',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/upload`), ''),
        },
      },
      open: true, // 项目启动后，自动打开
      warmup: {
        clientFiles: ['./index.html', './src/{views,components}/*'],
      },
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          modifyVars: generateModifyVars(),
        },
      },
    },
  },
});
