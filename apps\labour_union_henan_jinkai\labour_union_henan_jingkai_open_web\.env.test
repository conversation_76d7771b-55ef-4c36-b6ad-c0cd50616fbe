NODE_ENV = production
# Whether to open mock
VITE_USE_MOCK = true

# public path
VITE_PUBLIC_PATH = /

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VITE_BUILD_COMPRESS = 'none'

# Basic interface address SPA
VITE_GLOB_API_URL = /basic-api

# File upload address， optional
# It can be forwarded by nginx or write the actual address directly
VITE_GLOB_UPLOAD_URL = /upload


# Interface prefix
VITE_GLOB_API_URL_PREFIX =


