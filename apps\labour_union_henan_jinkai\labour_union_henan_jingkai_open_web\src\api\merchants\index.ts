import { BasicResponse } from '@monorepo-yysz/types';
import { openHttp } from '/@/utils/http/axios';

enum OBJ {
  insert = '/OpenInitOpenCustomer', //工会直接新增商家;
  update = '/updateCompanyMoreInfoByCompanyId', //修改商家
  findList = '/findVoList',
  view = '/getByCompanyId',
  removeGiftCompany = '/removeGiftCompany',
  chooseGiftCompany = '/chooseGiftCompany',
  findChildList = '/findChildList',
  getSecondLevelCompanyInfos = '/getSecondLevelCompanyInfos'
}

function getApi(url?: string) {
  if (!url) {
    return '/openCompanyInfo';
  }
  return '/openCompanyInfo' + url;
}

// 列表
export const list = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 子级列表
export const findChildList = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.findChildList), params },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//工会新增商户
export const insertMerchant = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(OBJ.insert), params },
    {
      isTransformResponse: false,
    }
  );
};

//修改商户主体信息(资质信息)
export const updateMerchant = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(OBJ.update), params },
    {
      isTransformResponse: false,
    }
  );
};

//选择一岁一礼商家
export const chooseGiftCompany = params => {
  return openHttp.post<BasicResponse>(
      { url: getApi(OBJ.chooseGiftCompany), params },
      {
        isTransformResponse: false,
      }
  );
};


//移除一岁一礼商家
export const removeGiftCompany = params => {
  return openHttp.post<BasicResponse>(
      { url: getApi(OBJ.removeGiftCompany), params },
      {
        isTransformResponse: false,
      }
  );
};

// 查询登陆的商户的二级商户列表
export const getSecondLevelCompanyInfos = () => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.getSecondLevelCompanyInfos) },
    {
      isTransformResponse: false,
    }
  );
};