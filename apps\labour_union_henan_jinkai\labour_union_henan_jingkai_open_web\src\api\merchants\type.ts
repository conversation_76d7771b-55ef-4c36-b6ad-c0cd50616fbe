import { BasicResponse } from '@monorepo-yysz/types';
import { openHttp } from '/@/utils/http/axios';

enum OBJ {
  saveOrUpdate = '/saveOrUpdateByDTO', //修改
  findList = '/findVoList',
  removeById = '/removeById',
  getMaxSortNumber = '/getMaxSortNumber',
}

function getApi(url?: string) {
  if (!url) {
    return '/merchantsType';
  }
  return '/merchantsType' + url;
}

// 列表
export const list = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增/编辑分类
export const saveOrUpdate = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(OBJ.saveOrUpdate), params },
    {
      isTransformResponse: false,
    }
  );
};

//删除分类
export const removeById = params => {
  return openHttp.delete<BasicResponse>(
      { url: getApi(OBJ.removeById)+ `?autoId=${params}`},
      {
        isTransformResponse: false,
      }
  );
};

//获取最大排序号
export const maxSortNumber = params => {
  return openHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.getMaxSortNumber),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};