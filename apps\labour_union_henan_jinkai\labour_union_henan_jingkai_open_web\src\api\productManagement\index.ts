import { openHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';
import { TreeDataItem } from 'ant-design-vue/lib/tree';

enum ConvenienceApplication {
  findIntegralList = '/workIntegralProductFindVoList', //查询积分商品列表
  findPuHuilList = '/inclusiveProductFindVoList', //查询普惠商品列表
  addInclusiveProduct = '/addInclusiveProduct', // 新增普惠商品
  updateProducts = '/updateIntegralProductInfo', //修改积分商品
  addProducts = '/addIntegralProductInfo', //新增积分商品
  upAndDown = '/enableOrDisableProduct', //管理积分商品上下架
  specifications = '/getProductPriceInfoByProductId', //获取某一积分商品的规格信息
  companyList = '/findVoList', //查询商户列表
  productIntroduces = '/getProductIntroduce',
  updateInclusiveProduct = '/updateInclusiveProduct', // 编辑普惠商品
}

function getApi(url?: string) {
  if (!url) {
    return '/customProductInfo';
  }
  return '/customProductInfo' + url;
}

function getCompanyInfoApi(url?: string) {
  if (!url) {
    return '/openCompanyInfo';
  }
  return '/openCompanyInfo' + url;
}

//查询积分商品列表
export const productList = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.findIntegralList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增积分商品
export const addInclusiveProduct = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(ConvenienceApplication.addInclusiveProduct), params },
    {
      isTransformResponse: false,
    }
  );
};

//商品的上下架
export const upAndDownProducts = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(ConvenienceApplication.upAndDown), params },
    {
      isTransformResponse: false,
    }
  );
};

//获取积分商品规格信息
export const getSpecifications = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.specifications), params },
    {
      isTransformResponse: false,
    }
  );
};

//查询商户列表
export const findCompanyList = params => {
  return openHttp.get<BasicResponse>(
    { url: getCompanyInfoApi(ConvenienceApplication.companyList), params },
    {
      isTransformResponse: false,
    }
  );
};

//获取商品信息描述
export const getProductsIntroduces = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.productIntroduces), params },
    {
      isTransformResponse: false,
    }
  );
};

//查询tree的列表(普惠商品)
export const treeProductList = params => {
  return openHttp.get<TreeDataItem[]>({
    url: getApi(ConvenienceApplication.findPuHuilList),
    params,
  });
};

//编辑积分商品
export const updateProducts = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(ConvenienceApplication.updateProducts), params },
    {
      isTransformResponse: false,
    }
  );
};

//编辑普惠商品
export const updateInclusiveProduct = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(ConvenienceApplication.updateInclusiveProduct), params },
    {
      isTransformResponse: false,
    }
  );
};