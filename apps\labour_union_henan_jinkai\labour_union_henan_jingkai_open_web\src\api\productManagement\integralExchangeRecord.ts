import { openHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';


enum OBJ {
  findList = '/merchantFindVoList',
  findOrderVoList = '/findOrderVoList',
  view = '/getCurrentExchangeDetails',
  userInfoSearch = '/userInfoSearch',
  shipments = '/export/shipments',
  saveOrUpdate = '/',
}

function getApi(url?: string) {
  if (!url) {
    return '/integralExchangeRecord';
  }
  return '/integralExchangeRecord' + url;
}

// 不分页查询所有用户
export const userInfoSearch = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.userInfoSearch), params },
    {
      isTransformResponse: false,
    }
  );
};

//列表
export const list = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//订单列表
export const findOrderVoList = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.findOrderVoList), params },
    {
      isTransformResponse: false,
    }
  );
};

//发货
export const deliveryGoods = params => {
  return openHttp.post<BasicResponse>(
    {
      url: '/customOrder/deliveryGoods',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//view
export const view = params => {
  return openHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = (autoId: number[] | number) => {
  return openHttp.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};


//发货记录导出
export const shipmentsExport = params => {
  return openHttp.post<any>(
    { url: getApi(OBJ.shipments), params, responseType: 'blob' },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(OBJ.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};