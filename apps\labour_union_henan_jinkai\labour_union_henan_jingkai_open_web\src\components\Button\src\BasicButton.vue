<template>
  <Button
    v-bind="getBindValue"
    :class="getButtonClass"
    @click="onClick"
    v-if="permission"
  >
    <template #icon>
      <slot name="icon"></slot>
    </template>
    <template #default="data">
      <Icon
        :icon="preIcon"
        v-if="preIcon"
        :size="iconSize"
      />
      <slot v-bind="data || {}"></slot>
      <Icon
        :icon="postIcon"
        v-if="postIcon"
        :size="iconSize"
      />
    </template>
  </Button>
</template>

<script lang="ts" setup>
import { Button } from 'ant-design-vue';
import { ComponentOptionsMixin, computed, unref } from 'vue';
import { Icon } from '@monorepo-yysz/ui';

import { buttonProps } from './props';
import { useAttrs } from '@monorepo-yysz/hooks';
import { usePermission } from '@/hooks/web/usePermission';

defineOptions({
  name: '<PERSON>utt<PERSON>',
  extends: Button as ComponentOptionsMixin,
  inheritAttrs: false,
});

const props = defineProps(buttonProps);
// get component class
const attrs = useAttrs({ excludeDefaultKeys: false });

const { hasPermission } = usePermission();

const getButtonClass = computed(() => {
  const { color, disabled } = props;
  return [
    {
      [`ant-btn-${color}`]: !!color,
      [`is-disabled`]: disabled,
    },
  ];
});

// get inherit binding value
const getBindValue = computed(() => ({ ...unref(attrs), ...props }));

const permission = computed(() => {
  return hasPermission(props.auth);
});
</script>
