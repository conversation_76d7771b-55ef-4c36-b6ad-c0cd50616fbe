<template>
  <div
    class="w-full h-full"
    :class="$style['cropper-form']"
  >
    <slot name="tip"></slot>
    <div
      class="items-center justify-center img-content"
      :class="{ flex: ifFlex, 'inline-flex': !ifFlex }"
    >
      <div
        class="ant-upload ant-upload-select ant-upload-select-picture-card !flex justify-center items-center !mr-0 !mb-0 overflow-hidden  border-dashed border-cover cursor-pointer"
        @click="handleUploadappCover"
        v-if="!circled"
      >
        <div
          v-if="url"
          class="w-full h-full overflow-hidden"
        >
          <img
            :src="url"
            alt=""
            class="w-full !h-auto"
          />
        </div>
        <!--  :class="`${ifH ? '!h-full' : ''} ${ifW ? '!w-full' : ''} ${imgClass}`" -->
        <div
          v-else
          class="flex flex-col items-center"
        >
          <plus-outlined v-if="!disabled"></plus-outlined>
          <div
            class="ant-upload-text !mr-5px"
            v-if="!disabled"
            >上传</div
          >
        </div>
      </div>
      <Avatar
        :size="{ xs: 24, sm: 32, md: 40, lg: 64, xl: 80, xxl: 100 }"
        :src="url"
        v-else
        @click="handleUploadappCover"
      >
        <template
          #icon
          v-if="!url"
        >
          <user-outlined />
        </template>
      </Avatar>
      <div
        class="flex items-center justify-center"
        v-if="url"
      >
        <Button
          shape="circle"
          @click="() => setVisible(true)"
          title="预览"
          :disabled="false"
        >
          <template #icon>
            <SearchOutlined />
          </template>
        </Button>
        <Image
          :style="{ display: 'none' }"
          :preview="{
            visible,
            onVisibleChange: setVisible,
          }"
          :src="url"
        />
      </div>
    </div>
    <CropperModal
      @register="register"
      :circled="circled"
      @upload-success="handleUploadSuccess"
      :imgSize="imgSize"
      :upload-api="uploadFile"
      :operateType="operateType"
      :watermarkName="watermarkName"
    />
  </div>
</template>

<script lang="ts" setup>
import { CropperModal } from '@/components/Cropper';
import { useModal } from '@/components/Modal';
import { Image, Button, Avatar } from 'ant-design-vue';
import { computed, ref, unref, useAttrs, watch } from 'vue';
import { uploadFile } from '@/api/sys/upload';
import { PlusOutlined, SearchOutlined, UserOutlined } from '@ant-design/icons-vue';
import { useUserStore } from '@/store/modules/user';

const props = defineProps({
  imgSize: {
    type: Number,
    default: 1,
  },
  operateType: {
    type: Number,
    default: 0,
  },
  value: {
    type: String,
  },
  ifFlex: {
    type: Boolean,
    default: false,
  },
  circled: {
    type: Boolean,
    default: false,
  },
  watermarkName: {
    type: String,
    default: undefined,
  },
  imgClass: String,
});

const emit = defineEmits(['change']);

const userStore = useUserStore();

const [register, { openModal, closeModal }] = useModal();

const attrs = useAttrs();

const disabled = computed(() => {
  return attrs.disabled;
});

const url = ref('');

const visible = ref<boolean>(false);

const setVisible = (value): void => {
  visible.value = value;
};

function handleUploadappCover() {
  if (unref(disabled)) {
    return false;
  }

  openModal(true);
}

function handleUploadSuccess({ filePath }) {
  url.value = userStore.getPrefix + filePath;

  emit('change', filePath);
  closeModal();
}

watch(
  () => props.value,
  () => {
    url.value = props.value ? userStore.getPrefix + props.value : '';
  },
  { deep: true, immediate: true }
);
</script>

<style lang="less" module>
.cropper-form {
  :global {
    .img-content {
      background-color: @app-content-background;
      border: 1px dashed @border-disabled;
    }

    .border-cover {
      border-color: @border-disabled;
    }

    .ant-upload {
      width: 128px !important;
      height: 128px !important;
    }
  }
}
</style>
