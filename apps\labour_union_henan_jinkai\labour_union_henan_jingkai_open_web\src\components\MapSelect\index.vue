<template>
  <div>
    <Input
      v-model:value="strValue"
      placeholder="请选择位置"
      :title="strValue"
      @change="handleInput"
      :disabled="disabled"
    >
      <template #suffix>
        <Button
          type="primary"
          v-if="!disabled"
          @click="handleChange"
          >选择位置</Button
        >
      </template>
    </Input>
    <MapModal
      @register="registerModal"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, unref, watch } from 'vue';
import { useModal } from '../Modal';
import MapModal from './components/map-modal.vue';
import { Input, Button } from 'ant-design-vue';
import { isString } from 'lodash-es';

defineOptions({ name: 'MapSelect' });

const emit = defineEmits(['change', 'input', 'change-lnglat']);

const props = defineProps({
  value: { type: String, default: '' },
  disabled: {
    type: Boolean,
    default: false,
  },
  lnglat: {
    type: String,
  },
});

const strValue = ref<string>();

const [registerModal, { openModal, closeModal }] = useModal();

function handleChange() {
  let record;
  if (isString(props.lnglat)) {
    const point = props.lnglat.split(',');
    record = {
      lng: point[0],
      lat: point[1],
      address: unref(strValue),
    };
  }
  openModal(true, { record });
}

function handleInput(e) {
  emit('input', e.target.value);
}

function handleSuccess({ location, address }) {
  strValue.value = address;

  emit('change', address);

  emit('change-lnglat', location);

  closeModal();
}

watch(
  () => props.value,
  () => {
    strValue.value = props.value;
  },
  { deep: true }
);
</script>
