<template>
  <div
    :class="prefixCls"
    :style="{ width: containerWidth }"
  >
    <ImgUpload
      :fullscreen="fullscreen"
      @uploading="handleImageUploading"
      @done="handleDone"
      v-if="showImageUpload"
      v-show="editorRef"
      :disabled="disabled"
    />
    <textarea
      :id="tinymceId"
      ref="elRef"
      :style="{ visibility: 'hidden' }"
      v-if="!initOptions.inline"
    ></textarea>
    <slot v-else></slot>
  </div>
</template>

<script lang="ts" setup>
import type { Editor, RawEditorSettings } from 'tinymce';
import tinymce from 'tinymce/tinymce';
import 'tinymce/themes/silver';
import 'tinymce/icons/default/icons';
import 'tinymce/plugins/advlist';
import 'tinymce/plugins/anchor';
import 'tinymce/plugins/autolink';
import 'tinymce/plugins/autosave';
import 'tinymce/plugins/code';
import 'tinymce/plugins/codesample';
import 'tinymce/plugins/directionality';
import 'tinymce/plugins/fullscreen';
import 'tinymce/plugins/hr';
import 'tinymce/plugins/insertdatetime';
import 'tinymce/plugins/link';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/media';
import 'tinymce/plugins/nonbreaking';
import 'tinymce/plugins/noneditable';
import 'tinymce/plugins/pagebreak';
import 'tinymce/plugins/paste';
import 'tinymce/plugins/preview';
import 'tinymce/plugins/print';
import 'tinymce/plugins/save';
import 'tinymce/plugins/searchreplace';
import 'tinymce/plugins/spellchecker';
import 'tinymce/plugins/tabfocus';
// import 'tinymce/plugins/table';
import 'tinymce/plugins/template';
import 'tinymce/plugins/textpattern';
import 'tinymce/plugins/visualblocks';
import 'tinymce/plugins/visualchars';
import 'tinymce/plugins/wordcount';
// 额外的
import 'tinymce/plugins/image';
import 'tinymce/plugins/indent2em';
import 'tinymce/plugins/formatpainter';
import 'tinymce/plugins/paragraphspacing';
import 'tinymce/plugins/letterspacing';
import 'tinymce-plugin';
import 'tinymce-plugin/plugins/upfile/code-vue';
import {
  computed,
  nextTick,
  ref,
  unref,
  watch,
  onDeactivated,
  onBeforeUnmount,
  PropType,
  useAttrs,
  useCssModule,
} from 'vue';
import ImgUpload from './ImgUpload.vue';
import {
  plugins as defaultPlugins,
  toolbar as defaultToolbar,
  fileType,
  fileTypes,
  fontFamilyFormats,
} from './tinymce';
import { bindHandlers } from './helper';
import { onMountedOrActivated, useDesign, useMessage } from '@monorepo-yysz/hooks';
import { isNumber, buildShortUUID } from '@monorepo-yysz/utils';
import { useLocale } from '@/locales/useLocale';
import { useAppStore } from '@/store/modules/app';
import { useGlobSetting } from '@/hooks/setting';
import { GateWayEnum } from '@/enums/appEnum';
import { useUserStore } from '@/store/modules/user';

defineOptions({ name: 'Tinymce', inheritAttrs: false });

const props = defineProps({
  options: {
    type: Object as PropType<Partial<RawEditorSettings>>,
    default: () => ({}),
  },
  value: {
    type: String,
  },

  toolbar: {
    type: Array as PropType<string[]>,
    default: defaultToolbar,
  },
  plugins: {
    type: Array as PropType<string[]>,
    default: defaultPlugins,
  },
  modelValue: {
    type: String,
  },
  height: {
    type: [Number, String] as PropType<string | number>,
    required: false,
    default: 400,
  },
  width: {
    type: [Number, String] as PropType<string | number>,
    required: false,
    default: 'auto',
  },
  showImageUpload: {
    type: Boolean,
    default: false,
  },
  operateType: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['change', 'update:modelValue', 'inited', 'init-error']);

const attrs = useAttrs();
const editorRef = ref<Editor | null>(null);
const fullscreen = ref(false);
const tinymceId = ref<string>(buildShortUUID('tiny-vue'));
const elRef = ref<HTMLElement | null>(null);

const clazz = useCssModule();

const { createWarningModal, createErrorModal } = useMessage();

const { prefixCls } = useDesign('tinymce-container');

const appStore = useAppStore();

const { uploadUrl, bucket_name } = useGlobSetting();

const userStore = useUserStore();

const containerWidth = computed(() => {
  const width = props.width;
  if (isNumber(width)) {
    return `${width}px`;
  }
  return width;
});

const skinName = computed(() => {
  return appStore.getDarkMode === 'light' ? 'oxide' : 'oxide-dark';
});

const langName = computed(() => {
  const lang = useLocale().getLocale.value;
  return ['zh_CN', 'en'].includes(lang) ? lang : 'zh_CN';
});

const initOptions = computed((): RawEditorSettings => {
  const { height, options, toolbar, plugins } = props;
  const publicPath = import.meta.env.VITE_PUBLIC_PATH || '/';
  return {
    selector: `#${unref(tinymceId)}`,
    height,
    toolbar,
    indent_type: 'pt',
    indent_default: false,
    font_formats: fontFamilyFormats,
    fontsize_formats: '8pt 10pt 12pt 14pt 16pt 18pt 24pt 36pt 48pt',
    menubar: 'file edit insert view format table',
    plugins,
    language_url: publicPath + 'resource/tinymce/langs/' + langName.value + '.js',
    language: langName.value,
    branding: false,
    default_link_target: '_blank',
    images_upload_handler: function (blobInfo, success, failure, progress) {
      let xhr: XMLHttpRequest;
      let formData;
      xhr = new XMLHttpRequest();
      xhr.withCredentials = false;
      xhr.open('POST', uploadUrl + GateWayEnum.file + '/yyfile/v2/uploadFileFormData');
      xhr.setRequestHeader('token', userStore.getToken);
      xhr.upload.onprogress = function (e) {
        progress && progress((e.loaded / e.total) * 100);
      };
      xhr.onload = function () {
        let json;
        if (xhr.status == 403) {
          failure('HTTP Error: ' + xhr.status, { remove: true });
          return;
        }
        if (xhr.status < 200 || xhr.status >= 300) {
          failure('HTTP Error: ' + xhr.status);
          return;
        }
        json = JSON.parse(xhr.responseText);
        if (json) {
          const { code, data } = json;
          if (code === 200 && data && data.length > 0) {
            success(userStore.getPrefix + data[0]?.completeFile);
          }
        }
      };

      xhr.onerror = function () {
        failure('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
      };

      formData = new FormData();
      formData.append('operateType', `${props.operateType}`);
      formData.append('bucketName', bucket_name);
      formData.append('file', blobInfo.blob(), blobInfo.filename());
      xhr.send(formData);
    },
    file_picker_callback: function (callback) {
      //文件分类
      //后端接收上传文件的地址
      let upurl = uploadUrl + GateWayEnum.file + '/yyfile/v2/uploadFileFormData';
      //为不同插件指定文件类型及后端地址
      //模拟出一个input用于添加本地文件
      let input = document.createElement('input');
      input.setAttribute('type', 'file');
      input.setAttribute('accept', fileTypes);
      input.click();
      input.onchange = function () {
        const { files } = this as any;
        let file = files[0];

        let xhr: XMLHttpRequest, formData;
        xhr = new XMLHttpRequest();
        xhr.withCredentials = false;
        xhr.open('POST', upurl);
        xhr.setRequestHeader('token', userStore.getToken);
        xhr.onload = function () {
          let json;
          if (xhr.status != 200) {
            createErrorModal({ content: '上传失败', wrapClassName: clazz.waring });
            // failure('HTTP Error: ' + xhr.status);
            return;
          }
          json = JSON.parse(xhr.responseText);

          if (json) {
            const { code, data } = json;
            if (code === 200 && data && data.length > 0) {
              callback(userStore.getPrefix + data[0]?.completeFile);
            }
          }
        };
        formData = new FormData();
        formData.append('operateType', `${props.operateType}`);
        formData.append('bucketName', bucket_name);
        formData.append('file', file, file.name);
        xhr.send(formData);
      };
    },
    file_callback: function (file: Recordable, callback: Fn) {
      const { name } = file;

      const nameArr = name ? name.split('.') : [];

      const type = nameArr[nameArr.length - 1];

      const typeFlg = fileType.includes(type);

      if (!typeFlg) {
        createWarningModal({
          content: '文件类型错误，只能上传.doc, .docx, .xls, .xlsx, .pdf后缀文件!',
          wrapClassName: clazz.waring,
        });
        return false;
      }

      let upurl = uploadUrl + GateWayEnum.file + '/yyfile/v2/uploadFileFormData';
      let xhr: XMLHttpRequest, formData;
      xhr = new XMLHttpRequest();
      xhr.withCredentials = false;
      xhr.open('POST', upurl);
      xhr.setRequestHeader('token', userStore.getToken);
      xhr.onload = function () {
        let json;
        if (xhr.status != 200) {
          createErrorModal({ content: '上传失败', wrapClassName: clazz.waring });
          // failure('HTTP Error: ' + xhr.status);
          return;
        }
        json = JSON.parse(xhr.responseText);

        if (json) {
          const { code, data } = json;
          if (code === 200 && data && data.length > 0) {
            callback(userStore.getPrefix + data[0]?.completeFile, { text: file.name });
          }
        }
      };
      formData = new FormData();
      formData.append('operateType', `${props.operateType}`);
      formData.append('bucketName', bucket_name);
      formData.append('file', file as Blob, file.name);
      xhr.send(formData);
    },
    file_picker_types: 'link file media',
    link_title: false,
    object_resizing: false,
    auto_focus: true,
    skin: skinName.value,
    skin_url: publicPath + 'resource/tinymce/skins/ui/' + skinName.value,
    content_css: publicPath + 'resource/tinymce/skins/ui/' + skinName.value + '/content.min.css',
    ...options,
    setup: (editor: Editor) => {
      editorRef.value = editor;
      editor.on('init', e => initSetup(e));
    },
  };
});

const disabled = computed(() => {
  const { options } = props;
  const getDisabled = options && Reflect.get(options, 'readonly');
  // const editor = unref(editorRef);
  // if (editor) {
  //   editor.setMode(getDisabled ? 'readonly' : 'design');
  // }
  return getDisabled ?? false;
});

// watch(
//   () => attrs.disabled,
//   () => {
//     const editor = unref(editorRef);
//     if (!editor) {
//       return;
//     }
//     editor.setMode(unref(attrs.disabled) ? 'readonly' : 'design');
//   }
// );

watch(
  () => props.options?.readonly,
  () => {
    nextTick(() => {
      const editor = unref(editorRef);
      if (!editor) {
        return;
      }
      setTimeout(() => {
        editor.setMode(props.options?.readonly ? 'readonly' : 'design');
      }, 30);
    });
  },
  { deep: true }
);

onMountedOrActivated(() => {
  if (!initOptions.value.inline) {
    tinymceId.value = buildShortUUID('tiny-vue');
  }
  nextTick(() => {
    setTimeout(() => {
      initEditor();
    }, 30);
  });
});

onBeforeUnmount(() => {
  destory();
});

onDeactivated(() => {
  destory();
});

function destory() {
  if (tinymce !== null) {
    tinymce?.remove?.(unref(initOptions).selector!);
  }
}

function initEditor() {
  const el = unref(elRef);
  if (el) {
    el.style.visibility = '';
  }
  tinymce
    .init(unref(initOptions))
    .then(editor => {
      emit('inited', editor);
    })
    .catch(err => {
      emit('init-error', err);
    });
}

function initSetup(e) {
  const editor = unref(editorRef);
  if (!editor) {
    return;
  }
  const value = props.modelValue || '';

  editor.setContent(value);
  bindModelHandlers(editor);
  bindHandlers(e, attrs, unref(editorRef));
}

function setValue(editor: Record<string, any>, val?: string, prevVal?: string) {
  if (!val) {
    val = '';
  }

  if (
    editor &&
    typeof val === 'string' &&
    val !== prevVal &&
    val !== editor.getContent({ format: attrs.outputFormat })
  ) {
    editor.setContent(val);
  }
}

function bindModelHandlers(editor: any) {
  const modelEvents = attrs.modelEvents ? attrs.modelEvents : null;
  const normalizedEvents = Array.isArray(modelEvents) ? modelEvents.join(' ') : modelEvents;

  watch(
    () => props.modelValue,
    (val, prevVal) => {
      setValue(editor, val, prevVal);
    }
  );

  watch(
    () => props.value,
    (val, prevVal) => {
      setValue(editor, val, prevVal);
    },
    {
      immediate: true,
    }
  );

  editor.on(normalizedEvents ? normalizedEvents : 'change keyup undo redo', () => {
    const content = editor.getContent({ format: attrs.outputFormat });
    emit('update:modelValue', content);
    emit('change', content);
  });

  editor.on('FullscreenStateChanged', e => {
    fullscreen.value = e.state;
  });
}

function handleImageUploading(name: string) {
  const editor = unref(editorRef);
  if (!editor) {
    return;
  }
  editor.execCommand('mceInsertContent', false, getUploadingImgName(name));
  const content = editor?.getContent() ?? '';
  setValue(editor, content);
}

function handleDone(name: string, url: string) {
  const editor = unref(editorRef);
  if (!editor) {
    return;
  }
  const content = editor?.getContent() ?? '';
  const val = content?.replace(getUploadingImgName(name), `<img src="${url}"/>`) ?? '';
  setValue(editor, val);
}

function getUploadingImgName(name: string) {
  return `[uploading:${name}]`;
}
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-tinymce-container';

.@{prefix-cls} {
  position: relative;
  line-height: normal;

  textarea {
    visibility: hidden;
    z-index: -1;
  }
}
</style>

<style lang="less" module>
.waring {
  :global {
    z-index: 999999 !important;
  }
}
</style>
