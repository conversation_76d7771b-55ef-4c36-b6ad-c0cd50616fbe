.loading-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading {
  margin: 100px;
  height: 100px;
  line-height: 100px;
}
.loading > span {
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 2px;
  animation: loadingWord 800ms ease-in infinite alternate;
  font-size: 24px;
}
/* 10个字母，每一个的延迟都不同 */
.loading-1 > span:nth-of-type(1) {
  animation-delay: 200ms;
}
.loading-1 > span:nth-of-type(2) {
  animation-delay: 300ms;
}
.loading-1 > span:nth-of-type(3) {
  animation-delay: 400ms;
}
.loading-1 > span:nth-of-type(4) {
  animation-delay: 500ms;
}
.loading-1 > span:nth-of-type(5) {
  animation-delay: 600ms;
}
.loading-1 > span:nth-of-type(6) {
  animation-delay: 700ms;
}
.loading-1 > span:nth-of-type(7) {
  animation-delay: 800ms;
}
.loading-1 > span:nth-of-type(8) {
  animation-delay: 900ms;
}
.loading-1 > span:nth-of-type(9) {
  animation-delay: 1000ms;
}
.loading-1 > span:nth-of-type(10) {
  animation-delay: 1100ms;
}
.loading-1 > span:nth-of-type(11) {
  animation-delay: 1200ms;
}
.loading-1 > span:nth-of-type(12) {
  animation-delay: 1300ms;
}
.loading-1 > span:nth-of-type(13) {
  animation-delay: 1400ms;
}
@keyframes loadingWord {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-16px);
  }
}
