import { defineStore } from 'pinia';

interface InfosState {
  record: Nullable<Recordable>;
  visible: boolean;
}

export const useInfos = defineStore({
  id: 'infos',
  state: (): InfosState => ({
    visible: false,
    record: null,
  }),
  getters: {
    getVisible(): boolean {
      return this.visible;
    },
    getRecord(): Nullable<Recordable> {
      return this.record;
    },
  },
  actions: {
    setRecord(obj: Nullable<Recordable>) {
      this.record = obj;
    },
    setVisible(flg) {
      this.visible = flg;
    },
  },
});
