<template>
  <div
    class="w-full h-full p-5px"
    :class="$style.business"
  >
    <a-card
      class="w-full h-full"
      :bordered="true"
    >
      <a-button
        type="primary"
        @click="handleClick"
        >{{ clickText }}</a-button
      >

      <a-row class="w-full h-full">
        <a-col
          :span="8"
          class="h-full border border-[#f0f0f0]"
        >
          <BasicForm
            @register="registerBasic"
            :class="disabledClass"
          />
        </a-col>
        <a-col
          :span="10"
          class="h-full border border-[#f0f0f0]"
        >
          <BasicForm
            @register="registerQualifications"
            :class="disabledClass"
          />
        </a-col>
        <!-- 先不开放商户交易配置信息 2024-12-28 -->
        <!-- <a-col
          :span="6"
          class="h-full border border-[#f0f0f0]"
        >
          <BasicForm
            @register="registerPay"
            :class="disabledClass"
          />
        </a-col> -->
      </a-row>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import {
  customUpdateConfigByCompanyId,
  findCompanyInfo,
  getCompanyTransConfig,
  getOpenCompanyMoreInfo,
  updateCompanyMainInfo,
  updateCompanyMoreInfoByCompanyId,
} from '@/api/businessInfo';
import { BasicForm, useForm } from '@/components/Form';
import { useMessage } from '@monorepo-yysz/hooks';
import { modalFormBasic, modalFormPay, modalFormQualifications } from './data';
import { computed, nextTick, onMounted, ref, unref } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { find } from 'lodash-es';

const userStore = useUserStore();

const { createMessage } = useMessage();

const basicRecord = ref<Recordable>();

const qualificationsRecord = ref<Recordable>();

const payRecord = ref<Recordable>();

const disabledClass = ref('back-transparent');

// 点击
const ifClick = ref(false);

const clickText = computed(() => (unref(ifClick) ? '保存' : '编辑'));

const [registerBasic, { setFieldsValue, setProps, validate }] = useForm({
  labelWidth: 120,
  schemas: modalFormBasic(),
  showActionButtonGroup: false,
  disabled: true,
});

const [
  registerQualifications,
  {
    setFieldsValue: setQualificationsVal,
    setProps: setQualificationsProp,
    validate: validateQualifications,
  },
] = useForm({
  labelWidth: 120,
  schemas: modalFormQualifications(),
  showActionButtonGroup: false,
  disabled: true,
});

// const [registerPay, { setFieldsValue: setPayVal, setProps: setPayProp, validate: validatePay }] =
//   useForm({
//     labelWidth: 120,
//     schemas: modalFormPay(),
//     showActionButtonGroup: false,
//     disabled: true,
//   });

async function handleClick() {
  ifClick.value = !unref(ifClick);
  setProps({ disabled: !unref(ifClick) });
  setQualificationsProp({ disabled: !unref(ifClick) });
  // setPayProp({ disabled: !unref(ifClick) });

  disabledClass.value = !unref(ifClick) ? 'back-transparent' : '';

  if (unref(ifClick)) return;

  const basicParam = { ...unref(basicRecord), ...(await validate()) };

  const qualificationsParam = {
    ...unref(qualificationsRecord),
    ...(await validateQualifications()),
    systemQueryType:"open",
  };
  qualificationsParam.publicityImg = qualificationsParam.publicityImg?.join(',')
  qualificationsParam.qualificationImg = qualificationsParam.qualificationImg?.join(',')
  // const payParam = { ...unref(payRecord), ...(await validatePay()) };

  // api
  Promise.all([
    updateCompanyMainInfo(basicParam),
    updateCompanyMoreInfoByCompanyId(qualificationsParam),
    // customUpdateConfigByCompanyId(payParam),
  ]).then(responseArr => {
    console.log(responseArr);

    const response = find(responseArr, v => v.code !== 200);
    
    createMessage.info(response ? response?.message : '保存成功');
  });
}

onMounted(async () => {
  await nextTick();

  // 基础信息
  basicRecord.value = await findCompanyInfo({ companyId: userStore.getUserInfo.companyId });
  await setFieldsValue(unref(basicRecord) || {});

  // 资质信息
  qualificationsRecord.value = await getOpenCompanyMoreInfo({
    companyId: userStore.getUserInfo.companyId,
  });
  await setQualificationsVal({
    ...unref(qualificationsRecord) || {},
    publicityImg: qualificationsRecord?.publicityImg ? qualificationsRecord.publicityImg.split(',') : [],
    qualificationImg: qualificationsRecord?.qualificationImg ? qualificationsRecord.qualificationImg.split(',') : []
  });
  

  // 交易信息
  payRecord.value = await getCompanyTransConfig({ companyId: userStore.getUserInfo.companyId });
  // await setPayVal(unref(payRecord) || {});
});
</script>

<style lang="less" module>
.business {
  :global {
    .ant-card-body {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
