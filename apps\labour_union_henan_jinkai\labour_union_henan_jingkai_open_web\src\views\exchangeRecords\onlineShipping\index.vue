<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleDown"
          :loading="spinning"
          >导出发货记录</a-button
        >
        <Upload
            name="multipartFile"
            accept=".xlsx,.xls"
            @change="handleImport"
            :before-upload="beforeUpload"
            :action="action"
            :max-count="1"
            :headers="{ token: userStore.getToken }"
            :showUploadList = false
            v-if="ifImport"
          >
            <a-button
              type="primary"
              :loading="spinningImport"
              >导入记录信息</a-button
            >
          </Upload>
          <!-- auth="/modelWorkerInfo/import" -->
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'mynaui:send',
                label: '发货',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                disabled: 'deliver' !== record.deliveryStatus,
              },
              // {
              //   icon: 'fluent:delete-16-filled',
              //   label: '删除',
              //   type: 'primary',
              //   danger: true,
              //   onClick: handleDelete.bind(null, record),
              // },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <OnlineShippingModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, unref ,computed} from 'vue';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import OnlineShippingModal from './onlineShippingModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  list,
  view,
  deleteLine,
  deliveryGoods,
  shipmentsExport
} from '@/api/productManagement/integralExchangeRecord';
import dayjs from 'dayjs';
import { downloadByUrl } from '@monorepo-yysz/utils';
import { Upload } from 'ant-design-vue';
import { useGlobSetting } from '@/hooks/setting';
import { useUserStore } from '@/store/modules/user';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const { uploadUrl } = useGlobSetting();

const spinning = ref<boolean>(false);

const spinningImport = ref<boolean>(false);

const params = ref<Recordable>();

const userStore = useUserStore();

const action = ref(`${uploadUrl}/open/integralExchangeRecord/import/shipments`);

const ifImport = computed(() => {
  // return hasPermission('/modelWorkerInfo/import');
  return true;
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  beforeFetch: p => {
    params.value = {
      integralPayment :'1',
      ...p,
  } 
    return unref(params);
  },
  searchInfo: { orderBy: 'create_time', sortType: 'desc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

//编辑
function handleEdit(record) {
  view({ recordId: record?.recordId }).then(({ data }) => {
    openModal(true, {
      isUpdate: true,
      disabled: false,
      record: {
        ...data,
        userName: record?.userName,
        writeOffUserName: null,
        writeOffUserPhone: null,
        logisticsCompany: null,
        logisticsNumber: null,
        shippingAddress: null,
      },
    });
  });
}

//详情
function handleView(record) {
  view({ recordId: record?.recordId }).then(({ data }) => {
    openModal(true, {
      isUpdate: true,
      disabled: true,
      record: { ...data, userName: record?.userName },
    });
  });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.title}`,
    onOk: function () {
      deleteLine({ ...record }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ values, isUpdate }) {
  deliveryGoods(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `发货成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `发货失败! ${message}`,
      });
    }
  });
}

function handleDown() {
  spinning.value = true;
  
  console.log(unref(params));
  shipmentsExport(unref(params)).then(res => {
    const url = window.URL.createObjectURL(res);
    const fileName = `发货记录${dayjs().format('YYYY-MM-DD HH:mm:ss')}`;
    downloadByUrl({
      url,
      fileName,
    });
    spinning.value = false;
  });
}

function handleImport({ file }) {
  if (file.status === 'done') {
    spinningImport.value = false;
    const { message, code } = file?.response;
    if (code !== 200) {
      createErrorModal({ content: `导入失败!${message}` });
      return;
    }
    createSuccessModal({ content: `导入成功` });
    reload();
  }
}

const beforeUpload: UploadProps['beforeUpload'] = file => {
  const { name } = file;
  const fileName = name?.split('.') || [''];
  const isExcel = ['xls', 'xlsx', 'csv'].includes(fileName[fileName.length - 1]);
  if (!isExcel) {
    createErrorModal({ content: '只能上传Excel表格' });
  }
  spinningImport.value = true;
  return isExcel || Upload.LIST_IGNORE;
};
</script>
