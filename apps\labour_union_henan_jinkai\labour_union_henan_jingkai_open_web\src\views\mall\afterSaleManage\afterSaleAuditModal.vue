<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    maskTransitionName="fade"
    transitionName="fade"
  >
    <BasicForm @register="registerForm" :class="disabledClass">
      <template #orderList>
        <!-- 订单商品列表 -->
        <div class="order-list">
          <a-table
            :columns="columns"
            :dataSource="orderData"
            :pagination="false"
            size="small"
            :bordered="true"
          >
          </a-table>
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { afterSaleAuditModalFormItem, orderListColumns } from './data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.xx || ''}--详情`
      : `${unref(record)?.orderId || ''}--售后详情`
    : '新增xx';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return afterSaleAuditModalFormItem(unref(disabled), unref(isUpdate));
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const columns = computed(() => {
  return orderListColumns();
});

const orderData = ref([]);

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  orderData.value = data.record?.detailVOList || [];

  disabled.value = !!data.disabled;
  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
    });
  }

  setProps({ disabled: unref(disabled) });
  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" scoped>
.order-list {
  margin: 8px 0;
  
  :deep(.ant-table-small) {
    border: 1px solid #f0f0f0;
  }
}
</style>
