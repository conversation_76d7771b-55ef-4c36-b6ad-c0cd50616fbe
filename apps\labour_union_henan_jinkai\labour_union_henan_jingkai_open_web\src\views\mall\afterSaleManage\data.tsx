import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useUserStore } from '/@/store/modules/user';
import { useDictionary } from '/@/store/modules/dictionary';
import { Image, Input } from 'ant-design-vue';
import { cloneDeep, filter, startsWith } from 'lodash-es';
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '退款商品信息',
      dataIndex: 'userName',
      width: 400,
      customRender({ record }) {
        return (
          <div style={{ textAlign: 'left' }}>
            <p>
              <span style={{ fontWeight: 'bold' }}>售后时间: </span>&nbsp;{record.createTime}
            </p>
            <p>
              <span style={{ fontWeight: 'bold' }}>订单编号: </span>&nbsp;{record.orderId}
            </p>
            {/* <div style={{ display: 'flex', alignItems: 'center' }}>
              <Image
                src={userStore.getPrefix + record.productCoverImg}
                width={50}
                height={50}
              />
              <div style={{ marginLeft: '20px' }}>
                <p>{record.productName}</p>
                <p style={{ fontSize: 12, color: '#999' }}>{record.productName}</p>
                <p>数量: {record.createTime}</p>
              </div>
            </div> */}
          </div>
        );
      },
    },
    {
      title: '退款人信息',
      dataIndex: '',
      customRender({ record }) {
        let saleServiceType =
          dictionary.getDictionaryMap.get(`saleServiceType_${record?.saleServiceType}`)?.dictName ||
          '';
        return (
          <div style={{ textAlign: 'left' }}>
            <p>
              <span style={{ fontWeight: 'bold' }}>退款人: </span>&nbsp;{record.createUser}
            </p>
            <p>
              <span style={{ fontWeight: 'bold' }}>退款类型: </span>&nbsp;{saleServiceType}
            </p>
            <p>
              <span style={{ fontWeight: 'bold' }}>退款原因: </span>&nbsp;{record.serviceReason}
            </p>
          </div>
        );
      },
    },
    {
      title: '退款信息',
      dataIndex: '',
      customRender({ record }) {
        return (
          <div style={{ textAlign: 'left' }}>
            <p>
              <span style={{ fontWeight: 'bold' }}>总退还金额￥: </span>&nbsp;
              {record.backAmountTotal}
            </p>
            <p>
              <span style={{ fontWeight: 'bold' }}>总退还积分: </span>&nbsp;
              {record.backIntegralTotal}
            </p>
          </div>
        );
      },
    },

    {
      title: '退款状态',
      dataIndex: 'serviceState',
      customRender({ record }) {
        let name =
          dictionary.getDictionaryMap.get(`saleServiceState_${record?.serviceState}`)?.dictName ||
          '';
        return <span title={name}>{name}</span>;
      },
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: 'orderId',
      label: '订单编号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'serviceState',
      label: '退款状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('saleServiceState'),
        };
      },
    },
  ];
};

export const modalFormItem = (disabled, isUpdate): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: '',
      label: '商品信息',
      component: 'Divider',
    },
    {
      field: 'companyName',
      label: '商户名称',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'serviceState',
      label: '处理状态',
      dynamicDisabled: !disabled || isUpdate,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('saleServiceState'),
        };
      },
    },
    {
      field: 'backAmountTotal',
      label: '退还金额',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'backIntegralTotal',
      label: '退还积分',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
     {
      field: 'orderListDivider',
      label: '退款商品/规格信息',
      component: 'Divider',
    },
    {
      field: 'orderList',
      label: '',
      component: 'Input',
      slot: 'orderList',
      colProps: { span: 24 },
    },
    {
      field: '',
      label: '退货信息',
      component: 'Divider',
    },
    {
      field: 'saleServiceType',
      label: '退款类型',
      dynamicDisabled: !disabled || isUpdate,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('saleServiceType'),
        };
      },
    },
    {
      field: 'serviceReason',
      label: '退款原因',
      dynamicDisabled: !disabled || isUpdate,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('saleServiceReason'),
        };
      },
    },
     {
      field: 'transportName',
      label: '物流名称',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
     {
      field: 'transportNumber',
      label: '物流单号',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'serviceRemark',
      label: '退款备注',
      component: 'InputTextArea',
      colProps: { span: 24 },
      componentProps: {
        autocomplete: 'off',
        // showCount: true,
        // maxlength: 30,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
  ];
};

export const afterSaleAuditModalFormItem = (disabled, isUpdate): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: '',
      label: '售后信息',
      component: 'Divider',
    },
    {
      field: 'orderId',
      label: '退款订单编号',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'backAmountTotal',
      label: '退款金额',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'saleServiceType',
      label: '退款类型',
      dynamicDisabled: !disabled || isUpdate,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('saleServiceType'),
        };
      },
    },
    {
      field: 'backIntegralTotal',
      label: '退还积分',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'serviceReason',
      label: '退款原因',
      dynamicDisabled: !disabled || isUpdate,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('serviceReason'),
        };
      },
    },
    {
      field: 'serviceRemark',
      label: '退款备注',
      component: 'InputTextArea',
      colProps: { span: 24 },
      dynamicDisabled: !disabled || isUpdate,
      componentProps: {
        autocomplete: 'off',
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    {
      field: 'orderListDivider',
      label: '退款商品/规格信息',
      component: 'Divider',
    },
    {
      field: 'orderList',
      label: '',
      component: 'Input',
      slot: 'orderList',
      colProps: { span: 24 },
    },
    {
      field: '',
      label: '售后审核',
      component: 'Divider',
    },
    {
      field: 'operateType',
      label: '审核结果',
      component: 'RadioGroup',
      colProps: { span: 24 },
      componentProps: {
        options: [
          {
            label: '通过',
            value: 'pass',
          },
          {
            label: '驳回',
            value: 'refuse',
          },
        ],
      },
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'remark',
      label: '审核备注',
      component: 'InputTextArea',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入审核备注',
        rows: 4,
        autocomplete: 'off',
      },
      required: function (Recordable) {
        if (Recordable.model.operateType == 'refuse') {
          return true
        } else {
          return false
        }
      },
      rulesMessageJoinLabel: true,
    },
  ];
};

export const orderListColumns = (): BasicColumn[] => {
  const userStore = useUserStore();
  const dictionary = useDictionary();
  return [
    {
      title: '商品名称',
      customRender({ record }) {
        return <span>{record.productSubSnapshotJson.productName}</span>;
      },
      key: 'productName',
      width: '30%',
    },
    {
      title: '商品规格',
      customRender({ record }) {
        return <span>{record.productSubSnapshotJson.productSubName}</span>;
      },
      key: 'productSubName',
      width: '20%',
    },
    {
      title: '商品规格封面',
      dataIndex: '',
      key: 'productSubImg',
      width: '15%',
      customRender: ({ record }) => {
        let text = record.productSubSnapshotJson.productSubImg;
        return (
          <Image
            src={startsWith(text, 'http') ? text : userStore.getPrefix + text}
            width={50}
            height={50}
          />
        );
      },
    },
    {
      title: '运费(元)',
      customRender({ record }) {
        return <span>{record.productSubSnapshotJson.transportPrice}</span>;
      },
      key: 'transportPrice',
      width: '15%',
    },
    {
      title: '退还数量(件)',
      customRender({ record }) {
        return <span>{record.productSubSnapshotJson.currentProductCount}</span>;
      },
      key: 'currentProductCount',
      width: '20%',
    },
  ];
};

export const columnSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: '',
      label: '',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get(''),
        };
      },
    },
  ];
};

export const childrenColumns = (): BasicColumn[] => {
  const userStore = useUserStore();
  const dictionary = useDictionary();
  return [
    // {
    //   title: '主键',
    //   dataIndex: 'autoId',
    //   defaultHidden: true,
    // },
    {
      title: '商品名称',
      dataIndex: '',
      width: '25%',
      ellipsis: true,
      customRender({ record }) {
        return <span>{record.productSubSnapshotJson.productName} </span>;
      },
    },
    {
      title: '商品规格名称',
      width: '25%',
      ellipsis: true,
      customRender({ record }) {
        return <span>{record.productSubSnapshotJson.productSubName} </span>;
      },
    },
    {
      title: '商品规格封面',
      dataIndex: '',
      width: '25%',
      customRender: ({ record }) => {
        let text = record.productSubSnapshotJson.productSubImg;
        return (
          <Image
            src={startsWith(text, 'http') ? text : userStore.getPrefix + text}
            width={50}
            height={50}
          />
        );
      },
    },
    {
      title: '运费(元)',
      dataIndex: '',
      width: '25%',
      ellipsis: true,
      customRender({ record }) {
        return <span>{record.productSubSnapshotJson.transportPrice} </span>;
      },
    },
    {
      title: '退还数量(件)',
      dataIndex: '',
      width: '25%',
      ellipsis: true,
      customRender({ record }) {
        return <span>{record.productSubSnapshotJson.currentProductCount} </span>;
      },
    },
  ];
};