<template>
  <div>
    <BasicTable
      @register="registerTable"
      :clickToRowSelect="false"
    >
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
        >
          <!-- auth="/productManagement/add" -->
          新增商品</a-button
        >
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleProductDetail.bind(null, record),
                // auth: '/productManagement/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                disabled: record.state !== 'down',
                type: 'primary',
                onClick: handleUpdateProducts.bind(null, record),
                ifShow: record.showFlag,
                // auth: '/productManagement/modify',
              },
              {
                icon: 'carbon:cut-out',
                label: '上架',
                disabled: record.state !== 'down',
                type: 'primary',
                onClick: handleUpProduct.bind(null, record),
                // auth: '/productManagement/up',
                ifShow: record.showFlag,
              },
              {
                icon: 'bx:log-out-circle',
                label: '下架',
                type: 'primary',
                  danger: true,
                onClick: handleDownProduct.bind(null, record),
                // auth: '/productManagement/down',
                ifShow: record.showFlag,
                disabled: record.state !== 'up',
              },
              {
                icon: 'tabler:list-details',
                label: '规格',
                type: 'primary',
                onClick: specificationManage.bind(null, record),
                ifShow: record.showFlag,
                // auth: '/productManagement/specifications',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ProductModal
      @register="registerModal"
      :canFullscreen="false"
      width="70%"
      @success="handleSubmitForm"
    />
    <SpecificationsModal
      @register="registerCommentModal"
      :canFullscreen="false"
      width="60%"
      @cancel="handleCancel"
    />
  </div>
</template>

<script lang="ts" setup>
import { createVNode } from 'vue';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { message, Modal } from 'ant-design-vue';
import ProductModal from '@/views/mall/products/productModal.vue';
import SpecificationsModal from '@/views/mall/products/SpecificationsModal.vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useModal } from '@/components/Modal';
import {
  treeProductList,
  upAndDownProducts,
  getSpecifications,
  addInclusiveProduct,
  updateInclusiveProduct,
  getProductsIntroduces,
} from '@/api/productManagement';
import { columns, formSchemas } from '@/views/mall/products/productManagement';

const [registerModal, { openModal, closeModal }] = useModal();
//注册规格商品列表弹框
const [registerCommentModal, { openModal: openSpecificationsModal }] = useModal();
const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: treeProductList,
  beforeFetch: params => {
    params.systemQueryType = 'open';
    return params;
  },
  //顶部搜索条件配置
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  searchInfo: {
    orderBy: 'create_time',
    sortType: 'desc',
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 400,
    dataIndex: 'action',
    fixed: undefined,
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
    // auth: [
    //   '/productManagement/down',
    //   '/productManagement/view',
    //   '/productManagement/up',
    //   '/productManagement/modify',
    // ],
  },
});

//主商品下架
function handleDownProduct(record) {
  const specificationsIds: string[] = [];
  getSpecifications({ productId: record.productId, systemQueryType: 'manage' }).then(res => {
    if (res.code === 200) {
      for (let i = 0; i < res.data.length; i++) {
        specificationsIds.push(res.data[i].productSubId);
      }
    }
  });
  Modal.confirm({
    title: '主商品下架时,所有规格也会被下架',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要下架` + '"' + record.productName + '"' + '?',
    cancelText: '取消',
    okText: '确认',
    async onOk() {
      const downParams = {
        operateProductType: 'inclusive',
        productId: record.productId,
        operateType: 'down',
        productSubIdList: specificationsIds,
      };
      try {
        return await new Promise<void>(resolve => {
          upAndDownProducts(downParams).then(res => {
            if (res.code === 200) {
              message.success('主商品下架成功');
            } else {
              message.error('主商品下架失败');
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}

//主商品上架接口
function handleUpProduct(record) {
  openSpecificationsModal(true, {
    record: record,
    isMain: true,
    isShow: false,
    canSubmit: false,
  });
}

//提交新增或者编辑弹框
function handleSubmitForm(values, isUpdate) {
  if (isUpdate) {
    updateInclusiveProduct(values).then(res => {
      if (res.code === 200) {
        message.success('编辑商品成功');
        reload();
        closeModal();
      } else {
        message.error(`编辑商品失败！${res.message}`);
      }
    });
  } else {
    addInclusiveProduct(values).then(res => {
      if (res.code === 200) {
        message.success('新增商品成功');
        reload();
        closeModal();
      } else {
        message.error(`新增商品失败！${res.message}`);
      }
    });
  }
}

//新增商品
function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

//编辑普惠商品
function handleUpdateProducts(record) {
  getProductsIntroduces({ productId: record.productId }).then(res => {
    if (res.code === 200) {
      record.productIntroduce = res.data.productIntroduce;
      openModal(true, {
        record,
        isUpdate: true,
        disabled: false,
      });
    }
  });
}

//查看商品规格管理操作
function specificationManage(record) {
  openSpecificationsModal(true, {
    record: record,
    isMain: false,
    isShow: true,
    canSubmit: false,
  });
}

//查看商品详情
function handleProductDetail(record) {
  getProductsIntroduces({ productId: record.productId }).then(res => {
    if (res.code === 200) {
      record.productIntroduce = res.data.productIntroduce;
      openModal(true, {
        record,
        isUpdate: true,
        disabled: true,
      });
    }
  });
}

// 添加取消处理函数
function handleCancel() {
  reload();
}
</script>
