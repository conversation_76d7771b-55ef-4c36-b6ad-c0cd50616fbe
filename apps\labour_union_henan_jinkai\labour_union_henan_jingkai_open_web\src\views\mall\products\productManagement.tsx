import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { CheckboxOptionType, Image, Input } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/user';
import { Tinymce } from '@/components/Tinymce';
import { userInfoSearch } from '@/api/productManagement/orderRecord';
import { getSpecifications } from '@/api/productManagement';
import { startsWith } from 'lodash-es';
//列表配置
export const columns = (phProductFlg): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      width: 150,
    },
    {
      title: '商户名称',
      dataIndex: 'companyName',
      width: 150,
    },
    {
      title: '商品封面',
      dataIndex: 'productCoverImg',
      width: 90,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '总销量',
      dataIndex: 'companyName',
      width: 90,
    },
    {
      title: '商品类型',
      dataIndex: 'productType',
      width: 90,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`productType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '销售状态',
      dataIndex: 'state',
      width: 90,
      customRender: ({ text, record }) => {
        const { state } = record;
        return (
          <span class={state == 'up' ? 'text-green-500' : state == 'down' ? 'text-red-500' : ''}>
            {dictionary.getDictionaryMap.get(`saleEnable_${text}`)?.dictName}
          </span>
        );
      },
    },
    {
      title: '添加人',
      dataIndex: 'createUser',
      width: 150,
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 150,
    }
  ];
};

//顶部菜单栏搜索条件配置
export const formSchemas = (flg): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'productName',
      label: '商品名称',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请输入商品名称',
        autocomplete: 'off',
      },
    },
    {
      field: 'state',
      label: '上架状态',
      component: 'Select',
      colProps: { span: 6 },
      ifShow: flg !== true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('saleEnable'),
          placeholder: '请选择上架状态',
        };
      },
    }
  ];
};

//详情弹框配置
export const modalForm = (isInclusive, isUpdate: boolean): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'productName',
      label: '商品名称',
      colProps: { span: 24 },
      labelWidth: 80,
      required: true,
      rulesMessageJoinLabel: true,
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 100,
      },
    },
    {
      field: 'productType',
      label: '商品类型',
      colProps: { span: 12 },
      labelWidth: 80,
      required: true,
      component: 'RadioGroup',
      // dynamicDisabled({ values }) {
      //   return values.sourceType === 'inclusive' ? true : false
      // },
      slot: 'productType',
      // componentProps: function () {
      //   return {
      //     options: dictionary.getDictionaryOpt.get('productType') as CheckboxOptionType[],
      //     placeholder: '请选择商品类型',
      //   };
      // },
    },
    {
      labelWidth: 80,
      field: 'inclusiveProductColumn',
      label: '商品栏目',
      colProps: { span: 12 },
      required: true,
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('inclusiveProductColumn') as CheckboxOptionType[],
      },
    },
    {
      labelWidth: 85,
      field: 'productCoverImg',
      label: '商品封面图',
      colProps: { span: 24 },
      required: true,
      component: 'CropperForm',
      slot: 'picOnlyOne',
      dynamicDisabled({ values }) {
        return values.sourceType === 'inclusive' ? true : false;
      },
    },
    {
      field: 'productPublicityImg',
      label: '宣传图',
      labelWidth: 60,
      colProps: { span: 24 },
      required: true,
      component: 'Input',
      slot: 'pic',
      rest: true,
    },
    {
      field: 'productIntroduce',
      label: '商品简介',
      colProps: { span: 24 },
      labelWidth: 80,
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      render({ model, field, disabled }) {
        return (
          <Tinymce
            value={model[field]}
            options={{ readonly: !!disabled }}
            onChange={value => {
              model[field] = value;
            }}
          ></Tinymce>
        );
      },
    },
  ];
};

//顶部菜单栏搜索条件配置
export const querySpecifications = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'productSubName',
      label: '商品规格名称',
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请输入商品规格名称',
        autocomplete: 'off',
      },
    },
    {
      field: 'saleState',
      label: '上架状态',
      component: 'Select',
      colProps: { span: 8 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('saleEnable'),
          placeholder: '请选择上架状态',
        };
      },
    },
  ];
};

//添加时候规格列表
export const specificationsColumns = (isUpdate: boolean): BasicColumn[] => {

  return [
    {
      title: '规格名',
      dataIndex: 'productSubName',
      width: 200,
    },
    {
      title: '规格封面',
      dataIndex: 'productSubImg',
      width: 180,
    },
    {
      title: '销售量',
      dataIndex: 'saleNum',
      width: 100,
      ifShow: isUpdate,
    },
    {
      title: '库存类型',
      dataIndex: 'reserveType',
      width: 100,
    },
    {
      title: '库存量(件)',
      dataIndex: 'reserve',
      width: 100,
    },
    {
      title: '商品现价(元)',
      dataIndex: 'nowPrice',
      width: 100,
    },
    {
      title: '商品原价(元)',
      dataIndex: 'oldPrice',
      width: 100,
    },
    {
      title: '运费',
      dataIndex: 'transportPrice',
      width: 100,
    },
    {
      title: '单次限购(件)',
      dataIndex: 'purchaseLimit',
      width: 100,
    },
  ];
};

//查询积分时候规格列表
export const querySpecificationsColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '规格名',
      dataIndex: 'productSubName',
      width: 100,
    },
    {
      title: '规格封面',
      dataIndex: 'productSubImg',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '库存类型',
      dataIndex: 'reserveType',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`reserveType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '上架状态',
      dataIndex: 'saleState',
      width: 100,
      customRender: ({ text, record }) => {
        const { saleState } = record;
        return (
          <span
            class={saleState == 'up' ? 'text-green-500' : saleState == 'down' ? 'text-red-500' : ''}
          >
            {dictionary.getDictionaryMap.get(`saleEnable_${text}`)?.dictName}
          </span>
        );
      },
    },
    {
      title: '库存量',
      dataIndex: 'reserve',
      width: 80,
      customRender: ({ text, record }) => {
        const { reserveType } = record;
        return <span>{reserveType === 'limited' ? text : '--'}</span>;
      },
    },
    {
      title: '原价(元)',
      dataIndex: 'oldPrice',
      width: 90,
    },
    {
      title: '现价(元)',
      dataIndex: 'nowPrice',
      width: 90,
    },
    {
      title: '运费(元)',
      dataIndex: 'transportPrice',
      width: 90,
    },
    {
      title: '单次限购(件)',
      dataIndex: 'purchaseLimit',
      width: 90,
    },
  ];
};

//查询普惠商品时候规格列表
export const querypuhuiColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '规格名',
      dataIndex: 'productSubName',
      width: 150,
    },
    {
      title: '规格封面',
      dataIndex: 'productSubImg',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '原价(元)',
      dataIndex: 'oldPrice',
      width: 100,
    },
    {
      title: '现价(元)',
      dataIndex: 'nowPrice',
      width: 100,
    },
    {
      title: '销售量',
      dataIndex: 'saleNum',
      width: 100,
    },
    {
      title: '库存类型',
      dataIndex: 'reserveType',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`reserveType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '库存量',
      dataIndex: 'reserve',
      width: 100,
      customRender: ({ text, record }) => {
        const { reserveType } = record;
        return <span>{reserveType === 'limited' ? text : '--'}</span>;
      },
    },
    {
      title: '上架状态',
      dataIndex: 'saleState',
      width: 100,
      customRender: ({ text, record }) => {
        const { saleState } = record;
        return (
          <span
            class={saleState == 'up' ? 'text-green-500' : saleState == 'down' ? 'text-red-500' : ''}
          >
            {dictionary.getDictionaryMap.get(`saleEnable_${text}`)?.dictName}
          </span>
        );
      },
    },
  ];
};

//顶部筛选商户条件参数
export const queryCompanyParams = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'companyName',
      label: '商户名称',
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请输入商户名称',
        autocomplete: 'off',
      },
    },
    {
      field: 'areaCode',
      label: '所属区域',
      component: 'Select',
      colProps: { span: 8 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('quxian'),
          placeholder: '请选择所属区域',
        };
      },
    },
  ];
};

//查询商户列表
export const queryCompanyList = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '商户id',
      dataIndex: 'companyId',
      defaultHidden: true,
    },
    {
      title: '商户名称',
      dataIndex: 'companyName',
      width: 100,
    },
    {
      title: '商家封面',
      dataIndex: 'companyIcon',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={startsWith(text, 'http') ? text : userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '联系方式',
      dataIndex: 'contractPhone',
      width: 100,
    },
    {
      title: '所属区域',
      dataIndex: 'areaCode',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`quxian_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '商户状态',
      dataIndex: 'state',
      width: 100,
      customRender: ({ text, record }) => {
        const { state } = record;
        return (
          <span
            class={state == 'normal' ? 'text-green-500' : state == 'down' ? 'text-red-500' : ''}
          >
            {dictionary.getDictionaryMap.get(`commonStatus_${text}`)?.dictName}
          </span>
        );
      },
    },
  ];
};

export const modalColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '兑换人',
      dataIndex: 'userName',
    },
    // {
    //   title: '电话',
    //   dataIndex: 'productSubName',
    // },
    {
      title: '规格名',
      dataIndex: 'productSubName',
    },
    {
      title: '兑换时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '规格封面',
      dataIndex: 'productSubImg',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    // {
    //   title: '提货方式',
    //   dataIndex: 'integralPayment',
    //   customRender({ text }) {
    //     const name = dictionary.getDictionaryMap.get(`integralPayment_${text}`)?.dictName || '';
    //     return <span title={name}>{name}</span>;
    //   },
    // },
    {
      title: '状态',
      dataIndex: '',
      customRender({ record }) {
        let name = '';
        if ('1' === record?.integralPayment) {
          name =
            dictionary.getDictionaryMap.get(`transOrderState_${record?.deliveryStatus}`)
              ?.dictName || '';
        } else {
          name = dictionary.getDictionaryMap.get(`qrCodeState_${record?.state}`)?.dictName || '';
        }
        return <span title={name}>{name}</span>;
      },
      width: 150,
    },
  ];
};

export const columnSchemas = (productId, integralPayment): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: 'userId',
      label: '兑换人',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          api: userInfoSearch,
          resultField: 'data',
          params: {},
          alwaysLoad: true,
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.userName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'userName', value: 'userId' },
        };
      },
    },
    {
      field: 'productSubId',
      label: '规格名',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          api: getSpecifications,
          resultField: 'data',
          params: { productId: productId ? productId : '', systemQueryType: 'manage' },
          alwaysLoad: true,
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.productSubName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'productSubName', value: 'productSubId' },
        };
      },
    },

    {
      field: '1' === integralPayment ? 'deliveryStatus' : 'state',
      label: '状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options:
            '1' === integralPayment
              ? [
                  { label: '待发货', value: 'deliver' },
                  { label: '待收货', value: 'receive' },
                  { label: '交易成功', value: 'over' },
                ]
              : dictionary.getDictionaryOpt.get('qrCodeState'),
        };
      },
    },
  ];
};

export const modalFormItem = (integralPayment): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();
  return [
    {
      field: '',
      label: '兑换信息',
      component: 'Divider',
    },
    {
      field: 'userName',
      label: '兑换人',
      colProps: { span: 12 },

      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'createTime',
      label: '兑换时间',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
    },
    {
      field: 'productName',
      label: '商品名',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
    },
    {
      field: 'productSubName',
      label: '规格名',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
    },
    {
      field: 'productCoverImg',
      label: '商品封面图',
      colProps: { span: 12 },

      component: 'CropperForm',
    },
    {
      field: 'productSubImg',
      label: '规格封面',
      colProps: { span: 12 },

      component: 'CropperForm',
    },
    {
      field: '1' === integralPayment ? 'deliveryStatus' : 'state',
      label: '状态',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options:
            '1' === integralPayment
              ? [
                  { label: '待发货', value: 'deliver' },
                  { label: '待收货', value: 'receive' },
                  { label: '交易成功', value: 'over' },
                ]
              : dictionary.getDictionaryOpt.get('qrCodeState'),
        };
      },
    },
    {
      field: '',
      label: '收货信息',
      component: 'Divider',
      ifShow: '1' === integralPayment,
    },
    {
      field: 'receiverName',
      label: '收货人姓名',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow: '1' === integralPayment,
    },
    {
      field: 'receiverPhone',
      label: '收货人电话',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
      ifShow: '1' === integralPayment,
    },
    {
      field: 'detailArea',
      label: '所在地区',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
      ifShow: '1' === integralPayment,
    },
    {
      field: 'detailAddress',
      label: '详细地址',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
      ifShow: '1' === integralPayment,
    },
    {
      field: '',
      label: '发货信息',
      component: 'Divider',
      ifShow: '1' === integralPayment,
    },
    {
      field: 'writeOffUserName',
      label: '发货人姓名',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 20,
        showCount: true,
      },
      ifShow: '1' === integralPayment,
    },
    {
      field: 'writeOffUserPhone',
      label: '发货人手机号',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 18,
        showCount: true,
      },
      ifShow: '1' === integralPayment,
    },
    {
      field: 'logisticsCompany',
      label: '物流公司',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 50,
        showCount: true,
      },
      ifShow: '1' === integralPayment,
    },
    {
      field: 'logisticsNumber',
      label: '物流单号',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 50,
        showCount: true,
      },
      ifShow: '1' === integralPayment,
    },
    {
      field: 'shippingAddress',
      label: '发货地址',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 300,
        showCount: true,
      },
      ifShow: '1' === integralPayment,
    },
    {
      field: '',
      label: '核销信息',
      component: 'Divider',
      ifShow: '2' === integralPayment,
    },
    {
      field: 'writeOffUserName',
      label: '核销人姓名',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 20,
        showCount: true,
      },
      ifShow: '2' === integralPayment,
    },
    {
      field: 'writeOffUserPhone',
      label: '核销人手机号码',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 18,
        showCount: true,
      },
      ifShow: '2' === integralPayment,
    },
  ];
};
