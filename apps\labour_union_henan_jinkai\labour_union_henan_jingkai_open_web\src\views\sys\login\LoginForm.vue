<template>
  <div
    :class="$style.login"
    class="relative"
  >
    <div class="flex justify-center items-center relative top-[-20px]">
      <div
        class="w-[410px] h-[59px] flex justify-center items-center"
        :style="{
          backgroundImage: `url(${loginTitleBg})`,
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
        }"
      >
        <img
          :src="loginTitle"
          class="h-[33px]"
        />
      </div>
    </div>
    <Form
      class="p-4 px-[50px] enter-x"
      :model="formData"
      :rules="getFormRules"
      ref="formRef"
      v-show="getShow"
      @keypress.enter="handleLogin"
    >
      <FormItem
        name="account"
        class="enter-x bg-[#F0F5FA] rounded-[30px]"
      >
        <Input
          size="large"
          v-model:value="formData.account"
          :placeholder="t('sys.login.accountPlaceholder')"
          class="fix-auto-fill"
        >
          <template #addonBefore>
            <img
              :src="userName"
              class="w-[17px] h-[20px]"
            />
          </template>
        </Input>
      </FormItem>

      <FormItem
        name="password"
        class="enter-x bg-[#F0F5FA] rounded-[30px]"
      >
        <InputPassword
          size="large"
          visibilityToggle
          v-model:value="formData.password"
          :placeholder="t('sys.login.passwordPlaceholder')"
        >
          <template #addonBefore>
            <img
              :src="password"
              class="w-[17px] h-[20px]"
            />
          </template>
        </InputPassword>
      </FormItem>

      <FormItem
        name="verifyCode"
        class="enter-x bg-[#F0F5FA] rounded-[30px]"
      >
        <Input
          size="large"
          v-model:value="formData.verifyCode"
          placeholder="请输入验证码"
        >
          <template #addonBefore>
            <img
              :src="verify"
              class="w-[17px] h-[20px]"
            />
          </template>
          <template #suffix>
            <img
              :src="codeUrl"
              @click="handleChangeCode"
              :style="{
                position: 'absolute',
                right: 0,
                cursor: 'pointer',
              }"
              class="!border border-gray-200 border-solid z-10 !h-full verify-code w-[135px] rounded-tr-[30px] rounded-br-[30px]"
            />
          </template>
        </Input>
      </FormItem>

      <FormItem class="enter-x rounded-[30px]">
        <Button
          type="primary"
          size="large"
          html-type="submit"
          block
          @click="handleLogin"
          :loading="loading"
          class="!rounded-[30px] !bg-[#68ABFF] !text-[#fff] !text-[16px]"
        >
          {{ t('sys.login.loginButton') }}
        </Button>
      </FormItem>
    </Form>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, unref, computed, onMounted } from 'vue';
import { Form, Input, Button } from 'ant-design-vue';
import { useI18n } from '@/hooks/web/useI18n';
import { useMessage } from '@monorepo-yysz/hooks';
import { useUserStore } from '@/store/modules/user';
import { LoginStateEnum, useLoginState, useFormRules, useFormValid } from './useLogin';
import { useDesign } from '@monorepo-yysz/hooks';
import { generatorWebVerifyCode } from '@/api';
import userName from '@/assets/images/login/userName.png';
import password from '@/assets/images/login/password.png';
import verify from '@/assets/images/login/verify.png';
import loginTitleBg from '@/assets/images/login/login-title-bg.png';
import loginTitle from '@/assets/images/login/login-title.png';

const FormItem = Form.Item;
const InputPassword = Input.Password;

const { t } = useI18n();
const { notification, createErrorModal } = useMessage();
const { prefixCls } = useDesign('login');
const userStore = useUserStore();

const codeUrl = ref();

const verifyCodeId = ref();

const { getLoginState } = useLoginState();
const { getFormRules } = useFormRules();

const formRef = ref();
const loading = ref(false);

const formData = reactive({
  account: undefined,
  password: undefined,
  verifyCode: undefined,
});

const { validForm } = useFormValid(formRef);

//onKeyStroke('Enter', handleLogin);

const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN);

async function handleChangeCode() {
  await getCode();
}

async function getCode() {
  const { imageBase64Data, verifyCodeId: id } = await generatorWebVerifyCode();
  codeUrl.value = imageBase64Data;
  verifyCodeId.value = id;
}

async function handleLogin() {
  const data = await validForm();
  if (!data) return;
  try {
    loading.value = true;
    const userInfo = await userStore.login({
      authSystem: 'OPEN', // 后端认证系统
      pwd: data.password,
      account: data.account,
      device: 'WEBSITE',
      loginType: 'ACCOUNT',
      verifyCodeId: unref(verifyCodeId),
      verifyCode: data.verifyCode,
      mode: 'none',
    });
    if (userInfo) {
      notification.success({
        message: t('sys.login.loginSuccessTitle'),
        description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.nickname || userInfo.account}`,
        duration: 3,
      });
    }
  } catch (error) {
    createErrorModal({
      title: t('sys.api.errorTip'),
      content: (error as unknown as Error).message || t('sys.api.networkExceptionMsg'),
      getContainer: () => document.body.querySelector(`.${prefixCls}`) || document.body,
    });
  } finally {
    loading.value = false;
    getCode();
    formData.verifyCode = undefined;
  }
}

onMounted(async () => {
  await getCode();
});
</script>

<style lang="less" module>
.login {
  :global {
    background-image: url('@/assets/images/login/login-form-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 338px;

    .ant-input-group-addon,
    .ant-input-affix-wrapper,
    .ant-input-affix-wrapper-focused,
    input {
      border: transparent !important;
      background-color: transparent !important;
      border-inline-end-width: unset !important;
      color: #000000;

      &:autofill {
        background: #e3efff !important; // 支持火狐
      }
      // 支持chrome
      &:-webkit-autofill,
      &:-webkit-autofill:hover,
      &:-webkit-autofill:focus,
      &:-webkit-autofill:active {
        transition: background-color 500000000s ease-in-out 0s; //背景色透明  生效时长  过渡效果  启用时延迟的时间
        -webkit-text-fill-color: #000000 !important;
      }

      &::-webkit-input-placeholder {
        --tw-placeholder-opacity: 1;
        color: rgba(107, 114, 128, var(--tw-placeholder-opacity));
      }
      &::-moz-placeholder {
        --tw-placeholder-opacity: 1;
        color: rgba(107, 114, 128, var(--tw-placeholder-opacity));
      }
      &:-ms-input-placeholder {
        --tw-placeholder-opacity: 1;
        color: rgba(107, 114, 128, var(--tw-placeholder-opacity));
      }
      &::-ms-input-placeholder {
        --tw-placeholder-opacity: 1;
        color: rgba(107, 114, 128, var(--tw-placeholder-opacity));
      }
      &::placeholder {
        --tw-placeholder-opacity: 1;
        color: rgba(107, 114, 128, var(--tw-placeholder-opacity));
      }

      &:focus {
        box-shadow: unset !important;
      }
    }
  }
}
</style>
