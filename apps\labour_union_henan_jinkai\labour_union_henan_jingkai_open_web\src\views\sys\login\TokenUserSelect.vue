<template>
  <LoginFormTitle class="enter-x" />
  <Form
    class="p-4 enter-x"
    ref="formRef"
    @keypress.enter="handleLogin"
    :class="$style.tokenUser"
  >
    <FormItem
      name="account"
      class="enter-x"
    >
      <RadioGroup
        :options="options"
        v-model:value="value"
      />
    </FormItem>
    <FormItem class="enter-x">
      <Button
        type="primary"
        size="large"
        block
        @click="handleLogin"
        :loading="loading"
      >
        {{ t('sys.login.loginButton') }}
      </Button>
    </FormItem>
  </Form>
</template>

<script lang="ts" setup>
import { Form, Button, RadioGroup } from 'ant-design-vue';
import { ref, unref } from 'vue';
import { useI18n } from 'vue-i18n';
import { adminLoginBack } from '/@/router/guard';
import { LoginStateEnum, useLoginState } from './useLogin';
import LoginFormTitle from './LoginFormTitle.vue';
import { find } from 'lodash-es';

const { setLoginState } = useLoginState();

setLoginState(LoginStateEnum.LOGIN);
const value = ref();

const options = [
  {
    label: '南充市总工会',
    value: '南充市总工会',
  },
  {
    label: '南充干部***********',
    value: '南充干部***********',
  },
];

const { t } = useI18n();

const FormItem = Form.Item;

const loading = ref(false);

function handleLogin() {
  try {
    loading.value = true;
    if (!unref(value)) {
      return false;
    }
    adminLoginBack({ query: { token: unref(value) } } as any, {
      account: find(options, v => v.value === unref(value))?.label,
      pwd: 'nanchong@123456',
    });
  } catch (error) {
  } finally {
    loading.value = false;
  }
}
</script>

<style lang="less" module>
.tokenUser {
  :global {
    .ant-radio-wrapper {
      color: rgb(73, 170, 255) !important;
    }
  }
}
</style>
