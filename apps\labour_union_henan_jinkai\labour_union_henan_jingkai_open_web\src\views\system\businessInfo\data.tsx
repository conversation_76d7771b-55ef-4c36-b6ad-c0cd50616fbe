import { list} from '@/api/merchants/type';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { useUserStore } from '@/store/modules/user';
import { searchNextUnionForm } from '@/utils/searchNextUnion';
import { list as roleList } from '@/api/system/role';
import { uploadApi } from '@/api/sys/upload';
import { validatePhone } from '@monorepo-yysz/utils';
import { Image } from 'ant-design-vue';
import { cloneDeep, filter, startsWith } from 'lodash-es';
import { nextTick } from 'vue';

// 列表配置
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '普惠商户名称',
      dataIndex: 'companyName',
    },
    {
      title: '所属工会',
      dataIndex: 'labourUnionName',
    },
    {
      title: '商家封面',
      dataIndex: 'companyIcon',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={startsWith(text, 'http') ? text : userStore.getPrefix + text}
            width={50}
            height={50}
          />
        );
      },
    },
    {
      title: '联系人',
      dataIndex: 'contractName',
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'desensitizationPhone',
      width: 120,
    },
    {
      title: '普惠商户地址',
      dataIndex: 'address',
      width: 200,
    },
    {
      title: '普惠商户状态',
      dataIndex: 'state',
      width: 120,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`state_${text}`)?.dictName}</span>;
      },
    },
  ];
};

//顶部搜索了配置
export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'companyName',
      label: '普惠商户名称',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { placeholder: '请输入普惠商户名称' },
    },

    {
      field: 'state',
      label: '普惠商户状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('state'),
          placeholder: '请选择普惠商户状态',
        };
      },
    },
  ];
};

export const childrenColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '普惠商户名称',
      dataIndex: 'companyName',
    },
    {
      title: '所属工会',
      dataIndex: 'labourUnionName',
    },
    {
      title: '商家封面',
      dataIndex: 'companyIcon',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={startsWith(text, 'http') ? text : userStore.getPrefix + text}
            width={50}
            height={50}
          />
        );
      },
    },
    {
      title: '联系人',
      dataIndex: 'contractName',
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'desensitizationPhone',
      width: 120,
    },
    {
      title: '普惠商户地址',
      dataIndex: 'address',
      width: 200,
    },
    {
      title: '普惠商户状态',
      dataIndex: 'state',
      width: 120,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`state_${text}`)?.dictName}</span>;
      },
    },
  ];
}

export const childrenFormSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'companyName',
      label: '普惠商户名称',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { placeholder: '请输入普惠商户名称' },
    },

    {
      field: 'state',
      label: '普惠商户状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('state'),
          placeholder: '请选择普惠商户状态',
        };
      },
    },
  ];
}

//新增弹框
export const modalFormItem = (isUpdate: boolean): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: '',
      label: '账号信息',
      component: 'Divider',
    },
    {
      field: 'accountAutoId',
      label: '用户名',
      required: false,
      component: 'Input',
      colProps: { span: 12 },
      ifShow: false,
    },
    {
      field: 'state',
      label: '用户名',
      required: false,
      component: 'Input',
      colProps: { span: 12 },
      ifShow: false,
    },
    {
      field: 'accountUserId',
      label: '用户名',
      required: false,
      component: 'Input',
      colProps: { span: 12 },
      ifShow: false,
    },
    {
      field: 'nickname',
      label: '用户名',
      required: true,
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入用户名',
        autocomplete: 'off',
      },
    },
    {
      field: 'account',
      label: '账号',
      component: 'Input',
      colProps: { span: 12 },
      rules: [{ required: true, validator: validatePhone, trigger: ['change', 'blur'] }],
      componentProps: {
        placeholder: '请输入(川工之家app注册/登录手机号)',
        autocomplete: 'off',
      },
    },
    {
      field: 'confirmRoleIds',
      label: '选择角色',
      required: true,
      component: 'ApiSelect',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          api: roleList,
          params: {
            pageSize: 0,
          },
          mode: 'multiple',
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.roleName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'roleName', value: 'autoId' },
        };
      },
    },
    {
      field: '',
      label: '交易配置信息',
      component: 'Divider',
    },
    {
      field: 'accountNickname',
      label: '开户户名',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入开户户名',
        autocomplete: 'off',
      },
    },
    {
      field: 'incomeAccountNumber',
      label: '收款银行卡号',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入收款银行卡号',
        autocomplete: 'off',
      },
    },
    {
      field: 'transPlatformBusinessId',
      label: '交易平台业务ID',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入交易平台业务ID',
        autocomplete: 'off',
      },
    },

    // {
    //   field: '',
    //   label: '集市信息',
    //   ifShow:({values,disabled}) =>{
    //     return disabled===false
    //   },
    //   component: 'Divider',
    // },
    //
    // {
    //   field: 'isBelongMarket',
    //   label: '是否加入集市',
    //   colProps: { span: 12 },
    //   component: 'RadioGroup',
    //   defaultValue: 'n',
    //   ifShow: false,
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get(`YesOrNo`) as RadioGroupChildOption[],
    //   },
    // },
    {
      field: '',
      label: '商户信息',
      component: 'Divider',
    },
    {
      field: 'companyName',
      label: '商户名称',
      required: true,
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入普惠商户名称',
        autocomplete: 'off',
      },
    },

    {
      field: 'contractName',
      label: '联系人姓名',
      required: true,
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入联系人姓名',
        autocomplete: 'off',
      },
    },
    {
      field: 'contractPhone',
      label: '联系人电话',
      required: true,
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入联系人电话',
        autocomplete: 'off',
      },
    },
    /*    {
      field: 'identityNum',
      label: '联系人证件号码',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
      slot:'identityNumber',
      componentProps: {
        placeholder: '请选择联系人证件号码',
        autocomplete: 'off',
      },
    },*/
    {
      field: 'companyType',
      label: '主体类型',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      ifShow: false,
      defaultValue:"childMerchant",
      rulesMessageJoinLabel: true,
      componentProps: {
        options: filter(
          cloneDeep(dictionary.getDictionaryOpt.get('companyType')),
          v => v.value === 'merchant'
        ),
      },
    },

    {
      field: 'areaCode',
      label: '所属区县',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          options: dictionary.getDictionaryOpt.get('regionCode'),
        };
      },
    },
    {
      field: 'typeId',
      label: '所属分类',
      required: true,
      component: 'ApiSelect',
      colProps: { span: 12 },
      itemProps: {
        autoLink: true,
      },
      componentProps: ({ formActionType }) => {
        return {
          placeholder: '请选择类型',
          api: list,
          resultField: 'data',
          params: {
            pageSize: 0,
          },
          alwaysLoad: true,
          immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.typeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'typeName', value: 'autoId' },
        };
      },
    },
    {
      field: 'companyIcon',
      label: '普惠商户图标',
      required: true,
      colProps: { span: 24 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
      componentProps: {
        operateType: 65,
      },
    },
    {
      field: 'introduce',
      label: '商户简介',
      required: true,
      component: 'InputTextArea',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'address',
      label: '经营地点',
      required: true,
      component: 'MapSelect',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      rest: true,
      componentProps({ formModel }) {
        return {
          onChangeLnglat: lnglat => (formModel['addressCoordinate'] = lnglat),
          lnglat: formModel['addressCoordinate'],
        };
      },
    },
    {
      field: 'addressCoordinate',
      label: '地址坐标',
      colProps: { span: 24 },
      component: 'ShowSpan',
      rulesMessageJoinLabel: true,
      show: false,
    },
    {
      field: '',
      label: '商户资质信息',
      component: 'Divider',
    },
    {
      field: 'identityType',
      label: '商户主体证件类型',
      colProps: { span: 12 },
      component: 'Select',
      defaultValue: 'idCard',
      show: false,
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('identityType'),
        };
      },
    },
    {
      field: 'identityNumber',
      label: '法人身份证号',
      component: 'Input',
      rulesMessageJoinLabel: true,
      componentProps: {
        maxlength: 20,
        showCount:true,
      },
    },
    {
      field: 'identityImgFront',
      label: '法人身份证(正)',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
      componentProps:function () {
        return {
          imgSize: 856 / 540,
        };
      },
    },
    {
      field: 'identityImgBack',
      label: '法人身份证(背)',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
      componentProps:function () {
        return {
          imgSize: 856 / 540,
        };
      },
    },
    {
      field: 'licenseImg',
      label: '营业执照照片',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
      componentProps:function () {
        return {
          imgSize: 856 / 540,
        };
      },
    },
    {
      field: 'openingImg',
      label: '开户许可证件',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
      componentProps:function () {
        return {
          imgSize: 856 / 540,
        };
      },
    },
    {
      field: 'publicityImg',
      label: '详情宣传图',
      component: 'Upload',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: {
        api: uploadApi,
        maxNumber: 3,
        uploadParams: {
          operateType: 65,
        },
      },
    },
    {
      field: 'qualificationImg',
      label: '资质证明',
      colProps: { span: 12 },
      component: 'Upload',
      rulesMessageJoinLabel: true,
      componentProps: {
        api: uploadApi,
        maxNumber: 3,
        uploadParams: {
          operateType: 65,
        },
      },
    },
  ];
};
