<template>
  <div>
    <BasicTable @register="registerTable" @expand="expand">
      <template #toolbar>
      <!-- auth="/management/add" -->
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增商户
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <MerchantsModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas, childrenColumns, childrenFormSchemas } from './data';
import MerchantsModal from './MerchantsModal.vue';
import { view, insertMerchant, updateMerchant, findChildList } from '@/api/merchants';
import { useMessage } from '@monorepo-yysz/hooks';
import { ref, unref } from 'vue'

const { createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: findChildList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
    actionColOptions: { span: 4 },
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

const tableRef = ref()

const record = ref({})

function expand(expanded, data) {
  record.value = data
}

// 新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

// 编辑
function handleEdit(record: Recordable<any>) {
  view({ ...record }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

// 详情
function handleView(record: Recordable<any>) {
  view({ companyId:record.companyId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

// 新增修改
function handleSuccess({ values, isUpdate }: Recordable<any>) {
  const api = isUpdate ? updateMerchant : insertMerchant;
  api(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败！${message}。`,
      });
    }
  });
}
</script>
