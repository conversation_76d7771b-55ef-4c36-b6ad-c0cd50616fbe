<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <a-tag
      color="red"
      class="mb-1"
      >* 请确认,添加用户的账号,必须为川工之家app用户的手机号</a-tag
    >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';
import { filter } from 'lodash-es';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.account || ''}--详情`
      : `编辑${unref(record)?.account || ''}`
    : '新增员工';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(unref(record)?.companyId,unref(record)?.accountType);
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    const confirmRoleIds = values?.confirmRoleIds||[];
    const dataConfirmRoleIds = unref(record)?.confirmRoleIds||[];
    const cancelRoleIds = filter(unref(dataConfirmRoleIds), v => !confirmRoleIds.includes(v));

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        cancelRoleIds
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
