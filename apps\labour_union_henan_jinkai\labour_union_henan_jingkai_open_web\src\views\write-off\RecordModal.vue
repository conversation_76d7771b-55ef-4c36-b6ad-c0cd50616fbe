<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { recordModalForm } from '@/views/write-off/data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(true);

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const formItem = computed(() => {
  return recordModalForm();
});

const title = computed(() => {
  return `${unref(record)?.userName} - 详情`;
});

const [registerForm, { resetFields, setProps, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  disabled.value = !!data.disabled;

  const {
    couponInfo: { couponType, amountLimit, discountAmount, discountPercent },
    ...other
  } = data.record;
  await setFieldsValue({ ...other, couponType, amountLimit, discountAmount, discountPercent });
  setProps({ disabled: unref(disabled) });
  setModalProps({ confirmLoading: false });
});
</script>
