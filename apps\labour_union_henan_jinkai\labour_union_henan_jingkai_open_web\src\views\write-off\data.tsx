import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import {findListByMerchantId,findActivityListByCoupon} from "@/api/write-off";
import {RadioGroupChildOption} from "ant-design-vue/es/radio/Group";

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '',
      dataIndex: '',
    },
    {
      title: '',
      dataIndex: '',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const recordColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '用户姓名',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '活动名称',
      dataIndex: 'activityName',
      width: 150,
    },
    {
      title: '票券名称',
      dataIndex: 'couponName',
      width: 150,
    },
    {
      title: '优惠类型',
      dataIndex: 'couponType',
      width: 150,
      customRender: ({ record }) => {
        return <span>{dictionary.getDictionaryMap.get(`ticketType_${record.couponInfo.couponType}`)?.dictName}</span>;
      },
    },
    {
      title: '金额/折扣',
      dataIndex: 'couponInfo.discountAmount',
      width: 150,
      customRender: ({ record }) => {
        if (record.couponInfo.couponType === 'discount' && record.couponInfo.discountPercent) {
          return <span>{record.couponInfo.discountPercent}折</span>
        }
        return <span>{record.couponInfo.discountAmount}元</span>
      }
    },
    {
      title: '领取时间',
      dataIndex: 'createTime',
      width: 120,
    },
    {
      title: '使用状态',
      dataIndex: 'state',
      width: 80,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`couponUseState_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '核销人',
      dataIndex: 'checkUserName',
      width: 120,
    },
    {
      title: '核销时间',
      dataIndex: 'checkTime',
      width: 120,
    },
  ];
};

export const recordFormSchemas = (): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'userName',
      label: '用户姓名',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
    {
      field: 'phone',
      label: '联系电话',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
    {
      field: 'activityId',
      label: '活动名称',
      component: 'ApiSelect',
      colProps: { span: 6 },
      componentProps: () => {
        return {
          placeholder: '请选择活动',
          api: findActivityListByCoupon,
          resultField: 'data',
          params: {
            orderBy: 'auto_id',
            sortType: 'desc',
          },
          immediate: true,
          onChange: () => {},
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.activityName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'activityName', value: 'activityId' },
        };
      },
    },
    {
      field: 'couponId',
      label: '票券名称',
      component: 'ApiSelect',
      colProps: { span: 6 },
      componentProps: () => {
        return {
          placeholder: '请选择票券',
          api: findListByMerchantId,
          resultField: 'data',
          params: {
            orderBy: 'auto_id',
            sortType: 'desc',
          },
          immediate: true,
          onChange: () => {},
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.couponName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'couponName', value: 'couponId' },
        };
      },
    },
    {
      field: 'couponType',
      component: 'Select',
      label: '优惠类型',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('ticketType'),
      },
      colProps: {
        span: 6,
      },
    },
    {
      field: 'receiveDateRange',
      label: '领取日期',
      component: 'RangePicker',
      colProps: { span: 6 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'destroyDateRange',
      label: '核销日期',
      component: 'RangePicker',
      colProps: { span: 6 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'checkUserName',
      label: '核销人',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
  ];
};

export const recordModalForm = (): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'userName',
      label: '用户姓名',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'companyName',
      label: '工会名称',
      colProps: { span: 24 },
      component: 'ShowSpan',
    },
    {
      field: 'couponName',
      label: '票券名称',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'activityName',
      label: '活动名称',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'couponType',
      component: 'ShowSpan',
      label: '优惠类型',
      render({ values }) {
        return dictionary.getDictionaryMap.get(`ticketType_${values.couponType}`)?.dictName || '';
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'amountLimit',
      component: 'ShowSpan',
      label: '门槛金额(元)',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.couponType === 'fullDecrement' || values.couponType === 'discount';
      },
    },
    {
      field: 'discountAmount',
      component: 'ShowSpan',
      label: ({values})=> values.couponType === 'noLimit' ? '票券金额' : '满减金额(元)',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.couponType === 'fullDecrement' || values.couponType === 'noLimit';
      },
    },
    {
      field: 'discountPercent',
      component: 'ShowSpan',
      label: '折扣比例',
      rulesMessageJoinLabel: true,

      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.couponType === 'discount';
      },
    },
    {
      field: 'discountAmount',
      component: 'ShowSpan',
      label: '最大折扣金额(元)',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.couponType === 'discount';
      },
    },
    // {
    //   field: 'shortRecordId',
    //   label: '券码',
    //   colProps: { span: 12 },
    //   component: 'ShowSpan',
    // },
    {
      field: 'createTime',
      label: '领取日期',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'useEndTime',
      label: '有效期',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'state',
      label: '使用状态',
      colProps: { span: 12 },
      component: 'ShowSpan',
      render({values}){
        return values.state === 'y' ? '已使用' : '未使用'
      },
    },
    {
      field: 'checkUserName',
      label: '核销人',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'checkCompanyName',
      label: '核销商户',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'checkTime',
      label: '核销时间',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
  ];
};

export const exportColumn = [
  {
    label: '票券名称',
    value: 'couponName',
  },
  {
    label: '用户姓名',
    value: 'userName',
  },
  {
    label: '联系电话',
    value: 'phone',
  },
  {
    label: '工会名称',
    value: 'companyName',
  },
  {
    label: '领取时间',
    value: 'createTime',
  },
  {
    label: '核销状态',
    value: 'state',
  },
  {
    label: '核销人',
    value: 'checkUserName',
  },
  {
    label: '核销人所属商户名称',
    value: 'checkCompanyName',
  },
  // {
  //   label: '核销人角色',
  //   value: 'roleName',
  // },
  {
    label: '核销时间',
    value: 'checkTime',
  },
];
