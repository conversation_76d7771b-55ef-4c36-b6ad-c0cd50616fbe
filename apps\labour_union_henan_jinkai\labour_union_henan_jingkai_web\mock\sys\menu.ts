import { resultSuccess } from 'mock/_util';
import { MockMethod } from 'vite-plugin-mock';
import { menuA } from './menu.a';
import { menuB } from './menu.b';

export default [
  {
    url: '/basic-api/datacenter/sysAuthAbout/getMenuListCurrent',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(menuA);
    },
  },
  {
    url: '/basic-api/datacenter/sysAuthAbout/getMenuTreeListCurrent',
    timeout: 1000,
    method: 'get',
    response: () => {
      return resultSuccess(menuB);
    },
  },
] as unknown as <PERSON>ckMethod[];
