{"name": "@labour_union_henan_jingkai/web", "version": "0.0.1", "homepage": "", "bugs": {"url": ""}, "repository": {"type": "git", "url": ""}, "license": "MIT", "author": {"name": "", "email": "", "url": ""}, "type": "module", "imports": {"#/*": "./src/*"}, "scripts": {"build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 pnpm vite build", "clean": "pnpm rimraf .turbo node_modules dist", "dev": "pnpm vite", "serve": "npm run dev", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@ant-design/colors": "^7.1.0", "@ant-design/icons-vue": "^7.0.1", "@iconify/iconify": "^3.1.1", "@iconify/vue": "^4.1.2", "@logicflow/core": "^1.2.28", "@logicflow/extension": "^1.2.28", "@monorepo-yysz/hooks": "workspace:*", "@monorepo-yysz/uno-config": "workspace:*", "@npkg/tinymce-plugins": "^0.0.7", "@pansy/china-division": "^2.1.0", "@vue/shared": "^3.4.36", "@vueuse/core": "^10.11.0", "ant-design-vue": "^4.2.3", "axios": "^1.7.3", "codemirror": "^5.65.17", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.12", "dompurify": "^3.1.7", "event-source-polyfill": "^1.0.31", "lodash-es": "^4.17.21", "lottie-web": "^5.12.2", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.2", "pinia": "2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "print-js": "^1.6.0", "qrcode": "^1.5.4", "qs": "^6.13.0", "showdown": "^2.1.0", "sortablejs": "catalog:", "tinymce": "^5.10.9", "uuid": "^9.0.1", "vditor": "^3.10.4", "video.js": "^8.21.0", "vue": "^3.5.4", "vue-color-kit": "^1.0.6", "vue-i18n": "^9.13.1", "vue-json-pretty": "^2.4.0", "vue-router": "^4.4.5", "vue3-seamless-scroll": "^2.0.1", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify/json": "^2.2.235", "@monorepo-yysz/enums": "workspace:*", "@monorepo-yysz/eslint-config": "workspace:*", "@monorepo-yysz/stylelint-config": "workspace:*", "@monorepo-yysz/ts-config": "workspace:*", "@monorepo-yysz/types": "workspace:*", "@monorepo-yysz/ui": "workspace:*", "@monorepo-yysz/utils": "workspace:*", "@monorepo-yysz/vite-config": "workspace:*", "@purge-icons/generated": "^0.10.0", "@types/codemirror": "^5.60.15", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.15", "@types/showdown": "^2.0.6", "@types/sortablejs": "catalog:", "@vue/compiler-sfc": "^3.4.36", "@vue/test-utils": "^2.4.6", "conventional-changelog-cli": "^4.1.0", "cross-env": "^7.0.3", "rimraf": "^5.0.10", "turbo": "^1.13.4", "typescript": "^5.5.4", "vite": "^5.4.3", "vite-plugin-mock": "^2.9.8", "vue-tsc": "^2.0.29"}}