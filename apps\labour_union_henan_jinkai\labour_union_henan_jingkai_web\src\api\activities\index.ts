import { h5Http } from '@/utils/http/axios';
import { ActivityItem, GetActivityListResultModel } from './model/index';
import { BasicResponse } from '@monorepo-yysz/types';

enum Activity {
  findList = '/findList',
  delete = '/delete',
  updateState = '/updateState',
  getDetails = '/getDetails',
  archivesAdd = '/saveOrUpdateArchives',
  archivesList = '/findArchivesList',
  todoList = '/archivesTodoList',
  saveOrUpdate = '/saveOrUpdate',
  baseInfo = '/statistics/base', //基础
  statisticsByDate = '/statistics/byDate',
  statisticsByAreaName = '/statistics/byAreaName',
  joinCountByPlatform = '/statistics/joinCountByPlatform',
  readCountByPlatform = '/statistics/readCountByPlatform',
  newUsersData = '/statistics/newUser', //投票
  prizeData = '/statistics/prize', //投票
  activityRedPacket = '/statistics/redPacket',
  registration = '/signUpRecord/list', //参与用户
  questionRecord = '/questionnaireRecord/list',
  questionReport = '/questionnaireRecord/dataSummary',
  questionnaireDataSummaryExport = '/questionnaireRecord/dataSummaryExport',
  audit = '/signUpRecord/audit', //审核报名信息
  details = '/getSignUpDetails', //参与用户详情
  questionRecordDetails = '/getQuestionDetails', //参与用户详情
  archivesSave = '/education-aid/archives/saveOrUpdate', //金秋归档
  archivesEducationList = '/education-aid/archives/findList',
  findPrizeRecordList = '/luckDrawRecordList',
  prizeAuditBatch = '/prize/auditBatch',
  prizeAuditAll = '/prize/auditAll',
  findCommentsList = '/findCommentsList',
  auditComments = '/auditComments',
  deleteComment = '/delComment',
  updateOpenState = '/updateOpenState',
  replyComments = '/replyComments',
  top = '/updateTopState', //置顶
  activityAudit = '/activityAudit', //置顶
  walkCycleList = '/walkingCycle/findList', //健步走期数
  walkCycle = '/walkingCycle/saveOrUpdate',
  walkCycleDelete = '/walkingCycle/delete',
  walkCycleUpdateState = '/walkingCycle/updateState',
  grantAwardsCode = '/grantAwards', //奖品发放
  pushToCity = '/pushToCity',
  cityAudit = '/cityAudit', //市级审核
  downQrCode = '/signInQrCode',
  queryBlueVestSignList = '/h5/queryBlueVestSignList',
  groupRecordList = '/inclusive/groupRecordList',
  inclusiveTicketRecordList = '/inclusive/ticketRecordList',
  findSimpleList = '/findSimpleList',
  integralTreeUserList = '/integralTree/findList',
  integralTreeOperateList = '/integralTree/operateList',
  getDetailByActivityMode = '/getDetailByActivityMode',
  exportActivityReport = '/exportActivityReport',
  getActivityReport = '/getActivityReport',
  voteList = '/vote/findList',
  voteAuditList = '/vote/findAuditList',
  opusesDetail = '/vote/opusInfo',
  opusAuditDetail = '/vote/auditDetail',
  addOpusInfo = '/vote/addOpusInfo',
  delOpusInfo = '/vote/delOpusInfo',
  voteAudit = '/vote/audit',
  exportVoteRank = '/vote/exportRankList',
  handlePushVideo = '/vote/pushShortVideo',
  findRedPacketActivityList = '/findRedPacketActivityList',
  importPrizeRecord = '/importRedPacketRecord', // 导入中奖记录
  downloadTemplate = '/downloadTemplate', // 下载模板
  saveBatchRedPackedRecord = '/saveBatchRedPackedRecord',//批量保存中奖记录
}

//删除作品
export const delOpusInfo = params => {
  return h5Http.delete<BasicResponse>(
      {
        url: getApi(Activity.delOpusInfo),
        params,
      },
      {
        isTransformResponse: false,
      }
  );
};

//导出排行榜
export const exportVoteRank = params =>{
  return h5Http.get<any>(
      { url: getApi(Activity.exportVoteRank), params, responseType: 'blob' },
      {
        isTransformResponse: false,
      }
  );
}

//审核作品
export const voteAudit = (params?: any) => {
  return h5Http.post<BasicResponse>(
      { url: getApi(Activity.voteAudit), params },
      {
        isTransformResponse: false,
      }
  );
};

//上传投票作品
export const addOpusInfo = (params?: any[]) => {
  return h5Http.post<BasicResponse>(
      { url: getApi(Activity.addOpusInfo), params },
      {
        isTransformResponse: false,
      }
  );
};

//投票作品列表
export const voteList = (params?: any) => {
  return h5Http.get<BasicResponse>(
      { url: getApi(Activity.voteList), params },
      {
        isTransformResponse: false,
      }
  );
};

//投票作品审核列表
export const voteAuditList = (params?: any) => {
  return h5Http.get<BasicResponse>(
      { url: getApi(Activity.voteAuditList), params },
      {
        isTransformResponse: false,
      }
  );
};

//作品详情
export const opusesDetail = (params?: any) => {
  return h5Http.get<BasicResponse>(
      { url: getApi(Activity.opusesDetail), params },
      {
        isTransformResponse: false,
      }
  );
};
//审核作品详情
export const opusAuditDetail = (params?: any) => {
  return h5Http.get<BasicResponse>(
      { url: getApi(Activity.opusAuditDetail), params },
      {
        isTransformResponse: false,
      }
  );
};

interface TotalItem {
  joinCountArr: number[];
  prizeList: [];
  todayJoinCount: number;
  todayUserCount: number;
  topicList: [];
  totalJoinCount: number;
  totalUserCount: number;
  userCountArr: number[];
  dateArr: string[];
  columnList: string[];
}

function getApi(url?: string) {
  if (!url) {
    return '/activityInfo';
  }
  return '/activityInfo' + url;
}

//列表
export const list = (params?: ActivityItem) => {
  return h5Http.get<GetActivityListResultModel>(
    { url: getApi(Activity.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//列表
export const findSimpleList = (params?: ActivityItem) => {
  return h5Http.get<GetActivityListResultModel>(
    { url: getApi(Activity.findSimpleList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteActivity = params => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(Activity.delete),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//置顶
export const top = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(Activity.top),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//发布下架
export const pushDown = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Activity.updateState), params },
    {
      isTransformResponse: false,
    }
  );
};

//详情
export const getDetails = (params, flg?: boolean) => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.getDetails), params },
    {
      isTransformResponse: flg,
    }
  );
};

//推送至市级审核
export const pushToCity = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(Activity.pushToCity),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//推送至市级审核
export const cityAudit = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(Activity.cityAudit),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//归档
export const archivesAdd = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Activity.archivesAdd), params },
    {
      isTransformResponse: false,
    }
  );
};

//归档list
export const archivesList = (params?: ActivityItem) => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.archivesList), params },
    {
      isTransformResponse: false,
    }
  );
};

//单个活动统计

export const baseInfo = params => {
  return h5Http.get<TotalItem>(
    { url: getApi(Activity.baseInfo), params },
    { errorMessageMode: 'modal' }
  );
};
export const statisticsByDate = params => {
  return h5Http.get<TotalItem>(
    { url: getApi(Activity.statisticsByDate), params },
    { errorMessageMode: 'modal' }
  );
};
//参与区域统计
export const statisticsByAreaName = params => {
  return h5Http.get<TotalItem>(
    { url: getApi(Activity.statisticsByAreaName), params },
    { errorMessageMode: 'modal' }
  );
};
//参与渠道数据统计
export const joinCountByPlatform = params => {
  return h5Http.get<TotalItem>(
    { url: getApi(Activity.joinCountByPlatform), params },
    { errorMessageMode: 'modal' }
  );
};
//访问渠道数据统计
export const readCountByPlatform = params => {
  return h5Http.get<TotalItem>(
    { url: getApi(Activity.readCountByPlatform), params },
    { errorMessageMode: 'modal' }
  );
};
//新增用户数据
export const newUsersData = params => {
  return h5Http.get<TotalItem>(
    { url: getApi(Activity.newUsersData), params },
    { errorMessageMode: 'modal' }
  );
};
//奖品发放情况
export const prizeData = params => {
  return h5Http.get<TotalItem>(
    { url: getApi(Activity.prizeData), params },
    { errorMessageMode: 'modal' }
  );
};

//新增用户数据
export const activityRedPacket = params => {
  return h5Http.get<TotalItem>(
    { url: getApi(Activity.activityRedPacket), params },
    { errorMessageMode: 'modal' }
  );
};

//报名参与用户
export const registration = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.registration), params },
    { isTransformResponse: false }
  );
};

//问卷参与用户
export const questionRecord = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.questionRecord), params },
    { isTransformResponse: false }
  );
};
export const questionnaireDataSummary = params => {
  return h5Http.get<BasicResponse>({ url: getApi(Activity.questionReport), params });
};

//导出参与用户信息
export const questionnaireDataSummaryExport = params => {
  return h5Http.get<any>(
    { url: getApi(Activity.questionnaireDataSummaryExport), params, responseType: 'blob' },
    {
      isTransformResponse: false,
    }
  );
};

//审核参与用户
export const audit = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Activity.audit), params },
    { isTransformResponse: false }
  );
};

//参与用户详情
export const details = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.details), params },
    { isTransformResponse: false }
  );
};
//参与用户详情
export const questionRecordDetails = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.questionRecordDetails), params },
    { isTransformResponse: false }
  );
};

//已完成活动
export const todoList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.todoList), params },
    { isTransformResponse: false }
  );
};

//金秋新增
export const archivesSave = params => {
  return h5Http.post<BasicResponse>(
    { url: Activity.archivesSave, params },
    { isTransformResponse: false }
  );
};

//金秋list
export const archivesEducationList = params => {
  return h5Http.get<BasicResponse>(
    { url: Activity.archivesEducationList, params },
    { isTransformResponse: false }
  );
};

//奖品list
export const findPrizeRecordList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.findPrizeRecordList), params },
    { isTransformResponse: false }
  );
};

//奖品审核
export const prizeAuditBatch = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Activity.prizeAuditBatch), params },
    { isTransformResponse: false }
  );
};

//奖品审核
export const prizeAuditAll = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Activity.prizeAuditAll), params },
    { isTransformResponse: false }
  );
};

//评论
export const findCommentsList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.findCommentsList), params },
    { isTransformResponse: false }
  );
};

//评论审核
export const auditComments = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Activity.auditComments), params },
    { isTransformResponse: false }
  );
};

//评论审核
export const deleteComment = params => {
  return h5Http.delete<BasicResponse>(
    { url: getApi(Activity.deleteComment), params },
    { isTransformResponse: false }
  );
};

//评价公开
export const updateOpenState = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Activity.updateOpenState), params },
    { isTransformResponse: false }
  );
};

//评价回复
export const replyComments = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Activity.replyComments), params },
    { isTransformResponse: false }
  );
};

//健步走
export const walkCycleList = params => {
  return h5Http.get<BasicResponse>(
    { url: Activity.walkCycleList, params },
    { isTransformResponse: false }
  );
};

//健步走
export const walkCycle = params => {
  return h5Http.post<BasicResponse>(
    { url: Activity.walkCycle, params },
    { isTransformResponse: false }
  );
};

// 删除
//健步走
export const walkCycleDelete = params => {
  return h5Http.delete<BasicResponse>(
    { url: Activity.walkCycleDelete + '?autoId=' + params },
    { isTransformResponse: false }
  );
};

//健步走
export const walkCycleUpdateState = params => {
  return h5Http.post<BasicResponse>(
    { url: Activity.walkCycleUpdateState, params },
    { isTransformResponse: false }
  );
};

//健步走
export const grantAwardsCode = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Activity.grantAwardsCode), params },
    { isTransformResponse: false }
  );
};

export const downQrCode = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.downQrCode), params },
    { isTransformResponse: false }
  );
};
///queryBlueVestSignList
export const blueVestSignList = params => {
  return h5Http.get<Recordable[]>({ url: getApi(Activity.queryBlueVestSignList), params });
};

///定向普惠- 普惠记录
export const groupRecordList = params => {
  return h5Http.get<Recordable[]>({ url: getApi(Activity.groupRecordList), params });
};

///定向普惠- 普惠记录 - 领取记录
export const inclusiveTicketRecordList = params => {
  return h5Http.get<Recordable[]>({ url: getApi(Activity.inclusiveTicketRecordList), params });
};

//活动审核
export const activityAudit = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(Activity.activityAudit),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

///积分种树用户信息
export const integralTreeUserList = params => {
  return h5Http.get<BasicResponse>(
    { url: Activity.integralTreeUserList, params },
    {
      isTransformResponse: false,
    }
  );
};

///积分种树用户操作记录
export const integralTreeOperateList = params => {
  return h5Http.get<BasicResponse>(
    { url: Activity.integralTreeOperateList, params },
    {
      isTransformResponse: false,
    }
  );
};

//详情
export const getDetailByActivityMode = (params, flg?: boolean) => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.getDetailByActivityMode), params },
    {
      isTransformResponse: flg,
    }
  );
};

// 活动报表导出
export const exportActivityReport = params => {
  return h5Http.post<any>(
    {
      url: getApi(Activity.exportActivityReport),
      params,
      responseType: 'blob',
    },
    { isTransformResponse: false }
  );
};
// 活动报表
export const getActivityReport = params => {
  return h5Http.get<Recordable>({ url: getApi(Activity.getActivityReport), params });
};


//推到视频专区
export const handlePushVideo = (params?: any[]) => {
  return h5Http.post<BasicResponse>(
      { url: getApi(Activity.handlePushVideo), params },
      {
        isTransformResponse: false,
      }
  );
};

export const findRedPacketActivityList = (params)=>{
  return h5Http.get<BasicResponse>(
      { url: getApi(Activity.findRedPacketActivityList), params },
      {
        isTransformResponse: false,
      }
  );
}

//导入中奖记录
export const importPrizeRecord = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Activity.importPrizeRecord), params },
    { isTransformResponse: false }
  );
};

//下载模板
export const downloadTemplate = params => {
  return h5Http.post<any>(
    { url: getApi(Activity.downloadTemplate), params, responseType: 'blob' },
    { isTransformResponse: false }
  );
};

//活动审核
export const saveBatchRedPackedRecord = params => {
  return h5Http.post<BasicResponse>(
      {
        url: getApi(Activity.saveBatchRedPackedRecord),
        params,
      },
      {
        isTransformResponse: false,
      }
  );
};
