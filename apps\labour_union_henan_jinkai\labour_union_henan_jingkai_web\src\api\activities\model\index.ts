import { BasicFetchResult } from '@monorepo-yysz/types';

export interface ActivityItem {
  autoId?: number;
  activityId?: string;
  publishPort?: string;
  activityName?: string;
  activityCategory?: string;
  customerType?: string;
  areaCode?: string;
  unit?: string;
  activityMode?: string;
  investigation?: string;
  luckDraw?: string;
  externalLink?: string;
  externalLinkUrl?: string;
  activityContent?: string;
  activityRules?: string;
  participationMode?: string;
  activityReward?: string;
  activityStartTime?: string;
  activityEndTime?: string;
  state?: string;
  appCover?: string;
  appDetailsCover?: string;
  archivesState?: string;
  createUser?: string;
  createTime?: string;
  updateUser?: string;
  updateTime?: string;
}
export type GetActivityListResultModel = BasicFetchResult<ActivityItem>;
