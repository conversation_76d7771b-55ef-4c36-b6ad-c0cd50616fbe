import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

enum Activity {
  join = '/statistics/join',
  joinList = '/joinRecordList',
  joinExport = '/export/joinUser',
  countByUnion = '/joinUnionList',
  prizeExport = '/luckDrawRecordList/export',
  signRecordExport = '/signUpRecord/export',
  questionRecordExport = '/questionnaireRecord/export',
  answerRecordList = '/answerRecordList',
  answerRecordRank = '/answer/recordRank',
  voteRank = '/vote/findRankList',
  voteRecordList = '/vote/recordList',
  actBlackList = '/actBlackList',
  saveBlackList = '/saveBlackList',
  delBlackList = '/delBlackList',
  clearBlackList = '/clearBlackList',
}

function getApi(url?: string) {
  if (!url) {
    return '/activityInfo';
  }
  return '/activityInfo' + url;
}

//参与量，访问量统计
export const join = params => {
  return h5Http.get<Recordable>({ url: getApi(Activity.join), params });
};

//参与用户信息列表
export const joinList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.joinList), params },
    {
      isTransformResponse: false,
    }
  );
};

//导出参与用户信息
export const joinExport = params => {
  return h5Http.post<any>(
    { url: getApi(Activity.joinExport), params, responseType: 'blob' },
    {
      isTransformResponse: false,
    }
  );
};

//参与工会统计
export const countByUnion = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.countByUnion), params },
    {
      isTransformResponse: false,
    }
  );
};

//中奖记录导出
export const prizeExport = params => {
  return h5Http.post<any>(
    { url: getApi(Activity.prizeExport), params, responseType: 'blob' },
    {
      isTransformResponse: false,
    }
  );
};

//导出报名活动参与用户
export const signRecordExport = params => {
  return h5Http.get<any>(
    { url: getApi(Activity.signRecordExport), params, responseType: 'blob' },
    {
      isTransformResponse: false,
    }
  );
};

//导出问卷活动参与用户
export const questionRecordExport = params => {
  return h5Http.get<any>(
    { url: getApi(Activity.questionRecordExport), params, responseType: 'blob' },
    {
      isTransformResponse: false,
    }
  );
};

//答题记录
export const answerRecordList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.answerRecordList), params },
    {
      isTransformResponse: false,
    }
  );
};

//答题记录排行
export const answerRecordRank = params => {
  return h5Http.get<BasicResponse>(
      { url: getApi(Activity.answerRecordRank), params },
      {
        isTransformResponse: false,
      }
  );
};

//投票排行榜
export const voteRank = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.voteRank), params },
    {
      isTransformResponse: false,
    }
  );
};

//投票记录
export const voteRecordList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Activity.voteRecordList), params },
    {
      isTransformResponse: false,
    }
  );
};


//活动黑名单列表
export const actBlackList = params => {
  return h5Http.get<BasicResponse>(
      { url: getApi(Activity.actBlackList), params },
      {
        isTransformResponse: false,
      }
  );
};

//批量新增黑名单
export const saveBatchBlackList = params => {
  return h5Http.post<BasicResponse>(
      { url: getApi(Activity.saveBlackList), params },
      {
        isTransformResponse: false,
      }
  );
};

//删除黑名单
export const delBlackList = params => {
  return h5Http.delete<BasicResponse>(
      { url: getApi(Activity.delBlackList), params },
      {
        isTransformResponse: false,
      }
  );
};

//清空黑名单
export const clearBlackList = params => {
  return h5Http.delete<BasicResponse>(
      { url: getApi(Activity.clearBlackList), params },
      {
        isTransformResponse: false,
      }
  );
};
