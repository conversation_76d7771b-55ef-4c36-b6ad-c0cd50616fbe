import { h5Http, dataCenterHttp } from '/@/utils/http/axios';

// 数据
export const visitLatelySummary = (params?: Recordable) => {
  return dataCenterHttp.get<Recordable>({
    url: '/dataCenterBusiness/getLast7daySummary',
    params,
  });
};

// 活动
export const activityInfo = params => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/activityInfo',
    params,
  });
};

// 票券
export const couponInfo = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/coupon',
    params,
  });
};

// 新闻
export const newsStatistics = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/newsStatistics',
    params,
  });
};

export const viewSubordinatesSummary = () => {
  return dataCenterHttp.get<Recordable>({
    url: '/dataCenterBusiness/viewSubordinatesSummary',
  });
};
