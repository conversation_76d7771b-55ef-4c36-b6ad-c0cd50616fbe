import { BasicResponse } from '@monorepo-yysz/types';
import { openHttp } from '@/utils/http/axios';

enum API {
  findList = '/findVoList',
  noBindingProductInfo = '/noBindingProductInfo',
  saveApi = '/addBirthdayProductInfo',
  removeBirthdayProduct = '/removeBirthdayProductInfo',
  enableOrDisable = '/birthdayProduct/enableOrDisable'
}

function getApi(url?: string) {
  if (!url) {
    return '/birthdayProductInfo';
  }
  return '/birthdayProductInfo' + url;
}

// 列表
export const list = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(API.findList), params },
    {
      isTransformResponse: false,
    }
  );
};
export const noBindingProductInfo = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(API.noBindingProductInfo), params },
    {
      isTransformResponse: false,
    }
  );
};

// 新增
export const saveApi = (params: Recordable) => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(API.saveApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const deleteLine = params => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(API.removeBirthdayProduct),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 上架下架
export const upOrDown = params => {
  return openHttp.post<BasicResponse>(
    {
      url: API.removeBirthdayProduct,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};


export const enableOrDisable = (params: Recordable) => {
  return openHttp.post<BasicResponse>(
    {
      url: API.enableOrDisable,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
