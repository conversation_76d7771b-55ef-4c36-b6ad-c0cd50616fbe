import { BasicResponse } from '@monorepo-yysz/types';
import { dataCenterHttp, defHttp, openHttp } from '/@/utils/http/axios';

enum ConvenienceApplication {
  insert = '/initOpenCustomer', //工会直接新增商家
  update = '/updateCompanyMoreInfoByCompanyId', //修改商家
  updateTrans = '/customUpdateConfigByCompanyId', //修改交易配置
  findList = '/findVoList', //查询商家列表
  getDetails = '/getByCompanyId', //工会查询商家详情
  getOtherInfo = '/getOpenCompanyMoreInfo', //工会查询商家营业资质
  transaction = '/getCompanyTransConfig', //工会查询商家交易配置信息
  logicallyDelete = '/deleteLogically', //删除商家
}

function getApi(url?: string) {
  if (!url) {
    return '/openCompanyInfo';
  }
  return '/openCompanyInfo' + url;
}

//工会新增商户
export const insertMerchant = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(ConvenienceApplication.insert), params },
    {
      isTransformResponse: false,
    }
  );
};

//修改商户主体信息(资质信息)
export const updateMerchant = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(ConvenienceApplication.update), params },
    {
      isTransformResponse: false,
    }
  );
};

//查询商户列表
export const list = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//查询行业类型接口
export const findIndustryType = params => {
  return openHttp.get<BasicResponse>(
    { url: '/industryInfo/simpleFindVoList', params },
    {
      isTransformResponse: false,
    }
  );
};

//商户逻辑删除
export const merLogicallyDelete = params => {
  return defHttp.post<BasicResponse>(
    { url: getApi(ConvenienceApplication.logicallyDelete), params },
    {
      isTransformResponse: false,
    }
  );
};

//商户详情
export const unionGetDetails = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.getDetails), params },
    {
      isTransformResponse: false,
    }
  );
};

//查询商户营业资质
export const getOtherInfo = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.getOtherInfo), params },
    {
      isTransformResponse: false,
    }
  );
};

//查询商户交易配置信息
export const getTransactionInfo = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.transaction), params },
    {
      isTransformResponse: false,
    }
  );
};

//修改交易配置信息
export const updateTransActionsInfo = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(ConvenienceApplication.updateTrans), params },
    {
      isTransformResponse: false,
    }
  );
};

//查询账号信息
export const getAccountInfo = params => {
  return dataCenterHttp.get<BasicResponse>(
    { url: '/accountInfo/getAccountVoInfoByDTO', params },
    {
      isTransformResponse: false,
    }
  );
};

//修改账号信息
export const updateAccountInfo = params => {
  return dataCenterHttp.post<BasicResponse>(
    { url: '/accountInfo', params },
    {
      isTransformResponse: false,
    }
  );
};

//查询招聘企业列表
export const findVoList = params => {
  return openHttp.get<BasicResponse>(
    { url: '/openCompanyInfo/findVoList', params },
    {
      isTransformResponse: false,
    }
  );
};
//查询招聘企业详情
export const getById = params => {
  return openHttp.get<BasicResponse>(
    { url: '/openCompanyInfo', params },
    {
      isTransformResponse: false,
    }
  );
};
//新增招聘企业
export const initOpenCustomer = params => {
  return openHttp.post<BasicResponse>(
    { url: '/openCompanyInfo/initOpenCustomer', params },
    {
      isTransformResponse: false,
    }
  );
};

//修改经营主体的主要信息
export const updateCompanyMainInfo = params => {
  return openHttp.post<BasicResponse>(
    { url: '/openCompanyInfo/updateCompanyMainInfo', params },
    {
      isTransformResponse: false,
    }
  );
};
