import { BasicResponse } from '@monorepo-yysz/types';
import { defHttp, openHttp } from '/@/utils/http/axios';

enum ConvenienceApplication {
  findList = '/findManageVoList', //查询申请记录列表
  app = '/audit', //审核
  getDetails = '/getById', //详情

  delete = '/batchDeleteIndexPicByIds',
  saveOrUpdate = '/addMerchantInfo',
  upDate = '/updateIndexPic',
}

function getApi(url?: string) {
  if (!url) {
    return '/openCompanyApplyRecord';
  }
  return '/openCompanyApplyRecord' + url;
}

//查询入驻申请列表
export const list = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//审核申请
export const app = params => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(ConvenienceApplication.app),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return defHttp.post<BasicResponse>(
    {
      url: getApi(ConvenienceApplication.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const update = params => {
  return defHttp.post<BasicResponse>(
    {
      url: getApi(ConvenienceApplication.upDate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteConvenienceApplication = (ids: number[]) => {
  return defHttp.delete<BasicResponse>(
    {
      url: getApi(ConvenienceApplication.delete),
      params: {
        ids,
      },
    },
    {
      isTransformResponse: false,
    }
  );
};

//详情
export const getDetails = params => {
  return defHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.getDetails), params },
    {
      isTransformResponse: false,
    }
  );
};
