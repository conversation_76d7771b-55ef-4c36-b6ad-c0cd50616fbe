import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

enum OBJ {
  findList = '/findVoList',
  queryListByAccountType = '/queryListByAccountType',
  view = '/getByEntity',
  saveOrUpdate = '/saveOrUpdateByDTO',
}

function getApi(url?: string) {
  if (!url) {
    return '/manageNewsReleaseUnion';
  }
  return '/manageNewsReleaseUnion' + url;
}

//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(OBJ.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//view
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = (autoId: number[] | number) => {
  return h5Http.delete<BasicResponse>(
    {
      url: '/h5CategoryInfo/removeNewsReleaseUnionById' + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};

//根据账号类型返回数据
export const queryListByAccountType = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(OBJ.queryListByAccountType), params },
    {
      isTransformResponse: false,
    }
  );
};
