import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

//批量审核
export const batchAudit = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/H5UserComment/batchAudit',
      params,
    },
    { isTransformResponse: false }
  );
};

//列表
export const userCommentFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/H5UserComment/manageGetUserCommentList',
      params,
    },
    { isTransformResponse: false }
  );
};

//删除
export const deleteuserComment = params => {
  return h5Http.delete<BasicResponse>(
    {
      url: '/H5UserComment',
      params,
    },
    { isTransformResponse: false }
  );
};

//审核单个
export const auditOne = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/H5UserComment/batchAudit',
      params,
    },
    { isTransformResponse: false }
  );
};
