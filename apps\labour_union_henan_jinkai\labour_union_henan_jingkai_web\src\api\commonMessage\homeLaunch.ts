import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum API {
  findVoList = '/findVoList',
  delete = '',
  getVoByDto = '/getVoByDto',
  viewList = '/homeLaunchViewRecord/findList',
  saveOrUpdateByDTO = '/saveOrUpdateByDTO',
  saveOrUpdate = '',
  getMaxSortNumber = "/getMaxSortNumber"
}

function getApi(url?: string) {
  if (!url) {
    return '/homeLaunchConfig';
  }
  return '/homeLaunchConfig' + url;
}

//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(API.findVoList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdateByDTO = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(API.saveOrUpdateByDTO),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(API.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(API.delete) + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};

//详情
export const view = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(API.getVoByDto), params },
    {
      isTransformResponse: false,
    }
  );
};

//观看记录
export const recordList = params => {
  return h5Http.get<BasicResponse>(
    { url: API.viewList, params },
    {
      isTransformResponse: false,
    }
  );
};

//最大数
export const maxNumber = () => {
  return h5Http.get<BasicResponse>(
    { url: getApi(API.getMaxSortNumber) },
    {
      isTransformResponse: false,
    }
  );
};
