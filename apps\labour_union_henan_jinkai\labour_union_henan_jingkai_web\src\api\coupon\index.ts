import { h5Http } from '/@/utils/http/axios'
import {BasicResponse} from "@monorepo-yysz/types";

enum ticket {
  base = 'couponInfo',
  findList = '/findList', //查询所有票卷信息
  remove = '/remove',
  detail = '/get',
  recordList = '/activityInfo/coupon/recordList',
  onSelectList = '/onSelectList',
  simpleList = '/simpleList',
  exportRecord = '/activityInfo/coupon/exportRecord',
  updateState = '/updateState'
}

function getApi(url?: string) {
  if (!url) {
    return '/couponInfo'
  }
  return '/couponInfo' + url
}

//新增、更新
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
      { url: getApi(), params },
      {
        isTransformResponse: false,
      },
  )
}

//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(ticket.findList), params },
    {
      isTransformResponse: false,
    },
  )
}

//列表
export const onSelectList = params => {
    return h5Http.get<BasicResponse>(
        { url: getApi(ticket.onSelectList), params },
        {
            isTransformResponse: false,
        },
    )
}

//列表
export const simpleList = params => {
    return h5Http.get<BasicResponse>(
        { url: getApi(ticket.simpleList), params },
        {
            isTransformResponse: false,
        },
    )
}



//票券详情
export const getTicketDetail = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(ticket.detail), params },
    {
      isTransformResponse: false,
    },
  )
}

//删除
export const deleteLine = params => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(ticket.remove),
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

export const recordList = params => {
    return h5Http.get<BasicResponse>(
        {
            url: ticket.recordList,
            params,

        },
        {
            isTransformResponse: false,
        },
    )
}

export const exportRecord = params =>{
    return h5Http.post<any>(
        { url: ticket.exportRecord, params, responseType: 'blob' },
        {
            isTransformResponse: false,
        }
    );
}

//发布
export const updateState = params => {
    return h5Http.post<BasicResponse>(
        { url: getApi(ticket.updateState), params },
        {
            isTransformResponse: false,
        },
    )
}

