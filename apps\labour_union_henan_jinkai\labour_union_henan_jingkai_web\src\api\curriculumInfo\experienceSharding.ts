import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

//小组列表
export const experienceSharingFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/experienceSharing/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//详情
export const getVoByDto = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/experienceSharing/getVoByDto',
      params,
    },
    { isTransformResponse: false }
  );
};
//新增
export const saveOrUpdateByDTO = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/groupsStudy/saveOrUpdateByDTO',
      params,
    },
    { isTransformResponse: false }
  );
};
//审核心得分享
export const auditExperience = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/experienceSharing/auditExperience',
      params,
    },
    { isTransformResponse: false }
  );
};

//删除心得分享
export const deleteInsertRecord = params => {
    return h5Http.delete<BasicResponse>(
        {
        url: '/experienceSharing?autoId='+params,
        params,
        },
        { isTransformResponse: false }
    );
};
//删除心得分享记录
export const deleteSharingReply = params => {
  return h5Http.delete<BasicResponse>(
      {
      url: '/experienceSharingReply?autoId='+params,
      params,
      },
      { isTransformResponse: false }
  );
};
//心得分享公开/撤回
export const setEnableDisable = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/experienceSharing/setEnableDisable',
      params,
    },
    { isTransformResponse: false }
  );
};

//心得分享记录公开/撤回
export const setEnableDisableReply = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/experienceSharingReply/setEnableDisable',
      params,
    },
    { isTransformResponse: false }
  );
};

//小组列表
export const experienceSharingReplyFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/experienceSharingReply/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};

