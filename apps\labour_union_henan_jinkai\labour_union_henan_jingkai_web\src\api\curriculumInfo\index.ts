import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

//课堂信息列表
export const curriculumInfoFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/curriculumInfo/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//课堂信息详情
export const getVoByDto = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/curriculumInfo/getVoByDto',
      params,
    },
    { isTransformResponse: false }
  );
};
//新增
export const saveOrUpdateByDTO = params => {
    return h5Http.post<BasicResponse>(
      {
        url: '/curriculumInfo/saveOrUpdateByDTO',
        params,
      },
      { isTransformResponse: false }
    );
};
//删除
export const deleteList = params => {
    return h5Http.delete<BasicResponse>(
        {
        url: '/curriculumInfo?autoId='+params,
        params,
        },
        { isTransformResponse: false }
    );
};
//撤销发布
export const publishCurr = params => {
    return h5Http.post<BasicResponse>(
      {
        url: '/curriculumInfo/publishCurr',
        params,
      },
      { isTransformResponse: false }
    );
};
//目录课表
export const curriculumCatalogue = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/curriculumCatalogue/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//删除
export const deleteCatalogue = params => {
  return h5Http.delete<BasicResponse>(
      {
      url: '/curriculumCatalogue?catalogueId='+params,
      params,
      },
      { isTransformResponse: false }
  );
};
//课程目录保存
export const curriculumCatalogueSave = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/curriculumCatalogue/saveOrUpdateByDTO',
      params,
    },
    { isTransformResponse: false }
  );
};
//开播关播
export const openStatus = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/curriculumCatalogue/isOpenStatus',
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}


//启用禁用
export const publishStatus = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/curriculumCatalogue/publishStatus',
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}


