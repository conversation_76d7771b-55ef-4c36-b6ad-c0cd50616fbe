import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum recordUrl {
  findList = '/findVoList',
  delete = '/record/delete',
  saveOrUpdateByDTO = '/saveOrUpdateByDTOManage',
  audit = '/applyAudit',
  details = '/record/getDetails',
  applyAudit = '/applyAudit',
  ToErase = '/ToErase',
  logout = '/logout',
  deleteById = '/deleteById',
  hardWorkerList = '/findList',
  isHardWorkerList = '/isHardWorkerList',
  getCompanyLevel = '/getCompanyLevel',
  findUnionUserListHasCompanyId = '/findUnionUserListHasCompanyId',
  hardRelationList = '/findVoList',
  hardRelationDetail = '/getByEntity',
  getVoByDto = '/getVoByDto',
  getHardWorkerTemplate = '/getHardWorkerTemplate',
  exportHardWorkerInfo = '/exportHardWorkerInfo',
  findAllUserByLawyer = '/findAllUserByLawyer',
}

function getApi(url?: string) {
  if (!url) {
    return '/hardWorker';
  }
  return '/hardWorker' + url;
}

function getRelationApi(url?: string) {
  if (!url) {
    return '/hardWorkerRelationInfo';
  }
  return '/hardWorkerRelationInfo' + url;
}

/*困难职工家属信息列表*/
export const getHardRelationList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getRelationApi(recordUrl.hardRelationList),
      params,
    },
    { isTransformResponse: false }
  );
};

/*困难职工认证模板*/
export const getHardWorkerTemplate = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(recordUrl.getHardWorkerTemplate),
      params,
    },
    { isTransformResponse: false }
  );
};

// 导出困难职工
export const exportHardWorkerInfo = params => {
  return h5Http.get<any>(
    { url: getApi(recordUrl.exportHardWorkerInfo), params, responseType: 'blob' },
    { isReturnNativeResponse: true }
  );
};

/*困难职工家属信息详情*/
export const getHardRelationDetail = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getRelationApi(recordUrl.hardRelationDetail),
      params,
    },
    { isTransformResponse: false }
  );
};

/*获取有工会的用户id*/
export const getUserListHasCompanyId = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(recordUrl.findUnionUserListHasCompanyId),
      params,
    },
    { isTransformResponse: false }
  );
};

/*获取有工会的用户(律师)*/
export const findAllUserByLawyer = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(recordUrl.findAllUserByLawyer),
      params,
    },
    { isTransformResponse: false }
  );
};

/*困难职工审核列表*/
export const getHardWorkerRecordList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(recordUrl.findList),
      params,
    },
    { isTransformResponse: false }
  );
};

/*困难职工详情*/
export const getHardWorkerRecordDetail = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(recordUrl.getVoByDto),
      params,
    },
    { isTransformResponse: false }
  );
};

/*困难职工管理列表*/
export const getHardWorkerList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(recordUrl.hardWorkerList),
      params,
    },
    { isTransformResponse: false }
  );
};

/*困难职工管理列表*/
export const getIsHardWorkerList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(recordUrl.isHardWorkerList),
      params,
    },
    { isTransformResponse: false }
  );
};

/*管理端新增困难职工*/
export const saveOrUpdateByDTO = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(recordUrl.saveOrUpdateByDTO),
      params,
    },
    { isTransformResponse: false }
  );
};

/*批量审核*/
export const applyAudit = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(recordUrl.applyAudit),
      params,
    },
    { isTransformResponse: false }
  );
};

/*脱困操作*/
export const toErase = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(recordUrl.ToErase),
      params,
    },
    { isTransformResponse: false }
  );
};

/*注销操作*/
export const logout = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(recordUrl.logout),
      params,
    },
    { isTransformResponse: false }
  );
};

/*删除操作*/
export const deleteById = params => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(recordUrl.deleteById),
      params,
    },
    { isTransformResponse: false }
  );
};

/*获取当前工会级别*/
export const getCompanyLevel = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(recordUrl.getCompanyLevel),
      params,
    },
    { isTransformResponse: false }
  );
};
