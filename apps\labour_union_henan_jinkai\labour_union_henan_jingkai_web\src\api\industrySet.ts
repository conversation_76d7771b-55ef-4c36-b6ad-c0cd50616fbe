import { openHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';
import { TreeDataItem } from 'ant-design-vue/lib/tree';

enum Employment {
  findList = '/simpleFindVoList',
}

function getApi(url?: string) {
  if (!url) {
    return '/industryInfo';
  }
  return '/industryInfo' + url;
}

//列表
export const list = params => {
  return openHttp.get<BasicResponse>(
    { url: getApi(Employment.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

export const listTree = params => {
  return openHttp.get<TreeDataItem[]>({ url: getApi(Employment.findList), params });
};

//获取行业信息详情
export const view = params => {
  return openHttp.get(
    { url: getApi(), params },
    {
      isTransformResponse: false,
    }
  );
};

export const listTransform = params => {
  return openHttp.get<BasicResponse>({ url: getApi(Employment.findList), params });
};

//新增
export const saveOrUpdate = params => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteIndustry = id => {
  return openHttp.delete<BasicResponse>(
    {
      url: getApi() + `?autoId=${id}`,
    },
    {
      isTransformResponse: false,
    }
  );
};

//查询行业类型接口
export const findIndustryType = params => {
  return openHttp.get<BasicResponse>(
    { url: '/industryInfo/simpleFindVoList', params },
    {
      isTransformResponse: false,
    }
  );
};
