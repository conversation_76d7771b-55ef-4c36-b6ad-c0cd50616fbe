import { cloudManageHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum IntegralApplication {
  // 分页查询
  findList = '/integralRuleManageList',
  // 新增规则
  saveOrUpdate = '/saveIntegralRuleManage',
  // 修改规则
  upDate = '/updateIntegralRuleManage',
  // 查询详情
  queryDetailByRuleId = '/queryDetailByRuleId',
  // 删除:目前没接口用不上
  /* delete = '/deleteConvenienceApplication',
  getDetails = '/queryConvenienceApplicationById',*/
}

function getApi(url?: string) {
  if (!url) {
    return '/integralRuleManage';
  }
  return '/integralRuleManage' + url;
}

//列表
export const list = params => {
  return cloudManageHttp.get<BasicResponse>(
    { url: getApi(IntegralApplication.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return cloudManageHttp.post<BasicResponse>(
    {
      url: getApi(IntegralApplication.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const update = params => {
  return cloudManageHttp.post<BasicResponse>(
    {
      url: getApi(IntegralApplication.upDate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
/*export const deleteIntegralApplication = (ids: number[]) => {
  return cloudManageHttp.post<BasicResponse>(
    {
      url: getApi(IntegralApplication.delete),
      params: {
        ids,
      },
    },
    {
      isTransformResponse: false,
    },
  );
};*/

//详情
export const getDetails = params => {
  return cloudManageHttp.get<BasicResponse>(
    { url: getApi(IntegralApplication.queryDetailByRuleId), params },
    {
      isTransformResponse: false,
    }
  );
};
