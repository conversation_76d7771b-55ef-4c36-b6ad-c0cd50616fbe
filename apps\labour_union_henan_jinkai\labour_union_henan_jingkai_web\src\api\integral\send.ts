// 批量上传数据，返回上传结果：/h5/systemDistributionIntegral/uploadPhones post
// 定向发放积分：/h5/systemDistributionIntegral/distributionIntegral post
// 积分发放列表：/h5/systemDistributionIntegral/distributionIntegralList get
// 积分发放详情：/h5/systemDistributionIntegral/distributionIntegralDetail get
// 积分发放成功、失败明细：/h5/distributionIntegralRecord/distributionIntegralRecordList get

import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum API {
  uploadPhones = '/uploadPhones',
  distributionIntegral = '/distributionIntegral',
  distributionIntegralList = '/distributionIntegralList',
  distributionIntegralDetail = '/distributionIntegralDetail',
  distributionIntegralRecordList = '/distributionIntegralRecordList',
}

function getApi(url?: string) {
  if (!url) {
    return '/h5/systemDistributionIntegral';
  }
  return '/h5/systemDistributionIntegral' + url;
}

// 批量上传数据，返回上传结果
export const uploadPhones = (params: Recordable) => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(API.uploadPhones),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 定向发放积分
export const distributionIntegral = (params: Recordable) => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(API.distributionIntegral),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 积分发放列表
export const distributionIntegralList = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(API.distributionIntegralList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 积分发放详情
export const distributionIntegralDetail = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(API.distributionIntegralDetail),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 积分发放成功、失败明细
export const distributionIntegralRecordList = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(API.distributionIntegralRecordList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
