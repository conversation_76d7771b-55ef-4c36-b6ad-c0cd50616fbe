import { h5Http } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum InterestGroup {

  //分页查询
  findList = '/interestGroupH5/findAuditList',
  audit = '/interestGroupH5/audit',
  details = '/interestGroupH5/getAuditDetails',

}


//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: InterestGroup.findList, params },
    {
      isTransformResponse: false,
    }
  );
};

//审核
export const groupAudit = params => {
    return h5Http.post<BasicResponse>(
        {
            url: InterestGroup.audit,
            params,
        },
        {
            isTransformResponse: false,
        }
    );
};

//详情
export const details = params => {
    return h5Http.get<BasicResponse>(
        { url: InterestGroup.details, params },
    );
};
