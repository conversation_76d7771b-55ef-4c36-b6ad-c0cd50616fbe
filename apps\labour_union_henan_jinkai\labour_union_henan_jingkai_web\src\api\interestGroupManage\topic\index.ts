import { h5Http } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum GroupTopic {
  base = "/interestGroupTopic",
  //分页查询
  findList = '/findList',
}

function getApi(url?: string) {
  if (!url) {
    return GroupTopic.base;
  }
  return GroupTopic.base + url;
}

//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(GroupTopic.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};


export const deleteLine = autoId => {
  return h5Http.delete<BasicResponse>(
      {url: `${getApi()}?autoId=${autoId}`},
      {
        isTransformResponse: false,
      }
  )
}

