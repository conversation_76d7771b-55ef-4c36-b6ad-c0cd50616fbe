import { BasicResponse } from '@monorepo-yysz/types';
import { manageHttp } from '/@/utils/http/axios';

enum recordUrl {
  findList = '/findVoList',
  saveOrUpdate = '',
  deleteById = '/deleteById',
  details = '/record/getDetails',
}

function getApi(url?: string) {
  if (!url) {
    return '/liveType';
  }
  return '/liveType' + url;
}

/*直播类型列表*/
export const findVoList = params => {
  return manageHttp.get<BasicResponse>(
    {
      url: getApi(recordUrl.findList),
      params,
    },
    { isTransformResponse: false }
  );
};

/*新增和编辑类型*/
export const saveOrUpdate = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getApi(recordUrl.saveOrUpdate),
      params,
    },
    { isTransformResponse: false }
  );
};

/*删除操作*/
export const deleteById = params => {
  return manageHttp.delete<BasicResponse>(
    {
      url: getApi(recordUrl.deleteById),
      params,
    },
    { isTransformResponse: false }
  );
};
