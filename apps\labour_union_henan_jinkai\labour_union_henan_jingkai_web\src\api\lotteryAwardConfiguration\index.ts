import { defHttp, h5Http } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum lotteryAwardConfiguration {
  //商品列表
  findList = '/findProductList',

  //保存
  saveOrUpdate = '/saveAwardsConfig',
  //列表
  list = '/getAwardsConfig',
}

enum record {
  findList = '/activityInfo/findPrizeRecordList',
  grantAwardsCode = '/activityInfo/grantAwards', //奖品发放
}

function getApi(url?: string) {
  if (!url) {
    return '/awardsInfo';
  }
  return '/awardsInfo' + url;
}

//查询商品列表
export const Businesslist = params => {
  return defHttp.get<BasicResponse>(
    { url: '/companyAuditRecord/findListProduct', params },
    {
      isTransformResponse: false,
    }
  );
};

//查询列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(lotteryAwardConfiguration.list), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增积分抽奖配置
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(lotteryAwardConfiguration.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//中奖记录列表
export const recordList = params => {
  return h5Http.get<BasicResponse>(
    { url: record.findList, params },
    {
      isTransformResponse: false,
    }
  );
};

//更新中奖记录 （兑换券发放，实物奖品发放）
export const update = params => {
  return h5Http.post<BasicResponse>(
    { url: record.grantAwardsCode, params },
    {
      isTransformResponse: false,
    }
  );
};
