import { BasicResponse } from '@monorepo-yysz/types';
import { manageHttp } from '/@/utils/http/axios';

enum MessageTemplate {
  findList = '/queryMessage',
  delete = '/deleteMessageById',
  getDetails = '/queryMessageByAutoId',
  messageSend = '/addMessageInfo',
  upDate = '/updateMessageInfo',
  getTree = '/queryReceiverList',
  getNzOrZdyTree = '/queryBQReceiverList', //获取内置或者自定义树数据
  countType = '/countType',
  getTotal = '/queryMessageCount',
  getDetailList = '/queryMessageRecord',
  app = '/appMessageInfo',
  smsList = '/queryReceiveUserList',
  sysList = '/queryMesStatus',
  person = '/historicalContact',
  querySysMesStatus = '/querySysMesStatus',
  queryMesTemplate = '/queryMesTemplate',
}

function getApi(url?: string) {
  if (!url) {
    return '/messageBasic';
  }
  return '/messageBasic' + url;
}

//列表
export const list = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(MessageTemplate.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//短信发送列表
export const getSmsList = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(MessageTemplate.getDetailList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 短信发送详情列表
export const getDetailList = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(MessageTemplate.smsList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 短信发送详情列表
export const detailList = params => {
  return manageHttp.get<Recordable[]>({ url: getApi(MessageTemplate.smsList), params });
};

// 历史人员查询
export const person = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(MessageTemplate.person), params },
    {
      isTransformResponse: false,
    }
  );
};

// 站内信发送详情列表
export const getSysDetailList = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(MessageTemplate.sysList), params },
    {
      isTransformResponse: false,
    }
  );
};

//统计数据
export const countType = () => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(MessageTemplate.countType) },
    {
      isTransformResponse: false,
    }
  );
};

//统计数据总计
export const getTotal = () => {
  return manageHttp.get<Recordable>({ url: getApi(MessageTemplate.getTotal) });
};

// 人员树
export const getTree = params => {
  return manageHttp.get<Recordable[]>({ url: getApi(MessageTemplate.getTree), params });
};
// 内置或者自定义人员树
export const getNzOrZdyTree = params => {
  return manageHttp.get<Recordable[]>({ url: getApi(MessageTemplate.getNzOrZdyTree), params });
};
export const findTree = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(MessageTemplate.getTree), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const messageSend = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getApi(MessageTemplate.messageSend),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const update = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getApi(MessageTemplate.upDate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteMessageTemplate = (id: number) => {
  return manageHttp.delete<BasicResponse>(
    {
      url: getApi(MessageTemplate.delete) + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};

//详情
export const getDetails = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(MessageTemplate.getDetails), params },
    {
      isTransformResponse: false,
    }
  );
};

//审核
export const appSms = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getApi(MessageTemplate.app),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const querySys = params => {
  return manageHttp.get<BasicResponse>(
    {
      url: getApi(MessageTemplate.querySysMesStatus),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const queryMesTemplate = params => {
  return manageHttp.get<Recordable[]>({
    url: getApi(MessageTemplate.queryMesTemplate),
    params,
  });
};
