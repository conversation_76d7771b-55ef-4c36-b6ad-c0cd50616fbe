import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

//留言类型列表
export const questionTypeFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/questionType/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//新增或更新栏目类型
export const questionType = params => {
    return h5Http.post<BasicResponse>(
      {
        url: '/questionType',
        params,
      },
      { isTransformResponse: false }
    );
  };

//改变留言类型状态
export const changeState = params => {
    return h5Http.post<BasicResponse>(
        {
            url: '/questionType/changeState',
            params,
        },
        { isTransformResponse: false }
    );
};

//删除
export const deleteQuestionType = params => {
  return h5Http.delete<BasicResponse>(
    {
      url: '/questionType?autoId=' + params,
      params,
    },
    { isTransformResponse: false }
  );
};

//最大排序号
export const getMaxCount = params => {
    return h5Http.get<BasicResponse>(
        {
            url: '/questionType/getMaxCount',
            params,
        },
        { isTransformResponse: false }
    );
};

//留言列表
export const questionFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/question/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//留言列表详情
export const questionVoByDto = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/question/getVoByDto',
      params,
    },
    { isTransformResponse: false }
  );
};
//改变留言状态
export const changePublicityState = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/question/changePublicityState',
      params,
    },
    { isTransformResponse: false }
  );
};
//删除
export const deleteLine = params => {
  return h5Http.delete<BasicResponse>(
    {
      url: '/question?autoId='+params,
      params,
    },
    { isTransformResponse: false }
  );
};
//回复留言
export const saveOrUpdateByDTO = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/questionReply/saveOrUpdateByDTO',
      params,
    },
    { isTransformResponse: false }
  );
};

//审核
export const auditSave = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/question/audit',
      params,
    },
    { isTransformResponse: false }
  );
};
