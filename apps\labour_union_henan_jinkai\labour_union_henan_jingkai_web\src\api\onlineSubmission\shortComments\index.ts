import { BasicResponse } from '@monorepo-yysz/types';
import {  h5Http } from '@/utils/http/axios';

enum LABEL {
    findVoList = '/findVoList',
    changeState = '/changeState',
}
function getApi(url?: string) {
    if (!url) {
        return '/shortVideoComments';
    }
    return '/shortVideoComments' + url;
}
// 列表
export const findVoList = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(LABEL.findVoList), params },
        {
            isTransformResponse: false,
        }
    );
}
// 查询详情
export const getById = id => {
    return h5Http.get<BasicResponse>(
        { url: getApi()+ `?autoId=${id}`, },
        {
            isTransformResponse: false,
        }
    );
}

// 更改评论公开状态
export const changeState = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(LABEL.changeState), params },
        {
            isTransformResponse: false,
        }
    );
}