import { BasicResponse } from '@monorepo-yysz/types';
import {  h5Http } from '@/utils/http/axios';

enum LABEL {
    getVoByDto = '/getVoByDto',
    findVoList = '/findVoList',
    changePublicityState = '/changePublicityState',
    audit = '/audit',
    exportRecord = '/exportRecord'
}
function getApi(url?: string) {
    if (!url) {
        return '/shortVideo';
    }
    return '/shortVideo' + url;
}
// 列表
export const findVoList = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(LABEL.findVoList), params },
        {
            isTransformResponse: false,
        }
    );
}
// 查询详情
export const getVoByDto = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(LABEL.getVoByDto), params },
        {
            isTransformResponse: false,
        }
    );
}

// 更改短视频公开状态
export const changePublicityState = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(LABEL.changePublicityState), params },
        {
            isTransformResponse: false,
        }
    );
}

// 批量/单个审核
export const audit = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(LABEL.audit), params },
        {
            isTransformResponse: false,
        }
    );
}

//删除
export const deleteById = id => {
    return h5Http.delete<BasicResponse>(
        {
            url: getApi()+ `?autoId=${id}`,
        },
        {
            isTransformResponse: false,
        }
    );
};

export const exportRecord = params =>{
    return h5Http.post<any>(
        { url: getApi(LABEL.exportRecord), params, responseType: 'blob' },
        {
            isTransformResponse: false,
        }
    );
}
