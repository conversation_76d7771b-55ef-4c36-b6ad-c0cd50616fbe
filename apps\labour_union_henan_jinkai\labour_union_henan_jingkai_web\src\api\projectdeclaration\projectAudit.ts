import { BasicResponse } from '@monorepo-yysz/types';
import { manageHttp } from '/@/utils/http/axios';

enum CorporateAudit {
  list = '/auditProjectDeclareRecords/findList',
  audit = '/auditProjectDeclareRecords/batchAuditProject',
  view = '/auditProjectDeclareRecords/getById',
}

//项目申报审核记录列表
export const projectList = params => {
  return manageHttp.get<BasicResponse>(
    { url: CorporateAudit.list, params },
    {
      isTransformResponse: false,
    }
  );
};

//项目申报审核接口
export const projectAudit = params => {
  return manageHttp.post<BasicResponse>(
    { url: CorporateAudit.audit, params },
    {
      isTransformResponse: false,
    }
  );
};

//根据autoId查询详情
export const getDetails = params => {
  return manageHttp.get<BasicResponse>(
    { url: CorporateAudit.view, params },
    {
      isTransformResponse: false,
    }
  );
};
