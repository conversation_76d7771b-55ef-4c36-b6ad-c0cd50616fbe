import { BasicResponse } from '@monorepo-yysz/types';
import { manageHttp } from '/@/utils/http/axios';

enum CorporateAudit {
  list = '/projectDeclareInfo/findList',
  audit = '/auditProjectDeclareRecords/addProject',
}

//项目申报审核记录列表
export const projectInfoList = params => {
  return manageHttp.get<BasicResponse>(
    { url: CorporateAudit.list, params },
    {
      isTransformResponse: false,
    }
  );
};
