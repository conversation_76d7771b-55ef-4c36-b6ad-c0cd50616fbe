import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';
import { UploadFileParams } from '#/axios';
import { AxiosProgressEvent } from 'axios';
import { UploadApiResult } from '@/api/sys/model/uploadModel';
import { GateWayEnum } from '@/enums/appEnum';

enum RechargeOrder {
  base = '/rechargeOrderManage',
  list = '/findList',
  upload = '/upload',
  cancel = '/cancel',
}

function getApi(url?: string) {
  if (!url) {
    return RechargeOrder.base;
  }
  return RechargeOrder.base + url;
}

//列表
export const list = params => {
  return h5Http.get<any>(
    { url: getApi(RechargeOrder.list), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增
export const save = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(), params },
    {
      isTransformResponse: false,
    }
  );
};

//撤销
export const cancel = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(RechargeOrder.cancel), params },
    {
      isTransformResponse: false,
    }
  );
};

export function upload(
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void
) {
  return h5Http.uploadFile<UploadApiResult>(
    {
      url: getApi(RechargeOrder.upload),
      onUploadProgress,
    },
    params,
    GateWayEnum.h5
  );
}
