import { BasicResponse } from '@monorepo-yysz/types';
import { dataCenterHttp } from '@/utils/http/axios';

enum sensitive {
  list = '/findList', //查看列表
  filter = '/filter',
}

function getApi(url?: string) {
  if (!url) {
    return '/sensitiveCheck';
  }
  return '/sensitiveCheck' + url;
}

//查询列表
export const list = params => {
  return dataCenterHttp.get<BasicResponse>(
    { url: getApi(sensitive.list), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = params => {
  return dataCenterHttp.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + params,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//敏感词过滤
export const sensitiveCheck = params => {
  return dataCenterHttp.post<BasicResponse>(
    { url: getApi(sensitive.filter), params },
    {
      isTransformResponse: false,
    }
  );
};
