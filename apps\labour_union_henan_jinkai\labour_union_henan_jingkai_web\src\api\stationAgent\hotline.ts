import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

//热线列表
export const hotlineFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/psychologicalHotline/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//新增或更新
export const saveOrUpdateByDTO = params => {
    return h5Http.post<BasicResponse>(
      {
        url: '/psychologicalHotline/saveOrUpdateByDTO',
        params,
      },
      { isTransformResponse: false }
    );
  };
//详情
export const getDetails = params => {
    return h5Http.get<BasicResponse>(
      {
        url: '/psychologicalHotline?autoId=' + params,
        params,
      },
      { isTransformResponse: false }
    );
  };
//删除
export const deleteModelType = params => {
  return h5Http.delete<BasicResponse>(
    {
      url: '/psychologicalHotline?autoId=' + params,
      params,
    },
    { isTransformResponse: false }
  );
};
