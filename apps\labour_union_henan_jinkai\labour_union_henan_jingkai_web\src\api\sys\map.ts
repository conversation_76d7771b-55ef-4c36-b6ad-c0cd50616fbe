import { outHttp } from '@/utils/http/axios';

//'156000000'
export const getArea = params => {
  return outHttp.get<Recordable>(
    {
      url: '/place/v2/search',
      params: {
        query: params,
        region: '南充',
        output: 'json',
        page_size: 20,
        ak: import.meta.env.VITE_GLOB_APP_BAIDU_SERVER,
      },
    },
    { isTransformResponse: false }
  );
};

export const byLngLat = params => {
  return outHttp.get<Recordable>(
    {
      url: '/reverse_geocoding/v3',
      params: {
        ...params,
        output: 'json',
        coordtype: 'wgs84ll',
        ret_coordtype: 'wgs84ll',
        ak: import.meta.env.VITE_GLOB_APP_BAIDU_SERVER,
      },
    },
    { isTransformResponse: false }
  );
};
