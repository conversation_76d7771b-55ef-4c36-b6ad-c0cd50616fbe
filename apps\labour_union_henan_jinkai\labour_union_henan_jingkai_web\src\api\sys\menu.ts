import { dataCenterHttp } from '@/utils/http/axios';
import { getMenuListResultModel } from './model/menuModel';
import { BasicResponse } from '@monorepo-yysz/types';

enum Api {
  GetMenuList = '/sysAuthAbout/getMenuTreeListCurrent',
  save = '/menu/menuSaveByDTO',
  update = '/menu/menuUpdateByDTO',
  remove = '/menu/removeMenu',
}

/**
 * @description: Get user menu based on id
 */

export const getMenuList = (params?: Recordable) => {
  return dataCenterHttp.get<getMenuListResultModel>({ url: Api.GetMenuList, params });
};

export const deleteLine = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: Api.remove,
      params,
    },
    { isTransformResponse: false }
  );
};

// 新增
export const menuSaveByDTO = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: Api.save,
      params,
    },
    { isTransformResponse: false }
  );
};

// 修改
export const menuUpdateByDTO = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: Api.update,
      params,
    },
    { isTransformResponse: false }
  );
};
