import { UploadApiResult } from './model/uploadModel';
import { defHttp, fileHttp } from '@/utils/http/axios';
import { UploadFileParams } from '#/axios';
import { AxiosProgressEvent } from 'axios';

/**
 * @description: Upload interface
 */
export function uploadApi(
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void
) {
  return defHttp.uploadFile<UploadApiResult>(
    {
      url: '/yyfile/v2/uploadFileFormData',
      onUploadProgress,
    },
    params
  );
}

export function download(params) {
  return fileHttp.post<any>(
    {
      url: '/yyfile/v2/downloadFiles',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
}

export const uploadFile = params => {
  return fileHttp.uploadFile(
    {
      url: '/yyfile/v2/uploadFileFormData',
    },
    {
      ...params,
    }
  );
};

export const queryFiles = params => {
  return fileHttp.post<Recordable[]>(
    {
      url: '/yyfile/v2/queryFiles',
      params
    },
  );
};