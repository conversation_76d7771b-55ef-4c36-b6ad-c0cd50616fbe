import { dataCenterHttp, defHttp } from '@/utils/http/axios';
import { LoginParams, GetUserInfoModel } from './model/userModel';
import { RouteItem } from './model/menuModel';
import { ErrorMessageMode } from '#/axios';
import { LoginInfo } from '#/store';

enum Api {
  Login = '/sysAuthAbout/login',
  Logout = '/sysAuthAbout/logoutCurrent',
  GetUserInfo = '/sysAuthAbout/getAccountDetail',
  GetPermCode = '/sysAuthAbout/getMenuListCurrent',
  TestRetry = '/testRetry',
  GetAccountInfoList = '/sysAuthAbout/getAccountInfoList',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return dataCenterHttp.post<LoginInfo>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    }
  );
}

/**
 * @description: getUserInfo
 */
export function getUserInfo(params) {
  return dataCenterHttp.get<GetUserInfoModel>(
    {
      url: Api.GetUserInfo,
      params,
    },
    { errorMessageMode: 'none' }
  );
}

export function getPermCode() {
  return dataCenterHttp.get<RouteItem[]>({ url: Api.GetPermCode });
}

export function doLogout() {
  return dataCenterHttp.post({ url: Api.Logout });
}

export function testRetry() {
  return defHttp.get(
    { url: Api.TestRetry },
    {
      retryRequest: {
        isOpenRetry: true,
        count: 5,
        waitTime: 1000,
      },
    }
  );
}

export function getAccountInfoList(params) {
    return dataCenterHttp.get<GetUserInfoModel>(
        {
            url: Api.GetAccountInfoList,
            params,
        },
        { isTransformResponse: false, }
    );
}
