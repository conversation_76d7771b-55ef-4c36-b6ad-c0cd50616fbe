import { BasicResponse } from '@monorepo-yysz/types';
import { dataCenterHttp } from '/@/utils/http/axios';

enum API {
  findList = '/getPostList',
  view = '/getPostInfo',
  saveApi = '/savePostInfo',
  updateApi = '/updatePostInfo',
  getPostTreeList = '/getPostTreeList',
  deletePostInfo = '/deletePostInfo',
}

function getApi(url?: string) {
  if (!url) {
    return '/sysAuthAbout';
  }
  return '/sysAuthAbout' + url;
}

// 列表
export const list = (params: Recordable) => {
  return dataCenterHttp.get<BasicResponse>(
    { url: getApi(API.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 新增
export const saveApi = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(API.saveApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 修改
export const updateApi = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(API.updateApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(API.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const deleteLine = (postId: number[] | number) => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(API.deletePostInfo) + '?postId=' + postId,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 获取岗位树
export const getPostTreeList = (params: Recordable) => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(API.getPostTreeList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
