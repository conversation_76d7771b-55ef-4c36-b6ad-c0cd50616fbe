import { BasicResponse } from '@monorepo-yysz/types';
import { dataCenterHttp } from '/@/utils/http/axios';

enum UserLabelManage {
  getMaxSortNumber = '/getMaxSortNumber',
  setEnableDisable = '/setEnableDisable',
  getByEntity = '/getByEntity',
}

function getApi(url?: string) {
  if (!url) {
    return '/userLabelManage';
  }
  return '/userLabelManage' + url;
}

//用户标签管理列表
export const list = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: '/userLabelPublicMethod/findUserLabelManageVoList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//用户标签列表
export const userList = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: '/userLabelRecord/findVoList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//查询库里面的会员信息
export const selectUser = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: '/accountInfo/findList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//设置用户标签
export const saveOrUpdateUserLabelRecord = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: '/userLabelPublicMethod/saveOrUpdateUserLabelRecord',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//设置用户标签
export const saveOrUpdateRecord = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: '/userLabelRecord',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//导出用户标签记录
export const exportUserLabelRecord = params => {
  return dataCenterHttp.get<any>(
    {
      url: '/userLabelPublicMethod/exportUserLabelRecord',
      params,
      responseType: 'blob',
      timeout: 1800 * 1000,
    },
    { isReturnNativeResponse: true }
  );
};

//下载标签人员导入模板
export const getUserLabelRecordTemplateURL = () => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: '/userLabelPublicMethod/getUserLabelRecordTemplateURL',
      responseType: 'blob',
      timeout: 1800 * 1000,
    },
    { isReturnNativeResponse: true }
  );
};

//用户标签管理新增或修改
export const saveOrUpdate = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取最大排序号
export const maxSortNumber = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(UserLabelManage.getMaxSortNumber),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//设置劳模启用禁用
export const setEnableDisable = params => {
  return dataCenterHttp.post<BasicResponse>(
    { url: getApi(UserLabelManage.setEnableDisable), params },
    {
      isTransformResponse: false,
    }
  );
};

//用户标签管理详情
export const view = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//用户标签记录详情
export const recordview = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: '/userLabelRecord',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//用户标签管理删除
export const deleteLine = id => {
  return dataCenterHttp.delete<BasicResponse>(
    {
      url: '/userLabelPublicMethod/removeLabelAndRecord?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};

//查询用户获得的标签
export const queryUserLabelList = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url:
        '/userLabelPublicMethod/queryUserLabel?phone=' +
        params.phone +
        '&systemQueryType=' +
        params.systemQueryType,
    },
    {
      isTransformResponse: false,
    }
  );
};

//单个或者批量设置用户标签
export const batchUpdateLabel = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: '/userLabelPublicMethod/batchUpdateLabel',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//用户标签记录删除
export const removeUserLabelRecord = id => {
  return dataCenterHttp.delete<BasicResponse>(
    {
      url: '/userLabelPublicMethod/removeUserLabelRecord?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 获取标签
export const userLabelManageGetByEntity = params => {
  return dataCenterHttp.get<Recordable>({
    url: getApi(UserLabelManage.getByEntity),
    params,
  });
};
