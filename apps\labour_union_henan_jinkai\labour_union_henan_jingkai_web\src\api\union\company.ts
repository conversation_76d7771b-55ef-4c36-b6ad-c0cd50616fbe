import { BasicResponse } from '@monorepo-yysz/types';
import { dataCenterHttp } from '/@/utils/http/axios';

enum API {
  findList = '/findCompanyList4Expand',
  view = '/getCompanyDetail4Expand',
}

function getApi(url?: string) {
  if (!url) {
    return '/expand';
  }
  return '/expand' + url;
}

// 列表
export const list = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: `${getApi(API.findList)}?pageSize=${params.pageSize || 10}&pageNum=${params.pageNum || 1}`,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 新增
export const saveApi = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: '/expand/directAddCompany4Expand',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 修改
export const updateApi = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: '/expand/directUpdateCompany4Expand',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(API.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
