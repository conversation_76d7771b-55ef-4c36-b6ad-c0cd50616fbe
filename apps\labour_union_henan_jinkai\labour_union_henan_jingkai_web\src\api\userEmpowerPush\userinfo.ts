import { h5Http } from '/@/utils/http/axios'
import { BasicResponse } from '@monorepo-yysz/types';

enum OBJ {
  findList = '/findUserinfoVOList',
  view = '/getUserinfoVOByDto',
  saveOrUpdate = '/saveOrUpdateByUserinfoDTO',
  delete = '/removeUserinfoById',
  generateRegistrationCode = '/generateRegistrationCode',
}

function getApi(url?: string) {
  if (!url) {
    return '/encryptionCommon'
  }
  return '/encryptionCommon' + url
}

//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    },
  )
}

//新增修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(OBJ.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

//view
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

//删除
export const deleteLine = (autoId: number[] | number) => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(OBJ.delete) + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    },
  )
}

//生成授权码
export const generateRegistrationCode = () => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(OBJ.generateRegistrationCode),
    },
    {
      isTransformResponse: true,
    },
  )
}