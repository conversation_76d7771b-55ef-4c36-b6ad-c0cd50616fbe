import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum OBJ {
  findList = '/findVOList',
  simpleList = '/findSimpleList',
}

function getApi(url?: string) {
  if (!url) {
    return '/activityInclusiveGroup';
  }
  return '/activityInclusiveGroup' + url;
}

//列表
export const list = params => {
  return h5Http.get<Recordable[]>({ url: getApi(OBJ.findList), params });
};

//简单列表 只查询群体id+名称
export const simpleList = params => {
  return h5Http.get<Recordable[]>({ url: getApi(OBJ.simpleList), params });
};

//新增修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//view
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = (autoId: number[] | number) => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};

//启动计划 - 撤销
export const cancel = params => {
  return h5Http.delete<BasicResponse>(
    {
      url: '/inclusiveGroupActivityExtend/cancel',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//启动计划 - 关联计划
export const joinPlan = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/inclusiveGroupActivityExtend',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
