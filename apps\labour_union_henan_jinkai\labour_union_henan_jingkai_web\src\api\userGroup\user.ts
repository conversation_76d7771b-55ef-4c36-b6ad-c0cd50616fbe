import {h5Http} from '/@/utils/http/axios'
import {BasicResponse} from '..'

enum OBJ {
  findList = '/findVOList',
  importUser = '/importUser',
  saveOrUpdateByDTO = '/saveOrUpdateByDTO',
  listGroupByDataSource = '/findList/groupByDataSource'
}

function getApi(url?: string) {
  if (!url) {
    return '/activityInclusiveGroupUser'
  }
  return '/activityInclusiveGroupUser' + url
}

//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    },
  )
}

//列表
export const listGroupByDataSource = params => {
    return h5Http.get<BasicResponse>(
        { url: getApi(OBJ.listGroupByDataSource), params },
        {
            isTransformResponse: false,
        },
    )
}

//新增修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

export const view = params => {
  return h5Http.get<BasicResponse>(
      {
        url: getApi(),
        params,
      },
      {
        isTransformResponse: false,
      },
  )
}

//新增修改
export const batchSave = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(OBJ.saveOrUpdateByDTO),
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}


//删除
export const deleteLine = (autoId: number[] | number) => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    },
  )
}
