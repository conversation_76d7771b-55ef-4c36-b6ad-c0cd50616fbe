import { BasicResponse } from '@monorepo-yysz/types';
import { dataCenterHttp, defHttp } from '@/utils/http/axios';
import { h5Http } from '@/utils/http/axios';

enum AdministratorsEnums {
  findList = '/findVenueAdministratorsVOList',
  save = '/saveVenueAdministrators',
  details = '/getVenueAdministratorsVOByDto',
  enableOrDisable = '/enableOrDisableVenueAdministrators',
}

function getApi(url?: string) {
  if (!url) {
    return '/venueInfo';
  }
  return '/venueInfo' + url;
}

//场所管理员列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(AdministratorsEnums.findList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//场所管理员列表
export const basicUnionUser = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: '/basicUnionUser/findVoList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//场所管理员新增
export const save = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(AdministratorsEnums.save),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//场所管理员启用或禁用
export const enableOrDisable = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(AdministratorsEnums.enableOrDisable),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//场所管理员详情
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(AdministratorsEnums.details),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
