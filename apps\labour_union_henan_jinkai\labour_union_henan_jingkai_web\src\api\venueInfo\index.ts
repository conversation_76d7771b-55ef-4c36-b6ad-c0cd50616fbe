import { h5Http } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum VenueInfo {
  findList = '/findList',
  findRecordList = '/findRecordVOList',
  entranceCount = '/entranceCount',
  auditRecord = '/auditRecord',
  getQrCode = '/getQrCode',
  recordView = '/recordView',
  saveOrUpdateInfo = '/saveOrUpdateInfo',
  removeById = '/removeById'
}

enum VenueCommentInfo {
  findVOList = '/findVoList',
  removeById = '/removeById',
  batchAudit = '/batchAudit'
}




function getApi(url?: string) {
  if (!url) {
    return '/venueInfo';
  }
  return '/venueInfo' + url;
}

function getCommentApi(url?: string) {
  if (!url) {
    return '/venuePositionComment';
  }
  return '/venuePositionComment' + url;
}

//阵地服务
export const list = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.findList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//阵地服务增加修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(VenueInfo.saveOrUpdateInfo),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//阵地服务详情
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//阵地服务删除
export const deleteLine = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(VenueInfo.removeById) + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取预约记录列表
export const findRecordList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.findRecordList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取评价记录
export const findCommentList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getCommentApi(VenueCommentInfo.findVOList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//评论删除
export const deleteCommentLine = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: getCommentApi(VenueCommentInfo.removeById) + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};

//批量审核
export const batchAudit = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getCommentApi(VenueCommentInfo.batchAudit),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};


//门禁统计
export const entranceCount = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.entranceCount),
      params,
    },
    {
      isTransformResponse: true,
    }
  );
};

//记录详情
export const recordView = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.recordView),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const auditRecord = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(VenueInfo.auditRecord),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//故障上报列表
export const findFaultRecordList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/venueFaultRecord/findVoList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
//故障上报详情
export const findFaultRecordView = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/venueFaultRecord',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const getQrCode = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.getQrCode),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
