import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http, dataCenterHttp } from '/@/utils/http/axios';

enum VenueInfo {
  findList = '/manageFindVenuePositionVoList',
  saveOrUpdate = '/saveOrUpdatePosition',
  releaseOrRevokePosition = '/releaseOrRevokePosition',
  details = '/manageGetVenuePositionVoByDto',
  delete = '/deletePositionInfoByAutoId',
}

function getApi(url?: string) {
  if (!url) {
    return '/venueInfo';
  }
  return '/venueInfo' + url;
}

//阵地列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.findList),
      params,
      timeout: 1800 * 1000,
    },
    {
      isTransformResponse: false,
    }
  );
};

//阵地新增或修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(VenueInfo.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//阵地发布或撤销
export const releaseOrRevokePosition = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(VenueInfo.releaseOrRevokePosition),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//阵地详情
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.details),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//阵地删除
export const deleteLine = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(VenueInfo.delete) + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};

//根据工会id获取可选区域
export const optionalArea = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: '/dictionaryInfo/optionalAreaByCompanyId',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
