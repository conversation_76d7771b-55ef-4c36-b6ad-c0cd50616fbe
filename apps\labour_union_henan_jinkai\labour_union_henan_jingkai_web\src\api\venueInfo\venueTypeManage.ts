import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum VenueInfo {
  findList = '/findVenueTypeManageVOList',
  saveOrUpdate = '/saveOrUpdateTypeManage',
  details = '/getVenueTypeManageVoByDto',
  delete = '/deleteTypeManageByAutoId',
  getMaxSortNumber = '/getMaxSortNumber',
}

function getApi(url?: string) {
  if (!url) {
    return '/venueInfo';
  }
  return '/venueInfo' + url;
}

//分类管理列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.findList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//分类管理新增或修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(VenueInfo.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//分类管理详情
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.details),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取最大排序号
export const maxSortNumber = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.getMaxSortNumber),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//分类管理删除
export const deleteLine = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(VenueInfo.delete) + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};
