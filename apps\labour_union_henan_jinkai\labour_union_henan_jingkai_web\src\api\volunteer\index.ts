import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

//列表
export const volunteerInfoFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/volunteerInfo/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//新增或更新
export const saveOrUpdateByDTO = params => {
    return h5Http.post<BasicResponse>(
      {
        url: '/volunteerInfo/saveOrUpdateByDTO',
        params,
      },
      { isTransformResponse: false }
    );
  };

//删除
export const deleteVolunteerInfo = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: '/volunteerInfo?autoId=' + id,
    },
    { isTransformResponse: false }
  );
};

//列表详情
export const getVoByDto = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/volunteerInfo/getVoByDto',
      params,
    },
    { isTransformResponse: false }
  );
};


//志愿者启用禁用
export const enableDisable = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/volunteerInfo/setEnableDisable',
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

