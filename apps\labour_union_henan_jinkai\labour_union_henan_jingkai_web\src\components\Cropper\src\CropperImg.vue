<template>
  <div class="w-full h-full">
    <slot name="tip"></slot>
    <div class="w-full h-full">
      <div
        class="ant-upload ant-upload-select ant-upload-select-picture-card !flex justify-center items-center !mr-0 !mb-0 !w-full !h-full"
        @click="handleUploadappCover"
        v-if="!disabled"
      >
        <img
          v-if="url"
          :src="url"
          alt=""
          :class="{ '!h-full': ifH, '!w-full': ifW }"
        />
        <div v-else class="flex flex-col items-center">
          <plus-outlined />
          <div class="ant-upload-text !mr-5px">上传图片</div>
        </div>
      </div>
      <Image
        :src="url"
        :fallback="!!errorImg ? errorImg : Error"
        class="!w-full !h-full !inline-block"
        v-else
      />
    </div>
    <CropperModal
      @register="register"
      :circled="false"
      @upload-success="handleUploadSuccess"
      :imgSize="imgSize"
      :upload-api="uploadFile"
      :operateType="operateType"
    />
  </div>
</template>

<script lang="ts" setup>
import { CropperModal } from '@/components/Cropper/';
import { useModal } from '/@/components/Modal';
import { Image } from 'ant-design-vue';
import { computed, ref, unref, useAttrs, watch } from 'vue';
import { uploadFile } from '@/api/sys/upload';
import { PlusOutlined } from '@ant-design/icons-vue';
import { useUserStore } from '/@/store/modules/user';
import Error from '/@/assets/images/error.png';
import { startsWith } from 'lodash-es';

const props = defineProps({
  imgSize: {
    type: Number,
    default: 1,
  },
  operateType: {
    type: Number,
    default: 0,
  },
  value: {
    type: String,
  },
  ifError: {
    type: Boolean,
    default: false,
  },
  errorImg: {
    type: String,
  },
  cropendRatio: {
    type: Number,
    default: 0.6,
  },
});

const emit = defineEmits(['change']);

//区别本地图片
const ifLocal = computed(() => {
  return props.value?.includes('/assets');
});

const ifHttp = computed(() => {
  return startsWith(props.value, 'http');
});
const attrs = useAttrs();

const userStore = useUserStore();

const [register, { openModal, closeModal }] = useModal();

const disabled = computed(() => {
  return attrs.disabled;
});

const ifW = computed(() => {
  return props.imgSize > 1;
});

const ifH = computed(() => {
  return props.imgSize < 1;
});

const url = ref('');

function handleUploadappCover() {
  if (unref(disabled)) {
    return false;
  }

  openModal(true);
}

function handleUploadSuccess({ filePath }) {
  url.value = userStore.getPrefix + filePath;

  // if (!!props.value && props.value !== filePath) {
  //   deleteFile({ filenames: [props.value] })
  // }

  emit('change', filePath);
  closeModal();
}

watch(
  () => props.value,
  () => {
    url.value = props.value
      ? unref(ifLocal) || unref(ifHttp)
        ? props.value
        : userStore.getPrefix + props.value
      : '';
  },
  { deep: true, immediate: true }
);
</script>
