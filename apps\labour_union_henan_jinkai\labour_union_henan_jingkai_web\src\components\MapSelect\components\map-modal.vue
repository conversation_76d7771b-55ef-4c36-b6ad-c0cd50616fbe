<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :can-fullscreen="false"
    width="60%"
  >
    <div class="w-full h-60vh">
      <a-select
        v-model:value="state.value"
        placeholder="请输入地址"
        :show-search="true"
        style="width: 100%"
        :label-in-value="true"
        :filter-option="false"
        :not-found-content="state.fetching ? undefined : null"
        :options="state.data"
        @search="fetchAddress"
        @select="handleSelect"
      >
        <template
          v-if="state.fetching"
          #notFoundContent
        >
          <a-spin size="small" />
        </template>
      </a-select>

      <MapContainer
        :lnglat="setLnglat"
        @get-location="handleLocation"
        :disabled="disabled"
      />
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed, reactive, watch } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import MapContainer from './map-container.vue';
import { isEmpty, debounce, map } from 'lodash-es';
import { getArea } from '@/api/sys/map';

const state = reactive<{ data: Recordable[]; value: Recordable | undefined; fetching: boolean }>({
  data: [],
  value: undefined,
  fetching: false,
});

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const setLnglat = ref<string>();

const result = ref<Recordable>();

const disabled = ref<boolean>(false);

const title = computed(() => {
  return '地图选择';
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  record.value = data.record;

  setLnglat.value = data.record?.lnglat;

  disabled.value = !!data.disabled;

  // 重置
  state.data = [];
  // state.value = undefined;
  state.fetching = false;

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

function handleLocation(res) {
  result.value = res;

  setSelectInput(res);
}

// 搜索
const fetchAddress = debounce(async value => {
  if (isEmpty(value)) return;

  state.data = [];
  state.fetching = true;

  const { results } = await getArea(value).finally(() => {
    state.fetching = false;
  });
  state.data = map(results || [], v => ({
    label: v.name,
    value: `${v.location.lng},${v.location.lat}`,
  }));
}, 1000);

function handleSelect(valueOpt) {
  result.value = {
    location: valueOpt.value,
    address: valueOpt.label,
  };

  setLnglat.value = valueOpt.value;
}

// 反向设置select值
function setSelectInput(res) {
  state.value = {
    value: res.location,
    label: res.address,
  };
}

watch(
  () => state.value,
  () => {
    state.data = [];
    state.fetching = false;
  }
);

async function handleSubmit() {
  if (isEmpty(unref(result))) return;

  try {
    setModalProps({ confirmLoading: true });

    emit('success', {
      ...unref(result),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
