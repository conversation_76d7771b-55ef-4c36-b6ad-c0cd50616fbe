@simple-prefix-cls: ~'@{namespace}-simple-menu';
@prefix-cls: ~'@{namespace}-menu';

.@{prefix-cls} {
  &-dark&-vertical .@{simple-prefix-cls}__parent {
    // background-color: @sider-dark-bg-color;
    background-color: transparent;
    border-bottom: 1px solid;
    border-image: linear-gradient(90deg, transparent, rgba(119, 176, 255), transparent) 27;
    > .@{prefix-cls}-submenu-title {
      // background-color: @sider-dark-bg-color;
      background-color: transparent;
    }
  }

  &-dark&-vertical .@{simple-prefix-cls}__children,
  &-dark&-popup .@{simple-prefix-cls}__children {
    // background-color: @sider-dark-lighten-bg-color;
    background-color: transparent;
    // 子菜单底部bottom
    border-bottom: 1px solid;
    border-image: linear-gradient(90deg, transparent, rgba(119, 176, 255), transparent) 27;

    > .@{prefix-cls}-submenu-title {
      // background-color: @sider-dark-lighten-bg-color;
      background-color: transparent;
    }

    // 子菜单底部bottom最后一个元素不展示
    &:last-child {
      border-color: transparent;
    }
  }

  .collapse-title {
    overflow: hidden;
    font-size: 12px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-item-selected {
    color: @white;
    background: @menu-selected-bg;

    .@{simple-prefix-cls} {
      &-sub-title {
        color: @menu-selected-text;
      }
    }
  }
}

.@{namespace} {
  &-menu-opened > &-menu-submenu-title {
    // background-color: @menu-p-selected !important;
    background: @menu-selected-bg-reverse;
    font-weight: 600;
    color: @menu-selected-text !important;
  }
}

.@{simple-prefix-cls} {
  &-sub-title {
    overflow: hidden;
    transition: all 0.3s;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-tag {
    display: inline-block;
    position: absolute;
    top: calc(50% - 8px);
    right: 30px;
    margin-right: 4px;
    padding: 2px 3px;
    border-radius: 2px;
    color: #fff;
    font-size: 10px;
    line-height: 14px;

    &--collapse {
      top: 6px !important;
      right: 2px;
    }

    &--dot {
      top: calc(50% - 2px);
      width: 6px;
      height: 6px;
      padding: 0;
      border-radius: 50%;
    }

    &--primary {
      background-color: @primary-color;
    }

    &--error {
      background-color: @error-color;
    }

    &--success {
      background-color: @success-color;
    }

    &--warn {
      background-color: @warning-color;
    }
  }
}
