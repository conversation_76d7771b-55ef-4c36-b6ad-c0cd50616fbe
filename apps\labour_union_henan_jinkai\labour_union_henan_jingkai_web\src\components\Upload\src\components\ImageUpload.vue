<template>
  <div>
    <Upload
      v-bind="$attrs"
      v-model:file-list="fileList"
      :list-type="listType"
      :accept="getStringAccept"
      :multiple="multiple"
      :maxCount="maxNumber"
      :before-upload="beforeUpload"
      :custom-request="customRequest"
      :disabled="disabled"
      @preview="handlePreview"
      @remove="handleRemove"
    >
      <div v-if="fileList && fileList.length < maxNumber">
        <plus-outlined />
        <div style="margin-top: 8px">{{ t('component.upload.upload') }}</div>
      </div>
    </Upload>
    <Modal
      :open="previewOpen"
      :title="previewTitle"
      :footer="null"
      @cancel="handleCancel"
    >
      <img
        alt=""
        style="width: 100%"
        :src="previewImage"
      />
    </Modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRefs, watch } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import type { UploadFile, UploadProps } from 'ant-design-vue';
import { Modal, Upload } from 'ant-design-vue';
import { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
import { useMessage } from '@monorepo-yysz/hooks';
import { isArray, isFunction, isObject, isString } from '@monorepo-yysz/utils';
import { warn } from '@/utils/log';
import { useI18n } from '@/hooks/web/useI18n';
import { useUploadType } from '../hooks/useUpload';
import { uploadContainerProps } from '../props';
import { checkFileType } from '../helper';
import { UploadResultStatus } from '@/components/Upload/src/types/typing';
import { get, omit } from 'lodash-es';
import { useGlobSetting } from '@/hooks/setting';

defineOptions({ name: 'ImageUpload' });

const emit = defineEmits(['change', 'update:value', 'delete']);
const props = defineProps({
  ...omit(uploadContainerProps, ['previewColumns', 'beforePreviewData']),
});
const { t } = useI18n();
const { createMessage } = useMessage();
const { bucket_name } = useGlobSetting();
const { accept, helpText, maxNumber, maxSize } = toRefs(props);
const isInnerOperate = ref<boolean>(false);
const { getStringAccept } = useUploadType({
  acceptRef: accept,
  helpTextRef: helpText,
  maxNumberRef: maxNumber,
  maxSizeRef: maxSize,
});
const previewOpen = ref<boolean>(false);
const previewImage = ref<string>('');
const previewTitle = ref<string>('');

const fileList = ref<UploadProps['fileList']>([]);
const isLtMsg = ref<boolean>(true);
const isActMsg = ref<boolean>(true);

watch(
  () => props.value,
  v => {
    if (isInnerOperate.value) {
      isInnerOperate.value = false;
      return;
    }
    let value: string[] = [];
    if (v) {
      if (isArray(v)) {
        value = v;
      } else {
        value.push(v);
      }
      fileList.value = value.map((item, i) => {
        if (item && isString(item)) {
          return {
            uid: -i + '',
            name: item.substring(item.lastIndexOf('/') + 1),
            status: 'done',
            url: item,
          };
        } else if (item && isObject(item)) {
          return item;
        } else {
          return;
        }
      }) as UploadProps['fileList'];
    }
    emit('update:value', value);
    emit('change', value);
  },
  {
    immediate: true,
    deep: true,
  }
);

function getBase64<T extends string | ArrayBuffer | null>(file: File) {
  return new Promise<T>((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      resolve(reader.result as T);
    };
    reader.onerror = error => reject(error);
  });
}

const handlePreview = async (file: UploadFile) => {
  if (!file.url && !file.preview) {
    file.preview = await getBase64<string>(file.originFileObj!);
  }
  previewImage.value = file.url || file.preview || '';
  previewOpen.value = true;
  previewTitle.value =
    file.name || previewImage.value.substring(previewImage.value.lastIndexOf('/') + 1);
};

const handleRemove = async (file: UploadFile) => {
  if (fileList.value) {
    const index = fileList.value.findIndex(item => item.uid === file.uid);
    index !== -1 && fileList.value.splice(index, 1);
    const value = getValue();
    isInnerOperate.value = true;
    emit('update:value', value);
    emit('change', value);
    emit('delete', file);
  }
};

const handleCancel = () => {
  previewOpen.value = false;
  previewTitle.value = '';
};

const beforeUpload = (file: File) => {
  const { maxSize, accept } = props;
  const isAct = checkFileType(file, accept);
  if (!isAct) {
    createMessage.error(t('component.upload.acceptUpload', [accept]));
    isActMsg.value = false;
    // 防止弹出多个错误提示
    setTimeout(() => (isActMsg.value = true), 1000);
  }
  const isLt = file.size / 1024 / 1024 > maxSize;
  if (isLt) {
    createMessage.error(t('component.upload.maxSizeMultiple', [maxSize]));
    isLtMsg.value = false;
    // 防止弹出多个错误提示
    setTimeout(() => (isLtMsg.value = true), 1000);
  }
  return (isAct && !isLt) || Upload.LIST_IGNORE;
};

async function customRequest(info: UploadRequestOption<any>) {
  const { api, uploadParams = {}, name, filename, resultField } = props;
  if (!api || !isFunction(api)) {
    return warn('upload api must exist and be a function');
  }
  try {
    const res = await api?.({
      data: {
        ...uploadParams,
        operateType: props.operateType || 0,
        bucketName: bucket_name,
      },
      file: info.file,
      name: name,
      filename: filename,
    });
    if (props.resultField) {
      let result = get(res, resultField);
      info.onSuccess!(result);
    } else {
      // 不传入 resultField 的情况
      info.onSuccess!(res.data);
    }
    const value = getValue();
    isInnerOperate.value = true;
    emit('update:value', value);
    emit('change', value);
  } catch (e: any) {
    console.log(e);
    info.onError!(e);
  }
}

function getValue() {
  const list = (fileList.value || [])
    .filter(item => item?.status === UploadResultStatus.DONE)
    .map((item: any) => {
      if (item?.response && props?.resultField) {
        return item?.response;
      }
      return item?.url || item?.response?.url;
    });
  return list;
}
</script>

<style lang="less">
.ant-upload-select-picture-card i {
  color: #999;
  font-size: 32px;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
