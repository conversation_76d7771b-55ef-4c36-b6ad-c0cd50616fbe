import type { AppRouteRecordRaw, AppRouteModule } from '@/router/types';
import { PAGE_NOT_FOUND_ROUTE, REDIRECT_ROUTE } from '@/router/routes/basic';
import { mainOutRoutes } from './mainOut';
import { PageEnum } from '@/enums/pageEnum';
import { t } from '@/hooks/web/useI18n';
import { useGlobSetting } from '@/hooks/setting';
import { LAYOUT } from '../constant';

const { ifLogin } = useGlobSetting();

// import.meta.glob() 直接引入所有的模块 Vite 独有的功能
const modules = import.meta.glob('./modules/**/*.ts', { eager: true });
const routeModuleList: AppRouteModule[] = [];

// 加入到路由集合中
Object.keys(modules).forEach(key => {
  const mod = (modules as Recordable)[key].default || {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];
  routeModuleList.push(...modList);
});

export const asyncRoutes = [PAGE_NOT_FOUND_ROUTE, ...routeModuleList];

// 根路由
export const RootRoute: AppRouteRecordRaw = {
  path: '/',
  name: 'Root',
  redirect: PageEnum.BASE_HOME,
  meta: {
    title: 'Root',
  },
};

export const LoginRoute: AppRouteRecordRaw = {
  path: '/login',
  name: 'Login',
  component: () => import('@/views/sys/login/Login.vue'),
  meta: {
    title: t('routes.basic.login'),
  },
};

// export const ScreenRoute: AppRouteRecordRaw = {
//   path: '/bigScreen',
//   name: 'bigScreen',
//   component: () => import('@/views/bigScreen/index.vue'),
//   meta: {
//     title: '大屏首页',
//   },
// };

const PositionManagement: AppRouteModule = {
  path: '/positionManagement',
  name: 'PositionManagement',
  component: LAYOUT,
  redirect: '/positionManagement/management',
  meta: {
    title: '场所管理',
    currentActiveMenu: '/positionManage',
  },
  children: [
    {
      path: 'management',
      name: 'Management',
      component: () => import('/@/views/position/management/index.vue'),
      meta: {
        // affix: true,
        title: '场所管理',
      },
    },
  ],
};
const reservation: AppRouteModule = {
  path: '/reservation',
  name: 'Reservation',
  component: LAYOUT,
  redirect: '/reservation/reservationAudit',
  meta: {
    title: '预约管理',
    currentActiveMenu: '/positionManage',
  },
  children: [
    {
      path: 'reservationAudit',
      name: 'ReservationAudit',
      component: () => import('/@/views/position/record/index.vue'),
      meta: {
        // affix: true,
        title: '预约管理',
      },
    },
  ],
};
const Administrators: AppRouteModule = {
  path: '/administrators',
  name: 'Administrators',
  component: LAYOUT,
  redirect: '/administrators/administratorsManage',
  meta: {
    title: '管理员',
    currentActiveMenu: '/positionManage',
  },
  children: [
    {
      path: 'administratorsManage',
      name: 'AdministratorsManage',
      component: () => import('/@/views/position/administrators/index.vue'),
      meta: {
        // affix: true,
        title: '管理员',
      },
    },
  ],
};
const Usage: AppRouteModule = {
  path: '/usage',
  name: 'Usage',
  component: LAYOUT,
  redirect: '/usage/usageManagement',
  meta: {
    title: '使用管理',
    currentActiveMenu: '/positionManage',
  },
  children: [
    {
      path: 'usageManagement',
      name: 'UsageManagement',
      component: () => import('/@/views/position/positionManage/usageManagement.vue'),
      meta: {
        // affix: true,
        title: '使用管理',
      },
    },
  ],
};

const NumberSetting: AppRouteModule = {
  path: '/numberSetting',
  name: 'NumberSetting',
  component: LAYOUT,
  redirect: '/numberSetting/mainPage',
  meta: {
    title: '期数设置',
    currentActiveMenu: '/walkActivity',
  },
  children: [
    {
      path: 'mainPage',
      name: 'MainPage',
      component: () => import('/@/views/activities/ActivityTable/NumberSetting.vue'),
      meta: {
        title: '期数',
      },
    },
  ],
};

//作品管理
const OpusesManage: AppRouteModule = {
  path: '/opusesManage',
  name: 'OpusesManage',
  component: LAYOUT,
  redirect: '/OpusesManage/opusList',
  meta: {
    title: '期数设置',
    currentActiveMenu: '/voteActivity',
  },
  children: [
    {
      path: 'opusList',
      name: 'OpusList',
      component: () => import('/@/views/activities/ActivityTable/OpusesListModal.vue'),
      meta: {
        title: '作品管理',
      },
    },
  ],
};

//用户信息授权
const userEmpower: AppRouteModule = {
  path: '/userEmpower',
  name: 'UserEmpower',
  component: LAYOUT,
  redirect: '/userEmpower/userEmpowerPush',
  meta: {
    title: '用户信息授权',
    currentActiveMenu: '/positionManage',
  },
  children: [
    {
      path: 'userEmpowerPush',
      name: 'userEmpowerPush',
      component: () => import('/@/views/userEmpowerPush/index.vue'),
      meta: {
        // affix: true,
        title: '用户信息授权',
      },
    },
  ],
};
// Basic routing without permission

// 关闭或开启登录页
const ifLoginArr = ifLogin === '0' ? [LoginRoute] : [];

// 未经许可的基本路由
export const basicRoutes = [
  RootRoute,
  // ScreenRoute,
  ...ifLoginArr,
  ...routeModuleList,
  ...mainOutRoutes,
  REDIRECT_ROUTE,
  PAGE_NOT_FOUND_ROUTE,
  PositionManagement,
  reservation,
  Administrators,
  Usage,
  userEmpower,
  NumberSetting,
  OpusesManage,
];
