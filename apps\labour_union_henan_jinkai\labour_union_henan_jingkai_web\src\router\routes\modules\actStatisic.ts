import { LAYOUT } from '../../constant'
import { AppRouteRecordRaw } from '../../types'
import { ActivityView, ActivityTypeZh, ActivityRouter } from '/@/views/activities/activities.d'

function fixAct() {
  const act: AppRouteRecordRaw[] = []
  for (const key in ActivityView) {
    const element = ActivityView[key]
    const cae = element[0].toUpperCase() + element.slice(1)
    act.push({
      path: `actStatisic/${element}`,
      name: `ActStatisic${cae}`,
      component: () => import('/@/views/activities/ActivityTable/ActStatistics.vue'),
      meta: {
        title: `${ActivityTypeZh[key]}统计`,
        currentActiveMenu: `/${ActivityRouter[key]}`,
      },
    })
  }
  return act
}

export default {
  path: '/actStatisic',
  name: 'ActStatisic',
  component: LAYOUT,
  meta: {
    title: '活动统计',
  },
  children: [...fixAct()],
}
