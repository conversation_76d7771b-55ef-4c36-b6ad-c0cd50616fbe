import { LAYOUT } from '../../constant'
import { AppRouteRecordRaw } from '../../types'
import { ActivityView, ActivityTypeZh, ActivityRouter } from '/@/views/activities/activities.d'

function fixAct() {
  const act: AppRouteRecordRaw[] = []
  for (const key in ActivityView) {
    const element = ActivityView[key]
    const cae = element[0].toUpperCase() + element.slice(1)
    act.push({
      path: element,
      name: `Act${cae}`,
      component: () => import('/@/views/activities/ActivityTable/ActView/index.vue'),
      meta: {
        title: `${ActivityTypeZh[key]}活动`,
        currentActiveMenu: `/${ActivityRouter[key]}`,
      },
    })
  }
  return act
}

export default {
  path: '/act',
  name: 'Act',
  component: LAYOUT,
  meta: {
    title: '活动预览',
  },
  children: [...fixAct()],
}
