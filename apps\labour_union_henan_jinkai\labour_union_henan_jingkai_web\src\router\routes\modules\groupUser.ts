import type { AppRouteModule } from '/@/router/types'
import { LAYOUT } from '/@/router/constant'

const groupUser: AppRouteModule = {
  path: '/group/user',
  name: 'GroupUser',
  component: LAYOUT,
  redirect: '/group/user/select/manage',
  meta: {
    title: '群体人员管理',
    currentActiveMenu: '/userGroupManagement',
  },
  children: [
    {
      path: 'select/manage',
      name: 'SelectManage',
      component: () => import('/@/views/userGroupManagement/GroupUser.vue'),
      meta: {
        // affix: true,
        title: '群体人员管理',
      },
    },
  ],
}
export default groupUser
