<template>
  <PageWrapper
    @back="() => router.go(-1)"
    :title="title"
    :contentClass="$style['act-statistic']"
    class="min-h-full"
  >
    <Tabs
      v-model:activeKey="activeKey"
      type="card"
      class="overflow-hidden"
      @change="handleTabs"
    >
      <TabPane
        v-for="item in tabsObj"
        :key="item.key"
        forceRender
        :tab="item.tab"
      >
        <div v-if="'basicInfo' === item.key">
          <CardList :activityId="activityId" />
        </div>

        <div v-if="item.key === 'join'">
          <JoinList
            :activityId="activityId"
            @reload="handle"
            :act-name="name"
          />
        </div>
        <div v-if="item.key === 'joinUnion'">
          <JoinUnion
            :activityId="activityId"
            @reload="handleUnion"
            :act-name="name"
          />
        </div>

        <div v-if="item.key === 'blackList'">
          <ActivityBlackList
            :activityId="activityId"
            @reload="handleBlackList"
            :act-name="name"
          />
        </div>

        <div v-if="item.key === 'luckDraw'">
          <PrizeAudit
            :activityId="activityId"
            :activityMode="activityType"
            @reload="handleReloadLuckDrawRecord"
          />
        </div>

        <div v-if="item.key === 'signUpRecord'">
          <JoinModal
            :activityId="activityId"
            :activityMode="activityType"
            @reload="handleReloadSignUpRecord"
          />
        </div>
        <div v-if="item.key === 'questionnaireReport'">
          <QuestionnaireReport
            :activityId="activityId"
            @reload="handleQuestionReportReload"
          />
        </div>
        <div v-if="item.key === 'questionnaire'">
          <JoinModal
            :activityId="activityId"
            :activityMode="activityType"
            @reload="handleReloadSignUpRecord"
          />
        </div>

        <div v-if="item.key === 'ticketRecord'">
          <RecordList :activityId="activityId"> </RecordList>
        </div>
        <div v-if="item.key === 'answerRecord'">
          <BasicTable @register="registerAnswerTable"> </BasicTable>
        </div>
        <div v-if="item.key === 'answerRank'">
          <BasicTable @register="registerAnswerRankTable"> </BasicTable>
        </div>
        <div v-if="item.key === 'voteRank'">
          <BasicTable @register="registerVoteRankTable">
            <template #toolbar>
              <a-button
                type="primary"
                @click="handleDownRank"
                :loading="spinning"
                >导出</a-button
              >
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <TableAction
                  :actions="[
                    {
                      icon: 'carbon:task-view',
                      label: '详情',
                      type: 'default',
                      onClick: handleView.bind(null, record),
                    },
                    {
                      icon: 'carbon:task-view',
                      label: '投票记录',
                      type: 'default',
                      onClick: handleRecord.bind(null, record),
                    },
                  ]"
                >
                </TableAction>
              </template>
            </template>
          </BasicTable>
          <!--投票记录-->
          <VoteRecordModal
            @register="registerModal"
            :can-fullscreen="false"
            width="40%"
          />
          <OpusModal
            @register="registerOpusModal"
            :is-audit="false"
            width="70%"
            :can-fullscreen="false"
          />
        </div>
        <div v-if="item.key === 'integralTree'">
          <IntegralTreeRecordList
            :activityId="activityId"
            :activityMode="activityType"
            @reload="handleIntegralTree"
          />
        </div>
      </TabPane>
    </Tabs>
  </PageWrapper>
</template>

<script lang="ts" setup>
import { Tabs } from 'ant-design-vue';
import { computed, onMounted, provide, ref, unref } from 'vue';
import { ActivityType, ActivityTypeZh, TabsObj } from '../activities.d';
import { answerRecordList, answerRecordRank, voteRank } from '@/api/activities/statistics';
import RecordList from '/@/views/coupon/record/index.vue';
import CardList from './CardList.vue';
import IntegralTreeRecordList from '/@/views/planting-trees/RecordList.vue';

import { useRoute, useRouter } from 'vue-router';
import { PageWrapper } from '@/components/Page';
import VoteRecordModal from './VoteRecordModal.vue';
import { JoinList, JoinUnion } from '@/views/activities/components';
import { BasicTable, TableAction, useTable } from '@/components/Table';
import {
  answerRecordColumns,
  answerRecordRankColumns,
  answerRecordSchemas,
  voteRankColumns,
  voteRankSchemas,
} from '@/views/activities/activity';
import { useModal } from '@/components/Modal';
import JoinModal from '@/views/activities/ActivityTable/JoinModal.vue';
import PrizeAudit from '@/views/activities/ActivityTable/PrizeAudit.vue';
import QuestionnaireReport from '@/views/activities/ActivityTable/QuestionnaireReport.vue';
import dayjs from 'dayjs';
import { downloadByUrl } from '@monorepo-yysz/utils';
import { exportVoteRank, getDetails, opusesDetail } from '@/api/activities';
import ActivityBlackList from '@/views/activities/components/ActivityBlackList.vue';
import OpusModal from '@/views/activities/ActivityTable/OpusModal.vue';

const TabPane = Tabs.TabPane;

defineEmits(['register']);

const route = useRoute();

const router = useRouter();

let fetchJoin = () => {};

let fetchUnion = () => {};

let reloadSignUpRecord = () => {};
let reloadLuckDrawRecord = () => {};
let questionReportReload = () => {};
let reloadIntegralTree = () => {};
let reloadBlackList = () => {};

const title = computed(() => {
  return `${unref(record)?.activityName || ''}--统计信息`;
});

const record = ref<Recordable>({});

const name = ref('');

const activeKey = ref(ActivityType.BASICINFO);

const activityType = ref(ActivityType.QUIZ);

const activityDetail = ref<Recordable>({});
provide('activityDetail', activityDetail);

const voteTypes = ref([]);

const tabsObj = ref<TabsObj[]>([]);

const voteRankParams = ref({});

const spinning = ref(false);
const handleDownRank = () => {
  spinning.value = true;
  exportVoteRank(unref(voteRankParams)).then(res => {
    const url = window.URL.createObjectURL(res);
    const fileName = `排行榜-${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    downloadByUrl({
      url,
      fileName,
    });
    spinning.value = false;
  });
};

//投票作品详情
const handleView = async (record: Recordable) => {
  const { data } = await opusesDetail({ autoId: record.autoId });
  const {
    voteInfo: { fileType, fileLimit },
  } = unref(activityDetail);
  openOpusModel(true, {
    record: { ...data, fileType, fileLimit },
    isUpdate: true,
    disabled: true,
  });
};

const rankSchemas = computed(() => {
  return voteRankSchemas(unref(voteTypes));
});

const activityId = ref<string>();

const [registerModal, { openModal }] = useModal();
const [registerOpusModal, { openModal: openOpusModel }] = useModal();

const [registerVoteRankTable] = useTable({
  rowKey: 'autoId',
  api: voteRank,
  columns: voteRankColumns(),
  maxHeight: 420,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  immediate: true,
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: rankSchemas,
    autoSubmitOnEnter: true,
    submitOnChange: true,
    resetFunc: () => {
      return new Promise<void>(resovle => {
        resovle();
      });
    },
  },
  actionColumn: {
    title: '操作',
    width: 250,
    dataIndex: 'action',
    fixed: undefined,
  },
  beforeFetch: params => {
    params.activityId = unref(activityId);
    voteRankParams.value = params;
    return params;
  },
});

const [registerAnswerTable, { reload: reloadAnswer }] = useTable({
  rowKey: 'autoId',
  api: answerRecordList,
  columns: answerRecordColumns(),
  maxHeight: 420,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  immediate: false,
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: answerRecordSchemas(),
    autoSubmitOnEnter: true,
    resetFunc: () => {
      return new Promise<void>(resovle => {
        resovle();
      });
    },
  },
  beforeFetch: params => {
    params.activityId = unref(activityId);
    params.sortType = 'desc';
    params.orderBy = 'create_time';
    return params;
  },
});

const [registerAnswerRankTable, { reload: reloadAnswerRank }] = useTable({
  rowKey: 'id',
  showIndexColumn: true,
  api: answerRecordRank,
  columns: answerRecordRankColumns(),
  maxHeight: 420,
  useSearchForm: false,
  showTableSetting: false,
  bordered: true,
  immediate: false,
  beforeFetch: params => {
    params.activityId = unref(activityId);
    return params;
  },
});

function handle(e) {
  fetchJoin = e;
}

function handleTabs(key) {
  const fetch = {
    join: fetchJoin,
    joinUnion: fetchUnion,
    blackList: reloadBlackList,
    answerRecord: reloadAnswer,
    answerRank: reloadAnswerRank,
    luckDraw: reloadLuckDrawRecord,
    signUpRecord: reloadSignUpRecord,
    questionnaire: reloadSignUpRecord,
    questionnaireReport: questionReportReload,
    integralTree: reloadIntegralTree,
  };

  fetch[key]?.();
}

function handleUnion(e) {
  fetchUnion = e;
}

function handleBlackList(e) {
  reloadBlackList = e;
}

function handleReloadSignUpRecord(e) {
  reloadSignUpRecord = e;
}

function handleIntegralTree(e) {
  reloadIntegralTree = e;
}

function handleReloadLuckDrawRecord(e) {
  reloadLuckDrawRecord = e;
}

function handleQuestionReportReload(e) {
  questionReportReload = e;
}

function handleRecord(record) {
  openModal(true, { isUpdate: true, disabled: true, record });
}

onMounted(async () => {
  //重置初始值
  record.value = JSON.parse(route.query?.record as string);

  activeKey.value = ActivityType.BASICINFO;

  name.value = unref(record)?.activityName;

  activityId.value = unref(record)?.activityId;

  const details = await getDetails({ activityId: unref(activityId) }, true);
  activityDetail.value = details;
  voteTypes.value = details?.voteInfo?.voteTypeConfigList || [];

  activityType.value = unref(record)?.activityMode;

  tabsObj.value = [];

  tabsObj.value.push({
    key: ActivityType.BASICINFO,
    tab: ActivityTypeZh[ActivityType.BASICINFO],
  });

  switch (unref(activityType)) {
    case ActivityType.SIGNUP:
    case ActivityType.COMPETITION:
    case ActivityType.FUN_COMPETITION:
    case ActivityType.VOLUNTEER_SERVICE:
    case ActivityType.FRIENDSHIP:
    case ActivityType.INTEREST_GROUP:
    case ActivityType.WOMEN:
      tabsObj.value.push({
        key: 'signUpRecord',
        tab: '报名记录',
      });
      break;
    case ActivityType.QUIZ:
      tabsObj.value.push({
        key: 'answerRecord',
        tab: '答题记录',
      });
      tabsObj.value.push({
        key: 'answerRank',
        tab: '得分排行',
      });
      break;
    case ActivityType.SURVEY:
      tabsObj.value.push({
        key: 'questionnaireReport',
        tab: '问卷报表',
      });
      tabsObj.value.push({
        key: 'questionnaire',
        tab: '问卷记录',
      });
      break;
    case ActivityType.INTEGRAL_TREE:
      tabsObj.value.push({
        key: 'integralTree',
        tab: '果树信息',
      });
      break;
    case ActivityType.MULTIPLE_VOTE:
    case ActivityType.VOTE:
      tabsObj.value.push({
        key: 'voteRank',
        tab: '排行榜',
      });
      break;
  }
  if ([ActivityType.SUMMER_COOLNESS, ActivityType.COUPON].includes(unref(activityType))) {
    tabsObj.value.push({
      key: 'ticketRecord',
      tab: '领取记录',
    });
  } else if (
    ![
      ActivityType.SIGNUP,
      ActivityType.INTEREST_GROUP,
      ActivityType.COMPETITION,
      ActivityType.FUN_COMPETITION,
      ActivityType.FRIENDSHIP,
      ActivityType.WOMEN,
      ActivityType.VOLUNTEER_SERVICE,
    ].includes(unref(activityType))
  ) {
    tabsObj.value.push({
      key: 'luckDraw',
      tab: '中奖记录',
    });
  }

  tabsObj.value.push(
    {
      key: 'join',
      tab: '参与用户',
    },
    {
      key: 'joinUnion',
      tab: '参与工会',
    },
    {
      key: 'blackList',
      tab: '黑名单',
    }
  );
});
</script>

<style lang="less" module>
.act-statistic {
  :global {
    overflow: auto !important;
    height: 88%;
    .ant-tabs {
      height: 100%;
      .ant-tabs-content {
        min-height: 100%;
      }
    }
  }
}
</style>
