<template>
  <div
    class="h-full"
    v-bind="$attrs"
  >
    <div
      :style="{ backgroundImage: `url(${modelBg})` }"
      class="h-full w-full bg-no-repeat bg-contain rounded-md pt-22 pb-4 bg-center"
      :class="className"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import modelBg from '@/assets/images/model_bg.png';

defineProps({ className: String });
</script>
