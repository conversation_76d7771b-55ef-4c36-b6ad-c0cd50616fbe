<template>
  <div
    :style="{ backgroundImage: `url(${defaultQBg})` }"
    class="w-full h-full bg-no-repeat bg-cover rounded-b-2xl"
  >
    <div class="flex justify-center items-center relative top-7 flex-col">

      <img
        :src="lackText"
        class="w-1/2 pt-8"
      />
    </div>
    <Prize :prizeList="lucks" />
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import defaultQBg from '@/assets/images/lotter/bg.jpg';
//import logo from '@/assets/images/lotter/logo.png';
import lackText from '@/assets/images/lotter/lack_text.png';
import Prize from './Prize.vue';

const props = defineProps({
  record: {
    type: Object as PropType<Recordable>,
  },
});

const lucks = computed(() => {
  return props.record?.luckDrawInfo?.prizeInfos || [];
});
</script>
