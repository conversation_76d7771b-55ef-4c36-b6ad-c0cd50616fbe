<template>
  <div
    :class="$style.prize"
    id="prize-container"
  >
    <div
      class="prize-list"
      ref="prizeWrap"
      :style="bgColor"
    >
      <div
        class="prize-item"
        v-for="(item, index) in prizeList"
        :style="prizeStyle(index)"
      >
        <img
          :src="`${userStore.getPrefix}${item.prizeImg}`"
          v-if="item.prizeImg"
          alt=""
        />
        <p>{{ item.prizeName }}</p>
      </div>
    </div>
    <div
      class="w-full h-full absolute bg-circle"
      ref="bgCircle"
    />
    <div
      class="btn-p"
      @click="start"
    ></div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref, reactive, nextTick } from 'vue';
import { useUserStore } from '@/store/modules/user';

const props = defineProps({
  prizeList: {
    type: Array as PropType<Recordable[]>,
    default: [],
  },
});

const userStore = useUserStore();

const state = reactive({
  isRunning: false, // 是否正在抽奖
  baseRunAngle: 360 * 5, // 总共转动角度 至少5圈
  prizeId: 0, // 中奖id
});
const prizeWrap = ref<Nullable<HTMLDivElement>>();

const bgCircle = ref<Nullable<HTMLDivElement>>();

// 计算绘制转盘背景
const bgColor = computed(() => {
  const _len = props.prizeList.length;
  const colorList = ['#FDF3AB', '#F0D4AF'];
  let colorVal = '';
  for (let i = 0; i < _len; i++) {
    colorVal += `${colorList[i % 2]} ${rotateAngle.value * i}deg ${rotateAngle.value * (i + 1)}deg,`;
  }
  return `
            background: conic-gradient(${colorVal.slice(0, -1)});
          `;
});

// 每个奖品布局
const prizeStyle = computed(() => {
  const _degree = rotateAngle.value;
  return i => {
    return `
    width: ${2 * 180 * Math.sin(((_degree / 2) * Math.PI) / 180)}px;
    height: 180px;
    transform: rotate(${_degree * i + _degree / 2}deg);
    transform-origin: 50% 100%;
    `;
  };
});

// 平均每个奖品角度
const rotateAngle = computed(() => {
  const _degree = 360 / props.prizeList.length;
  return _degree;
});

// 要执行总角度数
const totalRunAngle = computed(() => {
  return state.baseRunAngle + 360 - state.prizeId * rotateAngle.value - rotateAngle.value / 2;
});

// 获取随机数
const getRandomNum = () => {
  const num = Math.floor(Math.random() * props.prizeList.length);
  return num;
};

const start = () => {
  if (!state.isRunning) {
    state.isRunning = true;

    console.log('开始抽奖，后台请求中奖奖品');
    // 请求返回的奖品编号 这里使用随机数
    const prizeId = getRandomNum();

    console.log('中奖ID>>>', prizeId, props.prizeList[prizeId]);
    state.prizeId = prizeId;
    startRun();
  }
};

const startRun = () => {
  // 设置动效
  if (prizeWrap.value) {
    prizeWrap.value.style = `
            ${bgColor.value}
            transform: rotate(${totalRunAngle.value}deg);
            transition: all 5s ease;
          `;
    bgCircle.value &&
      (bgCircle.value.style = `
            transform: rotate(${-totalRunAngle.value}deg);
            transition: all 5s ease;
          `);
  }
  // 监听transition动效停止事件
  prizeWrap.value?.addEventListener('transitionend', stopRun);
};

const stopRun = e => {
  console.log(e);
  state.isRunning = false;
  if (prizeWrap.value) {
    prizeWrap.value.style = `
            ${bgColor.value}
            transform: rotate(${totalRunAngle.value - state.baseRunAngle}deg);
          `;
    bgCircle.value &&
      (bgCircle.value.style = `
           transform: rotate(${state.baseRunAngle - totalRunAngle.value}deg);
          `);
  }
};

onMounted(async () => {
  await nextTick();
  setTimeout(() => {
    prizeWrap.value &&
      (prizeWrap.value.style = `${bgColor.value} transform: rotate(-${rotateAngle.value / 2}deg)`);
  }, 500);
});

onUnmounted(() => {
  prizeWrap.value?.removeEventListener('transitionend', stopRun);
});
</script>

<style lang="less" module>
.prize {
  :global {
    width: 100%;
    height: 50%;
    position: relative;
    margin-top: 10vh;

    @apply flex justify-center items-center;

    .bg-circle {
      background-image: url('@/assets/images/lotter/zp_bg.png');
      background-repeat: no-repeat;
      background-size: cover;
      background-position: 50%;
    }

    .prize-list {
      width: 15vw;
      height: 15vw;
      border-radius: 50%;
      overflow: hidden;
      z-index: 1;
    }
    .prize-item {
      /*border: 2px solid red;*/
      position: absolute;
      left: 0;
      right: 0;
      top: -32px;
      margin: 0 auto;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    .prize-item img {
      width: 25%;
      height: 20%;
      margin: 0 auto;
    }

    .prize-item p {
      color: #7e250d;
      font-size: 10px;
      width: 100%;
      text-align: center;
      display: inline-block;
    }

    .btn-p {
      width: 3.53333rem;
      height: 4.06667rem;
      background: url('@/assets/images/lotter/cj_btn.png') no-repeat center / 100% 100%;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      cursor: pointer;
      z-index: 2;

      // &::before {
      //   content: '';
      //   width: 41px;
      //   height: 39px;
      //   background: url('@/assets/images/lotter/cj_btn.png') no-repeat center / 100% 100%;
      //   position: absolute;
      //   left: 0;
      //   right: 0;
      //   top: -33px;
      //   margin: auto;
      // }
    }
  }
}
</style>
