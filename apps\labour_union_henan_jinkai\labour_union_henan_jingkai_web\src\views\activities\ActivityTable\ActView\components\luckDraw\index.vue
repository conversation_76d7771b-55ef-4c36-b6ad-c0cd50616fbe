<template>
  <div
    class="h-full flex"
    :class="$style.luck"
  >
    <div
      v-for="item in number"
      class="h-full"
      :class="`w-1/${number}`"
    >
      <Phone className="px-26">
        <Home
          :cover="record?.appDetailsCover"
          :activityType="activityType"
          v-if="item === 1"
        />
        <ActDetail
          :record="record"
          v-if="item === 2"
          :activityType="activityType"
        />
        <Circle
          :record="record"
          v-if="item === 3"
        />
      </Phone>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Phone from '../Phone.vue';
import Home from '../quiz/Home.vue';
import ActDetail from '../quiz/ActDetail.vue';
import Circle from './Circle.vue';
import { ActivityType } from '@/views/activities/activities.d';

defineProps({
  number: Number,
  record: {
    type: Object as PropType<Recordable>,
  },
  activityType: String as PropType<ActivityType>,
});
</script>

<style lang="less" module>
.luck {
  :global {
  }
}
</style>
