<template>
  <div
    class="w-full h-full pl-2 pr-3"
    :class="$style['q-detail']"
  >
    <div class="h-9/10 overflow-y-auto gd">
      <div class="w-full bg-no-repeat bg-contain rounded-b-2xl banner-box">
        <Image
          :src="cover"
          :fallback="activitiesIng"
        />
        <div class="w-full">
          <span
            class="inline-block text-20px w-full overflow-hidden truncate"
            :title="record?.activityName"
            >{{ record?.activityName }}</span
          >
          <div class="pt-1 flex justify-between w-full">
            <span
              class="text-hex-[#999]"
              :title="record?.updateTime"
              >{{ record?.updateTime }}</span
            >
            <span
              class="rounded-lg text-white"
              :style="{
                border: `1px solid ${state.color}`,
                backgroundColor: state.color,
              }"
              >{{ state.name }}
            </span>
          </div>
        </div>
      </div>
      <div class="pt-2">
        <div v-if="record?.activityContent">
          <p class="text-16px font-semibold">活动介绍</p>
          <span v-html="record?.activityContent" />
        </div>
        <div>
          <p class="text-16px font-semibold">活动时间</p>
          <span
            >{{ fixDate(record?.activityStartTime) }}至{{ fixDate(record?.activityEndTime) }}</span
          >
        </div>
        <div v-if="record?.activityRules">
          <p class="text-16px font-semibold">活动规则</p>
          <span v-html="record?.activityRules" />
        </div>
        <div v-if="record?.participationMode">
          <p class="text-16px font-semibold">参与方式</p>
          <span v-html="record?.participationMode" />
        </div>
        <div v-if="record?.activityRemark">
          <p class="text-16px font-semibold">活动备注</p>
          <span v-html="record?.activityRemark" />
        </div>
      </div>
    </div>
    <div class="h-1/10 w-full flex justify-center items-center">
      <div class="w-1/2 flex justify-evenly">
        <Icon
          icon="mdi:comment-processing-outline"
          :size="22"
          v-if="record?.commentState === 'Y'"
        />
        <Icon
          icon="icon-park-outline:good-two"
          :size="22"
        />
        <Icon
          icon="uiw:weixin"
          :size="22"
          color="#15D36A"
        />
        <!-- <div>{{ 0 }}</div> -->
      </div>
      <div
        class="w-1/2"
        v-if="activityType !== ActivityType.INCLUSIVE_OTHER"
      >
        <div class="detail_btn_btn">开始{{ text }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { computed } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { Image } from 'ant-design-vue';
import { Icon } from '@monorepo-yysz/ui';

import activitiesIng from '@/assets/images/act/act_top.jpg';
import { ActivityType, ActivityText } from '@/views/activities/activities.d';

const props = defineProps({
  record: {
    type: Object as PropType<Recordable>,
  },
  activityType: String as PropType<ActivityType>,
});

const userStore = useUserStore();

const cover = computed(() => {
  return props.record?.appCover ? `${userStore.getPrefix}${props.record?.appCover}` : '';
});

const text = computed(() => {
  return ActivityText[`${props.activityType}`] || '';
});

const state = computed(() => {
  const { activityStartTime, activityEndTime } = props.record || {};
  const isBefore = dayjs().diff(dayjs(activityStartTime));
  const isAfter = dayjs().diff(dayjs(activityEndTime));

  return {
    name: isBefore < 0 ? '未开始' : isAfter > 0 ? '已结束' : '进行中',
    color: isBefore < 0 ? '#d9a970' : isAfter > 0 ? '#999' : '#68c29d',
  };
});

const fixDate = date => dayjs(date).format('YYYY-MM-DD HH:mm');
</script>

<style lang="less" module>
.q-detail {
  :global {
    .banner-box {
      .ant-image {
        .ant-image-img {
          border-radius: 5px;
        }
      }
    }

    .detail_btn_btn {
      text-align: center;
      font-size: 18px;
      background: linear-gradient(90deg, #fe9863, #bf1a05);
      border-radius: 0.53333rem;
      color: #fff;
    }
    .gd {
      &::-webkit-scrollbar {
        width: 0 !important;
      }
    }
  }
}
</style>
