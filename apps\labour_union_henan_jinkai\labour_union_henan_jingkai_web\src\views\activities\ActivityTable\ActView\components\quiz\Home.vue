<template>
  <div
    class="first-content w-full h-full bg-no-repeat bg-cover rounded-b-2xl"
    :style="{ backgroundImage: `url(${homeCover})` }"
    :class="$style.home"
  >
    <div
      class="absolute top-3/4 btns"
      :class="ifLuck ? '' : 'w-11/100 '"
    >
      <div class="btn_btn">开始{{ text }}</div>
      <div
        class="flex items-center mt-5"
        :class="ifLuck ? 'justify-between' : 'justify-center'"
      >
        <div class="px-8px text-white border mr-1 rounded-l">
          <Icon
            icon="mdi:file-document-outline"
            :size="18"
          />
          <span>活动详情</span>
        </div>
        <div
          class="px-8px text-white border ml-1 rounded-l"
          v-if="ifLuck"
        >
          <Icon icon="ion:ticket-outline" />
          <span>中奖记录</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user';
import { Icon } from '@monorepo-yysz/ui';

import defaultQBg from '@/assets/images/lotter/bg.jpg';
import { computed } from 'vue';
import { ActivityType, ActivityText } from '@/views/activities/activities.d';

const props = defineProps({
  cover: String,
  activityType: String as PropType<ActivityType>,
  record: {
    type: Object as PropType<Recordable>,
  },
});

const userStore = useUserStore();

const ifLuck = computed(() => {
  return props.record?.luckDraw === 'Y';
});

const homeCover = computed(() => {
  return props.cover
    ? props.cover?.includes('http')
      ? props.cover
      : `${userStore.getPrefix}${props.cover}`
    : defaultQBg;
});
const text = computed(() => {
  return ActivityText[`${props.activityType}`];
});
</script>

<style lang="less" module>
.home {
  :global {
    .btns {
      transform: translateX(45%);
    }
    .btn_btn {
      display: flex;
      align-items: center;
      font-size: 26px;
      color: #fff;
      background: url(/@/assets/images/stater_button.png) 100% no-repeat;
      background-size: 100% 100%;
      justify-content: space-evenly;
    }
  }
}
</style>
