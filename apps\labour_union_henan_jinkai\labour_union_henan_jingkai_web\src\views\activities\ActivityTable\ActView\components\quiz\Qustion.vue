<template>
  <div
    class="first-content w-full h-full bg-no-repeat bg-cover rounded-b-2xl pt-7"
    :style="{ backgroundImage: `url(${answers_bg})` }"
    :class="$style.questions"
  >
    <div
      :style="{ backgroundImage: `url(${ans_frame})` }"
      class="bg-contain w-full h-60px bg-no-repeat text-18px !text-hex-[#fff] text-16px bg-center relative z-10 flex justify-center items-center"
    >
      {{ `第${currentNum || 1}/${questions.length || 1}题` }}
    </div>
    <div class="qes-content relative top-[-58px]">
      <div>{{ selected?.topicContent || '' }}{{ opType }}</div>
      <div>
        <div v-for="(item, index) in selected?.options">
          <div class="answer_box">
            <span>{{ charCode[index] }}.{{ item.optionContent || '' }}</span>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div
        class="qes-next"
        @click="handleClick(true)"
        v-show="isEq"
      >
        下一题
      </div>
      <div
        class="qes-next"
        @click="handleClick(false)"
        v-show="isBefore"
      >
        上一题
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, unref, watch, onMounted } from 'vue';
import answers_bg from '@/assets/images/answers_bg.jpg';
import ans_frame from '@/assets/images/ans_frame.png';

interface Options {
  optionContent?: string;
}

interface Selected extends Recordable {
  options: Options[];
}

const props = defineProps({
  record: {
    type: Object as PropType<Recordable>,
  },
});

const selected = ref<Selected>({ options: [] });

const currentNum = ref<number>(0);

const isBefore = computed(() => {
  return (
    unref(currentNum) !== 1 &&
    unref(currentNum) !== 0 &&
    unref(currentNum) <= unref(questions).length
  );
});

const isEq = computed(() => {
  return unref(currentNum) !== unref(questions).length;
});

const questions = computed(() => {
  return props.record?.vieAnswerInfo?.topicInfoList || [];
});

//26个大写
const charCode = computed(() => {
  const char: string[] = [];
  for (let index = 0; index < 26; index++) {
    char.push(String.fromCharCode(65 + index));
  }
  return char;
});

const opType = computed(() => {
  return unref(selected)?.optionType === 'radio'
    ? '（单选）'
    : unref(selected)?.optionType === 'select'
      ? '（多选）'
      : '';
});

function handleClick(flg) {
  flg
    ? unref(currentNum) < unref(questions)?.length && (currentNum.value += 1)
    : unref(currentNum) > 1 && (currentNum.value -= 1);
}

watch(
  currentNum,
  () => {
    unref(currentNum) > 0 && (selected.value = unref(questions)[unref(currentNum) - 1]);
  },
  { deep: true }
);

onMounted(() => {
  setTimeout(() => {
    currentNum.value = 1;
  }, 500);
});
</script>

<style lang="less" module>
.questions {
  :global {
    .qes-content {
      width: calc(100% - 1.5rem);
      padding: 2.5rem 0.53333rem 0.53333rem 1.06667rem;
      background: #fff;
      border-radius: 0.5rem;
      margin: 1rem auto;
      box-sizing: border-box;
      min-height: 60%;
    }
    .qes-next {
      width: 60%;
      background: linear-gradient(#ee6631, #e03a27);
      padding: 0.21333rem 0;
      font-size: 0.9rem;
      color: #fff;
      text-align: center;
      border-radius: 0.8rem;
      margin: 0.8rem auto;
      position: relative;
      top: -58px;
      cursor: pointer;
    }
    .answer_box {
      background-color: #f5f1f1;
      display: flex;
      justify-content: space-between;
      margin: 0.4rem auto;
      font-size: 1rem;
      padding: 0.26667rem;
      border-radius: 0.08rem;
      align-items: center;
    }
  }
}
</style>
