<template>
  <div class="h-full flex">
    <div
      v-for="item in number"
      class="h-full"
      :style="{ width: `${divide(100, number || 1)}%` }"
    >
      <Phone className="px-26">
        <Home
          :cover="record?.appDetailsCover"
          :record="record"
          :activityType="activityType"
          v-if="item === 1"
        />
        <ActDetail
          :record="record"
          v-if="item === 2"
          :activityType="activityType"
        />
        <Qustion
          :record="record"
          v-if="item === 3"
        />
      </Phone>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Phone from '../Phone.vue';
import Home from './Home.vue';
import ActDetail from './ActDetail.vue';
import Qustion from './Qustion.vue';
import { ActivityType } from '@/views/activities/activities.d';
import { divide } from 'lodash-es';

defineProps({
  number: Number,
  record: {
    type: Object as PropType<Recordable>,
  },
  activityType: String as PropType<ActivityType>,
});
</script>
