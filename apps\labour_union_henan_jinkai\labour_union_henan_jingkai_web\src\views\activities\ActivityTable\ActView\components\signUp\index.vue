<template>
  <div class="h-full flex">
    <div
      v-for="_ in number"
      class="h-full"
      :class="`w-full`"
    >
      <Phone className="px-[41.5rem]">
        <ActDetail
          :record="record"
          :activityType="activityType"
        />
      </Phone>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ActivityType } from '@/views/activities/activities';
import Phone from '../Phone.vue';
import ActDetail from '../quiz/ActDetail.vue';

defineProps({
  number: Number,
  record: {
    type: Object as PropType<Recordable>,
  },
  activityType: String as PropType<ActivityType>,
});
</script>
