<template>
  <div class="h-full flex">
    <div
      v-for="item in number"
      class="h-full"
      :class="`w-1/${number}`"
    >
      <Phone className="px-[15.2rem]">
        <Home
          :cover="record?.appDetailsCover"
          :record="record"
          :activityType="activityType"
          v-if="item === 1"
        />
        <ActDetail
          :record="record"
          :activityType="activityType"
          v-if="item === 2"
        />
      </Phone>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ActivityType } from '@/views/activities/activities';
import Phone from '../Phone.vue';
import ActDetail from '../quiz/ActDetail.vue';
import Home from '../quiz/Home.vue';

defineProps({
  number: Number,
  record: {
    type: Object as PropType<Recordable>,
  },
  activityType: String as PropType<ActivityType>,
});
</script>
