<script lang="tsx">
import { computed, defineComponent, onMounted, ref, unref, useCssModule } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ActivityType, ActivityTypeZh, ActivityView } from '../../activities.d';
import { getDetails } from '@/api/activities';
import { PageWrapper } from '@/components/Page';
import { InclusiveOther, LuckDraw, Questionnaire, Quiz, SignUp, Vote } from './components';

export default defineComponent({
  name: 'ActView',
  setup(_) {
    const router = useRouter();

    const route = useRoute();

    const style = useCssModule();

    const name = ref<string>('');

    const type = ref<ActivityType>(ActivityType.QUIZ);

    const record = ref<Recordable>();

    const comps = computed<Recordable>(() => {
      return {
        quiz: (
          <Quiz
            number={3}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
        luckDraw: (
          <LuckDraw
            number={3}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
        inclusiveOther: (
          <InclusiveOther
            number={1}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
        signUp: (
          <SignUp
            number={1}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
        competition: (
          <SignUp
            number={1}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
        funCompetition: (
          <SignUp
            number={1}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
        volunteerService: (
          <SignUp
            number={1}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
        friendship: (
          <SignUp
            number={1}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
        vote: (
          <Vote
            number={2}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
        questionnaire: (
          <Questionnaire
            number={3}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
        inclusiveTicket: (
          <InclusiveOther
            number={1}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
        inclusiveSignUp: (
          <InclusiveOther
            number={1}
            record={unref(record)}
            activityType={unref(type)}
          />
        ),
      };
    });

    const title = computed(() => {
      return `预览${ActivityTypeZh[unref(type)]}活动--${unref(name)}`;
    });

    onMounted(async () => {
      const { activityId, activityName, activityType } = route.query;

      name.value = (activityName || '') as string;
      type.value = activityType as ActivityType;

      record.value = await getDetails({ activityId: activityId }, true);
    });

    return () => {
      return (
        <PageWrapper
          onBack={() => router.go(-1)}
          title={unref(title)}
          contentClass={style['act-view']}
        >
          {unref(comps)[ActivityView[unref(type)]]}
        </PageWrapper>
      );
    };
  },
});
</script>

<style lang="less" module>
.act-view {
  :global {
    height: calc(100vh - 140px);
    width: 100%;
    margin: 0px !important;
    padding: 15px;
  }
}
</style>
