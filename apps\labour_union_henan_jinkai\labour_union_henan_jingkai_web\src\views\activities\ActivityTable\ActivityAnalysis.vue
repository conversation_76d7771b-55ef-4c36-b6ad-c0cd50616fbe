<template>
  <div
    ref="chartRef"
    :style="{ height, width }"
  ></div>
</template>

<script lang="ts" setup>
import { onMounted, ref, Ref, useAttrs, watch } from 'vue';
import { useECharts } from '@/hooks/web/useECharts';
import { ActivityType, basicProps } from '../activities.d';
import { map } from 'lodash-es';
const emit = defineEmits(['clickEcharts']);
const props = defineProps({
  ...basicProps,
});

const attrs = useAttrs();

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions,getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

onMounted(() => {
  setEchart(props.sourceData, props.pieData);
});

watch(
  () => props.sourceData,
  val => {  
    setEchart(val, null);
  },
  {
    deep: true,
  }
);

watch(
  () => props.pieData,
  val => {
    setEchart(null, val);
  },
  {
    deep: true,
  }
);

function setLineEcharts(dataSource) {
  setOptions(dataSource);
  // 区域数据点击事件
  if(props.sourceData?.type=='area'){
    getInstance().on('click', function (params) {
    emit("clickEcharts",params)
  });
  }
}

function setEchart(sourceData, pieData) {
  if (attrs.type === ActivityType.LOTTERY) {
    setPie(pieData);
  } else {
    setLineEcharts(sourceData);
  }
}

function setPie(dataSource) {
  
  setOptions({
    tooltip: {
      trigger: 'item',
    },
    legend: {
      top: '5%',
      left: 'center',
    },
    series: [
      {
        name: '奖品占比',
        type: 'pie',
        radius: [30, 100],
        center: ['50%', '50%'],
        roseType: 'area',
        itemStyle: {
          borderRadius: 8,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        data: map(dataSource, v => {
          return {
            name: v.prizeName,
            value: v.count,
          };
        }),
      },
    ],
  });
}
</script>
