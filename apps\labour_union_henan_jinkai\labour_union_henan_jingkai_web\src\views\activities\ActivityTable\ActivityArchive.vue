<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleAdd"
          :auth="titleAuth"
          >新增活动归档</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleDetails.bind(null, record),
                auth: recordAuth.view,
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                disabled: record.state === 'publish',
                onClick: handleModify.bind(null, record),
                auth: recordAuth.modify,
              },
              {
                icon: 'mdi:report-timeline',
                label: '报告',
                type: 'default',
                onClick: handleReport.bind(null, record),
                auth: recordAuth.report,
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ActivityArchiveModal
      @register="registerModule"
      width="50%"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { useAttrs, computed, unref, createVNode } from 'vue';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { archivesList, archivesAdd } from '@/api/activities';
import { archiveColum, archiveSearchFormSchema } from '../activity';
import { isArray, map } from 'lodash-es';
import { useModal } from '@/components/Modal';
import ActivityArchiveModal from './ActivityArchiveModal.vue';
import { Modal } from 'ant-design-vue';
import { CheckCircleOutlined, CloseCircleFilled } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import dayjs from "dayjs";

const attrs = useAttrs();

const router = useRouter();

const type = computed(() => {
  return attrs.type;
});

//权限
const titleAuth = computed(() => {
  if (isArray(attrs?.titleAuth)) return attrs?.titleAuth as string[];
  return (attrs?.titleAuth || '') as string;
});

const columnAuth = computed(() => {
  return attrs?.columnAuth as string[];
});

const recordAuth = computed(() => {
  return attrs?.recordAuth as { view: string; modify: string; report: string };
});

const column = computed(() => {
  return archiveColum(unref(type));
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: column,
  authInfo: unref(titleAuth),
  formConfig: {
    labelWidth: 120,
    schemas: archiveSearchFormSchema(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  searchInfo: {
    activityCategory: unref(type),
  },
  beforeFetch: function (params) {
    params.activityCategory = unref(type);
    if (params?.activityDate) {
      params.startTime = params?.activityDate[0];
      params.endTime = params?.activityDate[1];
    }
    return params;
  },
  afterFetch: data => {
    const arr = map(data, v => {
      const { activityArchives, ...other } = v;
      v = { ...other, ...activityArchives };
      return v;
    });

    return arr;
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  api: archivesList,
  showIndexColumn: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    fixed: undefined,
    width: 260,
    auth: unref(columnAuth),
  },
});

const [registerModule, { openModal, closeModal }] = useModal();

function handleAdd() {
  openModal(true, { disabled: false, type: unref(type) });
}

function handleDetails(record) {
  openModal(true, { isUpdate: true, disabled: true, record, type: unref(type) });
}

//编辑
function handleModify(record: Recordable) {
  openModal(true, { isUpdate: true, record, disabled: false, type: unref(type) });
}

//编辑
function handleReport(record: Recordable) {
  const {activityDate,activityEndDate} = record
  const url = router.resolve({
    path: '/activity-report',
    query: { activityId: record.activityId,
      activityName: record.activityName,activityStartTime:dayjs(activityDate).format('YYYY-MM-DD 00:00:00'),
      activityEndTime:dayjs(activityEndDate).format('YYYY-MM-DD 23:59:59') },
  }).href; // 根据路由名称生成 URL
  window.open(url, '_blank'); // 打开新窗口
}

function handleSuccess({ values }) {
  archivesAdd(values).then(res => {
    const { code, message } = res;
    if (code === 200) {
      Modal.success({
        title: '提示',
        icon: createVNode(CheckCircleOutlined),
        content: '操作成功！',
        okText: '确认',
        closable: true,
      });
      closeModal();
      reload();
    } else {
      Modal.error({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: `操作失败！${message} `,
        okText: '确认',
        closable: true,
      });
    }
  });
}
</script>
