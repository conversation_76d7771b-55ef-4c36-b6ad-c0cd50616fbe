<template>
  <BasicModal
    @register="registerModule"
    :can-fullscreen="false"
    :title="title"
    :show-ok-btn="false"
    @ok="handleSuccess"
    :wrap-class-name="$style['archive-modal']"
  >
    <BasicForm
      @register="registerFrom"
      :class="disabledClass"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { archiveFormSchema } from '../activity';
import { computed, ref, unref } from 'vue';
import dayjs from 'dayjs';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>({});

const disabled = ref(false);

const isUpdate = ref(false);

const type = ref('');

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record).activityName || ''}--详情`
    : unref(isUpdate)
      ? `编辑${unref(record).activityName || ''}`
      : '新增活动归档';
});

const fromSchema = computed(() => {
  return archiveFormSchema(unref(type),unref(record)?.activityId);
});

const [registerModule, { setModalProps }] = useModalInner(async function (data) {
  await resetFields();
  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  type.value = data.type;

  record.value = data.record;

  await resetSchema(archiveFormSchema(unref(type), unref(record)?.activityId))
  if (unref(isUpdate)) {
    const {activityDate,activityEndDate} = data.record
    await setFieldsValue({
      ...data.record,
      dateRange:activityDate&&activityEndDate?[dayjs(activityDate).format('YYYY-MM-DD'),dayjs(activityEndDate).format('YYYY-MM-DD')] :[],
      filePath: data.record.filePath ? data.record.filePath.split(',') : [],
    });
  }

  setModalProps({
    confirmLoading: false,
    showOkBtn: !data.disabled,
  });

  setProps({
    disabled: unref(disabled),
  });
});

const [registerFrom, { resetFields, validate, setProps, setFieldsValue,resetSchema }] = useForm({
  labelWidth: 200,
  //schemas: fromSchema,
  showActionButtonGroup: false,
});

async function handleSuccess() {
  const {dateRange,...values} = await validate();

  let params = {
    ...values,
    activityDate:dateRange[0],
    activityEndDate:dateRange[1],
    filePath: values.filePath ? values.filePath.join(',') : undefined,
  };
  if (unref(isUpdate)) {
    params = {
      ...unref(record),
      ...params,
    };
  }

  emit('success', {
    values: {
      ...params,
    },
  });
}
</script>

<style lang="less" module>
.archive-modal {
  :global {
    .ant-form {

    }
  }
}
</style>
