<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :canFullscreen="false"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts">
import { defineComponent, ref, unref } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { Row, Col, Divider, RadioGroup } from 'ant-design-vue';
import { useForm, BasicForm } from '@/components/Form';
import { joinAuditFormSchema } from '../activity';

export default defineComponent({
  name: 'ActivityAuditModal',
  components: { BasicModal, Row, Col, Divider, BasicForm, RadioGroup },
  emits: ['register', 'success', 'cancel'],
  setup(_, { emit }) {
    const autoId = ref([]);

    const title = ref('');

    const sourceId = ref('');

    const [registerForm, { resetFields, validate }] = useForm({
      labelWidth: 100,
      schemas: joinAuditFormSchema(),
      showActionButtonGroup: false,
    });

    const [registerModal, {}] = useModalInner(async data => {
      await resetFields();
      autoId.value = data.ids;
      sourceId.value = data.sourceId;
      title.value = `审核参与用户--${data.record.userName}`;
    });

    async function handleSubmit() {
      const values = await validate();
      emit('success', {
        values: { ...values, autoIds: unref(autoId), sourceId: unref(sourceId) },
      });
    }

    return {
      registerModal,
      registerForm,
      handleSubmit,
      title,
    };
  },
});
</script>
