<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :canFullscreen="false"
    okText="回复"
    :showCancelBtn="false"
  >
    <div class="chatAppBody">
      <div class="chatBox">
        <div class="chatRow">
          <Avatar
            class="chatAvatar"
            :size="40"
          >
            <template #icon><UserOutlined /></template>
          </Avatar>
          <div class="chatMsgContent">
            <div class="chatUsername">{{ record?.createUser || '' }}</div>
            <div class="chatContent">{{ record?.content || '' }}</div>
          </div>
        </div>
        <div
          class="chatRow chatRowMe"
          v-for="item in replyList"
        >
          <div class="chatMsgContent">
            <div class="chatUsername">{{ item?.createUser || '' }}</div>
            <div class="chatContent">{{ item?.content || '' }}</div>
          </div>
          <Avatar
            class="chatAvatar"
            :size="40"
          >
            <template #icon><UserOutlined /></template>
          </Avatar>
        </div>
      </div>
    </div>
    <Input.TextArea
      v-model:value="value"
      :auto-size="{ minRows: 4, maxRows: 8 }"
      placeholder="请输入回复内容"
      :maxlength="200"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { Avatar, Input } from 'ant-design-vue';
import { UserOutlined } from '@ant-design/icons-vue';
import { useUserStore } from '@/store/modules/user';
import { replyComments } from '@/api/activities';
import { orderBy } from 'lodash-es';
import { useMessage } from '@monorepo-yysz/hooks';

const emit = defineEmits(['register', 'success']);

const { createErrorModal, createWarningModal } = useMessage();

const userStore = useUserStore();

const title = computed(() => {
  return `回复${unref(record)?.createUser || ''}评价`;
});

const value = ref<string | undefined>('');

const record = ref();

const replyList = ref<Recordable[]>([]);

const [registerModal, { setModalProps }] = useModalInner(async data => {
  record.value = data.record;

  replyList.value = data.record?.replyList
    ? orderBy(data.record?.replyList, ['createTime'], ['asc'])
    : [];

  value.value = '';

  setModalProps({
    confirmLoading: false,
  });
});

function handleSubmit() {
  if (!value.value) {
    createWarningModal({ content: '请输入回复内容！' });
    return false;
  }
  replyComments({
    sourceId: unref(record).sourceId,
    commentType: unref(record).commentType,
    content: unref(value),
    pid: unref(record).autoId,
  }).then(async res => {
    const { code, message } = res;
    if (code === 200) {
      replyList.value.push({
        createUser: userStore.getUserInfo.nickname,
        content: unref(value),
      });
      value.value = undefined;
      await emit('success');
    } else {
      createErrorModal({ content: `回复失败!${message}` });
    }
  });
}
</script>

<style lang="less" scoped>
.chatAppBody {
  display: flex;
  flex-direction: column;
  height: 50vh;
  overflow-y: scroll;
  background-color: #f1f5f8;
}
.chatTitle {
  background: #fff;
}
.chatBox {
  flex: 1;
  padding: 0 5px;
}
.chatBottom {
  background: #fff;
}
.chatRow {
  display: flex;
  align-items: flex-end;
  margin: 5px 0px;
}
.chatAvatar {
  margin-right: 5px;
  flex-shrink: 0;
}
.chatUsername {
  font-size: 12px;
  white-space: nowrap;
  color: #999;
  margin-bottom: 2px;
}
.chatContent {
  border-radius: 10px 10px 10px 0px;
  padding: 10px;
  background-color: rgb(255, 255, 255);
  box-shadow:
    0 5px 30px rgb(50 50 93 / 8%),
    0 1px 3px rgb(0 0 0 / 5%);
  font-size: 14px;
  word-break: break-all;
  line-height: 21px;
}
.chatRowMe {
  justify-content: flex-end;
}
.chatRowMe .chatContent {
  border-radius: 10px 10px 0px 10px;
}
</style>
