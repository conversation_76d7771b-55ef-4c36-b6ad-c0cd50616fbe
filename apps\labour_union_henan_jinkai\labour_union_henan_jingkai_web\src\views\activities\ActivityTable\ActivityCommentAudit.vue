<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :canFullscreen="false"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts">
import { computed, defineComponent, ref, unref } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { Row, Col, Divider, RadioGroup } from 'ant-design-vue';
import { useForm, BasicForm } from '@/components/Form';
import { commentAuditFormItem } from '../activity';

export default defineComponent({
  name: 'CommentAuditModal',
  components: { BasicModal, Row, Col, Divider, BasicForm, RadioGroup },
  emits: ['register', 'success', 'cancel'],
  setup(_, { emit }) {
    const name = ref('');

    const commentsIds = ref<string[]>([]);

    const title = computed(() => {
      return `${unref(name) ? `${unref(name)}--` : ''}评论审核`;
    });

    const [registerForm, { resetFields, validate }] = useForm({
      labelWidth: 100,
      schemas: commentAuditFormItem(),
      showActionButtonGroup: false,
    });

    const [registerModal, { setModalProps }] = useModalInner(async data => {
      await resetFields();
      commentsIds.value = [];
      if (data.record) {
        name.value = data.record.userName;
      }
      commentsIds.value = data.commentsIds;
      setModalProps({
        confirmLoading: false,
      });
    });

    async function handleSubmit() {
      try {
        const values = await validate();
        emit('success', {
          values: {
            ...values,
            commentsIds: unref(commentsIds),
          },
        });
      } catch (error) {
        setModalProps({
          confirmLoading: true,
        });
      }
    }

    return {
      registerModal,
      registerForm,
      handleSubmit,
      title,
    };
  },
});
</script>
