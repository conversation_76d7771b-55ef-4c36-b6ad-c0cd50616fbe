<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增活动类型
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/inclusiveActivityType/modify',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/inclusiveActivityType/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ActivityTypeModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
    >
    </ActivityTypeModal>
  </div>
</template>

<script lang="ts" setup>
import ActivityTypeModal from './ActivityTypeModal.vue';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { typeColumns } from '@/views/activities/activity';
import { addDictionary, delDictionary, queryDictionary } from '@/api/system/dictionary';
import { computed, unref, useAttrs } from 'vue';
import { useMessage } from '@monorepo-yysz/hooks';

const attrs = useAttrs();

const type = computed<any>(() => {
  return attrs.type;
});

//权限
const add = computed<any>(() => attrs.add);
const modify = computed<any>(() => attrs.modify);
const del = computed<any>(() => attrs.del);

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: typeColumns(),
  authInfo: unref(add),
  showIndexColumn: false,
  api: queryDictionary,
  beforeFetch: params => {
    params.groupCode = unref(type)?.groupCode;
    params.orderBy = 'order_num';
    params.sortType = 'asc';
    return params;
  },
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',

    fixed: undefined,
    auth: [unref(modify), unref(del)],
  },
});

//新增
function handleClick() {
  openModal(true, { isUpdate: false });
}

//编辑
function handleEdit(record) {
  openModal(true, { isUpdate: true, record });
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.dictName}`,
    onOk: function () {
      delDictionary(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ isUpdate, values }) {
  if (!isUpdate) {
    const { groupCode, groupName } = unref(type);
    values.groupCode = groupCode;
    values.groupName = groupName;
  }
  addDictionary(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
