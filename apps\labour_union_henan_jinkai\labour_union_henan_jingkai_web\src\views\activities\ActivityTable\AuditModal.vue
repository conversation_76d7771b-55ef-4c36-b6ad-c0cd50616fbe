<template>
  <BasicModal
    @register="registerModal"
    @ok="handleSuccess"
    title="审核"
    :wrapClassName="$style['top-modal']"
    v-bind="$attrs"
  >
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { useForm, BasicForm } from '@/components/Form';
import { auditModalForm } from './push';
import { useDictionary } from '@/store/modules/dictionary';
import { useModalInner, BasicModal } from '@/components/Modal';
import { RadioGroup, DatePicker } from 'ant-design-vue';

export default defineComponent({
  components: { BasicForm, BasicModal, RadioGroup, DatePicker },
  emits: ['register', 'success'],
  setup(_, { emit }) {
    const dictionary = useDictionary();

    const autoId = ref('');

    const [registerForm, { validate, resetFields, setFieldsValue }] = useForm({
      labelWidth: 100,
      schemas: auditModalForm,
      showActionButtonGroup: false,
    });

    const [registerModal, {}] = useModalInner(async data => {
      await resetFields();
      autoId.value = data.autoId || data.record?.autoId;
      if (data.record) {
        setFieldsValue({
          ...data.record,
        });
      }
    });

    async function handleSuccess() {
      const values = await validate();
      emit('success', { values, autoId: autoId.value });
    }

    return {
      dictionary,
      handleSuccess,
      registerForm,
      registerModal,
    };
  },
});
</script>

<style lang="less" module>
.top-modal {
  :global {
  }
}
</style>
