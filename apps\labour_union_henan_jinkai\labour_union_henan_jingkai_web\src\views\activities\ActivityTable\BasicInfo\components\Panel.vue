<template>
  <CollapsePanel :header="header">
    <Row>
      <Col
        class="text-sm leading-9"
        :span="16"
        >注:背景图规格大小为(<span class="text-red-500">{{ imgSizeText }}</span
        >)
      </Col>
      <Col
        :span="8"
        v-if="!readOnly"
      >
        <a-button
          type="text"
          danger
          class="!text-sm"
          @click="handleDeleteImg"
          v-if="!readOnly"
          >删除
        </a-button>
      </Col>
    </Row>
    <div
      class="cursor-pointer"
      :class="class"
    >
      <CropperImg
        :value="value"
        :img-size="imgSize"
        :operate-type="operateType"
        :disabled="readOnly"
        :errorImg="errorImg"
        @change="handleChange"
      />
    </div>
  </CollapsePanel>
</template>

<script lang="ts" setup>
import { CollapsePanel, Col, Row } from 'ant-design-vue';
import CropperImg from '@/components/Cropper/src/CropperImg.vue';
import { ref } from 'vue';

defineProps({
  imgSize: {
    type: Number,
    default: 1,
  },
  imgSizeText: {
    type: String,
  },
  operateType: {
    type: Number,
    default: 0,
  },
  value: {
    type: String,
  },
  header: {
    type: String,
  },
  class: {
    type: String,
  },
  readOnly: {
    type: Boolean,
  },
  errorImg: {
    type: String,
  },
});

const emit = defineEmits(['change', 'delete']);

const img = ref<string>();

//删除图片
function handleDeleteImg() {
  // deleteFile({ filenames: [unref(img)] }).then(() => {
  emit('delete');
  // })
}

function handleChange(val) {
  img.value = val;
  emit('change', { val });
}
</script>
