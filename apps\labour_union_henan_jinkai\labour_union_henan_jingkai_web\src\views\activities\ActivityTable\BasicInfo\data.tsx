import { FormSchema } from '@/components/Form';
import { h } from 'vue';
import { Tinymce } from '@/components/Tinymce/index';
import {
  ActivityDocAddr,
  ActivitySettingZh,
  ActivityType,
  BigActivityType,
} from '../../activities.d';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { isObject, map } from 'lodash-es';
import { simpleList } from '@/api/userGroup';
import { list } from '@/api/interestGroupManage/groupLabel';

// 需要联系人信息的活动类型
const CONTACT_ACTIVITY_TYPES = [
  ActivityType.SIGNUP,
  ActivityType.COMPETITION,
  ActivityType.FUN_COMPETITION,
  ActivityType.WOMEN,
  ActivityType.FRIENDSHIP,
  ActivityType.VOLUNTEER_SERVICE,
];

// 需要地址的活动类型
const ADDRESS_ACTIVITY_TYPES = [
  ActivityType.SIGNUP,
  ActivityType.INTEREST_GROUP,
  ActivityType.COMPETITION,
  ActivityType.FUN_COMPETITION,
  ActivityType.FRIENDSHIP,
  ActivityType.VOLUNTEER_SERVICE,
  ActivityType.WOMEN,
];

// 不需要积分设置的活动类型
const NO_INTEGRAL_ACTIVITY_TYPES = [
  ActivityType.WALK,
  ActivityType.COUPON,
  ActivityType.VOTE,
  ActivityType.MULTIPLE_VOTE,
];

// 不显示活动类型选择的活动类型
const HIDE_ACTIVITY_TYPE_SELECTION = [
  ActivityType.BIRTHDAY,
  ActivityType.WALK,
  ActivityType.INTEREST_GROUP,
  ActivityType.VOLUNTEER_SERVICE,
  ActivityType.FUN_COMPETITION,
  ActivityType.SUMMER_COOLNESS,
  ActivityType.WOMEN,
];

// 有报名配置的活动类型
const REGISTRATION_CONFIG_ACTIVITY_TYPES = [
  ActivityType.BLUE_VEST,
  ActivityType.COMPETITION,
  ActivityType.FUN_COMPETITION,
  ActivityType.FRIENDSHIP,
  ActivityType.VOLUNTEER_SERVICE,
  ActivityType.INTEREST_GROUP,
  ActivityType.WOMEN,
];

// 不显示配置的活动类型
const NO_CONFIG_ACTIVITY_TYPES = [ActivityType.EDUCATION_AID, ActivityType.INCLUSIVE_OTHER];

export const modalFormSchema = (
  activityType: ActivityType,
  {
    ifQuizLabel,
  }: {
    ifQuizLabel: string;
  },
  bigActivityType?: string
): FormSchema[] => {
  const userStore = useUserStore();
  const dictionary = useDictionary();
  const acTypeGroupCode = `${BigActivityType[activityType]}ActivityType`;
  const userInfo = userStore.getUserInfo;

  // 特定公司ID（省总）
  const PROVINCIAL_COMPANY_ID = '6650f8e054af46e7a415be50597a99d5';
  const isProvincialUser = userInfo.companyId === PROVINCIAL_COMPANY_ID;

  return [
    // 基本信息
    {
      field: 'activityName',
      required: true,
      label: '活动名称',
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入活动名称,不能超过100个字符',
        showCount: true,
        maxlength: 100,
      },
    },

    // 参与用户配置
    {
      field: 'customerType',
      required: true,
      label: '参与用户',
      dynamicDisabled: ({ disabled }) => disabled || activityType === ActivityType.BIRTHDAY,
      component: 'Input',
      colProps: { span: 12 },
      slot: 'customerType',
    },
    // 时间配置
    {
      field: 'checkDate',
      required: true,
      label({ values }) {
        return values.customerType === 'newRegister'
          ? '注册日期'
          : values.customerType === 'newAuth'
            ? '认证日期'
            : '开卡日期';
      },
      component: 'DatePicker',
      colProps: { span: 12 },
      ifShow({ values }) {
        return ['newRegister', 'newAuth', 'newCardUser'].includes(values.customerType);
      },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      className: '!w-full',
    },
    // 用户区域配置
    {
      field: 'areaType',
      required: true,
      label: '参与用户区域',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: isProvincialUser ? '0' : '1',
      ifShow({ values }) {
        return false;//['1', '2'].includes(values.customerType);
      },
      componentProps: ({ formModel }) => {
        let options = dictionary.getDictionaryOpt.get('areaType') as RadioGroupChildOption[];
        if (!isProvincialUser) {
          options = options?.filter(t => t.value !== '2');
        }
        return {
          placeholder: '请选择参与用户区域',
          options,
          onChange: e => {
            const value = isObject(e) ? e.target.value : e;
            if (value === '2') {
              formModel['areaCode'] = undefined;
            }
          },
        };
      },
    },

    // 区域选择
    {
      field: 'areaCode',
      label: '区域选择',
      component: 'Select',
      required: true,
      colProps: { span: 24 },
      ifShow: ({ values }) => {
        return values.areaType === '2' && isProvincialUser;
      },
      componentProps: {
        options: map(dictionary.getDictionaryOpt.get('regionCode'), t => ({
          value: t.label,
          label: t.label,
        })),
        mode: 'multiple',
        placeholder: '请选择参与区域',
      },
    },

      //指定工会
    {
      field: 'extendList',
      required: true,
      label: '指定工会',
      component: 'Input',
      colProps: { span: 18 },
      slot: 'extendList',
      ifShow({ values }) {
        return values.customerType === 'customUnion'
      },
    },
    {
      field: 'nextUnionFlag',
      label: '包含下级工会',
      component: 'RadioGroup',
      colProps: { span: 6 },
      defaultValue: 'Y',
      ifShow({ values }) {
        return values.customerType === 'customUnion'
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo') as any[],
      },
    },
    {
      field: 'extendList',
      label: '指定工会',
      component: 'ShowSpan',
      show:false,
    },
      //新业态
    {
      field: 'extendIds',
      required: false,
      label: '新就业形态类型',
      component: 'Select',
      colProps: { span: 24 },
      ifShow({ values }) {
        return values.customerType === 'newForMat'
      },
      componentProps: function ({ formModel }) {
        let options = dictionary.getDictionaryOBJMap.get('lhjyzw');
        return {
          fieldNames: { label: 'dictName', value: 'dictCode' },
          options: options,
          mode: 'multiple',
          onChange(value,nodes){
            formModel['extendList'] = nodes?.map(t=>({sourceId:t.dictCode,sourceName:t.dictName})) || undefined
          },
          placeholder: '请选中新就业形态类型',
        };
      },
    },
      //指定标签
    {
      field: 'extendIds',
      required: true,
      label: '指定标签',
      component: 'ApiSelect',
      colProps: { span: 24 },
      ifShow:false,
      componentProps: function () {
        return {
          placeholder: '请选择指定标签',
          // api: typeList,
          params: {
            pageSize: 0,
          },
          mode: 'multiple',
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          fieldNames: { label: 'labelName', value: 'labelCode' },
        }
      },
    },
      //指定群体
    {
      field: 'extendIds',
      required: true,
      label: '指定群体',
      component: 'ApiSelect',
      colProps: { span: 24 },
      ifShow({ values }) {
        return values.customerType === 'customGroup'
      },
      componentProps: ({ formModel }) => {
        return {
          placeholder: '请选择指定群体',
          api: simpleList,
          params: {
            pageSize: 0,
          },
          mode: 'multiple',
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.groupName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          onChange(value,nodes){
            formModel['extendList'] = nodes?.map(t=>({sourceId:t.groupId,sourceName:t.groupName}))
          },
          fieldNames: { label: 'groupName', value: 'groupId' },
        }
      },
    },

    // 时间配置
    {
      field: 'startEndDate',
      required: true,
      label: '活动起止日期',
      component: 'RangePicker',
      colProps: { span: 12 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'dailyTime',
      required: true,
      label: '每日开放时间',
      component: 'TimeRangePicker',
      ifShow: activityType !== ActivityType.EDUCATION_AID,
      colProps: { span: 12 },
      componentProps: {
        placeholder: ['起始时间', '结束时间'],
        showNow: true,
      },
    },

    // 平台配置
    {
      field: 'publishPort',
      label: '平台类型',
      required: false,
      component: 'CheckboxGroup',
      defaultValue: ['30'],
      slot: 'publishPort',
      show: false,
      colProps: { span: 12 },
    },

    // 工会信息
    {
      field: 'companyName',
      label: '所属工会名称',
      component: 'ShowSpan',
      colProps: { span: 12 },
      defaultValue: userInfo.companyName,
    },

    // 活动类型配置（Select方式）
    {
      field: 'activityType',
      label: '活动类型',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      ifShow: () => !HIDE_ACTIVITY_TYPE_SELECTION.includes(activityType),
      componentProps: {
        options: dictionary.getDictionaryOpt.get(acTypeGroupCode),
      },
    },

    // 活动类型配置（兴趣小组专用ApiSelect）
    {
      field: 'activityType',
      label: '活动类型',
      required: true,
      component: 'ApiSelect',
      colProps: { span: 12 },
      ifShow: activityType === ActivityType.INTEREST_GROUP,
      componentProps: () => ({
        placeholder: '请选择活动类型',
        api: list,
        resultField: 'data',
        afterFetch: res => {
          return (
            res?.data?.map(x => ({
              ...x,
              autoId: x.autoId.toString(),
            })) ?? []
          );
        },
        params: { pageSize: 0 },
        immediate: true,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        fieldNames: { label: 'labelName', value: 'autoId' },
      }),
    },

    // 年龄限制
    {
      field: 'ageFlag',
      label: '年龄限制18-60岁',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: 'N',
      ifShow: ({ values }) => false,//values.customerType === '2',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
      },
    },

    // 联系信息
    {
      field: 'contacts',
      required: false,
      label: '活动联系人',
      component: 'Input',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      ifShow: () => CONTACT_ACTIVITY_TYPES.includes(activityType),
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 50,
      },
    },
    {
      field: 'contactPhone',
      required: false,
      label: '活动联系方式',
      component: 'Input',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      ifShow: () => CONTACT_ACTIVITY_TYPES.includes(activityType),
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 50,
      },
    },

    // 活动地址
    {
      field: 'activityAddress',
      required: false,
      label: '活动地址',
      component: 'MapSelect',
      rest: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      ifShow: () => ADDRESS_ACTIVITY_TYPES.includes(activityType),
      componentProps: ({ formModel }) => ({
        onChangeLnglat: lnglat => (formModel['coordinate'] = lnglat),
      }),
    },
    {
      field: 'coordinate',
      label: '活动坐标',
      component: 'ShowSpan',
      show: false,
    },

    // 功能配置
    {
      field: 'commentState',
      label: '是否开启评论',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: 'N',
      ifShow: () => activityType !== ActivityType.BIRTHDAY,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
      },
    },

    // 积分配置
    {
      field: 'integralFlag',
      label: '设置积分',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: 'N',
      ifShow: ({ values }) => {
        return !NO_INTEGRAL_ACTIVITY_TYPES.includes(activityType) && values.externalLink !== 'Y';
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
      },
    },
    {
      field: 'integralThreshold',
      label: '参与积分门槛',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: { min: 0 },
      ifShow: ({ values }) => values.integralFlag === 'Y',
    },
    {
      field: 'integralOperateType',
      label: '积分操作类型',
      component: 'RadioGroup',
      colProps: { span: 12 },
      required: true,
      ifShow: ({ values }) => values.integralFlag === 'Y',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('IntegralOperateType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'integralScore',
      label: '奖励/消耗分值',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: { min: 0 },
      required: true,
      ifShow: ({ values }) => values.integralFlag === 'Y',
    },

    // 外链配置
    {
      field: 'externalLink',
      label: '是否外链',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: 'N',
      ifShow: () => activityType !== ActivityType.EDUCATION_AID,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
      },
    },
    {
      field: 'skipType',
      label: '跳转方式',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: 'builtIn',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('skipType') as RadioGroupChildOption[],
      },
      ifShow: ({ values }) => values.externalLink === 'Y',
    },
    {
      field: 'skipNoticeFlag',
      label: '跳转提醒',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: 'N',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
      },
      ifShow: ({ values }) => values.externalLink === 'Y',
    },
    {
      field: 'externalLinkUrl',
      label: '外链地址',
      component: 'Input',
      required: true,
      colProps: { span: 24 },
      ifShow: ({ values }) => values.externalLink === 'Y',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入外链地址',
      },
    },
    // 活动类型蓬安单独配置
    {
      field: 'activityTypeConfig',
      label: '活动类型',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: ActivityType.QUIZ,
      ifShow: bigActivityType === BigActivityType.SIGN_UP_AND_QUIZ,
      slot: 'activityTypeConfig',
    },
    // 活动配置
    {
      field: 'ifQuiz',
      label: ifQuizLabel,
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: bigActivityType === BigActivityType.SIGN_UP_AND_QUIZ ? '1' : '2',
      ifShow: !NO_CONFIG_ACTIVITY_TYPES.includes(activityType),
      slot: 'ifQuiz',
    },

    // 富文本内容
    {
      field: 'activityContent',
      label: '活动介绍',
      component: 'Input',
      colProps: { span: 24 },
      render: ({ model, field, disabled }) => (
        <Tinymce
          value={model[field]}
          options={{ readonly: !!disabled }}
          operateType={ActivityDocAddr[activityType]}
          showImageUpload={false}
          onChange={value => (model[field] = value)}
        />
      ),
    },
    {
      field: 'activityRules',
      label: '活动规则',
      component: 'Input',
      colProps: { span: 24 },
      ifShow: activityType !== ActivityType.EDUCATION_AID,
      render: ({ model, field, disabled }) =>
        h(Tinymce, {
          value: model[field],
          onChange: (value: string) => (model[field] = value),
          showImageUpload: false,
          options: { readonly: disabled },
          operateType: ActivityDocAddr[activityType],
        }),
    },
    {
      field: 'participationMode',
      label: '参与方式',
      component: 'Input',
      ifShow: activityType !== ActivityType.EDUCATION_AID,
      colProps: { span: 24 },
      render: ({ model, field, disabled }) =>
        h(Tinymce, {
          value: model[field],
          onChange: (value: string) => (model[field] = value),
          showImageUpload: false,
          options: { readonly: disabled },
          operateType: ActivityDocAddr[activityType],
        }),
    },
    {
      field: 'activityRemark',
      label: '活动备注',
      component: 'Input',
      colProps: { span: 24 },
      render: ({ model, field, disabled }) =>
        h(Tinymce, {
          value: model[field],
          onChange: (value: string) => (model[field] = value),
          showImageUpload: false,
          options: { readonly: disabled },
          operateType: ActivityDocAddr[activityType],
        }),
    },
    {
      field: 'activityEndRemark',
      label: '活动结束后展示内容',
      component: 'Input',
      colProps: { span: 24 },
      ifShow: false,
      render: ({ model, field, disabled }) =>
        h(Tinymce, {
          value: model[field],
          onChange: (value: string) => (model[field] = value),
          showImageUpload: false,
          options: { readonly: disabled },
          operateType: ActivityDocAddr[activityType],
        }),
    },
  ];
};

// 获取配置标签
export function getConfigLabel(activityType: ActivityType): string {
  if (REGISTRATION_CONFIG_ACTIVITY_TYPES.includes(activityType)) {
    return '报名配置';
  }
  if (activityType === ActivityType.BIRTHDAY) {
    return '奖品配置';
  }
  return `${ActivitySettingZh[activityType]}配置`;
}
