<template>
  <div :class="$style.topic">
    <Affix
      :target="container"
      class="bg-white"
    >
      <div v-if="false"
        ><a-input
          placeholder="请输入关键字"
          @pressEnter="handleEnter"
        ></a-input
      ></div>
      <div class="inline-flex items-center">
        <div
          class="flex items-center my-5px"
          v-if="!disabled"
        >
          <a-button
            type="primary"
            shape="round"
            @click="handleAddQuestions()"
            >添加题目</a-button
          >
        </div>
        <Pagination
          showLessItems
          size="small"
          :pageSize="pageSize"
          :total="getTotal"
          @change="handlePageChange"
        />
      </div>
    </Affix>
    <div
      class="flex justify-center items-center"
      :class="`${(v + 1) % 2 === 0 ? 'my-4' : ''}`"
      v-for="(m, v) in getPaginationList"
      :key="`question_${v}`"
      :id="`question_${v}`"
    >
      <div class="w-23/24">
        <Row class="border-r-1 bg-hex-F5F5F5">
          <Col
            :span="12"
            class="!flex !p-7px"
          >
            <label class="w-60px label-center">题目{{ v + 1 }}:</label>
            <!-- <p v-if="!m.showText" class="mb-0">{{ m.topicContent }}</p> -->
            <a-input
              :disabled="disabled"
              placeholder="请填写问题"
              v-model:value="m.topicContent"
              allowClear
              autocomplete="off"
            />
          </Col>
          <Col
            :span="7"
            class="!flex !p-7px"
          >
            <div class="flex justify-center items-center">
              <label class="w-60px label-center">题型:</label>
              <!-- <p v-if="!m.showText" class="mb-0">{{
                dictionary.getDictionaryMap.get(`activityOptionType_${m.optionType}`)?.dictName ||
                ''
              }}</p> -->
              <Select
                v-model:value="m.optionType"
                :disabled="disabled"
                :options="options"
              />
            </div>
            <div
              class="flex justify-center items-center"
              v-if="[ActivityType.QUIZ, ActivityType.INCLUSIVE_YJWD].includes(activityType)"
            >
              <label class="w-60px label-center">分数:</label>
              <!-- <p v-if="!m.showText" class="mb-0">{{ m.score }}</p> -->
              <InputNumber
                min="1"
                v-model:value="m.score"
                :disabled="disabled"
              />
            </div>
          </Col>
          <Col
            :span="4"
            class="!p-7px flex justify-center items-center"
          >
            <!-- <a-button
              v-if="!disabled"
              class="!rounded-1xl danger-question-list"
              type="primary"
              @click="m.showText = !m.showText"
            >
              <Icon class="mr-1" :icon="`typcn:pen`" />
              {{ `${!m.showText ? '修改' : '保存'}` }}
            </a-button> -->
            <a-button
              v-if="!disabled"
              class="!rounded-1xl danger-question-list"
              danger
              @click="handleDeleteQuestion(v)"
            >
              <Icon
                class="mr-1"
                :icon="`fluent:delete-12-filled`"
              />
              删除
            </a-button>
          </Col>
          <Col
            :span="1"
            class="!p-7px"
          >
            <div
              class="cursor-pointer"
              @click="m.ifShow = false"
              v-if="m.ifShow"
            >
              <Icon :icon="`ant-design:up-square-twotone`" />
            </div>
            <div
              class="cursor-pointer"
              @click="m.ifShow = true"
              v-else
            >
              <Icon :icon="`ant-design:down-square-twotone`" />
            </div>
          </Col>
        </Row>
        <div v-if="m.ifShow && (m.optionType === 'radio' || m.optionType === 'select')">
          <Row
            class="border-r-1 border-l-0"
            :class="`${index === 0 ? 'border-l-0' : ''} ${(index + 1) % 2 !== 0 ? '' : ''}`"
            v-for="(item, index) in m.options"
          >
            <Col
              :span="12"
              class="!flex !p-7px"
            >
              <label class="w-60px label-center">选项{{ index + 1 }}:</label>
              <!-- <p v-if="!m.showText">{{ item.optionContent }}</p> -->
              <a-input
                :disabled="disabled"
                v-model:value="item.optionContent"
                @change="() => (item.optionNo = index + 1)"
                allowClear
                autocomplete="off"
                placeholder="请输入答案, 最多40字"
              />
            </Col>
            <Col
              :span="4"
              v-if="[ActivityType.QUIZ, ActivityType.INCLUSIVE_YJWD].includes(activityType)"
            >
              <div class="h-full icon-class-right">
                <div @click="handleCorrect(m, index)">
                  <Icon
                    :icon="`ant-design:check-circle-outlined`"
                    :color="item.correct ? '#00c300' : ''"
                    :size="30"
                  />
                </div>
              </div>
            </Col>
            <Col :span="4">
              <div
                class="h-full icon-class-right icon-red"
                v-if="!disabled && index + 1 === m.options?.length"
              >
                <div @click="addEmpty(m)">
                  <Icon
                    :icon="`ic:round-add-circle-outline`"
                    :size="30"
                  />
                </div>
              </div>
            </Col>
            <Col :span="4">
              <div
                class="h-full icon-class-right icon-gray"
                v-if="!disabled"
              >
                <div @click="deleteEmpty(m, index)">
                  <Icon
                    :icon="`ant-design:close-outlined`"
                    color="red"
                  />
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
      <div
        class="up-down w-1/24"
        v-if="!disabled"
      >
        <div
          class="cursor-pointer"
          @click="handleUpDown(true, v)"
        >
          <Icon :icon="`ant-design:caret-up-filled`" />
        </div>
        <div
          class="cursor-pointer"
          @click="handleUpDown(false, v)"
        >
          <Icon :icon="`ant-design:caret-down-filled`" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Affix, Col, InputNumber, Pagination, Row, Select } from 'ant-design-vue';
import { DefaultOptionType } from 'ant-design-vue/lib/select';
import { ActivityType, AnswerType } from '../../../activities.d';
import { computed, ref, unref, watch } from 'vue';
import { find, forEach, remove } from 'lodash-es';
import { Icon } from '@monorepo-yysz/ui';

import { useMessage, usePagination } from '@monorepo-yysz/hooks';

const props = defineProps({
  list: { type: Array as PropType<Recordable[]>, default: [] },
  disabled: { type: Boolean, default: false },
  options: { type: Array as PropType<DefaultOptionType[]> },
  activityType: { type: String as PropType<ActivityType>, default: ActivityType.QUIZ },
});

const emit = defineEmits(['change']);

const { createWarningModal } = useMessage();

const pageSize = 10;

const container = computed(() => {
  return () => document.querySelector(`#basic-setting-${props.activityType}`);
});

const questionList = ref<Recordable[]>([]);

const { getPaginationList, getTotal, setCurrentPage, getCurrentPage } = usePagination(
  questionList,
  pageSize
);

function handleEnter(e) {
  // const val = e.target.value
  // const node = find(unref(questionList), v => v.topicContent.includes(val))
}

//设置页码
function handlePageChange(page: number) {
  setCurrentPage(page);
}

//删除题目
function handleDeleteQuestion(index) {
  if (unref(questionList)?.length === 1 && unref(props.activityType) === ActivityType.SIGNUP) {
    createWarningModal({ content: '已经是最后一个题目' });
    return false;
  }

  if (unref(questionList)?.length === 2 && unref(props.activityType) === ActivityType.VOTE) {
    createWarningModal({ content: '至少保留两个选项' });

    return false;
  }
  //转换为真实索引
  const realIndex = (unref(getCurrentPage) - 1) * pageSize + index;

  remove(unref(questionList), (_, k) => k === realIndex);
}

//勾选正确
function handleCorrect(m, index: string | number) {
  if (props.disabled) {
    return false;
  }

  if (!m.optionType) {
    createWarningModal({ content: '请选择题型' });
    return false;
  }
  const type = m.optionType === 'radio';
  forEach(m.options, (v, k) => {
    //如果已经选择了再次选择取消之前选择
    if (v.correct && k === index) {
      v.correct = false;
      return false;
    }

    //单选的时候只选择当前index的勾
    if (type) {
      v.correct = k === index;
    } else {
      //多选的时候点击为true
      if (k === index) {
        v.correct = true;
      }
    }
  });
}

//增加新输入框
function addEmpty(m) {
  m?.options.push({} as AnswerType);
}

//删除输入框
function deleteEmpty(m, index: number) {
  if (m?.options?.length === 2) {
    createWarningModal({ content: '至少保留两个选项' });
    return false;
  }
  remove(m.options, (_, k) => k === index);
}

//上下移
function handleUpDown(flg: any, index: number) {
  const length = unref(getPaginationList)?.length || 1;
  if (index === 0 && flg) {
    createWarningModal({ content: '已在顶部' });
    return false;
  } else if (index === length - 1 && !flg) {
    createWarningModal({ content: '已在底部' });

    return false;
  } else {
    if (unref(questionList) && unref(questionList).length > 0) {
      const realIndex = (unref(getCurrentPage) - 1) * pageSize + index;
      const step = flg ? realIndex - 1 : realIndex;
      unref(questionList).splice(
        step,
        1,
        ...unref(questionList).splice(flg ? realIndex : realIndex + 1, 1, unref(questionList)[step])
      );
    }
  }
}

//添加题目
function handleAddQuestions() {
  let index = 0;
  const item = find(unref(questionList), (v, k: number) => {
    index = k;
    return !v.topicContent;
  });
  if (item) {
    createWarningModal({ content: `题目${index + 1}, 题目不能为空` });
    return false;
  }
  questionList.value.push({
    ifShow: true,
    optionType: 'radio',
    score: 2,
    options: [{}, { correct: true }],
  });

  const number = Math.ceil(unref(questionList).length / pageSize) || 1;
  setCurrentPage(number);
}

watch(
  () => props.list,
  () => {
    questionList.value = props.list;
  },
  { deep: true }
);

watch(
  questionList,
  () => {
    emit('change', unref(questionList));
  },
  { deep: true }
);
</script>

<style lang="less" module>
.topic {
  :global {
    .ant-affix {
      background-color: #fff;
      width: 500px !important;
    }
  }
}
</style>
