<template>
  <div>
    <Affix :target="container">
      <div
        class="flex items-center my-5px"
        v-if="!disabled"
      >
        <a-button
          type="primary"
          shape="round"
          @click="handleAddQuestions()"
          >添加字段</a-button
        >
      </div>
    </Affix>
    <Row
      class="my-5px"
      v-for="(m, v) in dataList"
    >
      <Col :span="23">
        <Row class="border-r-1 bg-hex-F5F5F5">
          <Col
            :span="12"
            class="!flex !p-7px border-r-1"
          >
            <label class="w-100px label-center"
              ><span
                class="text-red-500"
                v-if="!disabled"
                >* </span
              >表单标题{{ v + 1 }}:
            </label>
            <a-input
              v-model:value="m.topicContent"
              autocomplete="off"
              :disabled="disabled"
            />
          </Col>
          <Col
            :span="10"
            class="!flex !p-7px"
          >
            <label class="w-60px label-center">是否必填: </label>
            <RadioGroup
              v-model:value="m.ifMust"
              name="ifNecessary"
              class="justify-center items-center !flex !ml-5px"
              :disabled="disabled"
              :options="activityAuditType"
            />
          </Col>
          <Col
            :span="2"
            class="!flex !p-7px"
          >
            <a-button
              class="!rounded-1xl danger-question-list"
              v-if="!disabled"
              danger
              @click="handleDeleteQuestion(v)"
            >
              <Icon
                class="mr-1"
                :icon="`fluent:delete-12-filled`"
              />
              删除
            </a-button>
          </Col>
        </Row>
        <Row class="border-l-0">
          <Col
            :span="12"
            class="!flex !p-7px border-r-1"
          >
            <label class="w-100px label-center"
              ><span
                class="text-red-500"
                v-if="!disabled"
                >* </span
              >数据类型:
            </label>
            <Select
              v-model:value="m.optionType"
              @change="val => handleAddOptions(val, m)"
              :options="dictionary.getDictionaryOpt.get('activityOptionType')"
              :disabled="disabled"
            />
          </Col>
          <Col
            :span="12"
            class="!flex !p-7px"
            v-if="
              (m.optionType === 'radio' ||
                m.optionType === 'select' ||
                m.optionType === 'checkbox') &&
              !disabled
            "
          >
            <label class="w-70px label-center">添加选项: </label>
            <div
              class="h-full icon-class-right icon-red"
              @click="addEmpty(m)"
            >
              <Icon
                :icon="`ic:round-add-circle-outline`"
                :size="25"
              />
            </div>
          </Col>
        </Row>
        <Row
          class="border-r-1"
          v-if="
            m.optionType === 'radio' || m.optionType === 'select' || m.optionType === 'checkbox'
          "
        >
          <Col
            :span="24"
            class="!flex !p-7px"
          >
            <label class="w-100px label-center"
              ><span
                class="text-red-500"
                v-if="!disabled"
                >* </span
              >选项值:
            </label>
            <Row class="user-form-select">
              <Col
                :span="5"
                v-for="(item, index) in m.options"
                class="m-5px"
              >
                <InputGroup
                  compact
                  class="!flex"
                >
                  <a-input
                    v-model:value="item.optionContent"
                    autocomplete="off"
                    :disabled="disabled"
                  />
                  <a-button
                    danger
                    @click="deleteEmpty(m, index)"
                    v-if="!disabled"
                  >
                    <Icon :icon="`ant-design:delete-twotone`" />
                  </a-button>
                </InputGroup>
              </Col>
            </Row>
          </Col>
        </Row>
      </Col>
      <Col
        :span="1"
        class="up-down"
        v-if="!disabled"
      >
        <div
          class="cursor-pointer"
          @click="handleUpDown(true, v)"
        >
          <Icon :icon="`ant-design:caret-up-filled`" />
        </div>
        <div
          class="cursor-pointer"
          @click="handleUpDown(false, v)"
        >
          <Icon :icon="`ant-design:caret-down-filled`" />
        </div>
      </Col>
    </Row>
  </div>
</template>

<script setup lang="ts">
import { find, remove } from 'lodash-es';
import { computed, ref, unref, watch } from 'vue';
import { useDictionary } from '@/store/modules/dictionary';
import { ActivityType } from '../../../activities.d';
import { Affix, Col, InputGroup, RadioGroup, Row, Select } from 'ant-design-vue';
import { Icon } from '@monorepo-yysz/ui';

import { useMessage } from '@monorepo-yysz/hooks';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

const props = defineProps({
  list: { type: Array as PropType<Recordable[]>, default: [] },
  disabled: { type: Boolean, default: false },
  activityType: { type: String as PropType<ActivityType>, default: ActivityType.QUIZ },
});

const emit = defineEmits(['change']);

const { createWarningModal } = useMessage();

const dictionary = useDictionary();

const dataList = ref<Recordable[]>([]);

const container = computed(() => {
  return () => {
    const element = document.querySelector(`#basic-setting-${props.activityType}`);
    return element ? (element as HTMLElement) : window;
  };
});

const activityAuditType = computed(
  () => dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]
);

//增加新输入框
function addEmpty(m: Recordable) {
  m?.options.push({} as Recordable);
}

//删除输入框
function deleteEmpty(m, index: number) {
  remove(m.options, (_, k) => k === index);
}

//删除题目
function handleDeleteQuestion(index) {
  if (unref(dataList)?.length === 1 && props.activityType === ActivityType.SIGNUP) {
    createWarningModal({ content: '已经是最后一个题目' });
    return false;
  }

  if (unref(dataList)?.length === 2 && props.activityType === ActivityType.VOTE) {
    createWarningModal({ content: '至少保留两个选项' });
    return false;
  }

  remove(unref(dataList), (_, k) => k === index);
}

//上下移
function handleUpDown(flg: any, index: number) {
  const length = unref(dataList)?.length || 1;
  if (index === 0 && flg) {
    createWarningModal({ content: '已在顶部' });
    return false;
  } else if (index === length - 1 && !flg) {
    createWarningModal({ content: '已在底部' });
    return false;
  } else {
    if (unref(dataList) && unref(dataList).length > 0) {
      const step = flg ? index - 1 : index;
      unref(dataList).splice(
        step,
        1,
        ...unref(dataList).splice(flg ? index : index + 1, 1, unref(dataList)[step])
      );
    }
  }
}

//添加选项
function handleAddOptions(val, m) {
  m.ifShow = val === 'radio' || val === 'select' || val === 'checkbox';
  if (m.ifShow) {
    if (!m.options) {
      m.options = [{}];
    }
  } else {
    m.options = undefined;
  }
}

//添加题目
function handleAddQuestions() {
  let index = 0;
  const item = find(unref(dataList), (v, k: number) => {
    index = k;
    return !v.topicContent;
  });
  if (item) {
    createWarningModal({ content: `标题${index + 1}, 标题不能为空` });
    return false;
  }
  dataList.value?.push({ ifMust: 'N', optionType: 'input' } as Recordable);
}

watch(
  () => props.list,
  () => {
    dataList.value = props.list;
  },
  { deep: true, immediate: true }
);

watch(
  dataList,
  () => {
    emit('change', unref(dataList));
  },
  { deep: true }
);
</script>
