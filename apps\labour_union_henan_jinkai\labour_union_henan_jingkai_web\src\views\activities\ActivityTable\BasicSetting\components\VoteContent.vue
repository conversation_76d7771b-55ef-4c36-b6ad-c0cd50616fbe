<template>
  <div class="border p-5px">
    <Affix :target="container">
      <div
        class="flex items-center my-5px"
        v-if="!disabled"
      >
        <a-button
          type="primary"
          shape="round"
          @click="handleAddQuestions()"
          >添加题目</a-button
        >
      </div>
    </Affix>

    <div v-for="(m, v) in dataList">
      <Row class="border bg-hex-F5F5F5">
        <Col
          :span="9"
          class="!flex !p-7px"
        >
          <label class="w-70px label-center"
            ><span
              class="text-red-500"
              v-if="!disabled"
              >*</span
            >编号:</label
          >
          <a-input
            v-model:value="m.opusNo"
            :disabled="disabled"
            placeholder="请填写内容编号"
            allowClear
            autocomplete="off"
          />
        </Col>
        <Col
          :span="9"
          class="!flex !p-7px"
        >
          <label class="w-70px label-center"
            ><span
              class="text-red-500"
              v-if="!disabled"
              >*</span
            >名称:</label
          >
          <a-input
            v-model:value="m.opusName"
            :disabled="disabled"
            placeholder="请填写投票内容"
            allowClear
            autocomplete="off"
          />
        </Col>
        <Col
          :span="6"
          class="!flex !p-7px"
        >
          <a-button
            class="!rounded-1xl danger-question-list"
            :disabled="disabled"
            danger
            @click="handleDeleteQuestion(v)"
            v-if="!disabled"
          >
            <Icon
              class="mr-1"
              :icon="`fluent:delete-12-filled`"
            />
            删除
          </a-button>
        </Col>
      </Row>
      <Row class="!border-t-0">
        <Col
          :span="24"
          class="!flex !p-7px"
        >
          <label class="w-75px label-center">
            <span
              class="text-red-500"
              v-if="!disabled"
              >*</span
            >
            选项封面
          </label>
          <div class="w-70px h-70px inline-block">
            <CropperImg
              :value="m.opusCover"
              :operate-type="ActivityDocAddr[activityType]"
              :disabled="disabled"
              @change="filePath => (m.opusCover = filePath)"
            />
          </div>
        </Col>
      </Row>
      <Row class="!border-t-0">
        <Col
          :span="24"
          class="!flex !p-7px"
        >
          <label class="w-70px label-center"
            ><span
              class="text-red-500"
              v-if="!disabled"
              >*</span
            >详情介绍:</label
          >
          <Tinymce
            :showImageUpload="false"
            :value="m.opusContent"
            @change="e => (m.opusContent = e)"
            :options="{
              readonly: disabled,
            }"
            :operate-type="ActivityDocAddr[activityType]"
          />
        </Col>
      </Row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, unref, watch } from 'vue';
import { ActivityDocAddr, ActivityType } from '../../../activities.d';
import { Icon } from '@monorepo-yysz/ui';

import { Tinymce } from '@/components/Tinymce';
import { Affix, Col, Row } from 'ant-design-vue';
import { find, remove } from 'lodash-es';
import CropperImg from '@/components/Cropper/src/CropperImg.vue';
import { useMessage } from '@monorepo-yysz/hooks';

const props = defineProps({
  list: { type: Array as PropType<Recordable[]>, default: [] },
  disabled: { type: Boolean, default: false },
  activityType: { type: String as PropType<ActivityType>, default: ActivityType.QUIZ },
});

const emit = defineEmits(['change']);

const { createWarningModal } = useMessage();

const dataList = ref<Recordable[]>([]);

const container = computed(() => {
  return () => document.querySelector(`#basic-setting-${props.activityType}`);
});

//删除题目
function handleDeleteQuestion(index) {
  if (unref(dataList)?.length === 1 && props.activityType === ActivityType.SIGNUP) {
    createWarningModal({ content: '已经是最后一个题目' });
    return false;
  }

  if (unref(dataList)?.length === 2 && props.activityType === ActivityType.VOTE) {
    createWarningModal({ content: '至少保留两个选项' });
    return false;
  }

  remove(unref(dataList), (_, k) => k === index);
}

//添加题目
function handleAddQuestions() {
  let index = 0;
  const item = find(unref(dataList), (v, k: number) => {
    index = k;
    return !v.topicContent;
  });
  if (item) {
    createWarningModal({ content: `标题${index + 1}, 标题不能为空` });
    return false;
  }
  dataList.value?.push({} as Recordable);
}

watch(
  () => props.list,
  () => {
    dataList.value = props.list;
  },
  { deep: true, immediate: true }
);

watch(
  dataList,
  () => {
    emit('change', unref(dataList));
  },
  { deep: true }
);
</script>
