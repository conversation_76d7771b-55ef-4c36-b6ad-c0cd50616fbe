<template>
  <BasicForm
    :class="disabledClass"
    @register="registerForm"
  >
  </BasicForm>
  <div class="p-5px">
    <!-- <Affix :target="container"> -->
    <div
      class="flex items-center my-5px"
      v-if="!disabled"
    >
      <a-button
        type="primary"
        shape="round"
        @click="handleAddQuestions"
        >添加奖品</a-button
      >
    </div>
    <!-- </Affix> -->

    <BasicTable
      @register="registerTable"
      class="dy-prize-table"
      :data-source="modelValue.prizeInfos"
    >
      <template #bodyCell="{ column, index }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:delete-twotone',
                label: '删除',
                type: 'text',
                danger: true,
                disabled: disabled,
                onClick: handleDeletePrize.bind(null, index),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
import { unref, computed, onMounted, nextTick, watch } from 'vue';
import { useForm, BasicForm, FormSchema } from '/@/components/Form';
import { tableLotteryColum } from '../data';
import { LuckDrawInfo, Question } from '@/views/activities/activities';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { isEmpty, remove } from 'lodash-es';
import dayjs from "dayjs";

const modelValue = defineModel<LuckDrawInfo>('value', {
  type: Object as () => LuckDrawInfo,
  default: () => ({}),
});

const props = defineProps({
  disabled: {
    type: Boolean,
    default: () => false,
  },
  activityId: {
    type: String,
    default: () => '',
  },
  mainType: {
    type: String,
    default: () => '',
  },
  handleClick: {
    type: Function,
    default: () => () => {},
  },
});

const schemas: FormSchema[] = [
  {
    field: 'awardCountMax',
    label: '个人总中奖次数',
    component: 'InputNumber',
    required: false,
    colProps: { span: 12 },
    ifShow:unref(modelValue).awardPoolName !== 'award3',
    componentProps: {
      placeholder: '请输入个人总中奖次数 (0:不限制次数)',
      min: 0,
      onChange: val => {
        unref(modelValue) && (modelValue.value.awardCountMax = val);
      },
    },
  },
  {
    field: 'dailyAwardCount',
    label: '个人每日中奖次数',
    ifShow:unref(modelValue).awardPoolName !== 'award3',
    required: false,
    component: 'InputNumber',
    colProps: { span: 12 },
    componentProps: {
      placeholder: '请输入个人每日中奖次数 (0:不限制次数)',
      min: 0,
      onChange: val => {
        unref(modelValue) && (modelValue.value.dailyAwardCount = val);
      },
    },
  },
  {
    field: 'receiveStartEndDate',
    label: unref(modelValue).awardPoolName === 'award3'?'领取日期':'红包领取日期',
    ifShow:unref(modelValue).awardPoolName === 'award3',
    component: 'RangePicker',
    colProps: { span: 12 },
    required: unref(modelValue).awardPoolName === 'award3',
    componentProps: {
      showTime: {
        defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
      },
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      allowClear: false,
      onChange: val => {
        if(unref(modelValue)){
          if(val?.length){
            modelValue.value.receiveStartTime = val[0]
            modelValue.value.receiveEndTime = val[1]
          }
        }
      },
    },
  },
];

const columns = computed(() => {
  return tableLotteryColum(props.disabled, props.mainType, props.activityId, props.handleClick,unref(modelValue).awardPoolName);
});

const disabledClass = computed(() => {
  return unref(props.disabled) ? 'back-transparent' : '';
});

const [registerForm, { resetFields, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas,
  showActionButtonGroup: false,
});

// 注册抽奖table
const [registerTable, { getDataSource, setTableData, insertTableDataRecord }] = useTable({
  rowKey: 'id',
  showTableSetting: false,
  columns: columns,
  bordered: true,
  showIndexColumn: true,
  pagination: false,
  actionColumn: {
    width: 50,
    title: '操作',
    dataIndex: 'action',
  },
});

// 添加
function handleAddQuestions() {
  insertTableDataRecord({} as Question);
}

// 删除table
function handleDeletePrize(index: number) {
  remove(getDataSource(), (_, k) => k === index);
}

onMounted(async () => {
  await nextTick(async () => {
    await resetFields();
    if (unref(modelValue)) {
      const {receiveStartTime,receiveEndTime,...other} = unref(modelValue)
      if(receiveStartTime && receiveEndTime){
        modelValue.value.receiveStartEndDate = [receiveStartTime,receiveEndTime]
      }
      console.log(unref(modelValue),other,'mo')
      await setFieldsValue(unref(modelValue) as Recordable<any>);

      !isEmpty(unref(modelValue)?.prizeInfos) && setTableData(unref(modelValue)?.prizeInfos);
    }
  });
});

watch(
  () => props.disabled,
  () => {
    //
    nextTick(() => {
      setProps({ disabled: unref(props.disabled) });
    });
  },
  { immediate: true }
);
</script>
