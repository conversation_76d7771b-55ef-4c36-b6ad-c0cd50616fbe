import { UploadOutlined } from '@ant-design/icons-vue';
import { Button, Input, InputNumber, Progress, Select, Tag, Upload } from 'ant-design-vue';
import { DefaultOptionType } from 'ant-design-vue/lib/select';
import dayjs from 'dayjs';
import { cloneDeep, divide, filter, includes, isNaN, toNumber } from 'lodash-es';
import { ActivityType, Question, VoteTypeConfig } from '../../activities.d';
import { download } from '@/api/sys/upload';
import { ApiSelect, FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useGlobSetting } from '@/hooks/setting';
import { useDictionary } from '@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { downloadByUrl } from '@monorepo-yysz/utils';
import { CropperForm } from '@/components/Cropper';
import { SearchOutlined } from '@ant-design/icons-vue';
import { onSelectList } from '@/api/coupon';

export const modalFormSchema = (
  activityType: string,
  mainType: string,
  activityId
): FormSchema[] => {
  const dictionary = useDictionary();
  const { bucket_name, apiUrl } = useGlobSetting();
  switch (activityType) {
    case ActivityType.INCLUSIVE_YJWD:
    case ActivityType.QUIZ:
      return [
        {
          field: 'typesetting',
          label: '竞答页排版',
          component: 'RadioGroup',
          colProps: { span: 24 },
          ifShow: false,
          defaultValue: 'N',
          componentProps: {
            options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          },
        },
        {
          field: 'totalParticipation',
          label: '总参与次数',
          component: 'InputNumber',
          colProps: { span: 12 },
          componentProps: { min: 1 },
          defaultValue: 1,
          required: true,
        },
        {
          field: 'numberPerDay',
          label: '单日参与次数',
          component: 'InputNumber',
          colProps: { span: 12 },
          componentProps: { min: 1 },
          defaultValue: 1,
          required: true,
        },
        {
          field: 'questionNumRandom',
          label: '随机选题数',
          component: 'InputNumber',
          required: true,
          colProps: { span: 12 },
          componentProps: { min: 1 },
          defaultValue: 5,
        },
        {
          field: 'raffleCondition',
          label: '抽奖要求答对题数',
          component: 'InputNumber',
          colProps: { span: 12 },
          componentProps: { min: 0 },
          required: true,
          defaultValue: 3,
        },
        {
          field: 'timeUse',
          label: '每题限制时长（秒）',
          component: 'InputNumber',
          colProps: { span: 12 },
          defaultValue: 100,
          componentProps: { min: 10 },
          required: true,
        },
        {
          field: 'questionNum',
          label: '题目数量',
          component: 'InputNumber',
          dynamicDisabled: true,
          colProps: { span: 4 },
          render: ({ values }) => {
            return <Tag color={'blue'}>{`${values.topicInfoList?.length || 0}条`}</Tag>;
          },
        },
        {
          field: 'ImportQuestionBank',
          label: '导入题库',
          component: 'Input',
          colProps: { span: 8 },
          ifShow: ({ disabled }) => {
            return !!!disabled;
          },
          render: ({ model }) => {
            function handleChange({ file }) {
              const { response } = file;

              if (response) {
                model['topicInfoList'] = response.data;
              }
            }

            async function handleDownFile() {
              download({
                filenames: [`common_1_topicTemp.xlsx`],
                bucketName: bucket_name,
              }).then(res => {
                const url = window.URL.createObjectURL(res);
                downloadByUrl({
                  url,
                  fileName: '竞答活动题目导入模板.xlsx',
                });
              });
            }

            return (
              <div>
                <Upload
                  name="multipartFile"
                  maxCount={1}
                  accept=".xlsx,.xls"
                  onChange={handleChange}
                  action={`${apiUrl}/h5/activityInfo/file/import`}
                >
                  <Button type="primary">
                    <UploadOutlined />
                    上传文件
                  </Button>
                </Upload>
                <Button
                  type="primary"
                  class="ml-1"
                  onClick={handleDownFile}
                >
                  下载模板
                </Button>
              </div>
            );
          },
        },
        {
          field: 'topicInfoList',
          label: '',
          component: 'InputTextArea',
          slot: 'topicInfoList',
          rest: true,
          defaultValue: [
            {
              ifShow: true,
              optionType: 'radio',
              score: 2,
              options: [{}, { correct: true }],
            } as Question,
          ],
          colProps: { span: 24 },
          class: 'question-list',
        },
      ];
    case ActivityType.INTEREST_GROUP:
    case ActivityType.BLUE_VEST:
    case ActivityType.SIGNUP:
    case ActivityType.COMPETITION:
    case ActivityType.FUN_COMPETITION:
    case ActivityType.VOLUNTEER_SERVICE:
    case ActivityType.FRIENDSHIP:
    case ActivityType.INCLUSIVE_SIGNUP:
    case ActivityType.WOMEN:
      return [
        {
          field: 'maxCount',
          label: '活动人数上限',
          component: 'InputNumber',
          colProps: { span: 8 },
          componentProps: { min: 1 },
          rulesMessageJoinLabel: true,
          required: true,
        },
        {
          field: 'maleMaxCount',
          label: '男生人数上限',
          component: 'InputNumber',
          colProps: { span: 8 },
          componentProps: { min: 1 },
          rulesMessageJoinLabel: true,
          ifShow: activityType === ActivityType.FRIENDSHIP,
          required: false,
        },
        {
          field: 'femaleMaxCount',
          label: '女生人数上限',
          component: 'InputNumber',
          colProps: { span: 8 },
          componentProps: { min: 1 },
          rulesMessageJoinLabel: true,
          ifShow: activityType === ActivityType.FRIENDSHIP,
          required: false,
        },
        {
          field: 'auditFlag',
          label: '是否审核: ',
          component: 'RadioGroup',
          colProps: { span: 8 },
          defaultValue: 'N',
          componentProps: {
            options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          },
        },
        {
          field: 'writeFlag',
          label: '填写报名表: ',
          component: 'RadioGroup',
          colProps: { span: 8 },
          defaultValue: 'N',
          componentProps: {
            options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          },
        },
        {
          field: 'registerFlag',
          label: '是否签到',
          component: 'RadioGroup',
          colProps: { span: 8 },
          defaultValue: 'N',
          componentProps: {
            options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          },
          ifShow: ActivityType.VOLUNTEER_SERVICE === activityType,
          required: true,
        },
        {
          field: 'signUpTime',
          label: '活动报名时间',
          component: 'RangePicker',
          colProps: { span: 8 },
          componentProps: {
            showTime: {
              hideDisabledOptions: true,
              defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('00:00:00', 'HH:mm:ss')],
            },
            format: 'YYYY-MM-DD HH:mm:ss',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
          },
          required: true,
        },
        {
          field: 'enrollmentScore',
          label: '报名消耗积分',
          component: 'InputNumber',
          colProps: { span: 8 },
          componentProps: { min: 0 },
          rulesMessageJoinLabel: true,
          required: true,
          ifShow: false,
        },
        {
          field: 'registerScore',
          label: '签到所获得积分',
          component: 'InputNumber',
          colProps: { span: 8 },
          componentProps: { min: 0 },
          rulesMessageJoinLabel: true,
          ifShow({ values: { registerFlag } }) {
            return registerFlag === 'Y';
          },
          required: true,
        },
        {
          field: 'topicInfoList',
          label: '用户表单: ',
          component: 'Render',
          rest: true,
          ifShow: ({ values }) => {
            return values.writeFlag === 'Y';
          },
          slot: 'userForm',
          colProps: { span: 24 },
          defaultValue: [{ ifMust: 'N', optionType: 'input' } as Question],
        },
      ];
    case ActivityType.BIRTHDAY:
    case ActivityType.LOTTERY:
      return [
        {
          field: 'totalParticipation',
          label: '个人总抽奖次数',
          ifShow: ![
            ActivityType.QUIZ,
            ActivityType.SURVEY,
            ActivityType.SIGNUP,
            ActivityType.COMPETITION,
            ActivityType.FUN_COMPETITION,
            ActivityType.WOMEN,
            ActivityType.FRIENDSHIP,
            ActivityType.VOLUNTEER_SERVICE,
            ActivityType.VOTE,
            ActivityType.MULTIPLE_VOTE,
          ].includes(mainType as ActivityType),
          component: 'InputNumber',
          required: true,
          colProps: { span: 12 },
          componentProps: { min: 1 },
        },
        {
          field: 'numberPerDay',
          label: '个人每日抽奖次数',
          required: true,
          component: 'InputNumber',
          colProps: { span: 12 },
          componentProps: { min: 0 },
          ifShow: ![
            ActivityType.QUIZ,
            ActivityType.BIRTHDAY,
            ActivityType.SURVEY,
            ActivityType.SIGNUP,
            ActivityType.COMPETITION,
            ActivityType.FUN_COMPETITION,
            ActivityType.WOMEN,
            ActivityType.FRIENDSHIP,
            ActivityType.VOLUNTEER_SERVICE,
            ActivityType.VOTE,
            ActivityType.MULTIPLE_VOTE,
          ].includes(mainType as ActivityType),
        },

        {
          field: 'awardCountMax',
          label: '个人总中奖次数',
          component: 'InputNumber',
          required: false,
          colProps: { span: 12 },
          ifShow: ![
            ActivityType.SURVEY,
            ActivityType.SIGNUP,
            ActivityType.COMPETITION,
            ActivityType.FUN_COMPETITION,
            ActivityType.WOMEN,
            ActivityType.FRIENDSHIP,
            ActivityType.VOLUNTEER_SERVICE,
          ].includes(mainType as ActivityType),
          componentProps: { placeholder: '请输入个人总中奖次数 (0:不限制次数)', min: 0 },
        },
        {
          field: 'dailyAwardCount',
          label: '个人每日中奖次数',
          required: false,
          component: 'InputNumber',
          colProps: { span: 12 },
          ifShow: ![
            ActivityType.BIRTHDAY,
            ActivityType.SURVEY,
            ActivityType.SIGNUP,
            ActivityType.COMPETITION,
            ActivityType.FUN_COMPETITION,
            ActivityType.WOMEN,
            ActivityType.FRIENDSHIP,
            ActivityType.VOLUNTEER_SERVICE,
          ].includes(mainType as ActivityType),
          componentProps: {
            placeholder: '请输入个人每日中奖次数 (0:不限制次数)',
            min: 0,
          },
        },
        {
          field: 'conditionType',
          label: '参与抽奖方式',
          required: true,
          component: 'RadioGroup',
          colProps: { span: 12 },
          defaultValue: '0',
          rulesMessageJoinLabel: true,
          ifShow: ActivityType.WALK === mainType,
          componentProps: function () {
            return {
              options: dictionary.getDictionaryOpt.get(
                'exLuckyDrawType'
              ) as RadioGroupChildOption[],
            };
          },
        },
        {
          field: 'fixedScore',
          label: ({ values }) => {
            if (ActivityType.WALK !== mainType) {
              return '消耗积分';
            }
            if (values.conditionType == '0') {
              return '每次消耗步数';
            }
            return '每次消耗工币数';
          },
          component: 'InputNumber',
          colProps: { span: 12 },
          ifShow: ActivityType.WALK === mainType,
          required: false,
          componentProps: { min: 0 },
        },
        {
          field: 'receiveStartEndDate',
          label: '红包领取日期',
          component: 'RangePicker',
          colProps: { span: 12 },
          required: false,
          ifShow: false,
          componentProps: {
            showTime: {
              defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
            },
            format: 'YYYY-MM-DD HH:mm:ss',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
            allowClear: true,
          },
        },
        {
          field: 'auditFlag',
          label: '中奖是否审核',
          component: 'RadioGroup',
          colProps: { span: 12 },
          defaultValue: 'N',
          ifShow: false,
          componentProps: {
            options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          },
        },
        {
          field: 'prizeInfos',
          label: '',
          component: 'Render',
          rest: true,
          slot: 'prizeList',
          defaultValue: [{} as Question],
          colProps: { span: 24 },
        },
      ];
    case ActivityType.SURVEY:
      return [
        {
          field: 'typesetting',
          label: '调查页排版',
          component: 'RadioGroup',
          colProps: { span: 24 },
          ifShow: false,
          defaultValue: '1',
          componentProps: {
            options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          },
        },
        {
          field: 'questionnaireName',
          label: '问卷名称',
          component: 'Input',
          colProps: { span: 12 },
          required: true,
          rulesMessageJoinLabel: true,
          componentProps: {
            autocomplete: 'off',
          },
        },
        {
          field: 'total',
          label: '需要收集总份数',
          component: 'InputNumber',
          colProps: { span: 12 },
          required: false,
          rulesMessageJoinLabel: true,
          componentProps: { min: 0 },
        },
        {
          field: 'questionNum',
          label: '题目数量',
          component: 'Input',
          colProps: { span: 12 },
          render: ({ values }) => {
            return <Tag color={'blue'}>{`${values.topicInfoList?.length || 0}条`}</Tag>;
          },
        },
        {
          field: 'topicInfoList',
          label: '题目列表',
          component: 'Input',
          slot: 'topicInfoList',
          colProps: { span: 24 },
          class: 'question-list',
          rest: true,
          defaultValue: [{ ifShow: true, optionType: 'radio', options: [{}, {}] } as Question],
        },
      ];
    case ActivityType.MULTIPLE_VOTE:
    case ActivityType.VOTE:
      return [
        {
          field: '',
          label: '投稿配置',
          component: 'Divider',
        },
        {
          field: 'enableSign',
          label: '开启投稿',
          component: 'RadioGroup',
          colProps: { span: 12 },
          defaultValue: 'N',
          required: true,
          slot: 'enableSign',
          componentProps: {
            options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          },
        },
        {
          field: 'userLimit',
          label: '每人可投稿次数',
          component: 'InputNumber',
          defaultValue: 1,
          required: true,
          colProps: { span: 12 },
          ifShow({ values }) {
            return values.enableSign === 'Y';
          },
          componentProps: {
            min: 1,
            step: 1,
          },
        },
        {
          field: 'signStartEndDate',
          label: '投稿征集日期',
          component: 'RangePicker',
          colProps: { span: 12 },
          required: true,
          ifShow({ values }) {
            return values.enableSign === 'Y';
          },
          componentProps: {
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
          },
        },
        {
          field: 'voteTypeConfigList',
          label: '作品分类配置',
          component: 'Input',
          slot: 'voteTypeConfigList',
          colProps: { span: 24 },
          class: 'question-list',
          required: true,
          rest: true,
          defaultValue: [
            { opusType: '默认', userLimit: 1, fileLimit: 0, fileType: 'none' } as VoteTypeConfig,
          ],
        },
        // {
        //   field: 'fileType',
        //   label: '上传作品类型',
        //   component: 'RadioGroup',
        //   colProps: { span: 12 },
        //   defaultValue: 'none',
        //   required: true,
        //   ifShow({values}){return values.enableSign==='Y'},
        //   componentProps: {
        //     options: dictionary.getDictionaryOpt.get('opusFileType') as RadioGroupChildOption[],
        //   },
        // },
        // {
        //   field: 'fileLimit',
        //   label: '上传图片数量',
        //   component: 'Select',
        //   defaultValue: 1,
        //   required: true,
        //   colProps: { span: 12 },
        //   ifShow({values}){
        //     return values.enableSign==='Y' && values.fileType === 'img'
        //   },
        //   componentProps: {
        //     options: Array.from({ length: 10 }, (_, i) => i + 1).map(t => ({ label: t, value: t })),
        //   },
        // },
        {
          field: '',
          label: '投票配置',
          component: 'Divider',
        },
        {
          field: 'enableVote',
          label: '开启投票',
          component: 'RadioGroup',
          defaultValue: 'N',
          required: true,
          componentProps: {
            options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          },
        },

        {
          field: 'dailyMax',
          label: '个人每天可投票数',
          component: 'InputNumber',
          defaultValue: 5,
          required: true,
          colProps: { span: 12 },
          ifShow({ values }) {
            return values.enableVote === 'Y';
          },
          componentProps: {
            min: 1,
            step: 1,
          },
        },
        {
          field: 'dailySingleCount',
          label: '每天单个作品可投票数',
          component: 'InputNumber',
          defaultValue: 5,
          required: true,
          colProps: { span: 12 },
          ifShow({ values }) {
            return values.enableVote === 'Y';
          },
          componentProps: {
            min: 1,
          },
        },
        {
          field: 'voteStartEndDate',
          label: '投票日期',
          component: 'RangePicker',
          colProps: { span: 12 },
          required: true,
          ifShow({ values }) {
            return values.enableVote === 'Y';
          },
          componentProps: {
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
          },
        },
        {
          field: 'enableRank',
          label: '开启排行榜展示',
          component: 'RadioGroup',
          colProps: { span: 12 },
          required: true,
          defaultValue: 'Y',
          ifShow({ values }) {
            return values.enableVote === 'Y';
          },
          componentProps: {
            options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          },
        },
        {
          field: '',
          label: '奖品配置',
          component: 'Divider',
        },
        {
          field: 'awardPoolName',
          label: '奖品配置',
          component: 'CheckboxGroup',
          required: false,
          slot: 'awardPoolName',
          // componentProps: function () {
          //   return {
          //     options: dictionary.getDictionaryOpt.get('awardPoolNames'),
          //   };
          // },
        },
        {
          field: 'luckDrawInfos',
          label: '',
          component: 'Input',
          required: false,
          defaultValue: [],
          rest: true,
          slot: 'luckDrawInfos',
        },
      ];
    case ActivityType.COUPON:
    case ActivityType.SUMMER_COOLNESS:
      return [
        {
          field: 'userLimit',
          component: 'InputNumber',
          label: '个人总领取次数',
          required: false,
          rulesMessageJoinLabel: true,
          colProps: { span: 12 },
          componentProps: { step: 1, min: 1 },
        },
        {
          field: 'monthLimit',
          component: 'InputNumber',
          label: '个人每月领取次数',
          required: false,
          rulesMessageJoinLabel: true,
          colProps: { span: 12 },
          componentProps: { step: 1, min: 1 },
        },
        {
          field: 'weekLimit',
          component: 'InputNumber',
          label: '个人每周领取次数',
          required: false,
          rulesMessageJoinLabel: true,
          colProps: { span: 12 },
          componentProps: { step: 1, min: 1 },
        },
        {
          field: 'userDailyLimit',
          component: 'InputNumber',
          label: '个人每天领取次数',
          required: false,
          rulesMessageJoinLabel: true,
          colProps: { span: 12 },
          componentProps: { step: 1, min: 1 },
        },
        {
          field: 'couponInfos',
          label: '选择票券',
          component: 'Render',
          rest: true,
          slot: 'couponList',
          colProps: { span: 24 },
        },
        {
          field: 'couponIds',
          label: '选择票券',
          component: 'Render',
          show: false,
          defaultValue: [],
        },
        // {
        //   field: 'couponIds',
        //   label: '选择票券',
        //   component: 'ApiSelect',
        //   componentProps: () => {
        //     return {
        //       mode: 'multiple',
        //       placeholder: '请选票券',
        //       api: onSelectList,
        //       resultField: 'data',
        //       params: {
        //         pageSize: 0,
        //         orderBy: 'auto_id',
        //         sortType: 'desc',
        //         grantType: 'receive',
        //         activityId,
        //       },
        //       immediate: true,
        //       showSearch: true,
        //       filterOption: (input: string, option: any) => {
        //         return option.couponName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        //       },
        //       fieldNames: { label: 'couponName', value: 'couponId' },
        //     };
        //   },
        // },
      ];
    case ActivityType.WALK:
      return [
        {
          field: 'signLimit',
          label: '签到工币上限',
          required: true,
          component: 'InputNumber',
          colProps: { span: 8 },
          className: '!w-full',
          componentProps: {
            min: 0,
          },
          rulesMessageJoinLabel: true,
        },
        {
          field: 'singnInit',
          label: '签到工币初始数',
          required: true,
          component: 'InputNumber',
          colProps: { span: 8 },
          rulesMessageJoinLabel: true,
          componentProps: {
            min: 0,
          },
          className: '!w-full',
        },
        {
          field: 'signRiseCount',
          label: '签到奖励连续增长数',
          required: true,
          component: 'InputNumber',
          colProps: { span: 8 },
          rulesMessageJoinLabel: true,
          componentProps: {
            min: 0,
          },
          className: '!w-full',
        },
        {
          field: 'onceEx',
          label: '单次兑换工币数',
          required: true,
          component: 'InputNumber',
          colProps: { span: 8 },
          className: '!w-full',
          rulesMessageJoinLabel: true,
          componentProps: {
            min: 0,
          },
        },
        {
          field: 'exCount',
          label: '每日兑换次数上限',
          required: true,
          component: 'InputNumber',
          colProps: { span: 8 },
          className: '!w-full',
          rulesMessageJoinLabel: true,
          componentProps: {
            min: 0,
          },
        },
        {
          field: 'exStep',
          label: '每次所需步数',
          required: true,
          component: 'InputNumber',
          colProps: { span: 8 },
          rulesMessageJoinLabel: true,
          componentProps: {
            min: 0,
          },
          className: '!w-full',
        },
        {
          field: 'friendEx',
          label: '邀请好友奖励工币数',
          required: true,
          component: 'InputNumber',
          colProps: { span: 8 },
          rulesMessageJoinLabel: true,
          componentProps: {
            min: 0,
          },
          className: '!w-full',
        },
        {
          field: 'friendExMax',
          label: '最大邀请好友数',
          required: true,
          component: 'InputNumber',
          colProps: { span: 8 },
          rulesMessageJoinLabel: true,
          componentProps: {
            min: 0,
          },
          className: '!w-full',
        },
        {
          field: 'friendExCountMax',
          label: '最大邀请好友总数',
          required: true,
          component: 'InputNumber',
          colProps: { span: 8 },
          className: '!w-full',
          rulesMessageJoinLabel: true,
          componentProps: {
            min: 0,
          },
        },
        {
          field: 'otherLimit',
          label: '其它工币上限(每日)',
          required: true,
          component: 'InputNumber',
          colProps: { span: 8 },
          className: '!w-full',
          rulesMessageJoinLabel: true,
          componentProps: {
            min: 0,
          },
        },
        {
          field: 'luckyDrawUrl',
          label: '抽奖地址',
          component: 'Input',
          colProps: { span: 8 },
          rulesMessageJoinLabel: true,
        },
        {
          field: 'phoneNumber',
          label: '客服电话',
          component: 'Input',
          colProps: { span: 8 },
          // required: true,
          rulesMessageJoinLabel: true,
        },
        {
          field: 'dailyTime',
          required: true,
          label: '兑换开放时间',
          component: 'TimeRangePicker',
          colProps: { span: 8 },
          componentProps: {
            placeholder: ['起始时间', '结束时间'],
            showNow: true,
          },
        },
        {
          field: 'rankSwitch',
          label: '排行榜',
          component: 'RadioGroup',
          colProps: { span: 8 },
          rulesMessageJoinLabel: true,
          defaultValue: 'Y',
          componentProps: {
            options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          },
        },
        {
          field: 'friendRankSwitch',
          label: '好友排行榜',
          component: 'RadioGroup',
          colProps: { span: 8 },
          rulesMessageJoinLabel: true,
          ifShow: ({ values }) => values.rankSwitch === 'Y',
          defaultValue: 'Y',
          componentProps: {
            options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          },
        },
        {
          field: 'prizeInfos',
          label: '',
          component: 'Render',
          rest: true,
          slot: 'prizeList',
          defaultValue: [{} as Question],
          colProps: { span: 24 },
        },
      ];
    default:
      return [];
  }
};

const getPercentage = (useCount, prizeCount) => {
  if (!useCount) return 100;
  if (!prizeCount) return 0;
  const percentage = (prizeCount - useCount) / prizeCount;
  return Math.floor(percentage * 100);
};

export const tableLotteryColum = (
  disabled?: boolean,
  activityMode?: string,
  activityId?: string,
  open?: Function,
  awardPoolName?: string
): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '名次',
      dataIndex: 'rankRange',
      width: 100,
      ifShow: awardPoolName === 'award3',
      customRender: ({ record }) => {
        return (
          <Input.TextArea
            value={record.rankRange}
            onChange={e => (record.rankRange = e.target.value)}
            placeholder="请输入名次（例：1 或者2~50）"
            autocomplete={'off'}
            disabled={disabled}
          />
        );
      },
    },
    {
      title: '奖项名称',
      dataIndex: 'prizeName',
      width: 100,
      customRender: ({ record }) => {
        return (
          <Input.TextArea
            value={record.prizeName}
            onChange={e => (record.prizeName = e.target.value)}
            placeholder="请输入奖项名称"
            autocomplete={'off'}
            disabled={disabled}
          />
        );
      },
    },
    {
      title: '奖项类型',
      dataIndex: 'prizeType',
      width: 100,
      customRender: ({ record }) => {
        let options = dictionary.getDictionaryOpt.get('prize') as DefaultOptionType[];
        if (activityMode === ActivityType.INTEGRAL_TREE) {
          options = filter(options, v => includes(['7', '3'], v.value));
        } else if (activityMode === ActivityType.WALK) {
          options = filter(options, v => v.value !== '3');
        }

        return (
          <Select
            value={record.prizeType}
            options={options}
            placeholder="请选择奖项类型"
            onChange={value => {
              record.prizeType = value;
              if (value !== '3') {
                record.receiveType === '3';
              }
            }}
            disabled={disabled}
          ></Select>
        );
      },
    },
    {
      title: '图片',
      dataIndex: 'prizeImg',
      width: 100,
      customRender: ({ record }) => {
        return (
          <CropperForm
            operateType={16}
            disabled={disabled}
            value={record.prizeImg}
            onChange={e => (record.prizeImg = e)}
          />
        );
      },
    },
    {
      title: '领取方式',
      dataIndex: 'receiveType',
      width: 100,
      customRender: ({ record }) => {
        let options = dictionary.getDictionaryOpt.get('receiveType') as DefaultOptionType[];
        if (record.prizeType === '3') {
          options = filter(cloneDeep(options), v => v.value !== '3');
        }
        return (
          <Select
            value={record.prizeType !== '3' ? '3' : record.receiveType}
            options={options}
            placeholder="请选择领取方式"
            onChange={value => (record.receiveType = value)}
            disabled={disabled || record.prizeType !== '3'}
          ></Select>
        );
      },
    },
    {
      title: '奖品内容',
      dataIndex: 'prizeContent',
      width: 100,
      customRender: ({ record }) => {
        function getContent() {
          switch (record.prizeType) {
            case '2': //积分
              return (
                <InputNumber
                  disabled={disabled}
                  value={record.prizeContent}
                  placeholder="请输入积分"
                  min={1}
                  autocomplete="off"
                  onChange={value => (record.prizeContent = value)}
                />
              );
            case '5': //红包
              return (
                <InputNumber
                  disabled={disabled}
                  value={record.prizeContent}
                  placeholder="请输入红包金额"
                  min={0.03}
                  addonAfter={'元'}
                  autocomplete="off"
                  onChange={value => (record.prizeContent = value)}
                />
              );
            case '7':
              return (
                <div class={'flex'}>
                  <ApiSelect
                    placeholder={'请选择票券'}
                    value={record.productId}
                    api={onSelectList}
                    filterOption={(input: string, option: any) => {
                      return option.couponName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                    resultField={'data'}
                    params={{
                      pageSize: 0,
                      orderBy: 'auto_id',
                      sortType: 'desc',
                      grantType: 'lottery',
                      dataSource: activityMode === ActivityType.BIRTHDAY ? 'birthday' : 'coupon',
                      activityId,
                    }}
                    alwaysLoad={true}
                    immediate={true}
                    showSearch={true}
                    onChange={e => {
                      record.productId = e;
                    }}
                    fieldNames={{ label: 'couponName', value: 'couponId' }}
                  />
                  <SearchOutlined
                    onClick={e => {
                      if (!record?.productId) {
                        return;
                      }
                      open?.({
                        dataSource: activityMode === ActivityType.BIRTHDAY ? 'birthday' : 'coupon',
                        couponId: record.productId,
                      });
                    }}
                  />
                </div>
              );
            default:
              return (
                <Input.TextArea
                  disabled={disabled}
                  value={record.prizeContent}
                  placeholder="请输入奖品内容"
                  autocomplete="off"
                  onChange={e => (record.prizeContent = e.target.value)}
                />
              );
          }
        }

        return <div>{getContent()}</div>;
      },
    },
    {
      title: '奖品总数',
      dataIndex: 'prizeCount',
      width: 80,
      ifShow: awardPoolName !== 'award3',
      customRender: ({ record }) => {
        return (
          <InputNumber
            disabled={disabled}
            value={record.prizeCount}
            placeholder="请输入奖品数量"
            min={1}
            autocomplete="off"
            onChange={value => (record.prizeCount = value)}
          />
        );
      },
    },
    {
      title: '每日发放数量',
      dataIndex: 'dailyCount',
      width: 80,
      ifShow: !includes([ActivityType.INTEGRAL_TREE], activityMode) && awardPoolName !== 'award3',
      customRender: ({ record }) => {
        return (
          <InputNumber
            disabled={disabled}
            value={record.dailyCount}
            placeholder="请输入每日发放数量"
            min={1}
            autocomplete="off"
            onChange={value => (record.dailyCount = value)}
          />
        );
      },
    },
    {
      title: '认证会员中奖率(%)',
      dataIndex: 'percentage',
      width: 50,
      ifShow: !includes([ActivityType.INTEGRAL_TREE], activityMode) && awardPoolName !== 'award3',
      customRender: ({ record }) => {
        return (
          <InputNumber
            disabled={disabled}
            value={record.percentage}
            placeholder="请输入中奖率"
            min={0}
            max={100}
            autocomplete="off"
            onChange={value => (record.percentage = (value as number) > 100 ? 100 : value)}
          />
        );
      },
    },
    {
      title: '注册用户中奖率(%)',
      dataIndex: 'registerPercentage',
      width: 50,
      ifShow:
        !includes([ActivityType.BIRTHDAY, ActivityType.INTEGRAL_TREE], activityMode) &&
        awardPoolName !== 'award3',
      customRender: ({ record }) => {
        return (
          <InputNumber
            disabled={disabled}
            value={record.registerPercentage}
            placeholder="请输入中奖率"
            min={0}
            max={100}
            autocomplete="off"
            onChange={value => (record.registerPercentage = (value as number) > 100 ? 100 : value)}
          />
        );
      },
    },
    {
      title: '领取情况(已发放/剩余)',
      dataIndex: '',
      resizable: true,
      width: 100,
      customRender: ({ record }) => {
        const divideAll = record.prizeCount - record.useCount;
        const divideDaily = record.dailyCount - record.dailyUseCount;
        const all = (
          <>
            <div style={{ width: '100%', textAlign: 'left' }}>
              总体情况：{record.useCount || 0} /{isNaN(divideAll) ? 0 : divideAll}
            </div>
            <Progress
              width={70}
              percent={getPercentage(record.useCount, record.prizeCount)}
              format={percent => `${percent}%`}
              strokeColor={'#40c340de'}
              size="small"
            />
          </>
        );
        const daily = (
          <>
            <div style={{ width: '100%', textAlign: 'left' }}>
              今日领取：{record.dailyUseCount || 0} /{isNaN(divideDaily) ? 0 : divideDaily}
            </div>
            <Progress
              width={70}
              percent={getPercentage(record.dailyUseCount, record.dailyCount)}
              format={percent => `${percent}%`}
              strokeColor={'#6f7ad3'}
              size="small"
            />
          </>
        );

        return (
          <div>
            {all}
            {!record.dailyCount || daily}
          </div>
        );
      },
    },
  ];
};

export const voteTypeConfigColumns = (disabled?: boolean, enableSign?: string): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '分类名称',
      dataIndex: 'opusType',
      customRender: ({ record }) => {
        return (
          <Input
            value={record.opusType}
            onChange={e => (record.opusType = e.target.value)}
            placeholder="请输入分类名称"
            autocomplete={'off'}
            disabled={disabled}
          />
        );
      },
    },
    {
      title: '用户单分类最大投稿次数',
      dataIndex: 'userLimit',
      ifShow: enableSign === 'Y',
      customRender: ({ record }) => {
        return (
          <InputNumber
            value={record.userLimit}
            onChange={e => (record.userLimit = e)}
            placeholder="用户单分类最大投稿次数"
            min={1}
            autocomplete={'off'}
            disabled={disabled}
          />
        );
      },
    },
    {
      title: '用户单分类最大审核通过数',
      dataIndex: 'passLimit',
      ifShow: enableSign === 'Y',
      customRender: ({ record }) => {
        return (
          <InputNumber
            value={record.passLimit}
            onChange={e => (record.passLimit = e)}
            placeholder="用户单分类最大审核通过数"
            min={1}
            autocomplete={'off'}
            disabled={disabled}
          />
        );
      },
    },
    {
      title: '附件类型',
      dataIndex: 'fileType',
      width: 300,
      customRender: ({ record }) => {
        let options = dictionary.getDictionaryOpt.get('opusFileType') as DefaultOptionType[];

        return (
          <Select
            value={record.fileType}
            options={options}
            placeholder="请选择奖项类型"
            onChange={selectedValues => {
              if (record.fileType?.includes('none') && selectedValues?.length > 1) {
                record.fileType = selectedValues.filter(v => v !== 'none');
              } else if (!record.fileType?.includes('none') && selectedValues.includes('none')) {
                record.fileType = ['none'];
              } else {
                record.fileType = selectedValues;
              }
              if (!selectedValues?.includes('img')) {
                record.fileLimit = '0';
              }
            }}
            mode={'multiple'}
            disabled={disabled}
          ></Select>
        );
      },
    },
    {
      title: '上传图片数量上限',
      dataIndex: 'fileLimit',
      customRender: ({ record }) => {
        return (
          <InputNumber
            value={record.fileLimit}
            onChange={e => (record.fileLimit = e)}
            placeholder="请输入上传图片数量上限"
            min={1}
            max={10}
            autocomplete={'off'}
            disabled={disabled || !record.fileType?.includes('img')}
          />
        );
      },
    },
    {
      width: 80,
      title: '操作',
      dataIndex: 'action',
      ifShow: !disabled,
    },
  ];
};

export const realPrizeColum = (disabled?: boolean): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '兑换品名称',
      dataIndex: 'prizeName',
      width: 100,
      customRender: ({ record }) => {
        return (
          <Input
            value={record.prizeName}
            onChange={e => (record.prizeName = e.target.value)}
            placeholder="请输入兑换品名称"
            autocomplete={'off'}
            disabled={disabled}
          ></Input>
        );
      },
    },
    {
      title: '图片',
      dataIndex: 'prizeImg',
      width: 80,
      customRender: ({ record }) => {
        return (
          <CropperForm
            operateType={16}
            disabled={disabled}
            value={record.prizeImg}
            onChange={e => (record.prizeImg = e)}
          />
        );
      },
    },
    {
      title: '兑换品类型',
      dataIndex: 'receiveType',
      width: 80,
      customRender: ({ record }) => {
        const options = dictionary.getDictionaryOpt.get('prizeReceiveType') as DefaultOptionType[];
        return (
          <Select
            value={record.receiveType}
            options={options}
            placeholder="请选择兑换品类型"
            onChange={value => {
              record.receiveType = value;
            }}
            disabled={disabled}
          ></Select>
        );
      },
    },
    {
      title: '兑换品内容',
      dataIndex: 'prizeContent',
      width: 100,
      customRender: ({ record }) => {
        return (
          <Input
            value={record.prizeContent}
            onChange={e => (record.prizeContent = e.target.value)}
            placeholder="请输入兑换品内容"
            autocomplete={'off'}
            disabled={disabled}
          ></Input>
        );
      },
    },
    {
      title: '单次兑换所需工币',
      dataIndex: 'prizeEx',
      width: 60,
      customRender: ({ record }) => {
        return (
          <InputNumber
            disabled={disabled}
            value={record.prizeEx}
            placeholder="请输入单次兑换所需工币"
            min={1}
            class={`!w-full`}
            autocomplete="off"
            onChange={value => (record.prizeEx = value)}
          />
        );
      },
    },
    {
      title: '兑换品数量',
      dataIndex: 'prizeCount',
      width: 60,
      customRender: ({ record }) => {
        return (
          <InputNumber
            disabled={disabled}
            value={record.prizeCount}
            placeholder="请输入兑换品数量"
            min={1}
            class={`!w-full`}
            autocomplete="off"
            onChange={value => (record.prizeCount = value)}
          />
        );
      },
    },
    {
      title: '已领取',
      dataIndex: 'useCount',
      customRender: ({ record }) => {
        return (
          <div class={`text-left`}>
            <div>已领数量：{record.useCount ?? 0}</div>
            <div>
              剩余数量：
              {record.prizeCount && record.useCount ? record.prizeCount - record.useCount : 0}
            </div>
            <div class={`w-58`}>
              <Progress
                percent={toNumber(
                  (divide(record.prizeCount - record.useCount, record.prizeCount) * 100).toFixed(2)
                )}
                format={percent => `${percent}%`}
                strokeColor={'#40c340de'}
                strokeWidth={15}
                size="small"
              ></Progress>
            </div>
          </div>
        );
      },
      width: 120,
    },
  ];
};
