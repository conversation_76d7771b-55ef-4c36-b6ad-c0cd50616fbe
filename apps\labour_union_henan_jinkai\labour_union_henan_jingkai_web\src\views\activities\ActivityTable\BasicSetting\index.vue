<template>
  <Row
    class="divide-x-reverse h-72vh overflow-y-auto"
    :id="`basic-setting-${activityType}`"
  >
    <Col class="">
      <BasicForm
        @register="registerSetting"
        :class="`!p-5px ${disabledClass}`"
      >
        <template
          #[item]="data"
          v-for="item in Object.keys($slots)"
        >
          <slot
            :name="item"
            v-bind="data || {}"
          ></slot>
        </template>
        <template #topicInfoList="{ model, field }">
          <TopicInfoList
            :list="model[field]"
            :disabled="disabled"
            :options="options"
            :activityType="activityType"
            @change="list => (model[field] = list)"
          />
        </template>
        <template #userForm="{ field, model }">
          <UserForm
            :list="model[field]"
            :disabled="disabled"
            :activityType="activityType"
            @change="list => (model[field] = list)"
          />
        </template>
        <template #enableSign="{ field, model }">
          <RadioGroup
            v-model:value="model[field]"
            :disabled="disabled"
            @change="e => enableSignChange(e)"
            name="radioGroup"
            :options="dictionary.getDictionaryOpt.get('YesOrNo')"
          />
        </template>
        <template #voteContent="{ field, model }">
          <VoteContent
            :list="model[field]"
            :disabled="disabled"
            :activityType="activityType"
            @change="list => (model[field] = list)"
          />
        </template>
        <template #voteTypeConfigList="{ field, model }">
          <div class="p-5px">
            <!-- <Affix :target="container"> -->
            <div
              class="flex items-center my-5px"
              v-if="!disabled"
            >
              <a-button
                type="primary"
                shape="round"
                @click="handleAddQuestions(model, field)"
                >添加分类</a-button
              >
            </div>
            <!-- </Affix> -->

            <BasicTable
              @register="registerVoteTypeTable"
              class="dy-prize-table"
            >
              <template #bodyCell="{ column, index }">
                <template v-if="column.key === 'action'">
                  <TableAction
                    :actions="[
                      {
                        icon: 'ant-design:delete-twotone',
                        label: '删除',
                        type: 'text',
                        danger: true,
                        disabled: disabled,
                        onClick: handleDeleteVoteType.bind(null, index),
                      },
                    ]"
                  />
                </template>
              </template>
            </BasicTable>
          </div>
        </template>
        <template #prizeList="{ model, field }">
          <div class="p-5px">
            <Affix :target="container">
              <div
                class="flex items-center my-5px"
                v-if="!disabled"
              >
                <a-button
                  type="primary"
                  shape="round"
                  @click="handleAddQuestions(model, field)"
                  >添加奖品</a-button
                >
              </div>
            </Affix>

            <BasicTable
              @register="registerTable"
              class="dy-prize-table"
              :data-source="model[field]"
            >
              <template #bodyCell="{ column, index }">
                <template v-if="column.key === 'action'">
                  <TableAction
                    :actions="[
                      {
                        icon: 'ant-design:delete-twotone',
                        label: '删除',
                        type: 'text',
                        danger: true,
                        disabled: disabled,
                        onClick: handleDeletePrize.bind(null, index),
                      },
                    ]"
                  />
                </template>
                <!-- <template v-if="column.key === 'prizeImg'">
                  <div class="w-70px h-70px inline-block">
                    <CropperImg
                      :value="record.prizeImg"
                      :operate-type="ActivityDocAddr[activityType]"
                      :disabled="disabled"
                      @change="filePath => (record.prizeImg = filePath)"
                    />
                  </div>
                </template> -->
              </template>
            </BasicTable>
          </div>
        </template>

        <template #couponList="{ model, field }">
          <BasicTable
            class="!h-auto"
            @register="registerCouponTable"
            :canResize="false"
            :data-source="model[field]"
            :maxHeight="200"
          >
            <template
              v-if="!disabled"
              #toolbar
            >
              <a-button
                type="primary"
                @click="handleChooseCoupon()"
                >选择票券</a-button
              >
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      icon: 'carbon:task-view',
                      label: '详情',
                      type: 'default',
                      onClick: handleClick.bind(null, record),
                    },
                    {
                      icon: 'fluent:delete-16-filled',
                      label: '删除',
                      type: 'primary',
                      danger: true,
                      ifShow: !disabled,
                      onClick: handleDeleteCoupon.bind(null, record),
                    },
                  ]"
                />
              </template>
            </template>
          </BasicTable>

          <ChooseCouponModal
            :canFullscreen="false"
            width="60%"
            @register="registerChooseCouponModal"
            @success="handleSuccess"
          />
        </template>
        <template #awardPoolName="{ model, field }">
          <CheckboxGroup
            v-model:value="model[field]"
            :disabled="disabled"
            :options="awardPoolNames as CheckboxOptionType[]"
            @change="v => handleChangeAwardPoolName(v, model, field)"
          />
        </template>
        <template #luckDrawInfos="{ model }">
          <Tabs
            v-model:activeKey="activeKey"
            v-if="model['awardPoolName'] && model['awardPoolName'].length"
          >
            <template
              v-for="item in model['luckDrawInfos']"
              :key="item.votePrize.awardPoolName"
            >
              <Tabs.TabPane
                v-if="includes(model['awardPoolName'], item.votePrize.awardPoolName)"
                :tab="getLabel(item.votePrize.awardPoolName)"
                :key="item.votePrize.awardPoolName"
              >
                <VotePrize
                  v-model:value="item.votePrize"
                  :disabled="disabled"
                  :activityId="activityId"
                  :mainType="mainType"
                  :handleClick="handleClick"
                />
              </Tabs.TabPane>
            </template>
          </Tabs>
          <!-- <div>
            <template
              v-for="item in formList"
              :key="item.key"
            >
              <a-form
                :model="item.formData"
                :labelCol="{ span: 6 }"
                v-if="activeKey === item.formData.awardPoolName"
              >
                <a-form-item label="个人总中奖次数">
                  <a-input-number v-model:value="item.formData.awardCountMax" />
                </a-form-item>
                <a-form-item label="个人每日中奖次数">
                  <a-input-number v-model:value="item.formData.dailyAwardCount" />
                </a-form-item>
              </a-form>
            </template>
          </div> -->
        </template>
      </BasicForm>

      <TicketConfigModal
        @register="registerModal"
        width="80%"
        :canFullscreen="false"
      />
    </Col>
  </Row>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, unref, useAttrs } from 'vue';
import { ActivityType, LuckDrawInfo, Question, VoteTypeConfig } from '../../activities.d';
import { modalFormSchema, realPrizeColum, tableLotteryColum, voteTypeConfigColumns } from './data';
import { Affix, CheckboxGroup, Col, RadioGroup, Row, Tabs } from 'ant-design-vue';
import { BasicForm, useForm } from '@/components/Form';
import { SettingActionProps, SettingActionType } from './useSetting';
import { filter, find, includes, isEmpty, join, map, remove, split, uniqBy } from 'lodash-es';
import { BasicTable, TableAction, useTable } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { TopicInfoList, UserForm, VoteContent } from './components';
import { useModal } from '@/components/Modal';
import ChooseCouponModal from '@/views/activities/ActivityTable/ChooseCouponModal.vue';
import TicketConfigModal from '@/views/coupon/TicketConfigModal.vue';
import { CheckboxOptionType } from 'ant-design-vue/lib';
import VotePrize from './components/VotePrize.vue';

const emit = defineEmits(['register']);
const enableSign = ref<string>('N');

const attrs = useAttrs();

const activityType = computed(() => {
  return attrs.activityType as ActivityType;
});

const activityId = computed(() => {
  return attrs.activityId as string;
});

const mainType = computed(() => {
  return attrs.mainType as string;
});

const activeKey = ref('award1');

const disabled = ref(false);

const dictionary = useDictionary();

const awardPoolNames = computed(() => {
  return dictionary.getDictionaryOpt.get('awardPoolNames') as Recordable[];
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const formItem = computed(() => {
  return modalFormSchema(
    unref(activityType) as string,
    unref(mainType) as string,
    unref(activityId)
  );
});

const columns = computed(() => {
  if (unref(activityType) === ActivityType.WALK) {
    return realPrizeColum(unref(disabled));
  }
  return tableLotteryColum(unref(disabled), unref(mainType), unref(activityId), handleClick);
});

const voteTypeColumns = computed(() => {
  return voteTypeConfigColumns(unref(disabled), unref(enableSign));
});

const container = computed(() => {
  return () => {
    const element = document.querySelector(`#basic-setting-${unref(activityType)}`);
    return element ? (element as HTMLElement) : window;
  };
});

const options = computed(() => {
  return [ActivityType.QUIZ, ActivityType.INCLUSIVE_YJWD].includes(unref(activityType))
    ? filter(
        dictionary.getDictionaryOpt.get('activityOptionType'),
        (v: Recordable) => v.value === 'select' || v.value === 'radio'
      )
    : dictionary.getDictionaryOpt.get('activityOptionType');
});

const enableSignChange = e => {
  enableSign.value = e?.target.value;
};

const [registerModal, { openModal: openCouponInfo }] = useModal();

// 获取数据字典label
const getLabel = val => dictionary.getDictionaryMap.get(`awardPoolNames_${val}`)?.dictName || '';

const handleChangeAwardPoolName = (values, m, f) => {
  activeKey.value = !isEmpty(values) ? values[values.length - 1] : '';
  if (!isEmpty(m['luckDrawInfos'])) {
    const luck = find(m['luckDrawInfos'], v => v.votePrize.awardPoolName === activeKey.value);
    if (!luck) {
      m['luckDrawInfos'].push({
        votePrize: {
          awardPoolName: activeKey.value,
          awardCountMax: 0,
          dailyAwardCount: 0,
          prizeInfos: [],
        } as unknown as LuckDrawInfo,
      });
    }
  } else {
    m['luckDrawInfos'] = [
      {
        votePrize: {
          awardPoolName: activeKey.value,
          awardCountMax: 0,
          dailyAwardCount: 0,
          prizeInfos: [],
        } as unknown as LuckDrawInfo,
      },
    ];
  }
};

// 票券详情
function handleClick(record) {
  openCouponInfo(true, {
    state: 'view',
    record,
  });
}
// 商品商户表格注册
const [
  registerCouponTable,
  {
    deleteTableDataRecord: deleteCoupon,
    setTableData: setCouponTableDate,
    getDataSource: getCouponDataSource,
  },
] = useTable({
  rowKey: 'couponId',
  columns: [
    {
      title: '票券名称',
      dataIndex: 'couponName',
    },
  ],
  useSearchForm: false,
  bordered: true,
  pagination: false,
  showIndexColumn: true,
  canResize: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',

    fixed: undefined,
    ifShow: !unref(disabled),
  },
});
// 选择票券列表
const [registerChooseCouponModal, { openModal }] = useModal();

// 注册form
const [registerSetting, { validate, resetFields, setFieldsValue, setProps: setPropsForm }] =
  useForm({
    labelWidth: 170,
    schemas: formItem,
    showActionButtonGroup: false,
  });
// 注册抽奖table
const [registerTable, { getDataSource, setTableData, insertTableDataRecord }] = useTable({
  rowKey: 'id',
  showTableSetting: false,
  columns: columns,
  bordered: true,
  showIndexColumn: true,
  pagination: false,
  actionColumn: {
    width: 50,
    title: '操作',
    dataIndex: 'action',
  },
});

// 注册作品类型table
const [
  registerVoteTypeTable,
  {
    getDataSource: getVoteTypeDataSource,
    setTableData: setVoteTypeDataSource,
    insertTableDataRecord: insertVoteTypeTableDataRecord,
  },
] = useTable({
  rowKey: 'id',
  showTableSetting: false,
  columns: voteTypeColumns,
  bordered: true,
  showIndexColumn: true,
  pagination: false,
});

// 表单取值
async function validateInfo() {
  const { luckDrawInfos, ...values } = await validate();

  const result = {
    ...values,
    awardPoolName: !isEmpty(values.awardPoolName) ? join(values.awardPoolName, ',') : undefined,
  };
  try {
    result['voteTypeConfigList'] = map(getVoteTypeDataSource(), (v: Recordable) => ({
      ...v,
      fileType: join(v.fileType, ','),
    }));

    if (values.awardPoolName?.length) {
      result['luckDrawInfos'] = luckDrawInfos
        .filter(t => values.awardPoolName.includes(t.votePrize.awardPoolName))
        .map(t => ({ ...t.votePrize }));
    } else {
      result['luckDrawInfos'] = [];
    }
  } catch (error) {
    console.log(error, 'error');
  }

  return result;
}

async function reset() {
  await resetFields();
}

async function setProps({ disabled: d }: Partial<SettingActionProps>) {
  disabled.value = !!d;
  await setPropsForm({ disabled: d });
}

async function setTableDataPrize(datas) {
  setTableData(datas);
}

// 设置表单值
async function setValues(values: Recordable<any>) {
  const { awardPoolName, luckDrawInfos, ...rest } = values;
  enableSign.value = values.enableSign;
  await setFieldsValue({
    ...rest,
    awardPoolName: isEmpty(awardPoolName) ? undefined : split(awardPoolName, ','),
    luckDrawInfos: map(luckDrawInfos, v => ({
      votePrize: { ...v },
    })),
  });

  !isEmpty(rest.voteTypeConfigList) &&
    setVoteTypeDataSource(
      map(rest.voteTypeConfigList, v => ({ ...v, fileType: split(v.fileType, ',') }))
    );

  !!rest.couponInfos && setCouponTableDate(rest.couponInfos);
}

async function getDataSourcePrize() {
  return getDataSource();
}

// 选择票券
function handleChooseCoupon() {
  openModal(true, { activityId: unref(activityId) });
}

// 设置商户商品列表
function handleSuccess(rows) {
  const list = uniqBy([...getCouponDataSource(), ...rows], t => t.couponId);
  setCouponTableDate(list);
  //赋值
  if (getCouponDataSource()?.length) {
    setFieldsValue({ couponIds: list?.map(t => t.couponId), couponInfos: list });
  } else {
    setFieldsValue({ couponIds: null, couponInfos: null });
  }
}

// 注册hooks
const settingActionType: Partial<SettingActionType> = {
  validate: validateInfo,
  reset: reset,
  setProps: setProps,
  setTableDataPrize: setTableDataPrize,
  setValues: setValues,
  getDataSourcePrize: getDataSourcePrize,
};

// 添加
function handleAddQuestions(model, field) {
  switch (unref(activityType)) {
    case ActivityType.WALK:
    case ActivityType.BIRTHDAY:
    case ActivityType.LOTTERY:
      insertTableDataRecord({} as Question);
      model[field] = getDataSource();
      break;
    case ActivityType.MULTIPLE_VOTE:
    case ActivityType.VOTE:
      insertVoteTypeTableDataRecord({
        opusType: '默认',
        userLimit: 1,
        fileLimit: 0,
        passLimit: 1,
        fileType: ['none'],
      } as VoteTypeConfig);
      break;
  }
}

// 删除table
function handleDeletePrize(index: number) {
  remove(getDataSource(), (_, k) => k === index);
}

// 删除table
function handleDeleteVoteType(index: number) {
  remove(getVoteTypeDataSource(), (_, k) => k === index);
}

// 删除商品或商户
function handleDeleteCoupon(record) {
  deleteCoupon(record.couponId);
  if (getCouponDataSource()?.length) {
    setFieldsValue({
      couponIds: getCouponDataSource()?.map(t => t.couponId),
      couponInfos: getCouponDataSource(),
    });
  } else {
    setFieldsValue({ couponIds: null, couponInfos: null });
  }
}

onMounted(() => {
  emit('register', settingActionType);
});
</script>
