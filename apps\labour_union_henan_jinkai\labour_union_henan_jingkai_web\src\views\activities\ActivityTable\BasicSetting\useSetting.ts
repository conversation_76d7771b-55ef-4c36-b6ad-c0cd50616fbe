import { nextTick, onUnmounted, ref, unref, watch } from 'vue';
import { isProdMode } from '@/utils/env';
import { error } from '@/utils/log';
import { getDynamicProps } from '@monorepo-yysz/utils';
import { DynamicProps } from '@monorepo-yysz/types';

export interface SettingActionProps {
  disabled?: boolean;
  activityType?: string;
}

type Props = Partial<DynamicProps<SettingActionProps>>;

export interface SettingActionType {
  validate: () => Promise<any>;
  reset: () => Promise<void>;
  setProps: (props: Partial<SettingActionProps>) => Promise<void>;
  setTableDataPrize: (datas) => Promise<void>;
  setValues: <T>(values: Recordable) => Promise<void>;
  getDataSourcePrize: () => Promise<any>;
}

export type RegisterFn = (domInstance: SettingActionType) => void;

export type UseSettingReturnType = [RegisterFn, SettingActionType];

export function useSetting(props?: Props): UseSettingReturnType {
  const domRef = ref<Nullable<SettingActionType>>(null);
  const loadedRef = ref<Nullable<boolean>>(false);

  async function getDom() {
    await nextTick();
    const dom = unref(domRef);
    if (!dom) {
      error('no dom!');
    }
    return dom as SettingActionType;
  }

  function register(instance: SettingActionType) {
    isProdMode() &&
      onUnmounted(() => {
        domRef.value = null;
        loadedRef.value = null;
      });
    if (unref(loadedRef) && isProdMode() && instance === unref(domRef)) return;

    domRef.value = instance;
    loadedRef.value = true;

    watch(
      () => props,
      () => {
        props && instance.setProps(getDynamicProps(props));
      },
      {
        immediate: true,
        deep: true,
      }
    );
  }

  const methods: SettingActionType = {
    validate: async (): Promise<Recordable> => {
      const dom = await getDom();
      return dom.validate();
    },
    reset: async () => {
      getDom().then(async dom => {
        await dom.reset();
      });
    },
    setProps: async (props: Partial<SettingActionProps>) => {
      const dom = await getDom();
      dom.setProps(props);
    },
    setTableDataPrize: async datas => {
      const dom = await getDom();
      dom.setTableDataPrize(datas);
    },
    setValues: async <T>(values: T) => {
      const dom = await getDom();
      dom.setValues<T>(values);
    },
    getDataSourcePrize: async (): Promise<Recordable> => {
      const dom = await getDom();
      return dom.getDataSourcePrize();
    },
  };
  return [register, methods];
}
