<template>
  <div
    class="report h-75vh overflow-auto"
    id="details"
  >
    <!-- 统计图表 -->
    <div
      class="report-container px-20px"
      id="section1"
    >
      <div class="flex w-full main-data pb-20px mb-30px">
        <div
          class="flex items-center justify-between w-16/100 text-14px mr-30px"
          v-for="item in overallData"
          :key="item.key"
        >
          <div class="flex items-center">
            <div
              class="w-50px h-50px flex items-center justify-center bg-[#fff5ef] rounded-8px mr-15px"
              :style="{ backgroundColor: item.background }"
            >
              <img
                :src="item.icon"
                alt=""
                class="w-25px h-25px"
              />
            </div>
            <div class="">
              <div class="mb-12px text-[#666]">{{ item.title }}</div>
              <div class="text-18px font-550 flex items-center">
                {{ item.data || '0' }}
                <span
                  class="text-0.6vw text-green-300 ml-10px"
                  v-if="item.todayAddCount && item.todayAddCount > 0"
                >
                  ↑ {{ item.todayAddCount }}
                </span>
              </div>
            </div>
          </div>
          <div class="w-1px h-30px bg-[#f0f0f0]"></div>
        </div>
      </div>
      <div
        class="echarts relative flex mb-50px"
        ref="echarts"
      >
        <div class="w-1/2 flex-shrink-0 border-r-1px border-r-[#f0f0f0] pr-20px">
          <div class="text-15px echarts-title pl-8px">数据统计</div>
          <div class="h-50vh">
            <ActivityAnalysis :sourceData="option" />
          </div>
        </div>
        <div class="w-1/2 flex-shrink-0 relative pl-20px">
          <a-button
            class="absolute top-0 right-120px z-9999"
            type="primary"
            icon=""
            @click="backFrontLevel"
            v-if="level === 2"
            >返回上一级</a-button
          >
          <div class="text-15px echarts-title pl-8px">区域数据统计</div>
          <div class="h-50vh">
            <ActivityAnalysis
              :sourceData="areaNameOption"
              @clickEcharts="clickEcharts"
              type="area"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="false"
      class="report-container px-20px pt-10px"
      id="section2"
    >
      <div
        class="text-16px mb-20px mt-10px font-600"
        id="section2"
        >红包发放数据</div
      >
      <div class="echarts relative flex mb-50px">
        <div class="w-1/2 flex-shrink-0 border-r-1px border-r-[#f0f0f0] pr-20px">
          <div class="text-15px echarts-title pl-8px mb-20px">红包个数</div>
          <div class="flex w-full main-data pb-20px mb-30px !border-b-transparent">
            <div
              class="flex items-center justify-between w-20/100 text-14px mr-30px"
              v-for="item in redEnvelopeCount"
            >
              <div class="flex items-center pl-10px">
                <div class="">
                  <div class="mb-12px text-[#666]">{{ item.title }}</div>
                  <div class="text-18px font-550">{{ item.data || '0' }}</div>
                </div>
              </div>
              <div class="w-1px h-30px bg-[#f0f0f0]"></div>
            </div>
          </div>
          <div class="h-50vh w-full">
            <ActivityAnalysis :sourceData="redEnvelopeCountOption" />
          </div>
        </div>
        <div class="w-1/2 flex-shrink-0 relative pl-20px">
          <div class="text-15px echarts-title pl-8px mb-20px">红包金额</div>
          <div class="flex w-full main-data pb-20px mb-30px !border-b-transparent">
            <div
              class="flex items-center justify-between w-20/100 text-14px mr-30px"
              v-for="item in redEnvelopeMoney"
            >
              <div class="flex items-center pl-10px">
                <div class="">
                  <div class="mb-12px text-[#666]">{{ item.title }}</div>
                  <div class="text-18px font-550">{{ item.data || '0' }}</div>
                </div>
              </div>
              <div class="w-1px h-30px bg-[#f0f0f0]"></div>
            </div>
          </div>
          <div class="h-50vh w-full">
            <ActivityAnalysis :sourceData="redEnvelopeMoneyOption" />
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="false"
      class="report-container px-20px pt-10px"
      id="section3"
    >
      <div
        class="text-16px mb-20px mt-10px font-600"
        id="section3"
        >用户渠道统计</div
      >
      <div class="echarts relative flex mb-50px">
        <div class="w-1/2 flex-shrink-0 border-r-1px border-r-[#f0f0f0] pr-20px">
          <div class="text-15px echarts-title pl-8px mb-20px">参与渠道</div>
          <div class="flex w-full main-data pb-20px mb-30px !border-b-transparent">
            <div
              class="flex items-center justify-between w-20/100 text-14px mr-30px"
              v-for="item in activityJoinChannelData"
            >
              <div class="flex items-center pl-10px">
                <div class="">
                  <div class="mb-12px text-[#666]">{{ item.title }}</div>
                  <div class="text-18px font-550">{{ item.data || '0' }}</div>
                </div>
              </div>
              <div class="w-1px h-30px bg-[#f0f0f0]"></div>
            </div>
          </div>
          <div class="h-50vh w-full">
            <ActivityAnalysis :sourceData="activityJoinChannelOption" />
          </div>
        </div>
        <div class="w-1/2 flex-shrink-0 relative pl-20px">
          <div class="text-15px echarts-title pl-8px mb-20px">访问渠道</div>
          <div class="flex w-full main-data pb-20px mb-30px !border-b-transparent">
            <div
              class="flex items-center justify-between w-20/100 text-14px mr-30px"
              v-for="item in activityReadChannelData"
            >
              <div class="flex items-center pl-10px">
                <div class="">
                  <div class="mb-12px text-[#666]">{{ item.title }}</div>
                  <div class="text-18px font-550">{{ item.data || '0' }}</div>
                </div>
              </div>
              <div class="w-1px h-30px bg-[#f0f0f0]"></div>
            </div>
          </div>
          <div class="h-50vh w-full">
            <ActivityAnalysis :sourceData="activityReadChannelOption" />
          </div>
        </div>
      </div>
    </div>
    <div
      class="report-container px-20px pt-10px"
      id="section2"
    >
      <div
        class="text-16px mb-20px mt-10px font-600"
        id="section2"
        >奖品发放情况</div
      >
      <div class="echarts relative flex mb-50px">
        <div class="w-full flex-shrink-0 border-r-1px border-r-[#f0f0f0] pr-20px">
          <div
            class="h-50vh w-full"
            v-if="prizeOption.xAxis.data.length"
          >
            <ActivityAnalysis :sourceData="prizeOption" />
          </div>
        </div>
      </div>
    </div>
    <div
      class="report-container px-20px pt-10px"
      id="section4"
    >
      <div
        class="text-16px mb-20px mt-10px font-600"
        id="section4"
        >新增用户趋势</div
      >
      <div class="echarts relative flex mb-50px">
        <div class="w-full flex-shrink-0 border-r-1px border-r-[#f0f0f0] pr-20px">
          <div class="h-50vh w-full">
            <ActivityAnalysis :sourceData="newUsersOption" />
          </div>
        </div>
      </div>
    </div>
    <div
      class="fixed right-50px top-1/2 transform -translate-y-130px text-16px bg-[#fff] rounded-5px anchor py-10px z-999999"
    >
      <a-anchor
        :affix="false"
        :target-offset="16"
        :get-container="getContainer"
        @click="handleAnchorClick"
      >
        <a-anchor-link
          v-for="(item, index) in anchors"
          :key="index"
          :href="item.href"
          :title="item.title"
        />
      </a-anchor>
    </div>
  </div>
</template>

<script>
import { merge } from 'lodash-es';

import ActivityAnalysis from '@/views/activities/ActivityTable/ActivityAnalysis.vue';
import icon01 from '@/assets/icon01.png';
import icon02 from '@/assets/icon02.png';
import icon03 from '@/assets/icon03.png';
import icon04 from '@/assets/icon04.png';
import icon05 from '@/assets/icon05.png';
import {
  activityRedPacket,
  baseInfo,
  getDetails,
  joinCountByPlatform,
  newUsersData,
  prizeData,
  readCountByPlatform,
  statisticsByAreaName,
  statisticsByDate,
} from '@/api/activities';
import { Anchor } from 'ant-design-vue';
export default {
  props: {
    activityId: {
      type: String,
    },
  },
  components: {
    ActivityAnalysis,
  },
  data() {
    return {
      anchors: [
        {
          title: '数据总览',
          href: '#section1',
          active: true,
          key: '1',
        },
        // {
        //   title: '红包发放',
        //   href: '#section2',
        //   active: false,
        //   key:'2'
        // },
        {
          title: '奖品发放',
          href: '#section2',
          active: false,
          key: '2',
        },
        // {
        //   title: '用户渠道',
        //   href: '#section3',
        //   active: false,
        //   key:'3'
        // },
        {
          title: '新增用户',
          href: '#section4',
          active: false,
          key: '4',
        },
      ],
      handleScroll: false,
      loading: null,
      activityInfo: {},
      // 基础折线统计数据
      option: {
        title: {
          text: '',
          subtext: '',
          left: 'center',
        },
        legend: {
          show: true,
          top: '20px',
          right: '3%',
          itemWidth: 30, // 图例标记的图形宽度。
          //   itemGap: 20, // 图例每项之间的间隔。
          itemHeight: 10, //  图例标记的图形高度。
          textStyle: {
            // color: '#fff',
            fontSize: 14,
            padding: [0, 8, 0, 8],
          },
        },
        grid: {
          top: '50px',
          left: '4%',
          right: '4%',
          bottom: '5%',
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
        },
        series: [
          {
            data: [],
            type: 'line',
            smooth: true, // 设置为光滑曲线
            name: '抽奖次数',
          },
          {
            data: [],
            type: 'line',
            name: '访问量',
            smooth: true, // 设置为光滑曲线
          },
          {
            data: [],
            type: 'line',
            name: '参与人次',
            smooth: true, // 设置为光滑曲线
          },
        ],
      },
      defaultOption: {
        title: {
          text: '',
          subtext: '',
          left: 'center',
        },
        legend: {
          show: true,
          top: '20px',
          right: '3%',
          itemWidth: 30, // 图例标记的图形宽度。
          //   itemGap: 20, // 图例每项之间的间隔。
          itemHeight: 10, //  图例标记的图形高度。
          textStyle: {
            // color: '#fff',
            fontSize: 14,
            padding: [0, 8, 0, 8],
          },
        },
        grid: {
          top: '50px',
          left: '4%',
          right: '4%',
          bottom: '5%',
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
        },
        series: [],
      },
      // 饼状区域数据
      areaNameOption: {
        type: 'area',
        title: {
          left: 'center',
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {d}%',
          // formatter: p => {
          //   return `<div>${p?.data?.areaName}:${p?.data?.value}</div>`;
          // },
        },
        graphic: {
          type: 'text',
          top: 'center',
          left: 'center',
          style: {
            text: '',
            font: 'bolder 18px cursive',
            fill: '#333',
            lineHeight: 22,
            textAlign: 'center',
          },
        },
        legend: {
          // type: 'scroll',
          orient: 'vertical',
          x: 'left',
          // right: 10,
          top: 20,
          // bottom: 20,
          // data: []
        },
        series: [
          {
            name: '市州',
            type: 'pie',
            selectedMode: 'single',
            radius: ['0%', '75%'],
            label: {
              position: 'inner',
              textStyle: {
                color: '#333',
                fontSize: 14,
                fontWeight: 'bold',
              },
            },
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            data: [],
          },
        ],
      },
      addReadDialog: false,
      formData: {
        statelessFlowType: 'read',
        count: 0,
      },
      areaTree: [],
      level: 1,
      areaCityName: [],
      // 整体数据
      overallData: [
        {
          title: '参与人数',
          key: 'userCount',
          data: 0,
          todayAddCount: 0,
          icon: icon01,
          background: '#cbe2fb',
        },
        {
          title: '参与人次',
          key: 'joinCount',
          data: 0,
          todayAddCount: 0,
          icon: icon02,
          background: '#f7eaf4',
        },
        {
          title: '访问量',
          key: 'readCount',
          data: 0,
          todayAddCount: 0,
          icon: icon03,
          background: '#f8eee1',
        },
        {
          title: '抽奖次数',
          key: 'lotteryCount',
          data: 0,
          todayAddCount: 0,
          icon: icon04,
          background: '#ebfbd4',
        },

        // {
        //   title: '投票次数',
        //   key: 'voteCount',
        //   data: 0,
        //   todayAddCount: 0
        // },
      ],
      // 红包个数
      redEnvelopeCount: [
        {
          title: '发放总数',
          key: 'total',
          data: 0,
        },
        {
          title: '已发放个数',
          key: 'issuedCount',
          data: 0,
        },
        {
          title: '已领取个数',
          key: 'receivedCount',
          data: 0,
        },
      ],
      redEnvelopeCountOption: {
        series: [
          {
            data: 0,
            type: 'line',
            name: '已发放数量',
            smooth: true, // 设置为光滑曲线
          },
          {
            data: 0,
            type: 'line',
            name: '已领取数量',
            smooth: true, // 设置为光滑曲线
          },
        ],
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
      },
      // 红包金额
      redEnvelopeMoney: [
        {
          title: '发放总金额',
          key: 'total',
          data: 0,
        },
        {
          title: '已发放金额',
          key: 'issuedCount',
          data: 0,
        },
        {
          title: '已领取金额',
          key: 'receivedCount',
          data: 0,
        },
      ],
      redEnvelopeMoneyOption: {
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: 0,
            type: 'line',
            name: '已发放金额',
            smooth: true, // 设置为光滑曲线
          },
          {
            data: 0,
            type: 'line',
            name: '已领取金额',
            smooth: true, // 设置为光滑曲线
          },
        ],
      },
      // 参与渠道图表数据
      activityJoinChannelOption: {
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: [],
            type: 'line',
            name: '川工之家App',
            smooth: true, // 设置为光滑曲线
          },
          {
            data: [],
            type: 'line',
            name: '微信',
            smooth: true, // 设置为光滑曲线
          },
        ],
      },
      activityJoinChannelData: [
        {
          title: '参与总次数',
          data: 0,
        },
        {
          title: '川工之家',
          data: 0,
        },
        {
          title: '微信',
          data: 0,
        },
      ],
      // 访问渠道图表数据
      activityReadChannelOption: {
        grid: {
          top: '50px',
          left: '100px',
          // right: '4%',
          bottom: '5%',
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: [],
            type: 'line',
            name: '川工之家App',
            smooth: true, // 设置为光滑曲线
          },
          {
            data: [],
            type: 'line',
            name: '微信',
            smooth: true, // 设置为光滑曲线
          },
        ],
      },
      activityReadChannelData: [
        {
          title: '访问总次数',
          data: 0,
        },
        {
          title: '川工之家',
          data: 0,
        },
        {
          title: '微信',
          data: 0,
        },
      ],
      // 新增用户数据
      newUsersOption: {
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: 0,
            type: 'line',
            name: '新增用户',
            smooth: true, // 设置为光滑曲线
          },
        ],
      },
      prizeOption: {
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [],
      },
    };
  },
  watch: {
    activityId() {
      if (this.activityId) {
        this.init();
      }
    },
  },
  async mounted() {
    if (this.activityId) {
      this.init();
    }
  },

  methods: {
    getContainer() {
      return document.querySelector('#details');
    },
    handleAnchorClick(e) {
      e.preventDefault();
    },
    async init() {
      const { data } = await getDetails({ activityId: this.activityId });
      this.activityInfo = data;
      const activityType = {
        vote: '投票次数',
        signUp: '报名次数',
        vieAnswer: '答题次数',
      };
      if (activityType[this.activityInfo.activityMode]) {
        this.overallData.push({
          title: activityType[this.activityInfo.activityMode],
          key: 'otherCount',
          data: 0,
          icon: icon05,
          background: '#f3d3e1',
        });
      }

      this.getBaseReport();
      this.getReport();
      this.getByAreaName();
      //this.getActivityJoinChannelData()
      //this.getActivityReadChannelData()
      this.getActivityRedEnvelopeData();
      this.getNewUsersData();
      this.getPrizeData();
      // 处理滚动事件
      const element = document.querySelector('#details');

      // this.anchors.forEach(item => {
      //   console.log(document.querySelector(`#${item.id}`));
      //   item.offsetTop = document.querySelector(`#${item.id}`).offsetTop - element.offsetTop
      // })
      // document.querySelector('#details').addEventListener('scroll', (e) => {
      //   if (this.handleScroll) return
      //   for (const item of this.anchors) {
      //     if (element.scrollTop - 100 < item.offsetTop) {
      //       // console.log(document.querySelector(`#${item.id}`));
      //       this.anchors.forEach(item => {
      //         item.active = false
      //       })
      //       item.active = true
      //       return
      //     }
      //   }
      // })
    },
    // onAnchor(item) {
    //   this.anchors.forEach(item => {
    //     item.active = false
    //   })
    //   item.active = true

    //   this.handleScroll = true
    //   setTimeout(() => {
    //     const dom = document.querySelector(`#${item.id}`)
    //     dom.scrollIntoView({
    //       behavior: 'smooth',
    //       block: 'end',
    //       inline: 'end'
    //    });
    //     this.handleScroll = false
    //   }, 300)
    // },
    // 数据统计
    async getBaseReport() {
      const { activityId, activityStartTime, activityEndTime, activityMode } = this.activityInfo;
      let data = await baseInfo({
        activityId,
        activityStartTime,
        activityEndTime,
        activityMode,
      });
      const {
        userCount,
        joinCount,
        readCount,
        lotteryCount,
        otherCount,
        todayReadCount,
        todayUserCount,
        todayJoinCount,
        todayLotteryCount,
        todayOtherCount,
      } = data;
      const dataObj = {
        userCount,
        joinCount,
        readCount,
        lotteryCount,
        otherCount,
      };
      const todayCountObj = {
        readCount: todayReadCount,
        userCount: todayUserCount,
        joinCount: todayJoinCount,
        lotteryCount: todayLotteryCount,
        otherCount: todayOtherCount,
      };
      for (const key in dataObj) {
        const obj = this.overallData.find(item => item.key === key);
        if (obj) {
          obj.data = dataObj[key];
          obj.todayAddCount = todayCountObj[key] || 0;
        }
      }
    },
    // 数据统计图表
    async getReport() {
      const { activityId, activityStartTime, activityEndTime, activityMode } = this.activityInfo;
      let data = await statisticsByDate({
        activityId,
        activityStartTime,
        activityEndTime,
      });
      console.log(data, 3232);
      this.option.xAxis.data = data.columnList;
      this.option.series[0].data = data.lotteryCountList;
      this.option.series[1].data = data.readCountList;
      this.option.series[2].data = data.joinCountList;
      if (activityMode === 'vote') {
        this.option.series.push({
          data: data.voteCountList,
          type: 'line',
          name: '投票次数',
          smooth: true, // 设置为光滑曲线
        });
        this.option.series[3].data = data.voteCountList;
      }
    },
    // 返回上一级
    backFrontLevel() {
      this.level = 1;
      this.areaNameOption.series[0].data = this.areaCityName;
      this.areaNameOption.graphic.style.text = '';
    },
    // 按区域统计
    async getByAreaName() {
      const { activityId } = this.activityInfo;
      const { areaInfos } = await statisticsByAreaName({ activityId });
      this.areaCityName =
        areaInfos?.map(({ areaName, count, areaCode }) => {
          return { name: areaName + ' ' + count + ' (人)', value: count, areaName, areaCode };
        }) || [];
      this.areaNameOption.series[0].data = this.areaCityName;
      this.areaTree = areaInfos;
    },
    // 点击
    clickEcharts(params) {
      const res = this.areaTree.find(item => item.areaCode === params.data.areaCode);
      // console.log(res);
      if (this.level === 1) {
        if (res && res.children && res.children.length) {
          this.areaNameOption.series[0].data = res.children.map(({ areaName, areaCode, count }) => {
            return { name: areaName + ' ' + count, value: count, areaName, areaCode };
          });
        }
        this.areaNameOption.graphic.style.text = res.areaName + ' ' + params.data.value;
        this.level = 2;
      }
    },
    // 参与渠道统计
    async getActivityJoinChannelData() {
      const { activityId, activityStartTime, activityEndTime } = this.activityInfo;
      const data = await joinCountByPlatform({
        activityId,
        activityStartTime,
        activityEndTime,
      });
      this.activityJoinChannelOption = merge(this.activityJoinChannelOption, this.defaultOption);
      this.activityJoinChannelOption.xAxis.data = data.dataX;
      this.activityJoinChannelOption.series[0].data = data.app;
      this.activityJoinChannelOption.series[1].data = data.wx;
      this.activityJoinChannelData[1].data = data.app.reduce((prev, cur, index) => {
        return cur + prev;
      }, 0);
      this.activityJoinChannelData[2].data = data.wx.reduce((prev, cur, index) => {
        return cur + prev;
      }, 0);
      this.activityJoinChannelData[0].data =
        this.activityJoinChannelData[1].data + this.activityJoinChannelData[2].data;
    },
    // 访问渠道统计
    async getActivityReadChannelData() {
      const { activityId, activityStartTime, activityEndTime } = this.activityInfo;
      const data = await readCountByPlatform({
        activityId,
        activityStartTime,
        activityEndTime,
      });
      this.activityReadChannelOption = merge(this.activityReadChannelOption, this.defaultOption);

      if (data) {
        this.activityReadChannelOption.xAxis.data = data?.dataX;
        this.activityReadChannelOption.series[0].data = data?.app;
        this.activityReadChannelOption.series[1].data = data?.wx;
        this.activityReadChannelData[1].data = data?.app?.reduce((prev, cur, index) => {
          return cur + prev;
        }, 0);
        this.activityReadChannelData[2].data = data?.wx?.reduce((prev, cur, index) => {
          return cur + prev;
        }, 0);
        this.activityReadChannelData[0].data =
          this.activityReadChannelData[1].data + this.activityReadChannelData[2].data;
      }
    },
    // 红包数据统计
    async getActivityRedEnvelopeData() {
      const { activityId } = this.activityInfo;
      const data = await activityRedPacket({
        activityId,
      });
      for (const key in data?.countData) {
        const obj = this.redEnvelopeCount.find(item => item.key === key);
        if (obj) {
          obj.data = data?.countData[key] || 0;
        }
      }
      for (const key in data?.amountData) {
        const obj = this.redEnvelopeMoney.find(item => item.key === key);
        if (obj) {
          obj.data = data?.amountData[key] || 0;
        }
      }
      this.redEnvelopeCountOption = merge(this.redEnvelopeCountOption, this.defaultOption);

      this.redEnvelopeCountOption.xAxis.data = data.dataX;
      this.redEnvelopeCountOption.series[0].data = data.countData?.issuedList || [];
      this.redEnvelopeCountOption.series[1].data = data.countData?.receivedList || [];
      this.redEnvelopeMoneyOption = merge(this.redEnvelopeMoneyOption, this.defaultOption);
      this.redEnvelopeMoneyOption.xAxis.data = data.dataX;
      this.redEnvelopeMoneyOption.series[0].data = data.amountData?.issuedList || [];
      this.redEnvelopeMoneyOption.series[1].data = data.amountData?.receivedList || [];
    },
    // 新增用户数据
    async getNewUsersData() {
      const { activityId } = this.activityInfo;
      const data = await newUsersData({
        activityId,
      });
      this.newUsersOption = merge(this.newUsersOption, this.defaultOption);
      this.newUsersOption.xAxis.data = data.dataX;
      this.newUsersOption.series[0].data = data.dataY || [];
    },
    // 奖品发放情况
    async getPrizeData() {
      const { activityId } = this.activityInfo;
      const data = await prizeData({
        activityId,
      });
      this.prizeOption = merge(this.prizeOption, this.defaultOption);
      this.prizeOption.xAxis.data = data.dataX;
      this.prizeOption.series = data.series || [];
    },
  },
};
</script>

<style lang="scss" scoped>
.report {
  display: flex;
  flex-direction: column;
  width: 100%;
  .report-container {
    border-bottom: 10px solid #f0f0f0;
  }
  .main-data {
    border-bottom: 1px solid #f0f0f0;
  }

  .report-total {
    padding: 10px;

    & > div {
      padding: 20px;
      font-size: 16px;
      width: 18%;
      height: 100px;
      border: 1px solid #e4e7ed;
      border-radius: 5px;
      background-color: #3da1fe;
      color: white;
    }

    p {
      font-size: 16px;
    }
  }

  .echarts {
    // height: 58vh;

    .echarts-title {
      border-left: 3px solid #1665e8;
    }
  }

  .anchor {
    box-shadow: 0px 3px 10px 3px rgba(0, 0, 0, 0.1);

    & > div {
      border-bottom: 1px solid #f0f0f0;
      padding: 10px 10px;
      cursor: pointer;
    }
  }

  .anchor-active {
    color: #3da1fe;
    border-left: 3px solid #3da1fe;
  }
}
</style>
