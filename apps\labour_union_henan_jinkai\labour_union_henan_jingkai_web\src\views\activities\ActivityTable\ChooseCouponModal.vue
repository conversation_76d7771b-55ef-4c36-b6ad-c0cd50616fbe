<template>
  <BasicModal
      :canFullscreen="false"
      title="选择票券"
      show-ok-btn
      v-bind="$attrs"
      @ok="handleOk"
      @register="registerModal"
      @visible-change="visibleChange"
  >
    <BasicTable @register="registerTable"></BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicTable, useTable } from '@/components/Table';
import {ref, unref,} from 'vue';
import {onSelectList} from "@/api/coupon";


const emit = defineEmits(['success', 'register']);
const modalVisible = ref(null);

const activityId = ref('')
const visibleChange = visible => {
  modalVisible.value = visible;
};
const [registerModal, { closeModal }] = useModalInner(async data => {
  await clearSelectedRowKeys();
  activityId.value = data.activityId

  // 清空表单搜索条件
  getForm().resetFields();
  reload();
});

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys, setProps, getForm }] =
    useTable({
      api: onSelectList,
      rowKey: 'autoId',
      columns: [    {
        title: '票券名称',
        dataIndex: 'couponName',
      },],
      beforeFetch: params => {
        return { ...params,pageSize:0,orderBy:'auto_id',sortType:'desc',grantType:'receive',activityId:unref(activityId)  };
      },
      pagination:false,
      formConfig: {
        labelWidth: 120,
        actionColOptions: {
          span: 12,
        },
        autoSubmitOnEnter: true,
        schemas: [
          {
            field: 'couponName',
            label: '票券名称',
            colProps: { span: 6 },
            component: 'Input',
            rulesMessageJoinLabel: true,
          },
        ],
      },
      immediate: false,
      useSearchForm: true,
      showTableSetting: false,
      bordered: true,
      showIndexColumn: true,
      maxHeight: 420,
      rowSelection: {
        type: 'checkbox',
      },
    });
// 提交
function handleOk() {
  const rows = getSelectRows();

  emit('success', rows);
  closeModal();
}
</script>

<style scoped></style>
