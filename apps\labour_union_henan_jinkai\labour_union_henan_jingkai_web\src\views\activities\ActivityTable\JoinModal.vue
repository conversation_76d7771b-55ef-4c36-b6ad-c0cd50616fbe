<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          v-if="activityMode !== ActivityType.SURVEY"
          @click="handleAudit(null)"
          >批量审核</a-button
        >
        <a-button
          type="primary"
          @click="handleDownload()"
          >导出</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                disabled: record.state !== 'wait',
                ifShow: activityMode !== ActivityType.SURVEY,
                onClick: handleAudit.bind(null, record),
              },
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ActivityAuditModal
      @register="registerAudit"
      width="50%"
      @success="handleSuccess"
    />
    <JoinViewModal
      @register="registerView"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, createVNode, nextTick, onMounted, ref, unref } from 'vue';
import { audit, questionRecord, registration } from '@/api/activities';
import { useModal } from '@/components/Modal';
import { BasicTable, TableAction, useTable } from '@/components/Table';
import { joinColumn, joinFormSchema } from '../activity';
import ActivityAuditModal from './ActivityAuditModal.vue';
import JoinViewModal from './JoinViewModal.vue';
import { Modal } from 'ant-design-vue';
import { CheckCircleOutlined, CloseCircleFilled, CloseCircleOutlined } from '@ant-design/icons-vue';
import { map } from 'lodash-es';
import { questionRecordExport, signRecordExport } from '@/api/activities/statistics';
import dayjs from 'dayjs';
import { ActivityType } from '../activities.d';
import { downloadByUrl } from '@monorepo-yysz/utils';

const props = defineProps({
  activityId: {
    type: String,
  },
  activityMode: {
    type: String,
  },
});
const title = ref('');


const emit = defineEmits(['reload']);

const column = computed(() => {
  return joinColumn(props.activityMode);
});

const schema = computed(() => {
  return joinFormSchema(props.activityMode);
});

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys, setProps }] = useTable({
  rowKey: 'autoId',
  columns: column,
  formConfig: {
    labelWidth: 120,
    schemas: schema,
    autoSubmitOnEnter: true,
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.state !== 'review',
    }),
  },
  maxHeight: 520,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  immediate: true,
  showIndexColumn: false,
  clickToRowSelect: false,
  beforeFetch: params => {
    return {
      ...params,
      activityId: props.activityId,
      activityMode: props.activityMode,
      orderBy: 'create_time',
      sortType: 'desc',
    };
  },
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerAudit, { openModal: openAudit, closeModal }] = useModal();

const [registerView, { openModal: openView }] = useModal();

function handleAudit(record: Nullable<Recordable>) {
  let arr: Recordable[] = [];
  if (record) {
    arr.push(record.autoId);
  } else {
    const rows = getSelectRows();
    if (!rows || rows.length === 0) {
      Modal.warning({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: '请选择至少一条数据进行审核！',
        okText: '确认',
        closable: true,
      });
      return false;
    }

    arr = map(rows, v => v.autoId);
  }
  openAudit(true, { record, ids: arr, sourceId: props.activityId });
}

async function handleView(record) {
  openView(true, { record: { ...record, activityMode: props.activityMode } });
}

function handleSuccess({ values }) {
  audit(values).then(res => {
    const { code, message: msg } = res;
    if (code === 200) {
      Modal.success({
        title: '提示',
        icon: createVNode(CheckCircleOutlined),
        content: '审核成功',
        okText: '确认',
        closable: true,
      });
      reload({
        searchInfo: {
          activityId: props.activityId,
          activityMode: props.activityMode,
        },
      });
      closeModal();
      clearSelectedRowKeys();
    } else {
      Modal.error({
        title: '提示',
        icon: createVNode(CloseCircleOutlined),
        content: msg || `审核失败`,
        okText: '确认',
        closable: true,
      });
    }
  });
}

async function handleDownload() {
  let res;
  if (props.activityMode === ActivityType.SURVEY) {
    res = await questionRecordExport(unref({ activityId: props.activityId }));
  } else {
    res = await signRecordExport(unref({ activityId: props.activityId }));
  }
  const url = window.URL.createObjectURL(res);
  const fileName = `参与用户${dayjs().format('YYYY-MM-DD HH:mm:ss')}`;
  downloadByUrl({
    url,
    fileName,
  });
}

function reloadAll() {
  setProps({
    api: props.activityMode === ActivityType.SURVEY ? questionRecord : registration,
  });
  reload({
    searchInfo: {
      activityId: props.activityId,
    },
  });
}

onMounted(() => {
  nextTick(() => {
    emit('reload', reloadAll);
  });
});
</script>
