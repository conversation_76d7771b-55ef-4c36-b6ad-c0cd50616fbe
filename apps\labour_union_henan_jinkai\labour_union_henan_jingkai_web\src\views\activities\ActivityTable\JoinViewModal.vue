<template>
  <BasicModal
    @register="registerModal"
    :can-fullscreen="false"
    :title="`${title}--详情`"
    :show-ok-btn="false"
  >
    <Description @register="register" />
<!--    <Description-->
<!--      @register="registerAudit"-->
<!--      v-if="activityMode !== ActivityType.SURVEY"-->
<!--    />-->
    <div v-if="info && info.length > 0">
      <Divider>{{ activityMode === ActivityType.SURVEY ? '填写信息' : '报名信息' }}</Divider>
      <div v-for="(item, index) in info">
        <Title :level="5">{{index+1}}、{{ item.title }}</Title>
        <Paragraph
          :content="activityMode !== ActivityType.SURVEY?item.content:item.remark"
        />
      </div>
    </div>
    <div v-if="activityMode === ActivityType.BLUE_VEST">
      <Divider>签到记录</Divider>
      <BasicTable
        :max-height="290"
        :data-source="dataSource"
        :columns="[
          {
            title: '主键',
            dataIndex: 'autoId',
            defaultHidden: true,
          },
          {
            title: '签到地点',
            dataIndex: 'signAddress',
          },
          {
            title: '签到时间',
            dataIndex: 'signTime',
          },
        ]"
      />
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, watch } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useDescription, Description } from '@/components/Description';
import { schema, audit } from '../activity';
import { ActivityType } from '../activities.d';
import { Divider, Typography } from 'ant-design-vue';
import { BasicTable } from '@/components/Table';
import { blueVestSignList } from '@/api/activities';

interface Info {
  title: string;
  content: string;
}

const Title = Typography.Title;

const Paragraph = Typography.Paragraph;

defineEmits(['register', 'success']);

const title = ref('');

const record = ref<Recordable>({});

const activityMode = ref<string>();

const auditRecord = ref({});

const info = ref<Info[]>([]);

const dataSource = ref<Recordable[]>([]);

const [registerModal, {}] = useModalInner(async function (data) {
  title.value = data.record.userName;

  const { auditRecord: ar, activityMode: am, answerRecord, ...other } = data.record;

  record.value = other;

  activityMode.value = am;

  auditRecord.value = ar;

  info.value = answerRecord;

  setDescProps({
    data: unref(record),
  });
  // setAudit({
  //   data: unref(record),
  // });

  if (unref(activityMode) === ActivityType.BLUE_VEST) {
    dataSource.value = await blueVestSignList({
      userId: unref(record)?.userId,
      activityId: unref(record)?.activityId,
    });
  }
});

const [register, { setDescProps }] = useDescription({
  title: '',
  column: 2,
});

// const [registerAudit, { setDescProps: setAudit }] = useDescription({
//   title: '',
//   schema: audit(),
//   column: 2,
// });

watch(activityMode, value => {
  setDescProps({ schema: schema(value) });

});
</script>
