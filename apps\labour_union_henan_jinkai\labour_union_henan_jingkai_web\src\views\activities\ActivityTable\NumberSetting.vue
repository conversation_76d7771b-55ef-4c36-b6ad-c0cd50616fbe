<template>
  <PageWrapper
    :title="title"
    @back="goBack"
  >
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          >新增活动期数</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleModify.bind(null, record),
              },
              {
                icon: 'eos-icons:compare-states',
                label: record.state === 'Y' ? '禁用' : '启用',
                type: 'primary',
                onClick: handleState.bind(null, record),
              },
              {
                icon: 'fluent:delete-12-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
              },
            ]"
          ></TableAction>
        </template>
      </template>
    </BasicTable>
    <NumberSettingModal
      @register="registerModal"
      width="50%"
      :canFullscreen="false"
      @success="handleSuccess"
    >
    </NumberSettingModal>
  </PageWrapper>
</template>

<script lang="ts" setup>
import { computed, unref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { columnNumber, formNumber } from '../activity';
import { walkCycle, walkCycleList, walkCycleDelete, walkCycleUpdateState } from '@/api/activities';
import { PageWrapper } from '@/components/Page';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import NumberSettingModal from './NumberSettingModal.vue';
import { useModal } from '@/components/Modal';
import { useMessage } from '@monorepo-yysz/hooks';

const route = useRoute();

const router = useRouter();

const { createErrorModal, createSuccessModal, createConfirm } = useMessage();

const activityId = computed(() => {
  return route.query.activityId;
});

const title = computed(() => {
  return `${route.query.activityName}期数设置`;
});

const [registerModal, { closeModal, openModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columnNumber(),
  formConfig: {
    labelWidth: 120,
    schemas: formNumber(),
    autoSubmitOnEnter: true,
  },
  searchInfo: {
    activityInfoId: unref(activityId),
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  api: walkCycleList,
  showIndexColumn: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',

    fixed: undefined,
    width: 300,
  },
});

// 页面左侧点击返回链接时的操作
function goBack() {
  router.go(-1);
}

function handleModify(record) {
  openModal(true, { record, isUpdate: true });
}

function handleClick() {
  openModal(true, { isUpdate: false });
}

function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除第${record.cycle}期`,
    onOk: function () {
      walkCycleDelete(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleState(record) {
  const state = record.state === 'Y' ? '禁用' : '启用';
  createConfirm({
    iconType: 'warning',
    content: `请确认要${state}第${record.cycle}期`,
    onOk: function () {
      walkCycleUpdateState({ ...record, state: record.state === 'Y' ? 'N' : 'Y' }).then(
        ({ code, message }) => {
          if (code === 200) {
            createSuccessModal({ content: `${state}成功` });
            reload();
          } else {
            createErrorModal({ content: `${state}失败，${message}` });
          }
        }
      );
    },
  });
}

function handleSuccess({ isUpdate, values }) {
  walkCycle({
    ...values,
    activityInfoId: unref(activityId),
  }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload({
        searchInfo: {
          activityInfoId: unref(activityId),
        },
      });
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
