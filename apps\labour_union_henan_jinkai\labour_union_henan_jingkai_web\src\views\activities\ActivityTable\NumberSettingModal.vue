<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalNumber } from '../activity';
import dayjs from 'dayjs';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const title = computed(() => {
  return unref(disabled)
    ? unref(isUpdate)
      ? `${unref(record)?.venueName || ''}--详情`
      : `编辑第${unref(record)?.cycle || 1}期健步走`
    : '新增健步走';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalNumber();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    const { beginTime, endTime, openingStartTime, openingEndTime, ...val } = data.record;
    //反填时间
    let dailyTimeStart = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    let dailyTimeEnd = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    dailyTimeStart.splice(1, 1, openingStartTime);
    dailyTimeEnd.splice(1, 1, openingEndTime);
    const dailyTime = [dayjs(dailyTimeStart.join(' ')), dayjs(dailyTimeEnd.join(' '))];
    console.log([dayjs(beginTime), dayjs(endTime)]);
    setFieldsValue({
      ...val,
      startEndDate: beginTime && endTime ? [dayjs(beginTime), dayjs(endTime)] : [],
      dailyTime: dailyTime,
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const { startEndDate, dailyTime, ...val } = await validate();
    console.log(startEndDate, 'startEndDate');
    console.log(dailyTime, 'dailyTime');
    emit('success', {
      values: {
        ...unref(record),
        ...val,
        beginTime:
          startEndDate && startEndDate.length === 2
            ? dayjs(startEndDate[0]).format('YYYY-MM-DD 00:00:00')
            : undefined,
        endTime:
          startEndDate && startEndDate.length === 2
            ? dayjs(startEndDate[1]).format('YYYY-MM-DD 23:59:59')
            : undefined,
        openingStartTime:
          dailyTime && dailyTime.length === 2 ? dayjs(dailyTime[0]).format('HH:mm:ss') : undefined,
        openingEndTime:
          dailyTime && dailyTime.length === 2 ? dayjs(dailyTime[1]).format('HH:mm:ss') : undefined,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
