<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleClick"> 新增作品</a-button>
        <a-button type="primary" v-if="fileType" @click="handlePush(null)"> 推送专区</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
              :actions="[
             {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'carbon:task-view',
              label: '投票记录',
              type: 'primary',
              onClick: handleRecord.bind(null, record),
           },
           {
              icon: 'clarity:export-line',
              label: '推送专区',
              type: 'primary',
              ifShow: record.fileType.includes('video'),
              disabled: record.state == 'Y',
              onClick: handlePush.bind(null, record),
           },
            {
              icon: 'fluent:delete-16-filled',
              label: '删除',
              type: 'primary',
              danger: true,
              onClick: handleDelete.bind(null, record),
            },
          ]"
          />
        </template>
      </template>
    </BasicTable>
    <OpusModal @register="registerModal" @success="handleSuccess"  width="70%" :can-fullscreen="false"/>
    <!--投票记录-->
    <VoteRecordModal
        @register="registerVoteModal"
        :can-fullscreen="false"
        width="40%"
    />
  </div>
</template>

<script lang="ts" setup>

import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import OpusModal from './OpusModal.vue';
import VoteRecordModal from './VoteRecordModal.vue';
import {useMessage} from "@monorepo-yysz/hooks";

import {columns,formSchemas} from "@/views/activities/ActivityTable/opuses";
import {addOpusInfo, delOpusInfo, getDetails, opusesDetail, voteList, handlePushVideo} from "@/api/activities";
import {useRoute} from "vue-router";
import {computed, createVNode, inject, onMounted, ref, unref} from "vue";
import {Modal} from "ant-design-vue";
import {CloseCircleFilled} from "@ant-design/icons-vue";
import {map} from "lodash-es";

const { createErrorModal, createSuccessModal,createConfirm } = useMessage()
const route = useRoute();
const activityInfo = inject('activityDetail');
const schemas = computed(()=>{
  return formSchemas(unref(activityInfo)?.voteInfo?.voteTypeConfigList)
})
onMounted(async ()=>{

})


const [registerModal, { openModal, closeModal }] = useModal();
const [registerVoteModal, { openModal: openVoteModal }] = useModal();

const [registerTable, { reload, getSelectRows }] = useTable({
  api: voteList,
  showIndexColumn: false,
  beforeFetch(p){
    p.activityId = route.query.activityId
    p.orderBy = 'auto_id'
    p.sortType = 'desc'
    return p
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.state === 'Y' || !record.fileType.includes('video'),
    }),
  },
  columns: columns(),
  formConfig: {
    labelWidth: 120,
    schemas: schemas,
    actionColOptions: { span: 3 },
    autoSubmitOnEnter: true,
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 400,
    dataIndex: 'action',
    fixed: undefined,
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
  },
});

const handleClick = () => {

  if(!unref(activityInfo)?.voteInfo){
    createErrorModal({
      content: `活动未开启投票配置~`,
    });
    return
  }
  const {voteInfo:{fileType,fileLimit}} = unref(activityInfo)
  openModal(true, {
    isUpdate: false,
    record: {fileType,fileLimit},
  });
};

const handleView = async (record: Recordable)=>{

  const {data} = await opusesDetail({autoId:record.autoId})
  const {voteInfo:{fileType,fileLimit}} = unref(activityInfo)
  openModal(true, {
    record: {...data,fileType,fileLimit},
    isUpdate: true,
    disabled: true,
  });
}
const handleEdit = async (record: Recordable) => {
  const {data} = await opusesDetail({autoId:record.autoId})
  const {voteInfo:{fileType,fileLimit}} = unref(activityInfo)
  openModal(true, {
    record: {...data,fileType,fileLimit},
    isUpdate: true,
    disabled: false,
  });
};

function handleRecord(record) {
  openVoteModal(true, { isUpdate: true, disabled: true, record });
}

function handlePush(record: Nullable<Recordable>){
  let arr: Recordable[] = [];
  if (record) {
    arr.push(record.opusInfoId);
  } else {
    const rows = getSelectRows();
    if (!rows || rows.length === 0) {
      Modal.warning({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: '请选择至少一条数据进行推送！',
        okText: '确认',
        closable: true,
      });
      return false;
    }

    arr = map(rows, v => v.opusInfoId);
  }
  createConfirm({
    iconType: 'warning',
    content: `请确认要进行推送吗?`,
    onOk: function () {
      handlePushVideo({ opusInfoIds: arr}).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({
            content: `推送成功`,
          });
          reload();
        } else {
          createErrorModal({
            content: `推送失败! ${message}`,
          });
        }
      });
    },
  });
}

const handleSuccess = ({ isUpdate, values })=>{
  addOpusInfo([{
    ...values,activityId:route.query.activityId,
  }]).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败！${message}。`,
      });
    }
  });
}

const handleDelete = async (record: Recordable) => {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.opusName}`,
    onOk: function () {
      delOpusInfo({activityId:record.activityId,opusInfoId:record.opusInfoId}).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
};
</script>
