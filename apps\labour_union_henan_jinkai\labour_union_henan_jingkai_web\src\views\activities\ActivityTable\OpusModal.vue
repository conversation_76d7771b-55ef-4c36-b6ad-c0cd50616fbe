<template>
  <BasicModal
      @register="registerModal"
      v-bind="$attrs"
      :title="title"
      :destroyOnClose="true"
      @ok="handleSubmit"
  >
    <BasicForm @register="registerForm"  :class="disabledClass"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import {ref, unref, computed, inject} from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import {modalForm} from "@/views/activities/ActivityTable/opuses";


const emit = defineEmits(['register', 'success']);

const props = defineProps({
  isAudit: { type: Boolean,default:false },
})

const record = ref<Recordable>();

const isUpdate = ref(false);
const disabled = ref(false)

const activityInfo = inject('activityDetail');

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const formItem = computed(() => {
  return modalForm(unref(isUpdate),props.isAudit,unref(activityInfo));
});

const title = computed(() => {
  if(unref(disabled)){
    return `${unref(record)?.opusName} - 详情`
  }
  return unref(isUpdate) ? `编辑 - ${unref(record)?.opusName || ''}` : '新增作品';
});

const [registerForm, { resetFields, validate,setProps, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  const opusFiles = data?.record?.opusFiles?.split(',') || []

  record.value = {...data.record,opusFiles};

  disabled.value = !!data.disabled
  isUpdate.value = !!data.isUpdate;

  await setFieldsValue({...record.value});

  setProps({ disabled: unref(disabled) });
  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});
async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    values.opusFiles = values.opusFiles?.join(',') ?? ''
    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
