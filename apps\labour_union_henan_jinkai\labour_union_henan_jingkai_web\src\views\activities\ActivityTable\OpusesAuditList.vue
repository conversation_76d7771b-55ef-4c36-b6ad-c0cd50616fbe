<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleAudit(null)"> 批量审核</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
              :actions="[
             {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
            },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                disabled: record.state !== 'wait',
                onClick: handleAudit.bind(null, record),
              },
          ]"
          />
        </template>
      </template>
    </BasicTable>
    <OpusModal @register="registerModal" :isAudit="true"  width="70%"/>
    <AuditModal
        @register="registerAudit"
        :can-fullscreen="false"
        width="40%"
        :title="auditTitle"

        @success="handleAuditSuccess"
    />
  </div>
</template>

<script lang="ts" setup>

import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import OpusModal from './OpusModal.vue';
import {useMessage} from "@monorepo-yysz/hooks";

import {auditColumns, auditFormSchemas, formSchemas,} from "@/views/activities/ActivityTable/opuses";
import { voteAudit, voteAuditList} from "@/api/activities";
import AuditModal from "@/views/activities/ActivityTable/AuditModal.vue";
import {computed, inject,  ref, unref} from "vue";
import {useRoute} from "vue-router";


const auditTitle = ref('审核')
const route = useRoute();

const activityInfo = inject('activityDetail');

const schemas = computed(()=>{
  return auditFormSchemas(unref(activityInfo)?.voteInfo?.voteTypeConfigList)
})


const { createErrorModal, createSuccessModal,createWarningModal } = useMessage()

const [registerModal, { openModal }] = useModal();

const [registerAudit, { openModal: openAudit, closeModal: closeAudit }] = useModal();

const [registerTable, { reload,getSelectRows,clearSelectedRowKeys }] = useTable({
  api: voteAuditList,
  showIndexColumn: false,
  beforeFetch(p){
    p.activityId = route.query.activityId
    p.orderBy = 'auto_id'
    p.sortType = 'desc'
    return p
  },
  columns: auditColumns(),
  formConfig: {
    labelWidth: 120,
    schemas: schemas,
    actionColOptions: { span: 3 },
    autoSubmitOnEnter: true,
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  rowSelection: {
    getCheckboxProps: record => ({
      disabled: record.state !== 'wait',
    }),
    type: 'checkbox',
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
  },
});


const handleView = (record: Recordable)=>{
  openModal(true, {
    record,
    isUpdate: false,
    disabled: true,
  });
}

//提交审核
function handleAuditSuccess({ values, autoId }) {
  const {
    auditState:state,
    auditOpinion:opinion,
  } = values
  let ids = []
  if(autoId){
    ids = [autoId]
  }else {
    ids = getSelectRows().map(t=>t.opusInfoId)
  }

  voteAudit({ state,opinion, opusInfoIds: ids, activityId:route.query.activityId }).then(res => {
    const { code, message: msg } = res;
    if (code === 200) {
      createSuccessModal({ content: '操作成功' });
      clearSelectedRowKeys();
      closeAudit();
      reload();
    } else {
      createErrorModal({ content: `操作失败，${msg}` });
    }
  });
}

//审核
function handleAudit(record) {
  if (record) {
    auditTitle.value = `审核-${record.opusName}`;
    openAudit(true, { record, autoId: record.opusInfoId });
  } else {
    auditTitle.value = `批量审核`;
    const rows = getSelectRows();
    if (!rows || rows.length === 0) {
      createWarningModal({ content: '请选择至少一条数据进行审核！' });
      return;
    }
    openAudit(true, {});
  }


}

</script>
