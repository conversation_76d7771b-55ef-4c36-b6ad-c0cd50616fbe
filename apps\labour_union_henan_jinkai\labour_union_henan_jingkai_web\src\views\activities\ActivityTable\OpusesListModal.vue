<template>
  <PageWrapper
      :title="title"
      @back="goBack"
  >
    <Tabs v-model:activeKey="activeKey">
      <TabPane key="opus" tab="作品列表"></TabPane>
      <TabPane key="audit" tab="审核列表"></TabPane>
    </Tabs>
    <OpusList  v-if="activeKey === 'opus'"/>
    <AuditList v-if="activeKey === 'audit'"/>
  </PageWrapper>


</template>

<script lang="ts" setup>
import {computed, onMounted, provide, ref} from 'vue';
import { Tabs } from 'ant-design-vue';
import OpusList from './OpusList.vue';
import AuditList from './OpusesAuditList.vue';
import {PageWrapper} from "@/components/Page";
import {useRoute, useRouter} from "vue-router";
import {getDetails} from "@/api/activities";

const router = useRouter();
const route = useRoute();
const TabPane = Tabs.TabPane;
const activeKey = ref('opus');
const title = computed(() => {
  return `${route.query.activityName}-作品管理`;
});
const activityDetail = ref({})
provide('activityDetail', activityDetail);
onMounted(async ()=>{
  //获取活动详情
  const {data} = await getDetails({ activityId: route.query.activityId })
  activityDetail.value = data
})
// 页面左侧点击返回链接时的操作
function goBack() {
  router.go(-1);
}
</script>
