<template>
  <div :class="$style['user-info']">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleDown"
          :loading="spinning"
          >导出中奖记录</a-button
        >
        <!--   暂时隐藏  导入中奖记录       -->
        <a-button
            v-if="activityMode === ActivityType.LOTTERY && false"
            type="primary"
            @click="handleImport"
        >导入中奖记录</a-button>
        <Upload
            name="file"
            accept=".xlsx,.xls"
            @change="handleImport2"
            :before-upload="beforeUpload"
            :action="importAction"
            :headers="{ token: userStore.getToken, }"
        >
          <a-button type="primary" :loading="importLoading">
            导入物流信息
          </a-button>
        </Upload>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
              :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleDetails.bind(null, record),
              },
              {
                icon: 'mdi:give-way',
                label: '确认发放',
                type: 'primary',
                disabled: record.assignState === 'Y',
                ifShow: (record.prizeType === '6' || record.prizeType === '3')&&record.receiveType === '2',
                onClick: handleAudit.bind(null, record, 'give'),
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                ifShow: false,
                disabled: record.state !== 'review',
                onClick: handleAudit.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PrizeAuditModal
      @register="registerAudit"
      width="40%"
      @success="handleAuditSuccess"
    />
    <PrizeView
      @register="registerView"
      width="50%"
    />
    <PrizeImportModal
        width="70%"
      @register="registerImport"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import {ref, unref, onMounted, nextTick,} from 'vue';
import { useModal } from '@/components/Modal';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { prizeFormItem, prizeColumns } from '../activity';
import {
  findPrizeRecordList,
  prizeAuditBatch,
  grantAwardsCode, saveBatchRedPackedRecord,
} from '@/api/activities';
import PrizeAuditModal from './PrizeAuditModal.vue';
import PrizeView from './PrizeView.vue';
import PrizeImportModal from './PrizeImportModal.vue';
import { map } from 'lodash-es';
import { prizeExport } from '@/api/activities/statistics';
import dayjs from 'dayjs';
import { useMessage } from '@monorepo-yysz/hooks';
import { downloadByUrl } from '@monorepo-yysz/utils';
import {ActivityType} from "@/views/activities/activities.d";
import {Modal, Upload, UploadProps} from "ant-design-vue";
import {useUserStore} from "@/store/modules/user";
import {useGlobSetting} from "@/hooks/setting";

const props = defineProps({
  activityId: {
    type: String,
  },
  activityMode: {
    type: String,
  },
});

const emit = defineEmits(['reload']);

const {createConfirm, createErrorModal, createSuccessModal, createWarningModal } = useMessage();
const userStore = useUserStore();
const { apiUrl } = useGlobSetting();
const spinning = ref<boolean>(false);

const params = ref<Recordable>();
const importLoading = ref(false);
const importAction = ref(`${apiUrl}/h5/activityInfo/luckDraw/importToUpdateRecord`);

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: prizeColumns(),
  beforeFetch: ({ dateRange, ...p }) => {
    if (dateRange?.length) {
      p.begin = dayjs(dateRange[0]).format('YYYY-MM-DDT00:00:00');
      p.end = dayjs(dateRange[1]).format('YYYY-MM-DDT23:59:59');
    }

    params.value = {
      ...p,
      activityId: props.activityId,
      orderBy: 'create_time',
      sortType: 'desc',
    };
    return unref(params);
  },
  formConfig: {
    labelWidth: 120,
    schemas: prizeFormItem(),
    autoSubmitOnEnter: true,
    autoAdvancedLine:2,
    submitOnChange:true,
  },
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  api: findPrizeRecordList,
  showIndexColumn: false,
  actionColumn: {
    width: 250,
    title: '操作',
    dataIndex: 'action',
    align: 'left',
    class: '!text-center',
  },
});

const [registerAudit, { closeModal, openModal: openAudit }] = useModal();

const [registerView, { openModal: openView }] = useModal();

const [registerImport, { openModal: openImport }] = useModal();

function handleAudit(record, type?: string) {
  let arr: string[] = [];
  if (record) {
    arr.push(record.recordId);
  } else {
    const rows = getSelectRows();
    if (!rows || rows.length === 0) {
      createWarningModal({ content: '请选择至少一条数据进行审核！' });
      return false;
    }
    arr = map(rows, v => v.recordId);
  }

  openAudit(true, { businessIds: arr, type, record, activityId: props.activityId });
}

function handleDetails(record) {
  openView(true, { record });
}

function handleAuditSuccess({ values, type }) {
  if (type === 'give') {
    const {autoId,remark} = values
    grantAwardsCode({autoId,remark}).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: '发放成功!' });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `发放失败!${message}` });
      }
    });
  } else {
    prizeAuditBatch(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: '审核成功!' });
        reload();
        closeModal();
        clearSelectedRowKeys();
      } else {
        createErrorModal({ content: `审核失败!${message}` });
      }
    });
  }
}

function handleDown() {
  createConfirm({
    iconType: 'warning',
    content: `请确认要导出中奖记录`,
    onOk: function () {
      spinning.value = true;
      prizeExport(unref(params)).then(res => {
        const url = window.URL.createObjectURL(res);
        const fileName = `中奖记录${dayjs().format('YYYY-MM-DD HH:mm:ss')}`;
        downloadByUrl({
          url,
          fileName,
        });
        spinning.value = false;
      })
    }
  })
}

function handleImport() {
  openImport(true, { activityId: props.activityId });
}

function handleImportSuccess({ type, data }) {
  if (type === 'import') {
    saveBatchRedPackedRecord(data.success).then(async res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: `导入成功！成功 ${data.success?.length || 0} 条数据` });
        reload();
      } else {
        createErrorModal({
          content: `操作失败！${message} `,
        });
      }
    });
  }
}

function reloadAll() {
  reload({
    searchInfo: {
      activityId: props.activityId,
    },
  });
}

// 上传前验证
const beforeUpload: UploadProps['beforeUpload'] = file => {

  const { name } = file;
  const fileName = name?.split('.') || [''];
  const isExcel = ['xls', 'xlsx'].includes(fileName[fileName.length - 1]);
  if (!isExcel) {
    createErrorModal({ content: '只能上传Excel表格' });
  }
  // 弹出确认框，等待用户确认
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: '确认上传',
      content: `严格保持导出的中奖记录字段结构，禁止修改/删除任何字段列，错误格式可能导致导入失败，在【物流信息】中填写信息`,
      onOk: () => resolve(true), // 用户点击确认
      onCancel: () => {
        reject(new Error('Upload canceled'));
      }
    });
  });

};
// 处理导入
function handleImport2({ file }) {
  if (file.status === 'done') {
    importLoading.value = false;
    const { message, code } = file?.response;
    if (code === 200) {
      createSuccessModal({ content: '导入成功！' });
      reload()
    } else {
      createErrorModal({ content: `导入失败：${message}` });
    }
  } else if (file.status === 'error') {
    importLoading.value = false;
    createErrorModal({ content: '文件上传失败！' });
  }
}


onMounted(() => {
  nextTick(() => {
    emit('reload', reloadAll);
  });
});
</script>

<style lang="less" module>
.user-info {
  :global {
    background-color: #fff;

    .ant-upload-list {
      display: none;
    }
  }
}
</style>
