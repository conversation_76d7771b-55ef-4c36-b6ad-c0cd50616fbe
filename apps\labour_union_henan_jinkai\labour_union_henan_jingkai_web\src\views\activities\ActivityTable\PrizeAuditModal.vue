<template>
  <BasicModal
    @register="registerModule"
    :title="title"
    :can-fullscreen="false"
    @ok="handleSuccess"
    :wrap-class-name="$style['prize-audit-modal']"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { modalAuditFormItem } from '../activity';

const emit = defineEmits(['register', 'success']);

const name = ref('');

const activityId = ref('');

const record = ref<Recordable>();

const businessIds = ref<string[]>([]);

const type = ref('');

const title = computed(() => {
  return `${unref(name) ? `${unref(name)}--` : ''}${
    unref(type) === 'give' ? '奖品发放' : '获奖审核'
  }`;
});

const form = computed(() => {
  return modalAuditFormItem(unref(type));
});

const [registerModule, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  businessIds.value = [];

  type.value = data.type;

  record.value = data.record;

  name.value = data.record?.userName || '';

  activityId.value = data.activityId;

  businessIds.value = data.businessIds;

  setModalProps({
    confirmLoading: false,
  });
});

const [registerForm, { validate, resetFields }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

async function handleSuccess() {
  try {
    const values = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        businessIds: unref(businessIds),
        activityId: unref(activityId),
      },
      type: unref(type),
    });
  } catch (error) {
    setModalProps({
      confirmLoading: true,
    });
  }
}
</script>

<style lang="less" module>
.prize-audit-modal {
  :global {
    .ant-modal-body {
      height: 70vh !important;
    }
  }
}
</style>
