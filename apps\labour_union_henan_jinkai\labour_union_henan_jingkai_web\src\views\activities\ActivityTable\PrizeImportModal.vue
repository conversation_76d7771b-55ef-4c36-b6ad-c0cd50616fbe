<template>
  <BasicModal
    @register="registerModule"
    title="导入中奖记录"
    :can-fullscreen="false"
    :width="800"
    @ok="handleClose"
    :show-ok-btn="false"
    :show-cancel-btn="false"
  >
    <div class="import-container">
      <!-- 导入操作区域 -->
      <div class="import-actions" v-if="!importResult">
        <div class="action-buttons">
          <a-button type="primary" @click="handleDownFile" :loading="downloadLoading">
            <template #icon>
              <DownloadOutlined />
            </template>
            下载模板
          </a-button>
          <Upload
            name="file"
            accept=".xlsx,.xls"
            @change="handleImport"
            :before-upload="beforeUpload"
            :action="action"
            :headers="{ token: userStore.getToken }"
            :data="{ activityId: activityId }"
          >
            <a-button type="primary" :loading="importLoading">
              <template #icon>
                <UploadOutlined />
              </template>
              导入文件
            </a-button>
          </Upload>
        </div>
        <div class="upload-tip">
          <p>请先下载模板，按照模板格式填写数据后上传（仅支持红包类奖品数据导入）</p>
          <p>支持格式：xls、xlsx</p>
        </div>
      </div>

      <!-- 导入结果展示 -->
      <div class="import-result" v-if="importResult">
        <div class="result-summary">
          <a-alert
            :message="`导入完成！成功 ${importResult.success?.length || 0} 条，失败 ${importResult.fail?.length || 0} 条`"
            :type="importResult.fail?.length > 0 ? 'warning' : 'success'"
            show-icon
          />
        </div>

        <!-- 成功数据表格 -->
        <div class="result-table" v-if="importResult.success?.length > 0">
          <h3>成功导入数据 ({{ importResult.success.length }}条)</h3>
          <BasicTable
            :columns="successColumns"
            :dataSource="importResult.success"
            :pagination="false"
            :maxHeight="200"
            bordered
          />
        </div>

        <!-- 失败数据表格 -->
        <div class="result-table" v-if="importResult.fail?.length > 0">
          <h3>导入失败数据 ({{ importResult.fail.length }}条)</h3>
          <BasicTable
            :columns="failColumns"
            :dataSource="importResult.fail"
            :pagination="false"
            :maxHeight="200"
            bordered
          />
        </div>

        <div class="result-actions">
          <a-button @click="handleClose">关闭</a-button>
          <a-button type="primary" @click="handleConfirm" v-if="importResult.success?.length > 0">
            确认导入成功数据
          </a-button>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicTable } from '/@/components/Table';
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons-vue';

import { useMessage } from '@monorepo-yysz/hooks';
import { downloadByUrl } from '@monorepo-yysz/utils';
import { useUserStore } from '/@/store/modules/user';
import { useGlobSetting } from '/@/hooks/setting';
import { UploadProps, Upload } from 'ant-design-vue';
import {download} from "@/api/sys/upload";

const emit = defineEmits(['register', 'success']);

const { createErrorModal, createSuccessModal } = useMessage();
const { uploadUrl,bucket_name } = useGlobSetting();
const userStore = useUserStore();

const importLoading = ref(false);
const downloadLoading = ref(false);
const importResult = ref<any>(null);
const activityId = ref('');

// 上传地址
const action = ref(`${uploadUrl}/h5/activityInfo/importRedPacketRecord`);

// 成功数据表格列配置
const successColumns = [
  {
    title: '手机号',
    dataIndex: 'userMobile',
    key: 'userMobile',
    width: 120,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    key: 'userName',
    width: 120,
  },
  {
    title: '工会名称',
    dataIndex: 'companyName',
    key: 'userName',
    width: 200,
  },
  {
    title: '奖品名称',
    dataIndex: 'prizeName',
    key: 'prizeName',
    width: 150,
  },
  {
    title: '奖品内容',
    dataIndex: 'prizeContent',
    key: 'prizeContent',
    width: 120,
  },
];

// 失败数据表格列配置
const failColumns = [
  {
    title: '手机号',
    dataIndex: 'userMobile',
    key: 'userMobile',
    width: 120,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    key: 'userName',
    width: 120,
  },
  {
    title: '工会名称',
    dataIndex: 'companyName',
    key: 'userName',
    width: 200,
  },
  {
    title: '奖品名称',
    dataIndex: 'prizeName',
    key: 'prizeName',
    width: 150,
  },
  {
    title: '奖品内容',
    dataIndex: 'prizeContent',
    key: 'prizeContent',
    width: 200,
  },
  {
    title: '失败原因',
    dataIndex: 'remark',
    key: 'remark',
    width: 200,
  },
];

const [registerModule, { closeModal }] = useModalInner(async data => {
  activityId.value = data.activityId;
  importResult.value = null;
  importLoading.value = false;
  downloadLoading.value = false;
});

// 下载模板
async function handleDownFile() {
  download({
    filenames: [`common_1_redPacketRecord.xlsx`],
    bucketName: bucket_name,
  }).then(res => {
    const url = window.URL.createObjectURL(res);
    downloadByUrl({
      url,
      fileName: '红包记录模板.xlsx',
    });
  });
}

// 上传前验证
const beforeUpload: UploadProps['beforeUpload'] = file => {
  const { name } = file;
  const fileName = name?.split('.') || [''];
  const isExcel = ['xls', 'xlsx'].includes(fileName[fileName.length - 1]);
  if (!isExcel) {
    createErrorModal({ content: '只能上传Excel表格' });
  }
  importLoading.value = true;
  return isExcel || Upload.LIST_IGNORE;
};

// 处理导入
function handleImport({ file }) {
  if (file.status === 'done') {
    importLoading.value = false;
    const { message, code, data } = file?.response;
    if (code === 200) {
      importResult.value = data;
      createSuccessModal({ content: '文件解析成功！' });
    } else {
      createErrorModal({ content: `导入失败：${message}` });
    }
  } else if (file.status === 'error') {
    importLoading.value = false;
    createErrorModal({ content: '文件上传失败！' });
  }
}

// 确认导入
function handleConfirm() {
  emit('success', { type: 'import', data: importResult.value });
  closeModal();
}

// 关闭弹窗
function handleClose() {
  closeModal();
}
</script>

<style lang="less" scoped>
.import-container {
  .import-actions {
    text-align: center;
    padding: 20px 0;

    .action-buttons {
      margin-bottom: 20px;

      .ant-btn {
        margin: 0 10px;
      }
    }

    .upload-tip {
      color: #666;
      font-size: 14px;

      p {
        margin: 5px 0;
      }
    }
  }

  .import-result {
    .result-summary {
      margin-bottom: 20px;
    }

    .result-table {
      margin-bottom: 20px;

      h3 {
        margin-bottom: 10px;
        color: #333;
        font-size: 16px;
        font-weight: 500;
      }
    }

    .result-actions {
      text-align: center;
      margin-top: 20px;

      .ant-btn {
        margin: 0 10px;
      }
    }
  }
}

//:global {
//  .ant-upload-list {
//    display: none;
//  }
//}
</style>
