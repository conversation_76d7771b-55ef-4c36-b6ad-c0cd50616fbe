<template>
  <BasicModal
    @register="registerModule"
    :title="title"
    :can-fullscreen="false"
    :show-ok-btn="false"
  >
    <BasicForm
      @register="registerForm"
      class="back-transparent"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { prizeModalFormItem } from '../activity';

const name = ref('');

const title = computed(() => {
  return `${unref(name)}--获奖详情`;
});

const [registerModule, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  if (data.record) {
    name.value = data.record.userName;

    setFieldsValue({
      ...data.record,
    });
  }
  setModalProps({
    confirmLoading: false,
    showOkBtn: false,
  });
  setProps({
    disabled: true,
  });
});

const [registerForm, { setProps, resetFields, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: prizeModalFormItem(),
  showActionButtonGroup: false,
});
</script>
