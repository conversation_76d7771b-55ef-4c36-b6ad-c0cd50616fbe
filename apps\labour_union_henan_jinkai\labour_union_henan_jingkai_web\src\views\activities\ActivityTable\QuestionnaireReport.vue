<script setup lang="ts">
import {nextTick, onMounted, ref, unref} from "vue";
import {questionnaireDataSummary, questionnaireDataSummaryExport} from "@/api/activities";

import dayjs from "dayjs";
import {downloadByUrl} from "@monorepo-yysz/utils";


const props = defineProps({
  activityId: {
    type: String,
  },
});

const emit = defineEmits(['reload']);

const total = ref(0)
const topicInfoList = ref([])
const columns = [
  { title: '选项', width: 90, dataIndex: 'optionContent' },
  {
    title: '百分比',
    width: 60,
    dataIndex: 'percentage',
    slots: {
      customRender: 'customRender',
    },
  },
  { title: '选择人次', width: 60, dataIndex: 'selectedCount' },
]
const spinning = ref<boolean>(false);
const handleDown = ()=>{
  spinning.value = true;
  questionnaireDataSummaryExport({activityId:props.activityId}).then(res => {
    const url = window.URL.createObjectURL(res);
    const fileName = `问卷报表-${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    downloadByUrl({
      url,
      fileName,
    });
    spinning.value = false;
  });
}
const reloadAll = async ()=> {
  const data = await questionnaireDataSummary({
    activityId:props.activityId
  })
  console.log(data,data.topicInfoList)
  topicInfoList.value = data.topicInfoList
  total.value = data.total
}

onMounted(() => {
  nextTick(() => {
    emit('reload', reloadAll);
  });
});
</script>

<template>
  <div class="max-h-68vh overflow-auto">
    <a-row>
      <a-col :span="12">
        <span>当前共收集 <span class="font-600">{{ total }}</span> 份问卷记录</span>
      </a-col>
      <a-col :span="2" :offset="10">
        <a-button
            type="primary"
            @click="handleDown"
            :loading="spinning"
        >导出报表</a-button
        >
      </a-col>
    </a-row>
    <div>
      <a-card   v-for="(item,index) in topicInfoList" :key="index" >
        <template #title>
          <div>
            <span>{{index+1}}、{{item.topicContent}}</span>
          </div>
        </template>
        <a-table :data-source="item.options" :columns="columns" v-if="['radio', 'select','checkbox'].includes(item.optionType)" :pagination="false">
          <template #customRender="{ text, column }">
            <a-progress :percent="text" />
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
