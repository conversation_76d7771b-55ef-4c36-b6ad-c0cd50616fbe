<template>
  <BasicModal
    @register="registerModal"
    :can-fullscreen="false"
    :title="`${title}--统计`"
    :show-ok-btn="false"
  >
    <Tabs
      v-model:activeKey="activeKey"
      type="card"
    >
      <TabPane
        v-for="item in tabsObj"
        :key="item.key"
        forceRender
        :tab="item.tab"
      >
        <CardList :dataSource="item.cardData" />
        <div
          class="my-15px"
          v-if="item.key !== ActivityType.LOTTERY"
        >
          <label class="mr-5px">数据报表</label>
          <RangePicker
            v-model:value="item.dateTime"
            @change="dates => handleChange(dates, item)"
          />
        </div>
        <ActivityAnalysis
          :source-data="item.echartData"
          :dateArr="item.dateArr"
          :pie-data="item.prizeList"
          :type="item.key"
        />
        <TopicList
          v-if="[ActivityType.QUIZ, ActivityType.SURVEY].includes(item.key)"
          :topic-list="item.topicList"
        />
      </TabPane>
    </Tabs>
  </BasicModal>
</template>

<script lang="ts">
import { Tabs, RangePicker } from 'ant-design-vue';
import { computed, defineComponent, ref, unref } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import {
  ActivityType,
  ActivityTypeApi,
  ActivityTypeZh,
  TabsObj,
  RangeValue,
} from '../activities.d';
import { baseInfo } from '@/api/activities';
import CardList from './CardList.vue';
import ActivityAnalysis from './ActivityAnalysis.vue';
import TopicList from './TopicList.vue';
import dayjs from 'dayjs';

const fmt = 'YYYY-MM-DDT00:00:00';
const fmtLast = 'YYYY-MM-DDT23:59:59';

export default defineComponent({
  name: 'StatisticsModal',
  emits: ['success', 'register', 'cancel'],
  components: {
    BasicModal,
    Tabs,
    TabPane: Tabs.TabPane,
    CardList,
    ActivityAnalysis,
    RangePicker,
    TopicList,
  },
  setup(_, {}) {
    const title = ref('');

    const record = ref<Recordable>({});

    const investigation = ref('N'); //是否问答

    const luckDraw = ref('N'); //是否抽奖

    const activeKey = ref(ActivityType.BASICINFO);

    const activityType = ref(ActivityType.QUIZ);

    const tabsTitle = computed(() => {
      return ActivityTypeZh[unref(activityType)];
    });

    const tabsObj = ref<TabsObj[]>([]);

    const dateTime = ref<RangeValue>([dayjs().subtract(7, 'day'), dayjs()]);

    const [registerModal, {}] = useModalInner(async data => {
      //重置初始值

      activeKey.value = ActivityType.BASICINFO;

      title.value = data.record.activityName;

      record.value = data.record;

      activityType.value = data.activityType;

      investigation.value = data.record.investigation;

      luckDraw.value = data.record.luckDraw;

      //加载遍历tabs
      const [first, last] = unref(dateTime);

      getData(data.record, first, last);
    });

    async function getData(record, first, last) {
      tabsObj.value = [];
      const { activityStartTime, activityEndTime, createTime, updateTime, ...others } = record;

      let params = {
        ...others,
        firstTime: first.format(fmt),
        lastTime: last.format(fmtLast),
      };

      const basic = await baseInfo(params);
      if (basic) {
        //基础统计
        const {
          joinCountArr,
          todayJoinCount,
          todayUserCount,
          totalJoinCount,
          totalUserCount,
          userCountArr,
          dateArr,
        } = basic;
        tabsObj.value.push({
          key: ActivityType.BASICINFO,
          tab: ActivityTypeZh[ActivityType.BASICINFO],
          cardData: [
            { name: '参与总人数', count: totalUserCount, icon: 'join|svg' },
            { name: '今日新增人数', count: todayUserCount, icon: 'joinTotal|svg' },
            { name: '参与总次数', count: totalJoinCount, icon: 'times|svg' },
            { name: '今日新增次数', count: todayJoinCount, icon: 'dailyAdd|svg' },
          ],
          echartData: [joinCountArr, userCountArr],
          dateTime: unref(dateTime),
          dateArr: dateArr,
        });
      }

      if (unref(activityType)) {
        //当前页统计
        const dataSource = await ActivityTypeApi[unref(activityType)](params);
        const {
          joinCountArr,
          todayJoinCount,
          todayUserCount,
          totalJoinCount,
          totalUserCount,
          userCountArr,
          dateArr,
          prizeList,
          topicList,
        } = dataSource;
        tabsObj.value.push({
          key: unref(activityType),
          tab: ActivityTypeZh[unref(activityType)],
          cardData: [
            { name: '参与总人数', count: totalUserCount, icon: 'join|svg' },
            { name: '今日新增人数', count: todayUserCount, icon: 'joinTotal|svg' },
            { name: '参与总次数', count: totalJoinCount, icon: 'times|svg' },
            { name: '今日新增次数', count: todayJoinCount, icon: 'dailyAdd|svg' },
          ],
          echartData: [joinCountArr, userCountArr],
          dateTime: unref(dateTime),
          dateArr: dateArr,
          prizeList: prizeList,
          topicList: topicList,
        });
      }

      if (unref(investigation) === 'Y') {
        //插件调查统计
        const dataSource = await ActivityTypeApi[ActivityType.SURVEY](params);
        const {
          joinCountArr,
          todayJoinCount,
          todayUserCount,
          totalJoinCount,
          totalUserCount,
          userCountArr,
          dateArr,
          topicList,
        } = dataSource;
        tabsObj.value.push({
          key: ActivityType.SURVEY,
          tab: ActivityTypeZh[ActivityType.SURVEY],
          cardData: [
            { name: '参与总人数', count: totalUserCount, icon: 'join|svg' },
            { name: '今日新增人数', count: todayUserCount, icon: 'joinTotal|svg' },
            { name: '参与总次数', count: totalJoinCount, icon: 'times|svg' },
            { name: '今日新增次数', count: todayJoinCount, icon: 'dailyAdd|svg' },
          ],
          echartData: [joinCountArr, userCountArr],
          dateTime: unref(dateTime),
          dateArr: dateArr,
          topicList: topicList,
        });
      }

      if (unref(luckDraw) === 'Y') {
        //插件抽奖统计
        const dataSource = await ActivityTypeApi[ActivityType.LOTTERY](params);
        const {
          joinCountArr,
          todayJoinCount,
          todayUserCount,
          totalJoinCount,
          totalUserCount,
          userCountArr,
          dateArr,
          prizeList,
        } = dataSource;
        tabsObj.value.push({
          key: ActivityType.LOTTERY,
          tab: ActivityTypeZh[ActivityType.LOTTERY],
          cardData: [
            { name: '参与总人数', count: totalUserCount, icon: 'join|svg' },
            { name: '今日新增人数', count: todayUserCount, icon: 'joinTotal|svg' },
            { name: '参与总次数', count: totalJoinCount, icon: 'times|svg' },
            { name: '今日新增次数', count: todayJoinCount, icon: 'dailyAdd|svg' },
          ],
          echartData: [joinCountArr, userCountArr],
          dateTime: unref(dateTime),
          dateArr: dateArr,
          prizeList: prizeList,
        });
      }
    }

    async function handleChange(dates, item) {
      try {
        const { activityStartTime, activityEndTime, createTime, updateTime, ...others } =
          unref(record);

        let params = {
          ...others,
          firstTime: dates[0].format(fmt),
          lastTime: dates[1].format(fmtLast),
        };
        const data = await ActivityTypeApi[item.key](params);

        const { joinCountArr, userCountArr, dateArr } = data;

        item.echartData = [joinCountArr, userCountArr];
        item.dateArr = dateArr;
      } catch (error) {}
    }

    return {
      registerModal,
      title,
      activeKey,
      luckDraw,
      investigation,
      activityType,
      ActivityType,
      tabsTitle,
      ActivityTypeZh,
      tabsObj,
      dateTime,
      handleChange,
    };
  },
});
</script>
