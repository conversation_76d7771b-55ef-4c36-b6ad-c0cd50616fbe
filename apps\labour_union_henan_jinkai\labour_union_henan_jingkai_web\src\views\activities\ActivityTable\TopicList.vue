<template>
  <div :class="$style.topic">
    <Divider>题目分析</Divider>
    <div>
      <Collapse v-model:activeKey="activeKey">
        <CollapsePanel
          v-for="item in list"
          :key="item.topicInfoId"
          :header="`标题: ${item.topicContent}`"
        >
          <Row>
            <Col :span="18" class="border px-5px">
              <span>选项</span>
            </Col>
            <Col :span="6" class="border px-5px">
              <span>选择次数</span>
            </Col>
          </Row>
          <Row
            v-for="(v, k) in item.options"
            :class="[{ 'bg-green-300': v.correct }, 'font-bold ']"
          >
            <Col :span="18" class="border px-5px">
              <TypographyText>{{
                `${String.fromCharCode(65 + k)}.${v.optionContent}`
              }}</TypographyText>
            </Col>
            <Col :span="6" class="border px-5px">
              <TypographyText>{{ v.selectCount }}</TypographyText>
            </Col>
          </Row>
        </CollapsePanel>
      </Collapse>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { basicProps } from '../activities.d'
import { Divider, Collapse, CollapsePanel, Row, Col, TypographyText } from 'ant-design-vue'
import { filter, map } from 'lodash-es'

const props = defineProps({
  ...basicProps,
})

const activeKey = ref<string[]>([])

const list = computed(() => {
  activeKey.value = map(props.topicList, v => v.topicInfoId)
  const arr = filter(props.topicList, v => v.optionType === 'radio' || v.optionType === 'select')
  return arr
})
</script>

<style lang="less" module>
.topic {
  :global {
    .ant-collapse-header {
      @apply text-base;
    }
  }
}
</style>
