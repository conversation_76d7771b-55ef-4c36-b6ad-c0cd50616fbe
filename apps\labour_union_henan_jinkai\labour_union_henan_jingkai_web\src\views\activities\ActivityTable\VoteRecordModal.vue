<template>
  <BasicModal
    @register="registerModal"
    :can-fullscreen="false"
    :title="title"
    :show-ok-btn="false"
  >
    <BasicTable @register="registerTable"> </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicTable, useTable } from '@/components/Table';
import { voteRecordList } from '@/api/activities/statistics';

const title = ref('');
const opusInfoId = ref('');

const [registerModal, {}] = useModalInner(async function (data) {
  title.value = `${data.record.opusName}-投票记录`;
  opusInfoId.value = data.record.opusInfoId;
  reload();
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  api: voteRecordList,
  columns: [
    {
      dataIndex: 'createUser',
      title: '用户姓名',
    },
    {
      dataIndex: 'createTime',
      title: '投票日期',
    },
  ],
  maxHeight: 420,
  useSearchForm: false,
  showTableSetting: false,
  bordered: true,
  immediate: false,
  showIndexColumn: false,
  beforeFetch: params => {
    params.sortType = 'desc';
    params.orderBy = 'create_time';
    params.opusInfoId = unref(opusInfoId);
    return params;
  },
});
</script>
