import { useSetting } from '../BasicSetting/useSetting'

export const useActivity = (): any => {
  const [
    registerQuiz,
    { validate: validateQuiz, reset: resetQuiz, setProps: setPropsQuiz, setValues: setValuesQuiz, },
  ] = useSetting()

  const [
    registerSignUp,
    {
      validate: validateSignUp,
      reset: resetSignUp,
      setProps: setPropsSignUp,
      setValues: setValuesSignUp,
    },
  ] = useSetting()

  const [
    registerLottery,
    {
      validate: validateLottery,
      reset: resetLottery,
      setProps: setPropsLottery,
      setTableDataPrize,
      setValues: setValuesPrize,
      getDataSourcePrize,
    },
  ] = useSetting()

  const [
    registerSurvey,
    {
      validate: validateSurvey,
      reset: resetSurvey,
      setProps: setPropsSurvey,
      setValues: setValuesSurvey,
    },
  ] = useSetting()

  const [
    registerVote,
    { validate: validateVote, reset: resetVote, setProps: setPropsVote, setValues: setValuesVote },
  ] = useSetting()

  const [
    registerInclusiveYJWD,
    {
      validate: validateInclusiveYJWD,
      reset: resetInclusiveYJWD,
      setProps: setPropsInclusiveYJWD,
      setValues: setValuesInclusiveYJWD,
    },
  ] = useSetting()

  const [
    registerInclusiveTicket,
    {
      validate: validateInclusiveTicket,
      reset: resetInclusiveTicket,
      setProps: setPropsInclusiveTicket,
      setValues: setValuesInclusiveTicket,
    },
  ] = useSetting()

  const [
    registerWalk,
    {
      validate: validateWalk,
      reset: resetWalk,
      setProps: setPropsWalk,
      setValues: setValuesWalk,
      setTableDataPrize: setWalkTableDataPrize,
      getDataSourcePrize: getDataSourceWalkPrize,},
  ] = useSetting()

  return [
    registerQuiz,
    registerSignUp,
    registerLottery,
    registerSurvey,
    registerVote,
    registerInclusiveYJWD,
    registerInclusiveTicket,
    registerWalk,
    {
      validateQuiz,
      resetQuiz,
      setPropsQuiz,
      setValuesQuiz,
    },
    {
      validateSignUp,
      resetSignUp,
      setPropsSignUp,
      setValuesSignUp,
    },
    {
      validateLottery,
      resetLottery,
      setPropsLottery,
      setTableDataPrize,
      setValuesPrize,
      getDataSourcePrize,
    },
    {
      validateSurvey,
      resetSurvey,
      setPropsSurvey,
      setValuesSurvey,
      setValuesPrize,
    },
    {
      validateVote,
      resetVote,
      setPropsVote,
      setValuesVote,
    },
    {
      validateInclusiveYJWD,
      resetInclusiveYJWD,
      setPropsInclusiveYJWD,
      setValuesInclusiveYJWD,
    },
    {
      validateInclusiveTicket,
      resetInclusiveTicket,
      setPropsInclusiveTicket,
      setValuesInclusiveTicket,
    },
    {
      validateWalk,  resetWalk,  setPropsWalk,  setValuesWalk,setWalkTableDataPrize,getDataSourceWalkPrize
    }
  ]
}
