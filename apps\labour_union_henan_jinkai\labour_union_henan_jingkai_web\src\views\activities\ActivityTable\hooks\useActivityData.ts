import { ref, computed, unref } from 'vue';
import { ActivityType } from '../../activities.d';

export interface EducationAidInfo {
  filePath?: string;
  expiredDateTime?: string;
  autoId?: number;
}

/**
 * 活动数据管理 Hook
 */
export function useActivityData() {
  // 基础状态
  const activeKey = ref('activity');
  const allActiveKey = ref(['activity']);
  const isUpdate = ref(true);
  const autoId = ref<number | undefined>(undefined);
  const activityId = ref('');
  const disabled = ref(false);
  const ifQuiz = ref<string>('');
  const otherTabs = ref<ActivityType[]>([]);
  const addTitle = ref('');
  const ifFilter = ref(true);

  // 活动数据状态
  const vieAnswerInfo = ref<Recordable>({});
  const signUpInfo = ref<Recordable>({});
  const luckDrawInfo = ref<Recordable>({});
  const questionnaireInfo = ref<Recordable>({});
  const voteInfo = ref<Recordable>({});
  const coupon = ref<Recordable>({});
  const educationAidInfo = ref<EducationAidInfo>({});
  const walkInfo = ref<Recordable>({});
  const record = ref<Recordable>({});

  // 计算属性
  const title = computed(() => {
    return unref(disabled)
      ? `${unref(record).activityName}--详情`
      : unref(isUpdate)
        ? `编辑--${unref(record).activityName}`
        : `新增${unref(addTitle)}`;
  });

  /**
   * 重置所有数据状态
   */
  const resetAllData = () => {
    activeKey.value = 'activity';
    allActiveKey.value = ['activity'];
    otherTabs.value = [];
    ifQuiz.value = '';
    autoId.value = undefined;
    activityId.value = '';
    ifFilter.value = true;

    // 重置各活动数据
    vieAnswerInfo.value = {};
    signUpInfo.value = {};
    luckDrawInfo.value = {};
    questionnaireInfo.value = {};
    voteInfo.value = {};
    coupon.value = {};
    educationAidInfo.value = {};
    walkInfo.value = {};
    record.value = {};
  };

  /**
   * 设置基础属性
   */
  const setBasicProps = (props: {
    disabled?: boolean;
    isUpdate?: boolean;
    addTitle?: string;
    activityId?: string;
    autoId?: number;
  }) => {
    if (props.disabled !== undefined) disabled.value = props.disabled;
    if (props.isUpdate !== undefined) isUpdate.value = props.isUpdate;
    if (props.addTitle !== undefined) addTitle.value = props.addTitle;
    if (props.activityId !== undefined) activityId.value = props.activityId;
    if (props.autoId !== undefined) autoId.value = props.autoId;
  };

  /**
   * 设置活动数据
   */
  const setActivityData = (data: {
    vieAnswerInfo?: Recordable;
    signUpInfo?: Recordable;
    luckDrawInfo?: Recordable;
    questionnaireInfo?: Recordable;
    voteInfo?: Recordable;
    coupon?: Recordable;
    educationAidInfo?: EducationAidInfo;
    walkInfo?: Recordable;
    record?: Recordable;
  }) => {
    if (data.vieAnswerInfo !== undefined) vieAnswerInfo.value = data.vieAnswerInfo;
    if (data.signUpInfo !== undefined) signUpInfo.value = data.signUpInfo;
    if (data.luckDrawInfo !== undefined) luckDrawInfo.value = data.luckDrawInfo;
    if (data.questionnaireInfo !== undefined) questionnaireInfo.value = data.questionnaireInfo;
    if (data.voteInfo !== undefined) voteInfo.value = data.voteInfo;
    if (data.coupon !== undefined) coupon.value = data.coupon;
    if (data.educationAidInfo !== undefined) educationAidInfo.value = data.educationAidInfo;
    if (data.walkInfo !== undefined) walkInfo.value = data.walkInfo;
    if (data.record !== undefined) record.value = data.record;
  };

  /**
   * 更新 ifQuiz 状态和相关的 activeKey
   */
  const updateIfQuiz = (value: string, activityType: ActivityType) => {
    ifQuiz.value = value;

    const arr = allActiveKey.value.filter(v => v !== activityType);

    if (value === '2') {
      allActiveKey.value = [...arr];
    } else {
      allActiveKey.value = [...arr, activityType];
    }
  };

  /**
   * 更新其他标签页
   */
  const updateOtherTabs = (value: ActivityType[]) => {
    otherTabs.value = value;

    // 移除抽奖和调查相关的标签
    const filteredKeys = allActiveKey.value.filter(
      v => v !== ActivityType.LOTTERY && v !== ActivityType.SURVEY
    );

    // 合并新的标签页
    allActiveKey.value = [...new Set([...filteredKeys, ...value])];
  };

  return {
    // 状态
    activeKey,
    allActiveKey,
    isUpdate,
    autoId,
    activityId,
    disabled,
    ifQuiz,
    otherTabs,
    addTitle,
    ifFilter,
    vieAnswerInfo,
    signUpInfo,
    luckDrawInfo,
    questionnaireInfo,
    voteInfo,
    coupon,
    educationAidInfo,
    walkInfo,
    record,

    // 计算属性
    title,

    // 方法
    resetAllData,
    setBasicProps,
    setActivityData,
    updateIfQuiz,
    updateOtherTabs,
  };
}
