import { nextTick, unref } from 'vue';
import { ActivityType, Question } from '../../activities.d';

// 简化的类型定义
type ResetFunctions = Record<string, (...args: any[]) => Promise<any>>;

/**
 * 活动设置管理 Hook
 */
export function useActivitySetting() {
  /**
   * 重置指定活动类型的设置 - 使用简化的参数类型
   */
  const resetActivitySetting = async (
    activityType: ActivityType,
    resetFunctions: ResetFunctions
  ) => {
    try {
      switch (activityType) {
        case ActivityType.QUIZ:
          await resetFunctions.resetQuiz();
          await resetFunctions.setValuesQuiz({
            topicInfoList: [
              {
                ifShow: true,
                optionType: 'radio',
                score: 2,
                options: [{}, { correct: true }],
              } as Question,
            ],
          });
          break;

        case ActivityType.BLUE_VEST:
          await resetFunctions.resetSignUp();
          break;

        case ActivityType.SIGNUP:
        case ActivityType.INTEREST_GROUP:
        case ActivityType.COMPETITION:
        case ActivityType.FUN_COMPETITION:
        case ActivityType.VOLUNTEER_SERVICE:
        case ActivityType.FRIENDSHIP:
        case ActivityType.INCLUSIVE_SIGNUP:
        case ActivityType.WOMEN:
          await resetFunctions.resetSignUp();
          await resetFunctions.setValuesSignUp({
            topicInfoList: [{ ifMust: 'N', optionType: 'input' } as Question],
          });
          break;

        case ActivityType.BIRTHDAY:
        case ActivityType.LOTTERY:
          await resetFunctions.resetLottery();
          await nextTick();
          await resetFunctions.setTableDataPrize([{} as Question]);
          break;

        case ActivityType.SURVEY:
          await resetFunctions.resetSurvey();
          await resetFunctions.setValuesSurvey({
            topicInfoList: [
              { ifShow: true, optionType: 'radio', options: [{}, { correct: true }] } as Question,
            ],
          });
          break;

        case ActivityType.MULTIPLE_VOTE:
        case ActivityType.VOTE:
          await resetFunctions.resetVote();
          await resetFunctions.setValuesVote({
            opusesInfos: [{} as Question, {} as Question],
          });
          break;

        case ActivityType.INCLUSIVE_YJWD:
          await resetFunctions.resetInclusiveYJWD();
          await resetFunctions.setValuesInclusiveYJWD({
            topicInfoList: [
              {
                ifShow: true,
                optionType: 'radio',
                score: 2,
                options: [{}, { correct: true }],
              } as Question,
            ],
          });
          break;

        case ActivityType.SUMMER_COOLNESS:
        case ActivityType.COUPON:
          await resetFunctions.resetInclusiveTicket();
          break;

        case ActivityType.WALK:
          await resetFunctions.resetWalk();
          await nextTick();
          await resetFunctions.setWalkTableDataPrize([{} as Question]);
          break;
      }
    } catch (error) {
      throw new Error(`Reset error for activity type ${activityType}: ${error}`);
    }
  };

  /**
   * 设置活动配置数据 - 使用简化的参数类型
   */
  const setActivityConfigData = async (
    key: string,
    disabled: boolean,
    dataRefs: Record<string, any>,
    setFunctions: Record<string, (...args: any[]) => Promise<any>>
  ) => {
    await nextTick();

    try {
      const props = { disabled };

      switch (key) {
        case `${ActivityType.QUIZ}`:
          await setFunctions.setPropsQuiz(props);
          await setFunctions.setValuesQuiz(unref(dataRefs.vieAnswerInfo));
          break;

        case `${ActivityType.BLUE_VEST}`:
        case `${ActivityType.SIGNUP}`:
        case `${ActivityType.INCLUSIVE_SIGNUP}`:
        case `${ActivityType.COMPETITION}`:
        case `${ActivityType.FUN_COMPETITION}`:
        case `${ActivityType.WOMEN}`:
        case `${ActivityType.INTEREST_GROUP}`:
        case `${ActivityType.VOLUNTEER_SERVICE}`:
        case `${ActivityType.FRIENDSHIP}`:
          await setFunctions.setPropsSignUp(props);
          await setFunctions.setValuesSignUp(unref(dataRefs.signUpInfo));
          break;

        case `${ActivityType.LOTTERY}`:
          await setFunctions.setPropsLottery(props);
          await nextTick();
          await setFunctions.setValuesPrize(unref(dataRefs.luckDrawInfo));
          await setFunctions.setTableDataPrize(
            unref(dataRefs.luckDrawInfo)?.prizeInfos || [{} as Question]
          );
          break;

        case `${ActivityType.SURVEY}`:
          await setFunctions.setPropsSurvey(props);
          await setFunctions.setValuesSurvey(unref(dataRefs.questionnaireInfo));
          break;

        case `${ActivityType.MULTIPLE_VOTE}`:
        case `${ActivityType.VOTE}`:
          await setFunctions.setPropsVote(props);
          await setFunctions.setValuesVote(unref(dataRefs.voteInfo));
          break;

        case `${ActivityType.INCLUSIVE_YJWD}`:
          await setFunctions.setPropsInclusiveYJWD(props);
          await setFunctions.setValuesInclusiveYJWD(unref(dataRefs.vieAnswerInfo));
          break;

        case `${ActivityType.COUPON}`:
        case `${ActivityType.SUMMER_COOLNESS}`:
          await setFunctions.setPropsInclusiveTicket(props);
          await setFunctions.setValuesInclusiveTicket(unref(dataRefs.coupon));
          break;

        case `${ActivityType.WALK}`:
          await setFunctions.setPropsWalk(props);
          await setFunctions.setValuesWalk(unref(dataRefs.walkInfo));
          await setFunctions.setWalkTableDataPrize(
            unref(dataRefs.walkInfo)?.prizeInfos || [{} as Question]
          );
          break;
      }

      // 处理插件标签页
      await nextTick();

      if (unref(dataRefs.allActiveKey).includes(ActivityType.LOTTERY)) {
        await setFunctions.setPropsLottery(props);
        await nextTick();
        await setFunctions.setValuesPrize(unref(dataRefs.luckDrawInfo));
        await setFunctions.setTableDataPrize(
          unref(dataRefs.luckDrawInfo)?.prizeInfos || [{} as Question]
        );
      }

      if (unref(dataRefs.allActiveKey).includes(ActivityType.SURVEY)) {
        await setFunctions.setPropsSurvey(props);
        await setFunctions.setValuesSurvey(unref(dataRefs.questionnaireInfo));
      }
    } catch (error) {
      console.warn('Error in setActivityConfigData:', error, { key, disabled });
    }
  };

  return {
    resetActivitySetting,
    setActivityConfigData,
  };
}
