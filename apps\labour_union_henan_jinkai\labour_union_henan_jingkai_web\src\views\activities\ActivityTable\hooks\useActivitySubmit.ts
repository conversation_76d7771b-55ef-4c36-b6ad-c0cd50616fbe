import { ref } from 'vue';
import { Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { CloseCircleFilled, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useUserStore } from '@/store/modules/user';

/**
 * 活动提交处理 Hook
 */
export function useActivitySubmit() {
  const submitLoading = ref(false);

  /**
   * 显示验证错误提示
   */
  const showValidationError = () => {
    Modal.error({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: `请检查活动信息是否填写正确`,
      okText: '确认',
      closable: true,
    });
  };

  /**
   * 校验积分配置
   */
  const checkIntegralConfig = async (params: any, emit: any, handleCancel: () => Promise<void>) => {
    const { integralFlag } = params;

    emit('success', { params });
    handleCancel();

    // // 特定公司直接提交
    // if (useUserStore().getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5') {
    //   emit('success', { params });
    //   await handleCancel();
    //   return;
    // }
    //
    // // 检查是否涉及积分相关配置
    // const hasIntegralConfig =
    //   params?.luckDrawInfo?.prizeInfos?.some(t => t.prizeType === '2') ||
    //   params?.couponExtend?.couponInfos?.some(t => t.integralFlag === 'Y');
    //
    // if (integralFlag === 'Y' || hasIntegralConfig) {
    //   Modal.confirm({
    //     title: '信息',
    //     icon: createVNode(ExclamationCircleOutlined),
    //     content: `活动涉及积分相关配置，需要提交上级审核?`,
    //     okText: '确认',
    //     cancelText: '取消',
    //     onOk() {
    //       emit('success', { params: { ...params, auditState: 'wait' } });
    //       handleCancel();
    //     },
    //   });
    // } else {
    //   emit('success', { params });
    //   handleCancel();
    // }
  };

  /**
   * 处理提交流程
   */
  const handleSubmitFlow = async (
    getParamsFunction: () => Promise<any>,
    sensitiveCheckFunction: (params: any) => Promise<void>,
    emit: any,
    handleCancel: () => Promise<void>,
    setModalProps: (props: any) => void
  ) => {
    try {
      submitLoading.value = true;
      setModalProps({ confirmLoading: true });

      const params = await getParamsFunction();

      // 进行敏感词检查
      await sensitiveCheckFunction(params);
    } catch (error) {
      const { errorFields } = error as Recordable;

      if (errorFields?.length > 0) {
        showValidationError();
      } else {
        console.error('Submit error:', error);
        throw error;
      }
    } finally {
      submitLoading.value = false;
      setModalProps({ confirmLoading: false });
    }
  };

  return {
    submitLoading,
    showValidationError,
    checkIntegralConfig,
    handleSubmitFlow,
  };
}
