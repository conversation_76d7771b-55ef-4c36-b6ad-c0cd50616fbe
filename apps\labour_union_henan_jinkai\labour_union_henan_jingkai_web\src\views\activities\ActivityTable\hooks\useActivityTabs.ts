import { ActivityType, ActivitySettingZh } from '../../activities.d';

/**
 * 活动标签页管理 Hook
 */
export function useActivityTabs() {
  /**
   * 检查是否显示报名配置标签页
   */
  const shouldShowSignupTab = (ifQuiz: string, activityType: ActivityType) => {
    const SIGNUP_TYPES = [
      ActivityType.SIGNUP,
      ActivityType.INTEREST_GROUP,
      ActivityType.COMPETITION,
      ActivityType.FUN_COMPETITION,
      ActivityType.FRIENDSHIP,
      ActivityType.VOLUNTEER_SERVICE,
      ActivityType.WOMEN,
    ];

    return ifQuiz === '1' && SIGNUP_TYPES.includes(activityType);
  };

  /**
   * 检查是否显示抽奖配置标签页
   */
  const shouldShowLotteryTab = (
    ifQuiz: string,
    activityType: ActivityType,
    otherTabs: ActivityType[]
  ) => {
    const LOTTERY_TYPES = [ActivityType.LOTTERY, ActivityType.BIRTHDAY];

    return (
      (ifQuiz === '1' && LOTTERY_TYPES.includes(activityType)) ||
      otherTabs.includes(ActivityType.LOTTERY)
    );
  };

  /**
   * 检查是否显示调查配置标签页
   */
  const shouldShowSurveyTab = (
    ifQuiz: string,
    activityType: ActivityType,
    otherTabs: ActivityType[]
  ) => {
    return (
      (ifQuiz === '1' && activityType === ActivityType.SURVEY) ||
      otherTabs.includes(ActivityType.SURVEY)
    );
  };

  /**
   * 检查是否显示投票配置标签页
   */
  const shouldShowVoteTab = (ifQuiz: string, activityType: ActivityType) => {
    const VOTE_TYPES = [ActivityType.VOTE, ActivityType.MULTIPLE_VOTE];
    return ifQuiz === '1' && VOTE_TYPES.includes(activityType);
  };

  /**
   * 检查是否显示票券配置标签页
   */
  const shouldShowCouponTab = (ifQuiz: string, activityType: ActivityType) => {
    const COUPON_TYPES = [ActivityType.COUPON, ActivityType.SUMMER_COOLNESS];
    return ifQuiz === '1' && COUPON_TYPES.includes(activityType);
  };

  /**
   * 检查是否显示插件库选择器
   */
  const shouldShowPluginSelector = (activityType: ActivityType, disabled: boolean) => {
    const PLUGIN_SUPPORTED_TYPES = [
      ActivityType.QUIZ,
      ActivityType.SIGNUP,
      ActivityType.SURVEY,
      ActivityType.WALK,
    ];

    return PLUGIN_SUPPORTED_TYPES.includes(activityType) && !disabled;
  };

  /**
   * 获取抽奖标签页标题
   */
  const getLotteryTabTitle = (activityType: ActivityType) => {
    const targetType =
      activityType === ActivityType.BIRTHDAY ? ActivityType.BIRTHDAY : ActivityType.LOTTERY;
    return `${ActivitySettingZh[targetType]}配置`;
  };

  /**
   * 获取标签页配置
   */
  const getTabConfig = (activityType: ActivityType, ifQuiz: string, otherTabs: ActivityType[]) => {
    return {
      showQuizTab: ifQuiz === '1' && activityType === ActivityType.QUIZ,
      showSignupTab: shouldShowSignupTab(ifQuiz, activityType),
      showWalkTab: ifQuiz === '1' && activityType === ActivityType.WALK,
      showLotteryTab: shouldShowLotteryTab(ifQuiz, activityType, otherTabs),
      showSurveyTab: shouldShowSurveyTab(ifQuiz, activityType, otherTabs),
      showVoteTab: shouldShowVoteTab(ifQuiz, activityType),
      showInclusiveTab: ifQuiz === '1' && activityType === ActivityType.INCLUSIVE_YJWD,
      showCouponTab: shouldShowCouponTab(ifQuiz, activityType),
    };
  };

  return {
    shouldShowSignupTab,
    shouldShowLotteryTab,
    shouldShowSurveyTab,
    shouldShowVoteTab,
    shouldShowCouponTab,
    shouldShowPluginSelector,
    getLotteryTabTitle,
    getTabConfig,
  };
}
