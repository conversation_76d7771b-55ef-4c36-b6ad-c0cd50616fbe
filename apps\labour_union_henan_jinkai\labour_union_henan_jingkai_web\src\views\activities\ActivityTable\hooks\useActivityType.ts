import { computed, ref, unref } from 'vue';
import { ActivityType } from '../../activities.d';
import { RadioChangeEvent } from 'ant-design-vue/lib';

/**
 * 活动类型管理 Hook
 */
export function useActivityType() {
  // 活动类型状态
  const activityType = ref<ActivityType>(ActivityType.QUIZ);

  /**
   * 设置活动类型
   */
  const setActivityType = async (type: ActivityType) => {
    activityType.value = type;
  };
  // 监听活动类型变化 蓬安整合竞答和报名在弹窗内选择
  const changeActivityType = (e: RadioChangeEvent, setProps, resetDefaultField) => {
    activityType.value = e.target.value as ActivityType;
    setProps({ activityType: getCurrentActivityType() });
    // 示例：重置特定字段 暂时不用 部分使用的是v-if不在手动重置
    // resetDefaultField(['contacts', '', 'contactPhone', 'activityAddress']);
  };

  /**
   * 获取当前活动类型
   */
  const getCurrentActivityType = () => {
    return unref(activityType);
  };

  /**
   * 检查是否为指定活动类型
   */
  const isActivityType = (type: ActivityType) => {
    return unref(activityType) === type;
  };

  /**
   * 检查是否为指定活动类型之一
   */
  const isOneOfActivityTypes = (types: ActivityType[]) => {
    return types.includes(unref(activityType));
  };

  /**
   * 计算属性：活动类型
   */
  const computedActivityType = computed(() => {
    return unref(activityType);
  });

  /**
   * 判断是否显示插件选择器
   */
  const shouldShowPluginSelector = computed(() => {
    return [
      ActivityType.QUIZ,
      ActivityType.SIGNUP,
      ActivityType.SURVEY,
      ActivityType.WALK,
    ].includes(unref(activityType));
  });

  /**
   * 判断是否为生日活动
   */
  const isBirthdayActivity = computed(() => {
    return unref(activityType) === ActivityType.BIRTHDAY;
  });

  /**
   * 判断是否为抽奖活动
   */
  const isLotteryActivity = computed(() => {
    return unref(activityType) === ActivityType.LOTTERY;
  });

  /**
   * 判断是否为调查活动
   */
  const isSurveyActivity = computed(() => {
    return unref(activityType) === ActivityType.SURVEY;
  });

  /**
   * 判断是否为投票活动
   */
  const isVoteActivity = computed(() => {
    return [ActivityType.VOTE, ActivityType.MULTIPLE_VOTE].includes(unref(activityType));
  });

  /**
   * 判断是否为普惠一键问答活动
   */
  const isInclusiveYJWDActivity = computed(() => {
    return unref(activityType) === ActivityType.INCLUSIVE_YJWD;
  });

  /**
   * 判断是否为夏日清凉活动
   */
  const isSummerCoolnessActivity = computed(() => {
    return unref(activityType) === ActivityType.SUMMER_COOLNESS;
  });

  /**
   * 判断是否为票券活动
   */
  const isCouponActivity = computed(() => {
    return unref(activityType) === ActivityType.COUPON;
  });

  /**
   * 重置活动类型到默认值
   */
  const resetActivityType = () => {
    activityType.value = ActivityType.QUIZ;
  };

  return {
    // 状态
    activityType,
    computedActivityType,

    // 方法
    setActivityType,
    getCurrentActivityType,
    isActivityType,
    isOneOfActivityTypes,
    resetActivityType,
    changeActivityType,

    // 计算属性
    shouldShowPluginSelector,
    isBirthdayActivity,
    isLotteryActivity,
    isSurveyActivity,
    isVoteActivity,
    isInclusiveYJWDActivity,
    isSummerCoolnessActivity,
    isCouponActivity,
  };
}
