import { unref, nextTick } from 'vue';
import { ActivityType, BigActivityType } from '../../activities.d';
import { filter, cloneDeep, union, remove } from 'lodash-es';
import type { DefaultOptionType } from 'ant-design-vue/es/select';

interface Recordable {
  [key: string]: any;
}

/**
 * 活动类型操作管理 Hook
 * 封装与活动类型相关的复杂操作逻辑
 */
export function useActivityTypeOperations() {
  /**
   * 初始化活动类型相关的状态和选项
   */
  const initializeActivityTypeData = (
    activityType: ActivityType,
    dictionary: any,
    bigActivityType: string
  ) => {
    // 生成其他活动选项
    const otherOptions = filter(
      cloneDeep(dictionary.getDictionaryOpt.get('activitiesAllType')),
      (v: Recordable) => {
        return v.value === ActivityType.LOTTERY;
        // 原逻辑保留注释供参考
        // if (activityType === ActivityType.SURVEY) {
        //   return v.value === ActivityType.LOTTERY
        // } else if (activityType === ActivityType.SIGNUP) {
        //   return v.value === ActivityType.SURVEY
        // } else {
        //   return v.value === ActivityType.SURVEY || v.value === ActivityType.LOTTERY
        // }
      }
    ) as DefaultOptionType[];

    return {
      otherOptions,
      shouldInitQuiz: bigActivityType === BigActivityType.SIGN_UP_AND_QUIZ,
    };
  };

  /**
   * 处理活动数据回填时的Tab显示逻辑
   */
  const processActivityTabsForUpdate = (
    activityType: ActivityType,
    dataRecord: Recordable,
    otherTabs: any,
    allActiveKey: any
  ) => {
    const {
      vieAnswerInfo: vieAnswer,
      signUpInfo: registration,
      questionnaireInfo: questionnaire,
      voteInfo: vote,
      luckDrawInfo: luckDraw,
      walkingInfo,
      couponExtend,
    } = dataRecord;

    // 确定是否展开tabs
    const shouldExpandTabs =
      vieAnswer || registration || questionnaire || vote || luckDraw || walkingInfo || couponExtend;
    const ifQuizValue = shouldExpandTabs ? '1' : '2';

    // 处理其他Tab的逻辑
    if (ifQuizValue === '1' && ![ActivityType.LOTTERY].includes(activityType)) {
      const newOtherTabs: ActivityType[] = [];

      if (!!questionnaire && activityType !== ActivityType.SURVEY) {
        newOtherTabs.push(ActivityType.SURVEY);
      }
      if (!!luckDraw) {
        newOtherTabs.push(ActivityType.LOTTERY);
      }

      otherTabs.value = newOtherTabs;
    }

    // 更新所有活动键
    allActiveKey.value = union(unref(allActiveKey), unref(otherTabs));
    allActiveKey.value =
      ifQuizValue === '1' ? [...unref(allActiveKey), activityType] : unref(allActiveKey);

    return {
      ifQuizValue,
      shouldExpandTabs,
    };
  };

  /**
   * 处理其他活动参数的生成和重置
   */
  const handleOtherActivityParams = async (
    activityType: ActivityType,
    allActiveKey: any,
    activityId: any,
    luckDrawInfo: any,
    questionnaireInfo: any,
    validators: {
      validateLottery: () => Promise<any>;
      validateSurvey: () => Promise<any>;
    },
    resetters: {
      resetLottery: () => Promise<void>;
      resetSurvey: () => Promise<void>;
      setTableDataPrize: (data: any) => void;
      setValuesSurvey: (data: any) => void;
    },
    getDataSourcePrize: () => Promise<any>,
    isReset = false
  ) => {
    let params = {};

    // 处理抽奖活动参数
    if (
      unref(allActiveKey).includes(ActivityType.LOTTERY) &&
      activityType !== ActivityType.LOTTERY &&
      activityType !== ActivityType.BIRTHDAY
    ) {
      if (isReset) {
        await resetters.resetLottery();
        await nextTick();
        resetters.setTableDataPrize([{} as any]);
      } else {
        const dataSource = await getDataSourcePrize();
        params = {
          luckDraw: 'Y',
          luckDrawInfo: {
            ...unref(luckDrawInfo),
            ...(await validators.validateLottery()),
            prizeInfos: dataSource,
            activityId: unref(activityId),
          },
        };
      }
    }

    // 处理调查活动参数
    if (unref(allActiveKey).includes(ActivityType.SURVEY) && activityType !== ActivityType.SURVEY) {
      if (isReset) {
        await resetters.resetSurvey();
        await resetters.setValuesSurvey({
          topicInfoList: [
            { ifShow: true, optionType: 'radio', options: [{}, { correct: true }] } as any,
          ],
        });
      } else {
        params = {
          ...params,
          investigation: 'Y',
          questionnaireInfo: {
            ...unref(questionnaireInfo),
            ...(await validators.validateSurvey()),
            activityId: unref(activityId),
          },
        };
      }
    }

    return params;
  };

  /**
   * 处理ifQuiz状态变化时的逻辑
   */
  const handleIfQuizChange = (
    newValue: string,
    activityType: ActivityType,
    ifQuiz: any,
    allActiveKey: any
  ) => {
    const prevIfQuiz = unref(ifQuiz);
    ifQuiz.value = newValue;

    // 更新活动键状态
    if (newValue === '1') {
      allActiveKey.value = union(unref(allActiveKey), [activityType]);
    } else {
      remove(allActiveKey.value, v => v === activityType);
    }

    return {
      prevIfQuiz,
      shouldResetSettings: prevIfQuiz === '2' && newValue === '1',
    };
  };

  /**
   * 处理其他活动Tab的添加/移除
   */
  const handleOtherActivityTabs = async (
    newTabs: ActivityType[],
    otherTabs: any,
    allActiveKey: any,
    isUpdate: boolean,
    disabled: boolean,
    activityData: {
      luckDrawInfo: any;
      questionnaireInfo: any;
    },
    tabSetters: {
      setPropsLottery: (props: any) => Promise<void>;
      setPropsSurvey: (props: any) => Promise<void>;
      setValuesPrize: (data: any) => Promise<void>;
      setValuesSurvey: (data: any) => Promise<void>;
      setTableDataPrize: (data: any) => Promise<void>;
    }
  ) => {
    const prevOtherTabs = [...unref(otherTabs)];

    // 更新Tab状态
    otherTabs.value = newTabs;
    remove(allActiveKey.value, v => v === ActivityType.LOTTERY || v === ActivityType.SURVEY);
    allActiveKey.value = union(unref(allActiveKey), newTabs);

    // 如果是更新模式且添加了新Tab，需要设置对应数据
    if (unref(isUpdate)) {
      const addedTabs = newTabs.filter(tab => !prevOtherTabs.includes(tab));

      for (const tab of addedTabs) {
        await nextTick();
        try {
          if (tab === ActivityType.LOTTERY) {
            await tabSetters.setPropsLottery({ disabled: unref(disabled) });
            await nextTick();
            await tabSetters.setValuesPrize(unref(activityData.luckDrawInfo));
            await tabSetters.setTableDataPrize(
              unref(activityData.luckDrawInfo)?.prizeInfos || [{}]
            );
          } else if (tab === ActivityType.SURVEY) {
            await tabSetters.setPropsSurvey({ disabled: unref(disabled) });
            await tabSetters.setValuesSurvey(unref(activityData.questionnaireInfo));
          }
        } catch (error) {
          console.warn(`Error setting data for tab ${tab}:`, error);
        }
      }
    }

    return {
      prevOtherTabs,
      addedTabs: newTabs.filter(tab => !prevOtherTabs.includes(tab)),
      removedTabs: prevOtherTabs.filter(tab => !newTabs.includes(tab)),
    };
  };

  /**
   * 检查活动类型是否支持特定功能
   */
  const checkActivityTypeSupport = (activityType: ActivityType) => {
    return {
      supportsLottery: ![ActivityType.LOTTERY, ActivityType.BIRTHDAY].includes(activityType),
      supportsSurvey: activityType !== ActivityType.SURVEY,
      supportsPlugins: [
        ActivityType.QUIZ,
        ActivityType.SIGNUP,
        ActivityType.SURVEY,
        ActivityType.WALK,
      ].includes(activityType),
      supportsOtherTabs: ![ActivityType.LOTTERY].includes(activityType),
    };
  };

  /**
   * 根据活动类型获取默认配置
   */
  const getActivityTypeDefaults = (activityType: ActivityType) => {
    const baseDefaults = {
      publishPort: '30',
      customerType: null,
    };

    // 特殊类型的配置
    if (activityType === ActivityType.BIRTHDAY) {
      const birthdayDefaults = {
        publishPort: '30',
        customerType: '2',
      };
      return birthdayDefaults;
    }

    return baseDefaults;
  };

  return {
    initializeActivityTypeData,
    processActivityTabsForUpdate,
    handleOtherActivityParams,
    handleIfQuizChange,
    handleOtherActivityTabs,
    checkActivityTypeSupport,
    getActivityTypeDefaults,
  };
}
