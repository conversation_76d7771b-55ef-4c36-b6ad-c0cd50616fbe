import { ActivityType } from '../../activities.d';

/**
 * 通用工具 Hook - 提供简化的通用处理函数
 */
export function useCommonUtils() {
  /**
   * 简化的通用处理器 - 用于处理各种数据操作
   */
  const handleData = {
    // 通用数据处理
    process: (data: any, processor: (data: any) => any) => {
      try {
        return data ? processor(data) : {};
      } catch (error) {
        console.warn('Data processing error:', error);
        return {};
      }
    },

    // 通用数组处理
    array: (items: any[], processor: (item: any) => any) => {
      try {
        return Array.isArray(items) ? items.map(processor) : [];
      } catch (error) {
        console.warn('Array processing error:', error);
        return [];
      }
    },

    // 通用对象合并
    merge: (...objects: any[]) => {
      try {
        return Object.assign({}, ...objects);
      } catch (error) {
        console.warn('Object merge error:', error);
        return {};
      }
    },

    // 通用空值检查
    safe: (value: any, fallback: any = {}) => {
      return value != null ? value : fallback;
    },
  };

  /**
   * 简化的类型检查器
   */
  const typeCheck = {
    // 活动类型检查
    is: (type: ActivityType, target: ActivityType | ActivityType[]) => {
      return Array.isArray(target) ? target.includes(type) : type === target;
    },

    // 值存在检查
    exists: (value: any) => value != null && value !== '' && value !== undefined,

    // 数组有效检查
    validArray: (arr: any[]) => Array.isArray(arr) && arr.length > 0,
  };

  return {
    handleData,
    typeCheck,
  };
}
