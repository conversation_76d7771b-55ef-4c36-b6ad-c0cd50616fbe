import dayjs from 'dayjs';

/**
 * 数据处理器 Hook - 简化数据处理逻辑
 */
export function useDataProcessors() {
  // 简化的时间处理工具
  const timeUtils = {
    // 安全的时间数组创建
    safe: (startTime?: string, endTime?: string, baseDate?: string): any[] => {
      try {
        if (!startTime || !endTime) return [];
        const base = baseDate || dayjs().format('YYYY-MM-DD');
        const start = dayjs(`${base} ${startTime}`);
        const end = dayjs(`${base} ${endTime}`);
        return start.isValid() && end.isValid() ? [start, end] : [];
      } catch {
        return [];
      }
    },

    // 安全的日期数组创建
    dates: (startDate?: string, endDate?: string): string[] => {
      return startDate && endDate ? [startDate, endDate] : [];
    },
  };

  // 简化的数据处理器
  const processors = {
    // 处理健步走信息
    walking: (data: any) => {
      if (!data) return {};
      const { openingStartTime, openingEndTime, ...rest } = data;
      return { ...rest, dailyTime: timeUtils.safe(openingStartTime, openingEndTime) };
    },

    // 处理报名信息
    signup: (data: any) => {
      if (!data) return {};
      const { signUpStartTime, signUpEndTime, ...rest } = data;
      return { ...rest, signUpTime: timeUtils.dates(signUpStartTime, signUpEndTime) };
    },

    // 处理抽奖信息
    lottery: (data: any) => {
      if (!data) return {};
      const { receiveStartTime, receiveEndTime, ...rest } = data;
      const receiveStartEndDate = timeUtils.dates(receiveStartTime, receiveEndTime);
      return receiveStartEndDate.length ? { ...rest, receiveStartEndDate } : rest;
    },

    // 处理投票信息
    vote: (data: any) => {
      if (!data) return {};
      const { voteStartDate, voteEndDate, signStartDate, signEndDate, ...rest } = data;

      const result = { ...rest };
      const voteRange = timeUtils.dates(voteStartDate, voteEndDate);
      const signRange = timeUtils.dates(signStartDate, signEndDate);

      if (voteRange.length) result.voteStartEndDate = voteRange;
      if (signRange.length) result.signStartEndDate = signRange;

      return result;
    },
  };

  return {
    timeUtils,
    processors,
    // 导出简化的函数名
    safeCreateTimeArray: timeUtils.safe,
    safeDateArray: timeUtils.dates,
    processWalkingInfo: processors.walking,
    processSignUpInfo: processors.signup,
    processLotteryInfo: processors.lottery,
    processVoteInfo: processors.vote,
  };
}
