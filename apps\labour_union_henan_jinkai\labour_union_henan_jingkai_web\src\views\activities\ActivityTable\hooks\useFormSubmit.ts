import { inject, ref } from 'vue';
import { getParams } from '../utils';
import { sensitiveCheck } from '@/api/sensitive/check';
import { replaceHtml } from '@monorepo-yysz/utils';

// 简化的类型定义
type FormSubmitParams = Record<string, any>;

/**
 * 表单提交处理 Hook
 */
export function useFormSubmit() {
  const isSubmitting = ref(false);

  const bigActivityType = inject<string | undefined>('bigActivityType', undefined);

  /**
   * 处理表单提交 - 使用简化的配置对象
   */
  const handleFormSubmit = async (config: FormSubmitParams) => {
    try {
      isSubmitting.value = true;

      const submissionParams = await getParams({
        activityType: config.activityType,
        validate: config.validators.validate,
        validateQuiz: config.validators.validateQuiz,
        validateSignUp: config.validators.validateSignUp,
        validateLottery: config.validators.validateLottery,
        validateSurvey: config.validators.validateSurvey,
        validateVote: config.validators.validateVote,
        validateInclusiveYJWD: config.validators.validateInclusiveYJWD,
        validateInclusiveTicket: config.validators.validateInclusiveTicket,
        validateWalk: config.validators.validateWalk,
        getDataSourcePrize: config.dataSources.getDataSourcePrize,
        getDataSourceWalkPrize: config.dataSources.getDataSourceWalkPrize,
        otherParams: config.otherParams,
        vieAnswerInfo: config.activityData.vieAnswerInfo,
        signUpInfo: config.activityData.signUpInfo,
        luckDrawInfo: config.activityData.luckDrawInfo,
        questionnaireInfo: config.activityData.questionnaireInfo,
        voteInfo: config.activityData.voteInfo,
        coupon: config.activityData.coupon,
        record: config.activityData.record,
        isUpdate: config.activityData.isUpdate,
        autoId: config.activityData.autoId,
        activityId: config.activityData.activityId,
        ifQuiz: config.activityData.ifQuiz,
        educationAidInfo: config.activityData.educationAidInfo,
        walkInfo: config.activityData.walkInfo,
        bigActivityType,
      });

      return submissionParams;
    } finally {
      isSubmitting.value = false;
    }
  };

  /**
   * 执行敏感词检查
   */
  const performSensitiveCheck = async (params: any) => {
    const {
      activityContent,
      activityRules,
      participationMode,
      activityRemark,
      appCover,
      appDetailsCover,
      pcCover,
      pcDetailsCover,
    } = params;

    const content = replaceHtml(
      [activityContent, activityRules, participationMode, activityRemark]
        ?.filter(t => t)
        .join(',') || ''
    );

    const imageUrls = [appCover, appDetailsCover, pcCover, pcDetailsCover]?.filter(t => t) || [];
    const { code, data } = await sensitiveCheck({ code: 'activity', content, imageUrls });

    return {
      needsReview: code === 200 && data?.matchAny,
      sensitiveData: data,
    };
  };

  return {
    isSubmitting,
    handleFormSubmit,
    performSensitiveCheck,
  };
}
