import { ref, unref, nextTick } from 'vue';
import { useModalInner } from '@/components/Modal';
import { useDictionary } from '@/store/modules/dictionary';
import { cloneDeep, filter } from 'lodash-es';
import { ActivityType } from '../../activities.d';
import { DefaultOptionType } from 'ant-design-vue/lib/select';
import dayjs from 'dayjs';

/**
 * 模态框管理 Hook
 */
export function useModalManagement() {
  const dictionary = useDictionary();

  /**
   * 注册模态框 - 使用简化的参数类型
   */
  const useActivityModal = (
    dataHandlers: Record<string, any>,
    dataProcessors: Record<string, any>,
    stateRefs: Record<string, any>,
    setFromSetting: (key: any) => Promise<void>
  ) => {
    const otherOptions = ref<DefaultOptionType[]>([]);

    const [registerModal, { setModalProps }] = useModalInner(async data => {
      try {
        // 重置部分值
        await dataHandlers.reset();

        stateRefs.addTitle.value = data.addTitle;
        stateRefs.otherTabs.value = [];
        stateRefs.ifQuiz.value = '';
        stateRefs.autoId.value = undefined;
        stateRefs.activityId.value = '';
        stateRefs.ifFilter.value = true;

        // 重置各活动数据
        stateRefs.vieAnswerInfo.value = {};
        stateRefs.signUpInfo.value = {};
        stateRefs.questionnaireInfo.value = {};
        stateRefs.voteInfo.value = {};
        stateRefs.luckDrawInfo.value = {};
        stateRefs.coupon.value = {};
        stateRefs.educationAidInfo.value = {};
        stateRefs.walkInfo.value = {};

        // 设置其他选项
        otherOptions.value = filter(
          cloneDeep(dictionary.getDictionaryOpt.get('activitiesAllType')),
          (v: any) => v.value === ActivityType.LOTTERY
        ) as DefaultOptionType[];

        stateRefs.disabled.value = !!data?.disabled;
        stateRefs.isUpdate.value = !!data?.isUpdate;
        stateRefs.record.value = {};

        setModalProps({
          confirmLoading: false,
          showOkBtn: !data.disabled,
        });

        if (unref(stateRefs.isUpdate)) {
          await handleUpdateMode(data, dataProcessors, stateRefs, dataHandlers, setFromSetting);
        } else {
          await handleCreateMode(dataHandlers);
        }

        dataHandlers.setProps({
          activityType: data.activityType,
          disabled: unref(stateRefs.disabled),
        });
      } catch (error) {
        console.error('Error in modal initialization:', error);
        dataHandlers.setProps({
          activityType: data.activityType,
          disabled: unref(stateRefs.disabled),
        });
      }
    });

    return {
      registerModal,
      setModalProps,
      otherOptions,
    };
  };

  /**
   * 处理更新模式 - 使用简化的参数类型
   */
  const handleUpdateMode = async (
    data: any,
    dataProcessors: Record<string, any>,
    stateRefs: Record<string, any>,
    dataHandlers: Record<string, any>,
    setFromSetting: (key: any) => Promise<void>
  ) => {
    // 注册原始值
    stateRefs.record.value = unref(data.record);
    const dataRecord = unref(data.record);

    const {
      activityId: id,
      autoId: aId,
      vieAnswerInfo: vieAnswer,
      signUpInfo: registration,
      questionnaireInfo: questionnaire,
      voteInfo: vote,
      luckDrawInfo: luckDraw,
      walkingInfo,
      areaCode,
      openingStartTime,
      openingEndTime,
      activityStartTime,
      activityEndTime,
      educationAidInfo: education,
      couponExtend,
    } = dataRecord;

    // 展开tabs
    stateRefs.ifQuiz.value =
      vieAnswer || registration || questionnaire || vote || luckDraw || walkingInfo || couponExtend
        ? '1'
        : '2';

    // 使用数据处理函数
    stateRefs.vieAnswerInfo.value = vieAnswer || {};
    stateRefs.signUpInfo.value = registration ? dataProcessors.processSignUpInfo(registration) : {};
    stateRefs.questionnaireInfo.value = questionnaire || {};
    stateRefs.voteInfo.value = vote ? dataProcessors.processVoteInfo(vote) : {};
    stateRefs.luckDrawInfo.value = luckDraw ? dataProcessors.processLotteryInfo(luckDraw) : {};
    stateRefs.coupon.value = couponExtend || {};
    stateRefs.walkInfo.value = walkingInfo ? dataProcessors.processWalkingInfo(walkingInfo) : {};
    stateRefs.educationAidInfo.value = education || {};

    // 设置其他标签页
    if (unref(stateRefs.ifQuiz) === '1' && ![ActivityType.LOTTERY].includes(data.activityType)) {
      if (!!questionnaire && data.activityType !== ActivityType.SURVEY) {
        stateRefs.otherTabs.value = [ActivityType.SURVEY];
      }
      if (!!luckDraw) {
        stateRefs.otherTabs.value = [...unref(stateRefs.otherTabs), ActivityType.LOTTERY];
      }
    }

    stateRefs.allActiveKey.value = [
      ...new Set([...unref(stateRefs.allActiveKey), ...unref(stateRefs.otherTabs)]),
    ];
    stateRefs.allActiveKey.value =
      unref(stateRefs.ifQuiz) === '1'
        ? [...unref(stateRefs.allActiveKey), data.activityType]
        : unref(stateRefs.allActiveKey);

    // 反填时间
    const dailyTime = dataProcessors.safeCreateTimeArray(openingStartTime, openingEndTime);

    await dataHandlers.setValues({
      ...unref(data.record),
      ifQuiz: unref(stateRefs.ifQuiz),
      areaCode: areaCode ? areaCode?.split(',') : undefined,
      dailyTime: dailyTime as any,
      startEndDate: dataProcessors.safeDateArray(activityStartTime, activityEndTime),
      filePath: unref(stateRefs.educationAidInfo)
        ? [unref(stateRefs.educationAidInfo).filePath]
        : undefined,
      expiredDateTime: unref(stateRefs.educationAidInfo)
        ? unref(stateRefs.educationAidInfo).expiredDateTime
        : dayjs().format('YYYY-MM-DD HH:mm:ss'),
    });

    stateRefs.activityId.value = id;
    stateRefs.autoId.value = aId;

    if (!unref(stateRefs.educationAidInfo) && unref(stateRefs.ifQuiz) === '1') {
      await nextTick();
      await setFromSetting(data.activityType);
    }
  };

  /**
   * 处理创建模式
   */
  const handleCreateMode = async (dataHandlers: any) => {
    await dataHandlers.setValues({
      publishPort: '30',
      customerType: '2',
    });
  };

  return {
    useActivityModal,
  };
}
