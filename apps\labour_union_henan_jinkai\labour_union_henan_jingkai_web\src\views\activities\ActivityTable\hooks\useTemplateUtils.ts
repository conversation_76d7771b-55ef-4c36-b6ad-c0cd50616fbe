import { ActivityType, ActivitySettingZh } from '../../activities.d';

/**
 * 模板辅助工具 Hook
 * 用于简化模板中的条件判断逻辑
 */
export function useTemplateUtils() {
  /**
   * 检查是否显示特定类型的配置标签页
   */
  const createTabVisibilityChecker = () => {
    // 报名类活动类型
    const SIGNUP_TYPES = [
      ActivityType.SIGNUP,
      ActivityType.INTEREST_GROUP,
      ActivityType.COMPETITION,
      ActivityType.FUN_COMPETITION,
      ActivityType.FRIENDSHIP,
      ActivityType.VOLUNTEER_SERVICE,
      ActivityType.WOMEN,
    ];

    // 抽奖生日类活动类型
    const LOTTERY_BIRTHDAY_TYPES = [ActivityType.LOTTERY, ActivityType.BIRTHDAY];

    // 投票类活动类型
    const VOTE_TYPES = [ActivityType.VOTE, ActivityType.MULTIPLE_VOTE];

    // 票券类活动类型
    const COUPON_TYPES = [ActivityType.COUPON, ActivityType.SUMMER_COOLNESS];

    // 支持插件的活动类型
    const PLUGIN_SUPPORTED_TYPES = [
      ActivityType.QUIZ,
      ActivityType.SIGNUP,
      ActivityType.SURVEY,
      ActivityType.WALK,
    ];

    return {
      // 检查是否显示竞答配置
      isQuizTabVisible: (ifQuiz: string, activityType: ActivityType) =>
        ifQuiz === '1' && activityType === ActivityType.QUIZ,

      // 检查是否显示报名配置
      isSignupTabVisible: (ifQuiz: string, activityType: ActivityType) =>
        ifQuiz === '1' && SIGNUP_TYPES.includes(activityType),

      // 检查是否显示健步走配置
      isWalkTabVisible: (ifQuiz: string, activityType: ActivityType) =>
        ifQuiz === '1' && activityType === ActivityType.WALK,

      // 检查是否显示抽奖配置
      isLotteryTabVisible: (
        ifQuiz: string,
        activityType: ActivityType,
        otherTabs: ActivityType[]
      ) =>
        (ifQuiz === '1' && LOTTERY_BIRTHDAY_TYPES.includes(activityType)) ||
        otherTabs.includes(ActivityType.LOTTERY),

      // 检查是否显示调查配置
      isSurveyTabVisible: (ifQuiz: string, activityType: ActivityType, otherTabs: ActivityType[]) =>
        (ifQuiz === '1' && activityType === ActivityType.SURVEY) ||
        otherTabs.includes(ActivityType.SURVEY),

      // 检查是否显示投票配置
      isVoteTabVisible: (ifQuiz: string, activityType: ActivityType) =>
        ifQuiz === '1' && VOTE_TYPES.includes(activityType),

      // 检查是否显示普惠一键问答配置
      isInclusiveTabVisible: (ifQuiz: string, activityType: ActivityType) =>
        ifQuiz === '1' && activityType === ActivityType.INCLUSIVE_YJWD,

      // 检查是否显示票券配置
      isCouponTabVisible: (ifQuiz: string, activityType: ActivityType) =>
        ifQuiz === '1' && COUPON_TYPES.includes(activityType),

      // 检查是否显示插件库选择器
      isPluginSelectorVisible: (activityType: ActivityType, disabled: boolean) =>
        PLUGIN_SUPPORTED_TYPES.includes(activityType) && !disabled,

      // 获取抽奖配置标签标题
      getLotteryTabTitle: (activityType: ActivityType) => {
        const targetType =
          activityType === ActivityType.BIRTHDAY ? ActivityType.BIRTHDAY : ActivityType.LOTTERY;
        return `${ActivitySettingZh[targetType]}`;
      },

      // 获取活动类型对应的主类型（用于抽奖配置）
      getLotteryActivityType: (activityType: ActivityType) =>
        activityType === ActivityType.BIRTHDAY ? ActivityType.BIRTHDAY : ActivityType.LOTTERY,
    };
  };

  return {
    createTabVisibilityChecker,
  };
}
