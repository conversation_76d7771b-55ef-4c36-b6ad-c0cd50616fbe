<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleCreate"
          :auth="titleAuth"
        >
          {{ `新增${addTitle}` }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record, false),
                auth: auth.view,
              },
              {
                icon: 'ic:baseline-remove-red-eye',
                label: '预览',
                type: 'default',
                ifShow: false,
                onClick: handleView.bind(null, record, true),
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                ifShow: fixCutOut(record) || record.auditState === 'refuse',
                disabled: record.state === 'publish',
                onClick: handleModify.bind(null, record),
                auth: auth.modify,
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                ifShow: showAudit(record),
                onClick: handleAudit.bind(null, record),
                auth: auth.audit,
              },
              {
                icon: 'tabler:number',
                label: '期数设置',
                type: 'primary',
                ifShow: littleType === ActivityType.WALK,
                onClick: handleNumber.bind(null, record),
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '奖品审核',
                type: 'primary',
                ifShow: littleType === ActivityType.WALK,
                onClick: prizeHandleAudit.bind(null, record),
              },
              {
                icon: 'carbon:cut-out',
                label: handleChangeState(record),
                type: 'primary',
                ifShow: fixCutOut(record),
                onClick: pushOrCutdown.bind(null, record),
                auth: auth.pushOrCut,
              },
              {
                icon: 'carbon:align-vertical-top',
                label: '置顶',
                type: 'primary',
                ifShow: fixCutOut(record),
                onClick: handleTop.bind(null, record),
                auth: auth.top,
              },
              {
                icon: 'material-symbols:summarize-outline-rounded',
                label: '统计',
                type: 'primary',
                ifShow: fixCutOut(record),
                onClick: handleSum.bind(null, record),
                auth: auth.sum,
              },
              {
                icon: 'fluent:vote-24-filled',
                label: '作品管理',
                type: 'primary',
                ifShow:
                  fixCutOut(record) &&
                  [ActivityType.VOTE, ActivityType.MULTIPLE_VOTE].includes(record.activityMode),
                onClick: handleOpuses.bind(null, record),
                auth: auth.opus,
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '评论',
                type: 'primary',
                ifShow:
                  fixCutOut(record) &&
                  record.commentState === 'Y' &&
                  record.activityMode !== 'interestGroup',
                onClick: handleComments.bind(null, record),
                auth: auth.comments,
              },
              {
                icon: 'ic:baseline-qrcode',
                label: '签到二维码',
                type: 'primary',
                onClick: handleDownQr.bind(null, record),
                ifShow: littleType === ActivityType.VOLUNTEER_SERVICE,
                auth: auth.qrcode,
              },
              {
                icon: 'vaadin:archives',
                label: '归档',
                type: 'primary',
                ifShow: record.archivesState !== 'Y' && record.auditState === 'pass',
                disabled:
                  record.archivesState !== 'N' ||
                  record.progress !== '3' ||
                  record.state !== 'publish',
                onClick: handleArchives.bind(null, record),
                auth: auth.archives,
              },
              {
                icon: 'mdi:report-timeline',
                label: '归档报告',
                type: 'default',
                ifShow: record.archivesState === 'Y',
                onClick: handleReport.bind(null, record),
                auth: auth.archives,
              },
              {
                icon: 'fluent:delete-12-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                ifShow: fixCutOut(record),
                disabled: record.state !== 'draft',
                onClick: handleDelete.bind(null, record),
                auth: auth.delete,
              },
              {
                icon: 'material-symbols:link',
                label: '活动链接',
                type: 'primary',
                ifShow: false, //record.activityMode === ActivityType.SIGNUP && record.externalLink === 'N',
                onClick: handleLink.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ActivityModal
      @register="registerModal"
      width="88%"
      :canFullscreen="false"
      @success="handleSuccess"
    />
    <TopModal
      @register="registerTop"
      :can-fullscreen="false"
      width="40%"
      @success="handleTopSuccess"
      :title="topTitle"
    />
    <CommentModal
      @register="registerComment"
      width="88%"
      :can-fullscreen="false"
    />
    <AuditModal
      @register="registerAudit"
      :can-fullscreen="false"
      width="40%"
      :title="auditTitle"
      @success="handleAuditSuccess"
    />
    <ActivityArchiveModal
      @register="registerArchivesModal"
      width="50%"
      @success="handleArchivesSuccess"
    />
    <PrizeAudit
      @register="registerPrize"
      width="65%"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, h, ref, unref, useAttrs } from 'vue';
import { BasicTable, TableAction, useTable } from '@/components/Table';
import { ActivityType, ActivityTypeZh, ActivityView, BigActivityType } from '../activities.d';
import PrizeAudit from '@/views/activities/PrizeAudit/PrizeAudit.vue';
import { searchFormSchema, tableColumns } from '../activity';
import { useModal } from '@/components/Modal';
import ActivityModal from './ActivityModal.vue';
import CommentModal from '@/views/activities/comment/CommentModal.vue';
import {
  activityAudit,
  archivesAdd,
  deleteActivity,
  downQrCode,
  getDetails,
  list,
  pushDown,
  saveOrUpdate,
  top,
} from '@/api/activities';
import { useDictionary } from '@/store/modules/dictionary';
import { isArray, map } from 'lodash-es';
import TopModal from '@/views/news/channelNews/TopModal.vue';
import { useRoute, useRouter } from 'vue-router';
import { useMessage, useCopyToClipboard } from '@monorepo-yysz/hooks';
import { downloadByBase64 } from '@monorepo-yysz/utils';
import AuditModal from './AuditModal.vue';
import { fixCutOut, handleChangeState } from './utils';
import ActivityArchiveModal from '@/views/activities/ActivityTable/ActivityArchiveModal.vue';
import dayjs from 'dayjs';
import { useUserStore } from '@/store/modules/user';

interface RecordAuth {
  //编辑
  modify: string;
  //发布
  pushOrCut?: string;
  //统计
  sum?: string;
  //删除
  delete: string;
  //审核
  audit?: string;
  //期数
  number?: string;
  //详情
  view: string;

  join?: string;
  //置顶
  top?: string;
  //二维码
  qrcode?: string;
  //作品管理
  opus?: string;
  //评论
  comments?: string;
  //归档
  archives?: string;
}

const props = defineProps({
  activityType: {
    type: String as PropType<ActivityType>,
    default: ActivityType.QUIZ,
  },
  recordAuth: {
    type: Object as PropType<RecordAuth>,
    default: () => {},
  },
});

const route = useRoute();

const attrs = useAttrs();

const dictionary = useDictionary();

const router = useRouter();

const { createConfirm, createWarningModal, createSuccessModal, createErrorModal, createMessage } =
  useMessage();

const { clipboardRef, isSuccessRef } = useCopyToClipboard();

const topTitle = ref<string>();
const auditTitle = ref<string>();

const routerActivityType = computed(() => {
  return route.query.activityType;
});

const routerActivityMode = computed(() => {
  return route.query.activityMode;
});
const littleType = computed(() => props.activityType);

const addTitle = computed(() => {
  return `${ActivityTypeZh[props.activityType]}活动`;
});

//权限
const titleAuth = computed(() => {
  if (isArray(attrs?.titleAuth)) return attrs?.titleAuth as string[];
  return (attrs?.titleAuth || '') as string;
});

const columnAuth = computed(() => {
  return attrs.columnAuth as string[];
});

const auth = computed(() => {
  return props.recordAuth;
});

const column = computed(() => {
  return tableColumns(unref(littleType));
});

const [registerModal, { openModal, closeModal }] = useModal();

//join
// const [registerJoin, { openModal: openJoin }] = useModal();

//置顶
const [registerTop, { openModal: openTop, closeModal: closeTop }] = useModal();

const [registerArchivesModal, { openModal: openArchives, closeModal: closeArchives }] = useModal();

//评论
const [registerComment, { openModal: openComment }] = useModal();

// 奖品审核
const [registerPrize, { openModal: openPrize }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: column,
  authInfo: unref(titleAuth),
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema(),
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    actionColOptions: {
      span: 3,
    },
  },
  beforeFetch: params => {
    const { startEndDate, ...val } = params;
    if (startEndDate && startEndDate.length > 0) {
      val['firstTime'] = startEndDate[0] ? startEndDate[0] + ' 00:00:00' : undefined;
      val['lastTime'] = startEndDate[1] ? startEndDate[1] + ' 23:59:59' : undefined;
    }
    unref(routerActivityType) && (val.activityType = unref(routerActivityType));
    return val;
  },
  searchInfo: {
    activityMode: routerActivityMode.value ? routerActivityMode.value : props.activityType,
    activityCategory: BigActivityType[props.activityType],
  },
  useSearchForm: unref(routerActivityType) ? false : true,
  showTableSetting: false,
  bordered: true,
  api: list,
  showIndexColumn: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    fixed: undefined,
    auth: unref(columnAuth),
    width: 400,
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
  },
});

//审核
const [registerAudit, { openModal: openAudit, closeModal: closeAudit }] = useModal();

//新增
async function handleCreate() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
    activityType: props.activityType,
    addTitle: unref(addTitle),
  });
}

//详情
function handleView(record: Recordable, type: boolean) {
  if (type) {
    if (record.externalLink === 'Y') {
      createConfirm({
        content: `请确定跳转外链活动${record.activityName}`,
        iconType: 'warning',
        onOk: function () {
          if (
            (record.externalLinkUrl && record.externalLinkUrl.includes('https://')) ||
            record.externalLinkUrl.includes('http://')
          ) {
            window.open(record.externalLinkUrl, '_blank');
          } else {
            createWarningModal({
              content: '此链接不是一个正确的链接，请使用https://或者http://开头',
            });
          }
        },
      });
      return false;
    }

    router.push({
      path: `/act/${ActivityView[props.activityType]}`,
      query: {
        activityId: record.activityId,
        activityName: record.activityName,
        activityType: props.activityType,
      },
    });
  } else {
    getDetails({ activityId: record.activityId }).then(res => {
      if (res.code === 200) {
        openModal(true, {
          isUpdate: true,
          record: res.data,
          activityType: props.activityType,
          disabled: true,
        });
      }
    });
  }
}

//编辑
function handleModify(record: Recordable) {
  getDetails({ activityId: record.activityId }).then(res => {
    if (res.code === 200) {
      openModal(true, {
        isUpdate: true,
        record: res.data,
        activityType: props.activityType,
        disabled: false,
      });
    }
  });
}

//置顶
function handleTop(record) {
  topTitle.value = `置顶${record.activityName}`;
  openTop(true, { record, autoId: record.activityId });
}

//下架发布
function pushOrCutdown(record: Recordable) {
  const port = ['端口'];
  if (record.publishPort) {
    const text = map(
      record.publishPort?.split(','),
      v => dictionary.getDictionaryMap.get(`appType_${v}`)?.dictName || ''
    ).join(',');
    port.push(text);
  }
  createConfirm({
    content: h('div', [
      `确定${handleChangeState(record)}${record.activityName}`,
      h('div', `${port.length > 1 ? port.join(': ') : ''}`),
    ]),
    async onOk() {
      try {
        let state = 'draft';
        if (record.state === 'draft') {
          state = 'publish';
        } else if (record.state === 'publish') {
          state = 'unpublish';
        } else if (record.state === 'unpublish') {
          state = 'publish';
        }

        return await new Promise<void>(resolve => {
          pushDown({
            activityId: record.activityId,
            activityMode: record.activityMode,
            activityCategory: record.activityCategory,
            state: state,
          }).then(res => {
            const { code, message } = res;

            if (code === 200) {
              createSuccessModal({
                content: `${handleChangeState(record)}成功！`,
              });

              reload();
            } else {
              createErrorModal({
                content: `${handleChangeState(record)}失败！${message} `,
              });
            }
            resolve();
          });
        });
      } catch {
        return console.log(' errors!');
      }
    },
  });
}

// 活动链接
function handleLink(record) {
  switch (record.activityMode) {
    case ActivityType.SIGNUP:
      // clipboardRef.value = `https://yaapp.yasgh.org.cn/activity_detail?id=${record.activityId}&type=${record.activityMode}`;
      clipboardRef.value = record.activityId;
      if (unref(isSuccessRef)) {
        createMessage.success('复制成功');
      }

      break;
    default:
      // clipboardRef.value = `https://yaapp.yasgh.org.cn/activity_homepage?id=${record.activityId}&type=${record.activityMode}`
      break;
  }
}

//统计
function handleSum(record: Recordable) {
  const { groupIds, ...params } = record;
  router.push({
    path: `/actStatisic/actStatisic/${ActivityView[props.activityType]}`,
    query: {
      record: JSON.stringify(params),
    },
  });
}

function handleComments(record) {
  openComment(true, { record });
}

// 奖品审核
function prizeHandleAudit(record) {
  openPrize(true, { record });
}

function handleNumber({ activityId, activityName, openingStartTime, openingEndTime }: Recordable) {
  router.push({
    path: '/numberSetting',
    query: {
      activityId: activityId,
      activityName,
      openingEndTime,
      openingStartTime,
    },
  });
}

//提交
async function handleSuccess({ params }) {
  saveOrUpdate(params).then(async res => {
    const { code, message } = res;
    if (code === 200) {
      createSuccessModal({ content: '操作成功！' });
      closeModal();
      reload();
    } else {
      createErrorModal({
        content: `操作失败！${message} `,
      });
    }
  });
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.activityName}`,
    onOk: function () {
      deleteActivity(record).then(res => {
        if (res.code === 200) {
          createSuccessModal({ content: '删除成功' });
        } else {
          createErrorModal({ content: `删除失败，${res.message}` });
        }
        reload();
      });
    },
  });
}

//提交置顶
function handleTopSuccess({ values: { topOption: stickyOption, topEndTime:topTime }, autoId }) {
  top({ stickyOption, topTime, activityId: autoId }).then(res => {
    const { code, message: msg } = res;
    if (code === 200) {
      createSuccessModal({ content: '操作成功' });
      closeTop();
      reload();
    } else {
      createErrorModal({ content: `操作失败，${msg}` });
    }
  });
}

function handleDownQr(record) {
  downQrCode({ ...record }).then(({ code, data, message }) => {
    if (code === 200) {
      downloadByBase64(data as string, `${record.activityName}.png`);
    } else {
      createErrorModal({ content: `操作失败，${message}` });
    }
  });
}

//审核
function handleAudit(record) {
  auditTitle.value = `审核-${record.activityName}`;
  openAudit(true, { autoId: record.activityId });
}
//提交审核
function handleAuditSuccess({ values, autoId }) {
  activityAudit({ ...values, activityId: autoId }).then(res => {
    const { code, message: msg } = res;
    if (code === 200) {
      createSuccessModal({ content: '操作成功' });
      closeAudit();
      reload();
    } else {
      createErrorModal({ content: `操作失败，${msg}` });
    }
  });
}

function handleArchives(record: Recordable) {
  const type = record.activityCategory === 'inclusive' ? 'inclusive' : 'union';
  const {
    activityId,
    activityName,
    activityAddress,
    activityStartTime,
    activityEndTime,
    companyName,
  } = record;

  openArchives(true, {
    isUpdate: true,
    record: {
      activityId,
      activityName,
      activityAddress,
      companyName,
      activityDate: dayjs(activityStartTime).format('YYYY-MM-DD'),
      activityEndDate: dayjs(activityEndTime).format('YYYY-MM-DD'),
    },
    disabled: false,
    type,
  });
}

//编辑
function handleReport(record: Recordable) {
  const { activityId, activityName, activityStartTime, activityEndTime } = record;
  const url = router.resolve({
    path: '/activity-report',
    query: { activityId, activityName, activityStartTime, activityEndTime },
  }).href; // 根据路由名称生成 URL
  window.open(url, '_blank'); // 打开新窗口
}

function handleArchivesSuccess({ values }) {
  archivesAdd(values).then(res => {
    const { code, message } = res;
    if (code === 200) {
      createSuccessModal({ content: '操作成功' });
      closeArchives();
      reload();
    } else {
      createErrorModal({
        content: `操作失败！${message} `,
      });
    }
  });
}
//审核按钮
const showAudit = record => {
  if (record.auditState !== 'wait') {
    return false;
  }
  if (useUserStore().getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5') {
    return true;
  }
  if (record.activityMode === ActivityType.COUPON) {
    return false;
  }
  return record.integralFlag !== 'Y';
};
//作品管理
const handleOpuses = record => {
  router.push({
    path: '/opusesManage',
    query: {
      activityId: record.activityId,
      activityName: record.activityName,
    },
  });
};
</script>
