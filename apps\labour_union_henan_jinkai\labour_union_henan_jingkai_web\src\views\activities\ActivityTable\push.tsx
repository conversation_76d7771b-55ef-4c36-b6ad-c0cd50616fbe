import {  FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';

//
export const auditModalForm: FormSchema[] = [
  {
    field: 'auditState',
    label: '审核状态',
    component: 'RadioGroup',
    required: true,
    componentProps: {
      options: [
        {
          label: '审核通过',
          value: 'pass',
        },
        {
          label: '审核驳回',
          value: 'refuse',
        },
      ],
    },
  },
  {
    field: 'auditOpinion',
    label: '审核意见',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入审核意见',
      showCount: true,
      maxlength: 200,
    },
  },
];
