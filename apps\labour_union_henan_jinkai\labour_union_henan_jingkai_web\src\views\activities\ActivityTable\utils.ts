import { useUserStore } from '@/store/modules/user';
import dayjs from 'dayjs';
import { Ref, unref } from 'vue';
import { ActivityType, BigActivityType } from '../activities.d';
import { find, includes, map } from 'lodash-es';
import { Modal } from 'ant-design-vue';
import { useDictionary } from '@/store/modules/dictionary';

interface GetParamsOptions {
  activityType: Ref<string>;
  validate: Fn;
  validateQuiz: Fn;
  validateSignUp: Fn;
  validateLottery: Fn;
  validateSurvey: Fn;
  validateVote: Fn;
  validateInclusiveYJWD: Fn;
  validateInclusiveTicket: Fn;
  validateWalk: Fn;
  getDataSourcePrize: Fn;
  getDataSourceWalkPrize: Fn;
  otherParams: Fn;
  vieAnswerInfo: any;
  signUpInfo: any;
  luckDrawInfo: any;
  questionnaireInfo: any;
  voteInfo: any;
  coupon: any;
  record: any;
  isUpdate: Ref<boolean>;
  autoId: Ref<number | undefined>;
  activityId: Ref<string>;
  ifQuiz: Ref<string>;
  educationAidInfo: any;
  walkInfo: any;
  bigActivityType?: string;
}

export const getParams = async (options: GetParamsOptions): Promise<any> => {
  const user = useUserStore();
  const {
    activityType,
    validate,
    validateQuiz,
    validateSignUp,
    validateLottery,
    validateSurvey,
    validateVote,
    validateInclusiveYJWD,
    validateInclusiveTicket,
    validateWalk,
    getDataSourcePrize,
    getDataSourceWalkPrize,
    otherParams,
    vieAnswerInfo,
    signUpInfo,
    luckDrawInfo,
    questionnaireInfo,
    voteInfo,
    coupon,
    record,
    isUpdate,
    autoId,
    activityId,
    ifQuiz,
    educationAidInfo,
    walkInfo,
    bigActivityType
  } = options;
  const values = await validate();
  let params = {};

  if (unref(ifQuiz) === '1') {
    try {
      switch (unref(activityType)) {
        case ActivityType.QUIZ:
          params = {
            ...params,
            vieAnswerInfo: { ...unref(vieAnswerInfo), ...(await validateQuiz()) },
          };
          break;
        case ActivityType.INTEREST_GROUP:
        case ActivityType.BLUE_VEST:
        case ActivityType.SIGNUP:
        case ActivityType.COMPETITION:
        case ActivityType.FUN_COMPETITION:
        case ActivityType.VOLUNTEER_SERVICE:
        case ActivityType.FRIENDSHIP:
        case ActivityType.INCLUSIVE_SIGNUP:
        case ActivityType.WOMEN:
          const info = { ...unref(signUpInfo), ...(await validateSignUp()) };
          const { signUpTime, ...other } = info;

          params = {
            ...params,
            signUpInfo: {
              ...other,
              signUpStartTime: signUpTime[0],
              signUpEndTime: signUpTime[1],
            },
          };
          break;
        case ActivityType.BIRTHDAY:
        case ActivityType.LOTTERY:
          const dataSource = await getDataSourcePrize();
          const lottery = await validateLottery();

          params = {
            ...params,
            luckDrawInfo: {
              ...unref(luckDrawInfo),
              ...lottery,
              prizeInfos: dataSource,
            },
          };
          break;
        case ActivityType.SURVEY:
          params = {
            ...params,
            questionnaireInfo: { ...unref(questionnaireInfo), ...(await validateSurvey()) },
          };
          break;
        case ActivityType.MULTIPLE_VOTE:
        case ActivityType.VOTE:
          const { voteStartEndDate, signStartEndDate, ...voteValues } = await validateVote();

          if (voteStartEndDate?.length) {
            voteValues.voteStartDate = voteStartEndDate[0];
            voteValues.voteEndDate = voteStartEndDate[1];
          }

          if (signStartEndDate?.length) {
            voteValues.signStartDate = signStartEndDate[0];
            voteValues.signEndDate = signStartEndDate[1];
          }

          params = {
            ...params,
            voteInfo: { ...unref(voteInfo), ...voteValues },
          };
          break;
        case ActivityType.INCLUSIVE_YJWD:
          params = {
            ...params,
            vieAnswerInfo: { ...unref(vieAnswerInfo), ...(await validateInclusiveYJWD()) },
          };
          break;
        case ActivityType.WALK:
          const walkDataSource = await getDataSourceWalkPrize();
          const { dailyTime: daily, ...walkOther } = {
            ...unref(walkInfo),
            ...(await validateWalk()),
          };
          params = {
            ...params,
            walkingInfo: {
              ...walkOther,
              openingStartTime: dayjs(daily[0]).format('HH:mm:ss'),
              openingEndTime: dayjs(daily[1]).format('HH:mm:ss'),
              prizeInfos: walkDataSource,
            },
          };
          break;
        case ActivityType.SUMMER_COOLNESS:
        case ActivityType.COUPON:
          const couponExtend = { ...unref(coupon), ...(await validateInclusiveTicket()) };
          params = {
            ...params,
            couponExtend,
          };
      }
    } catch (error) {
      console.error(error);
      Modal.error({
        title: '提示',
        //@ts-ignore
        content: error?.errorFields?.[0]?.errors?.[0] || '完善表单必填信息',
      });
      throw new Error('获取参数失败');
    }
  } else {
    params = {
      ...params,
      luckDrawInfo: null,
      vieAnswerInfo: null,
      questionnaireInfo: null,
      voteInfo: null,
      signUpInfo: null,
      couponExtend: null,
      walkingInfo: null,
    };
  }
  //不用循环异步问题
  params = { ...params, ...(await otherParams()) };
  const { dailyTime, startEndDate, areaCode, filePath, expiredDateTime, areaType } = values;

  let ac = areaCode;

  await _validate(params, unref(activityType));

  if (filePath && expiredDateTime) {
    params = {
      ...params,
      educationAidInfo: {
        ...unref(educationAidInfo),
        filePath: filePath?.join(''),
        expiredDateTime,
      },
    };
  }
  //todo yiguanchu
  // if (areaType && areaType === '1') {
  //   const companyId = user.getUserInfo.companyId;
  //   ac = find(
  //     useDictionary().getDictionaryOBJMap.get('regionCode'),
  //     t => t.remark === companyId
  //   )?.dictName;
  //
  //   if (!ac) {
  //     Modal.error({
  //       title: '提示',
  //       content: '未获取到当前账号所属区域~',
  //     });
  //     return;
  //   }
  // } else if (areaType && areaType === '0') {
  //   ac = '南充市';
  // } else if (areaType && areaType === '3') {
  //   ac = '四川省';
  // } else {
  //   ac = areaCode ? areaCode?.join(',') : undefined;
  // }
  const sTime = dailyTime ? dayjs(dailyTime[0]).format('HH:mm:ss') : '00:00:00';

  const eTime = dailyTime ? dayjs(dailyTime[1]).format('HH:mm:ss') : '23:59:59';

  params = {
    ...unref(record),
    ...params,
    ...values,
    activityCategory: bigActivityType ||  BigActivityType[unref(activityType)],
    activityMode: unref(activityType),
    openingStartTime: sTime,
    openingEndTime: eTime,
    activityStartTime: startEndDate
      ? dayjs(startEndDate[0]).format(`YYYY-MM-DD ${sTime}`)
      : undefined,
    activityEndTime: startEndDate
      ? dayjs(startEndDate[1]).format(`YYYY-MM-DD ${eTime}`)
      : undefined,
    companyId: user.getUserInfo.companyId,
    isUpdate: unref(isUpdate),
    areaCode: ac,
    autoId: unref(autoId),
    activityId: unref(activityId),
  };

  return params;
};

async function _validate(params, activityMode) {
  try {
    const { vieAnswerInfo, signUpInfo, luckDrawInfo, questionnaireInfo, voteInfo, walkingInfo } =
      params;

    if (vieAnswerInfo) {
      const { topicInfoList: vieAnswer } = vieAnswerInfo;

      let index = 0;
      const item = find(vieAnswer, (v, k: number) => {
        index = k;
        return !v.topicContent;
      });
      if (item) {
        throw new Error(`题目${index + 1}，题目不能为空`);
      }
    }

    if (signUpInfo && signUpInfo.writeFlag === 'Y') {
      const { topicInfoList: registration } = signUpInfo;

      map(registration, (v, k: number) => {
        if (!v.topicContent) {
          throw new Error(`标题${k + 1}，标题不能为空`);
        }
        if (v.optionType === 'select' || v.optionType === 'radio') {
          if (!v.options || (v.options && v.options.length < 2)) {
            throw new Error(`标题${k + 1}至少有两个选项值`);
          }
        }
      });
    }

    if (questionnaireInfo) {
      const { topicInfoList: questionnaire } = questionnaireInfo;
      map(questionnaire, (v, k: number) => {
        if (!v.topicContent) {
          throw new Error(`题目${k + 1}， 题目不能为空`);
        }
      });
    }

    if (luckDrawInfo) {
      if (luckDrawInfo.receiveStartEndDate?.length) {
        luckDrawInfo.receiveStartTime = luckDrawInfo.receiveStartEndDate[0];
        luckDrawInfo.receiveEndTime = luckDrawInfo.receiveStartEndDate[1];
      }else {
        luckDrawInfo.receiveStartTime = null;
        luckDrawInfo.receiveEndTime = null
      }
      _validatePrize(luckDrawInfo,activityMode)
    }
    if (walkingInfo) {
      map(walkingInfo?.prizeInfos ?? [], (v, k) => {
        if (!v.prizeName) {
          throw new Error(`兑换品奖品${k + 1}名称不能为空`);
        }
        if (!v.receiveType) {
          throw new Error(`兑换品奖品${k + 1}类型不能为空`);
        }
        if (!v.prizeContent && v.receiveType!="7") {
          throw new Error(`兑换品奖品${k + 1}内容不能为空`);
        }
        if (!v.prizeEx) {
          throw new Error(`兑换品奖品${k + 1}单次兑换所需工币不能为空`);
        }
        if (!v.prizeCount) {
          throw new Error(`兑换品奖品${k + 1}数量不能为空`);
        }
      });
    }

    if(voteInfo){
      const {voteTypeConfigList,luckDrawInfos,awardPoolName} = voteInfo
      if(!voteTypeConfigList?.length){
        throw new Error('作品分类配置不能为空');
      }
      voteTypeConfigList.forEach((v,index)=>{
        const {opusType,userLimit,fileLimit,passLimit,fileType} = v
        if(!opusType){
          throw new Error(`作品分类配置${index+1}【分类名称】不能为空`);
        }
        if(!userLimit){
          throw new Error(`作品分类配置${index+1}【最大投稿次数】不能为空`);
        }
        if(!passLimit){
          throw new Error(`作品分类配置${index+1}【最大审核通过数】不能为空`);
        }
        if(!fileType){
          throw new Error(`作品分类配置${index+1}【附件类型】不能为空`);
        }
        if(!fileLimit && fileType.includes('img')){
          throw new Error(`作品分类配置${index+1}【上传图片数量上限】不能为空`);
        }
      })
      if(awardPoolName){
        luckDrawInfos.forEach(t=>_validatePrize(t,activityMode))
      }

    }

  } catch (error) {
    Modal.error({
      title: '提示',
      //@ts-ignore
      content: error.message || '参数错误',
    });
    throw error;
  }
}

function _validatePrize(luckDrawInfo,activityMode){
  const { prizeInfos,awardPoolName,receiveStartTime,receiveEndTime } = luckDrawInfo;
  if(!prizeInfos?.length){
    throw new Error(`奖品配置不能为空`);
  }
  const isRank = awardPoolName === 'award3'
  if(isRank && !receiveStartTime && !receiveEndTime){
    throw new Error(`排行奖励【领取日期】不能为空`);
  }


  map(prizeInfos, (v, k) => {

    if(isRank){
      if(!v.rankRange){
        throw new Error(`奖品${k + 1}名次不能为空`);
        //校验名次填写是否正确，规范格式例如 1、2~5、5~10
      }
      const rankPattern = /^(\d+)(~(\d+))?$/;
      if (isRank && v.rankRange && !rankPattern.test(v.rankRange)) {
        throw new Error(`奖品${k + 1}名次格式不正确，应为类似 1 或 2~10 的格式`);
      }
    }
    if (!v.prizeName) {
      throw new Error(`奖品${k + 1}名称不能为空`);
    }
    if (!v.prizeType) {
      throw new Error(`奖品${k + 1}类型不能为空`);
    }


    //谢谢参与、实物、票券
    if (!['1','3','7'].includes(v.prizeType) && !v.prizeContent) {
      throw new Error(`奖品${k + 1}内容不能为空`);
    }
    if(v.prizeType === '7' && !v.productId){
      throw new Error(`奖品${k + 1}票券配置不能为空`);
    }
    if (!v.dailyCount && !isRank) {
      throw new Error(`奖品${k + 1}每日发放数量不能为空`);
    }
    if (!v.prizeCount && !isRank) {
      throw new Error(`奖品${k + 1}数量不能为空`);
    }
    if (!v.percentage && v.percentage != 0 && !isRank) {
      throw new Error(`奖品${k + 1}认证会员中奖率不能为空`);
    }
    if (
        !v.registerPercentage &&
        v.registerPercentage != 0 &&
        !includes([ActivityType.BIRTHDAY, ActivityType.INTEGRAL_TREE], activityMode)
        && !isRank
    ) {
      throw new Error(`奖品${k + 1}注册用户中奖率不能为空，没有则填0`);
    }
  });

  if (isRank) {
    const rankRanges = prizeInfos.map(v => v.rankRange).filter(Boolean);
    const allRanks = new Set();

    for (const range of rankRanges) {
      const match = range.match(/^(\d+)(?:~(\d+))?$/);
      if (!match) continue;

      const start = parseInt(match[1]);
      const end = match[2] ? parseInt(match[2]) : start;

      if (start > end) {
        throw new Error(`名次范围【 ${range}】 的起始值不能大于结束值`);
      }

      for (let i = start; i <= end; i++) {
        if (allRanks.has(i)) {
          throw new Error(`名次【 ${i}】 在多个奖品中被重复设置`);
        }
        allRanks.add(i);
      }
    }
  }
}


//处理
export const fixCutOut = record => !record.auditState || record.auditState === 'pass';

//按钮发布下架
export function handleChangeState(record) {
  return record.state === 'draft' ? '发布' : record.state === 'publish' ? '下架' : '发布';
}
