<template>
  <BasicModal
    @register="registerModule"
    :title="title"
    :can-fullscreen="false"
    @ok="handleSuccess"
    :wrap-class-name="$style['prize-audit-modal']"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '@/components/Form'
import { luckymodalAuditFormItem } from './data'

const emit = defineEmits(['register', 'success'])

const name = ref('')

const activityInfoId = ref('')

const record = ref<Recordable>()

const businessIds = ref<string[]>([])

const title = computed(() => {
  return `${unref(name) ? `${unref(name)}--` : ''}${unref(type) === 'give' ? '奖品发放' : '开奖'}`
})

const form = computed(() => {
  return luckymodalAuditFormItem(unref(type))
})

const type = ref('')

const [registerModule, { setModalProps }] = useModalInner(async data => {
  await resetFields()
  businessIds.value = []

  type.value = data.type

  record.value = data.record

  name.value = data.record?.userName || ''

  activityInfoId.value = data.activityInfoId

  businessIds.value = data.businessIds

  setModalProps({
    confirmLoading: false,
  })
})

const [registerForm, { validate, resetFields }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
})

async function handleSuccess() {
  try {
    const values = await validate()
    emit('success', {
      values: {
        ...values,
        businessIds: unref(businessIds),
        activityInfoId: unref(activityInfoId),
      },
      type: unref(type),
    })
  } catch (error) {
    setModalProps({
      confirmLoading: true,
    })
  }
}
</script>

<style lang="less" module>
.prize-audit-modal {
  :global {
    .ant-modal-body {
      height: 20vh !important;
    }
  }
}
</style>
