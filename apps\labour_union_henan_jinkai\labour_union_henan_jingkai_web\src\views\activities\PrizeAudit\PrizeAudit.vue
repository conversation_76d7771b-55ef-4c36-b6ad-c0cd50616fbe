<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :canFullscreen="false"
    @cancel="handelCancel"
  >
    <!-- <Tabs v-model:activeKey="activeKey" @change="reloadTable" v-if="getVisible"> -->
    <Tabs v-model:activeKey="activeKey" @change="reloadTable" >
      <TabPane
        forceRender
        destroyInactiveTabPane
        v-for="item in defaultTabs"
        :tab="item.title"
        :key="item.key"
      />
    </Tabs>
    <BasicTable @register="registerTable" v-show="activeKey === 'basicInfo'">
      <template #form-userName>
        <Input v-model:value="search.userName" autocomplete="off" placeholder="请输入兑换人" />
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleAudit(null)">批量审核</a-button>
        <a-button type="primary" @click="exportData('1')">导出数据</a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleDetails.bind(null, record),
            },
            {
              icon: 'ant-design:audit-outlined',
              label: '审核',
              type: 'primary',
              ifShow: record.auditState === '-10',
              onClick: handleAudit.bind(null, record),
            },
            {
              icon: 'mdi:give-way',
              label: '发放',
              type: 'primary',
              disabled: !(record.assignState === 'N'),
              onClick: handleAudit.bind(null, record, 'give'),
              ifShow: record.auditState === '0' || record.auditState === '20',
            },
          ]"
        />
      </template>
    </BasicTable>
    <BasicTable @register="registerLuckTable" v-show="activeKey === 'luckDraw'">
      <template #form-userName>
        <Input v-model:value="search.userName" autocomplete="off" />
      </template>
      <template #toolbar>
        <a-button type="primary" @click="luckyAudit(null)">批量开奖</a-button>
        <a-button type="primary" @click="exportData('0')">导出数据</a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleDetails.bind(null, record),
            },
            {
              icon: 'ant-design:audit-outlined',
              label: '开奖',
              type: 'primary',
              ifShow: record.luckyState === '-1',
              onClick: luckyAudit.bind(null, record),
            },
            {
              icon: 'mdi:give-way',
              label: '发放',
              type: 'primary',
              disabled: !(record.assignState === 'N'),
              onClick: luckyAudit.bind(null, record, 'give'),
              ifShow: record.luckyState === '1',
            },
          ]"
        />
      </template>
    </BasicTable>
    <!-- <BasicTable @register="registerLuckRecordTable" v-show="activeKey === 'luckRecord'">
      <template #form-userName>
        <Input v-model:value="search.userName" autocomplete="off" />
      </template>
      <template #toolbar>
        <a-button type="primary" @click="exportLuckyRecordData()">导出数据</a-button>
      </template>
    </BasicTable> -->
    <PrizeAuditModal @register="registerAudit" width="40%" @success="handleAuditSuccess" />
    <LuckyAuditModal
      @register="luckyregisterAudit"
      width="40%"
      @success="luckyhandleAuditSuccess"
    />
    <PrizeView @register="registerView" width="50%" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue'
import { useModalInner, BasicModal, useModal } from '/@/components/Modal'
import { Tabs, Input } from 'ant-design-vue'
import { useTable, BasicTable, TableAction } from '/@/components/Table'
import { prizeFormItem, prizeColumns, luckRecordColumns, luckRecordFormItem } from './data'
import {
  findPrizeRecordList,
  auditPrize,
  disposePrize,
  lotteryDraw,
  exportPrizeRecord,
  findLuckRecordList,
  exportLuckRecord,
} from '/@/api/walking/prizeAudit'
import PrizeAuditModal from './PrizeAuditModal.vue'
import LuckyAuditModal from './LuckyAuditModal.vue'
import PrizeView from './PrizeView.vue'
import dayjs from 'dayjs'
import { downloadByUrl } from '@monorepo-yysz/utils'
import { map } from 'lodash-es'
import { useMessage } from '@monorepo-yysz/hooks'

const TabPane = Tabs.TabPane

const { createErrorModal, createSuccessModal, createWarningModal } = useMessage()

//默认的tab
const defaultTabs = ref<Recordable[]>([
  { title: '实物兑换', key: 'basicInfo', obj: 'info' },
  { title: '抽奖资格兑换', key: 'luckDraw', obj: 'luckDraw' },
  // { title: '抽奖记录', key: 'luckRecord', obj: 'luckRecord' },
])

const name = ref('')

const search = ref<Recordable>({})

const activityInfoId = ref('')

const activeKey = ref('basicInfo')

const title = computed(() => {
  return `${unref(name)}--兑换品审核`
})

const [registerModal, { getVisible }] = useModalInner(async data => {
  await clearSelectedRowKeys()
  luckyClearSelectedRowKeys()
  if (data.record) {
    name.value = data.record.activityName
    activityInfoId.value = data.record.activityId
    reload({
      searchInfo: {
        activityInfoId: unref(activityInfoId),
        receiveType: 0,
      },
    })
  }
  activeKey.value = 'basicInfo'
})

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: prizeColumns('1'),
  beforeFetch: params => {
    params.receiveType = 1
    params.activityInfoId = unref(activityInfoId)
    params.userName = unref(search)?.userName
    return params
  },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: prizeFormItem(),
    submitOnReset: false,
    resetFunc: () => reset(unref(activeKey)),
  },
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  api: findPrizeRecordList,
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.auditState !== '-10',
    }),
  },
  showIndexColumn: false,
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
  },
})

const [
  registerLuckTable,
  {
    reload: luckyReload,
    getSelectRows: luckyGetSelectRows,
    clearSelectedRowKeys: luckyClearSelectedRowKeys,
  },
] = useTable({
  rowKey: 'autoId',
  columns: prizeColumns('0'),
  beforeFetch: params => {
    params.receiveType = 0
    params.activityInfoId = unref(activityInfoId)
    params.userName = unref(search)?.userName
    return params
  },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: prizeFormItem(),
    submitOnReset: false,
    resetFunc: () => reset(unref(activeKey)),
  },
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  api: findPrizeRecordList,
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.luckyState !== '-1',
    }),
  },
  showIndexColumn: false,
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
  },
})

//抽奖记录
const [registerLuckRecordTable, { reload: reloadRecord }] = useTable({
  rowKey: 'autoId',
  api: findLuckRecordList,
  columns: luckRecordColumns(),
  beforeFetch: params => {
    params.activityInfoId = unref(activityInfoId)
    params.userName = unref(search)?.userName
    return params
  },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: luckRecordFormItem(),
    submitOnReset: false,
    resetFunc: () => reset(unref(activeKey)),
  },
  immediate: true,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: false,
})

const [registerAudit, { closeModal, openModal: openAudit }] = useModal()
const [luckyregisterAudit, { closeModal: luckycloseModal, openModal: luckyopenAudit }] = useModal()

const [registerView, { openModal: openView }] = useModal()

function handleAudit(record, type?: string) {
  let arr: string[] = []
  if (record) {
    arr.push(record.autoId)
  } else {
    const rows = getSelectRows()
    if (!rows || rows.length === 0) {
      createWarningModal({ content: '请选择至少一条数据进行审核！' })
      return false
    }
    arr = map(rows, v => v.autoId)
  }
  openAudit(true, { businessIds: arr, type, activityInfoId: unref(activityInfoId) })
}

function luckyAudit(record, type?: string) {
  let arr: string[] = []
  if (record) {
    arr.push(record.autoId)
  } else {
    const rows = luckyGetSelectRows()
    if (!rows || rows.length === 0) {
      createWarningModal({ content: '请选择至少一条数据进行审核！' })
      return false
    }
    arr = map(rows, v => v.autoId)
  }
  luckyopenAudit(true, { businessIds: arr, type, activityInfoId: unref(activityInfoId) })
}

function handleDetails(record) {
  openView(true, { record })
}

function handleAuditSuccess({ values, type }) {
  if (type === 'give') {
    const { businessIds, remark, activityInfoId } = values
    disposePrize({ autoId: businessIds[0], remark, activityInfoId }).then(res => {
      const { code, message } = res
      if (code === 200) {
        createSuccessModal({ content: '发放成功!' })
        reload()
        closeModal()
      } else {
        createErrorModal({ content: `发放失败!${message}` })
      }
    })
  } else {
    auditPrize(values).then(res => {
      const { code, message } = res
      if (code === 200) {
        createSuccessModal({ content: '审核成功!' })
        reload()
        closeModal()
        clearSelectedRowKeys()
      } else {
        createErrorModal({ content: `审核失败!${message}` })
      }
    })
  }
}

function luckyhandleAuditSuccess({ values, type }) {
  if (type === 'give') {
    const { businessIds, remark, activityInfoId } = values
    disposePrize({ autoId: businessIds[0], remark, activityInfoId }).then(res => {
      const { code, message } = res
      if (code === 200) {
        createSuccessModal({ content: '发放成功!' })
        luckyReload()
        luckycloseModal()
      } else {
        createErrorModal({ content: `发放失败!${message}` })
      }
    })
  } else {
    lotteryDraw(values).then(res => {
      const { code, message } = res
      if (code === 200) {
        createSuccessModal({ content: '开奖成功!' })
        luckyReload()
        luckycloseModal()
        clearSelectedRowKeys()
      } else {
        createErrorModal({ content: `开奖失败!${message}` })
      }
    })
  }
}

async function reloadTable(key) {
  activeKey.value = key
  await reset(key)
}

async function reset(key) {
  search.value.userName = undefined
  if (key === 'basicInfo') {
    await reload({
      searchInfo: {
        activityInfoId: unref(activityInfoId),
        receiveType: 1,
      },
    })
  } else if (key === 'luckDraw') {
    await luckyReload({
      searchInfo: {
        activityInfoId: unref(activityInfoId),
        receiveType: 0,
      },
    })
  } else {
    await reloadRecord({
      searchInfo: {
        activityInfoId: unref(activityInfoId),
      },
    })
  }
}

//导出
function exportData(type) {
  search.value.activityInfoId = unref(activityInfoId)
  search.value.receiveType = type
  exportPrizeRecord(unref(search)).then(res => {
    const url = window.URL.createObjectURL(res?.data)
    const day = dayjs().format('YYYY-MM-DD HH:mm:ss')
    downloadByUrl({
      url,
      fileName: '兑换人员信息导出' + day + '.xlsx',
    })
  })
}

//抽奖记录导出
function exportLuckyRecordData() {
  search.value.activityInfoId = unref(activityInfoId)
  exportLuckRecord(unref(search)).then(res => {
    const url = window.URL.createObjectURL(res?.data)
    const day = dayjs().format('YYYY-MM-DD HH:mm:ss')
    downloadByUrl({
      url,
      fileName: '抽奖人员信息导出' + day + '.xlsx',
    })
  })
}

async function handelCancel() {
  search.value.userName = undefined
}
</script>
