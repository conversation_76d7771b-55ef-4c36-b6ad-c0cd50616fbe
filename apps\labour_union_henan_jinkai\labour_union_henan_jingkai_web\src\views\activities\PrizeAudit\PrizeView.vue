<template>
  <BasicModal
    @register="registerModule"
    :title="title"
    :can-fullscreen="false"
    :show-ok-btn="false"
  >
    <BasicForm @register="registerForm" class="back-transparent" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { prizeModalFormItem } from './data'

const name = ref('')

const prizeType = ref('')

const searchColumn = computed(() => {
  return prizeModalFormItem(unref(prizeType))
})

const title = computed(() => {
  return `${unref(name)}--兑换详情`
})

const [registerModule, { setModalProps }] = useModalInner(async data => {
  await resetFields()

  if (data.record) {
    name.value = data.record.userName
    prizeType.value = data.record.receiveType
    setFieldsValue({
      ...data.record,
    })
  }
  setModalProps({
    confirmLoading: false,
    showOkBtn: false,
  })
  setProps({
    disabled: true,
  })
})

const [registerForm, { setProps, resetFields, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: searchColumn,
  showActionButtonGroup: false,
})
</script>
