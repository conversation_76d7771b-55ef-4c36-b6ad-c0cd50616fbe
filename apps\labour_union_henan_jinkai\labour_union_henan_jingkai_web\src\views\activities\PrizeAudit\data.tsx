import { Image } from 'ant-design-vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { useUserStore } from '/@/store/modules/user'

export function prizeFormItem(): FormSchema[] {
  return [
    {
      field: 'userName',
      component: 'Input',
      label: '兑换人',
      colProps: {
        span: 8,
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入兑换人',
      },
      slot: 'userName',
    },
  ]
}

export function luckRecordFormItem(): FormSchema[] {
  return [
    {
      field: 'userName',
      component: 'Input',
      label: '抽奖人',
      colProps: {
        span: 8,
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入抽奖人',
      },
      slot: 'userName',
    },
  ]
}

export function prizeColumns(receiveType: string): BasicColumn[] {
  const dictionary = useDictionary()
  const userStore = useUserStore()
  return [
    {
      dataIndex: 'userName',
      title: '兑换人',
      width: 90,
    },
    {
      dataIndex: 'prizeName',
      title: '兑换品名称',
      width: 110,
    },
    {
      dataIndex: 'prizeImg',
      title: '兑换品图',
      customRender: ({ text }) => {
        return <Image src={userStore.getPrefix + text} width={55} height={55}></Image>
      },
      width: 120,
    },
    {
      dataIndex: 'createTime',
      title: '兑换时间',
      width: 150,
    },
    {
      dataIndex: 'address',
      title: '收货地址',
      width: 200,
    },
    {
      dataIndex: 'luckyState',
      title: '中奖状态',
      width: 130,
      ifShow: receiveType === '0',
      customRender: ({ text }) => {
        const dictName = dictionary.getDictionaryMap.get(`PrizeLuckyType_${text}`)?.dictName
        return <span title={dictName}>{dictName}</span>
      },
    },
    {
      dataIndex: 'auditState',
      title: '处理状态',
      width: 150,
      ifShow: receiveType === '1',
      customRender: ({ text }) => {
        const dictName = dictionary.getDictionaryMap.get(`PrizeAuditType_${text}`)?.dictName
        return <span title={dictName}>{dictName}</span>
      },
    },
  ]
}

export function luckRecordColumns(): BasicColumn[] {
  const dictionary = useDictionary()
  const userStore = useUserStore()
  return [
    {
      dataIndex: 'userName',
      title: '抽奖人',
    },
    {
      dataIndex: 'prizeType',
      title: '中奖类型',
      customRender: ({ text }) => {
        const dictName = dictionary.getDictionaryMap.get(`LuckyDrawType_${text}`)?.dictName
        return <span title={dictName}>{dictName}</span>
      },
    },
    {
      dataIndex: 'prizeImg',
      title: '奖品图',
      customRender: ({ text }) => {
        return <Image src={userStore.getPrefix + text} width={55} height={55}></Image>
      },
    },
    {
      dataIndex: 'prizeName',
      title: '奖品内容',
    },
    {
      dataIndex: 'createTime',
      title: '抽奖时间',
    },
  ]
}

export function modalAuditFormItem(type): FormSchema[] {
  if (type === 'give') {
    return [
      {
        field: 'remark',
        label: '备注信息',
        component: 'InputTextArea',
        rulesMessageJoinLabel: true,
        required: true,
      },
    ]
  } else {
    return [
      {
        field: 'auditState',
        label: '是否通过',
        component: 'RadioGroup',
        required: true,
        defaultValue: 'pass',
        componentProps: {
          options: [
            { label: '审核通过', value: '0' },
            { label: '驳回', value: '10' },
          ],
        },
      },
      {
        field: 'auditRemark',
        label: '审核意见',
        required: false,
        component: 'InputTextArea',
        componentProps: {
          autocomplete: 'off',
          placeholder: '请输入审核意见',
        },
      },
    ]
  }
}

export function luckymodalAuditFormItem(type): FormSchema[] {
  if (type === 'give') {
    return [
      {
        field: 'remark',
        label: '备注信息',
        component: 'InputTextArea',
        rulesMessageJoinLabel: true,
        required: true,
      },
    ]
  } else {
    return [
      {
        field: 'auditState',
        label: '是否中奖',
        component: 'RadioGroup',
        required: true,
        defaultValue: 'pass',
        componentProps: {
          options: [
            { label: '中奖', value: '1' },
            { label: '未中奖', value: '0' },
          ],
        },
      },
    ]
  }
}

export function prizeModalFormItem(type): FormSchema[] {
  const userStore = useUserStore()
  return [
    {
      field: 'userName',
      label: '兑换人',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'consigneeMobile',
      label: '手机号',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'address',
      label: '地址',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'prizeName',
      label: '兑换品名称',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'createTime',
      label: '兑换时间',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'prizeImg',
      label: '图片',
      component: 'Image',
      colProps: {
        span: 12,
      },
      render({ values }) {
        return <Image src={userStore.getPrefix + values.prizeImg} width="70" height="70"></Image>
      },
    },
    {
      field: 'prizeContent',
      label: '兑换品内容',
      component: 'InputTextArea',
      componentProps: {
        autoSize: true,
      },
    },
    {
      field: 'luckyState',
      label: '中奖状态',
      component: 'RadioGroup',
      ifShow: type === '0',
      colProps: {
        span: 24,
      },
      componentProps: {
        options: [
          { label: '未开奖', value: '-1' },
          { label: '未中奖', value: '0' },
          { label: '已中奖', value: '1' },
        ],
      },
    },
    {
      field: 'auditState',
      label: '是否通过',
      component: 'RadioGroup',
      ifShow: type === '1',
      colProps: {
        span: 24,
      },
      componentProps: {
        options: [
          { label: '待审核', value: '-10' },
          { label: '通过', value: '0' },
          { label: '驳回', value: '10' },
          { label: '通过', value: '20' },
        ],
      },
    },
    {
      field: 'auditRemark',
      label: '审核意见',
      component: 'InputTextArea',
      ifShow: type === '1',
    },
    {
      field: 'assignState',
      label: '发放状态',
      component: 'RadioGroup',
      ifShow: ({ values }) => {
        return values.auditState === '20' || values.auditState === '0' || values.luckyState == '1'
      },
      componentProps: {
        options: [
          { label: '待发放', value: 'N' },
          { label: '已发放', value: 'Y' },
        ],
      },
    },
  ]
}

export function commentSearchFormSchema(): FormSchema[] {
  return [
    {
      field: 'activityName',
      label: '活动名称',
      component: 'Input',
      colProps: { span: 6 },
      class: 'z-0',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入活动名称',
      },
    },
  ]
}
