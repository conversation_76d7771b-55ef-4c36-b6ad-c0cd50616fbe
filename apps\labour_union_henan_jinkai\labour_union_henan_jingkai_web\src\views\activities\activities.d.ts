import MiniDecimal from 'ant-design-vue/lib/input-number/src/utils/MiniDecimal';
import type { Dayjs } from 'dayjs';
import { PropType } from 'vue';
import { baseInfo } from '@/api/activities';

export const CustomerType = {
  interestGroup: [{ dictCode: 'interestGroup', dictName: '所属兴趣小组成员' }],
  volunteerService: [{ dictCode: 'volunteer', dictName: '志愿者' }],
  friendship: [{ dictCode: 'friendship', dictName: '单身联谊会员' }],
};

// 暂时无用inclusiveMS此类型
export enum ActivityType {
  QUIZ = 'vieAnswer',
  SIGNUP = 'signUp',
  LOTTERY = 'luckDraw',
  SURVEY = 'questionnaire',
  VOTE = 'vote',
  INCLUSIVE_YJWD = 'inclusiveYJWD',
  EDUCATION_AID = 'education',
  INTEREST_GROUP = 'interestGroup',
  INCLUSIVE = 'inclusive',
  UNION = 'union',
  BASICINFO = 'basicInfo',
  JOINT = 'joint',
  INCLUSIVE_OTHER = 'inclusiveOther',
  WALK = 'walk',
  BLUE_VEST = 'blueVest',
  COUPON = 'coupon',
  INCLUSIVE_SIGNUP = 'inclusiveSignUp',
  // 南充竞赛
  COMPETITION = 'competition',
  // 趣味竞赛
  FUN_COMPETITION = 'funCompetition',
  // 一岁一礼
  BIRTHDAY = 'birthday',
  // 志愿服务
  VOLUNTEER_SERVICE = 'volunteerService',
  FRIENDSHIP = 'friendship',
  SUMMER_COOLNESS = 'summerCoolness',
  // 积分种树
  INTEGRAL_TREE = 'integralTree',
  // 每日签到抽奖
  SIGN_LOTTERY = 'signLottery',
  // 积分抽奖
  INTEGRAL_LOTTERY = 'integralLottery',
  // 玫瑰书香
  WOMEN = 'women',
  // 多作品类型投票活动
  MULTIPLE_VOTE = 'multipleVote',
}

//大类归属
export const BigActivityType = {
  vieAnswer: 'union',
  signUp: 'union',
  luckDraw: 'union',
  questionnaire: 'union',
  vote: 'union',
  inclusiveYJWD: 'inclusive',
  inclusiveOil: 'inclusive',
  inclusiveMovie: 'inclusive',
  inclusiveMS: 'inclusive',
  inclusiveOther: 'inclusive',
  education: 'education',
  interestGroup: 'interestGroup',
  walk: 'walk',
  blueVest: 'blueVest',
  coupon: 'inclusive',
  inclusiveSignUp: 'inclusive',
  //南充竞赛
  competition: 'competition',
  //趣味竞赛
  funCompetition: 'funCompetition',
  //一岁一礼
  birthday: 'birthday',
  //志愿服务
  volunteerService: 'volunteerService',
  friendship: 'friendship',
  summerCoolness: 'summerCoolness',
  // 积分种树
  integralTree: 'integralTree',
  women: 'women',
  multipleVote: 'multipleVote',
  SIGN_UP_AND_QUIZ: 'signUpAndQuiz',
};

//文件地址
export const ActivityDocAddr = {
  vieAnswer: 16,
  signUp: 16,
  luckDraw: 16,
  questionnaire: 16,
  vote: 17,
  inclusiveYJWD: 16,
  inclusiveOil: 16,
  inclusiveMovie: 16,
  inclusiveMS: 16,
  inclusiveOther: 16,
  education: 16,
  interestGroup: 16,
  walk: 16,
  blueVest: 16,
  coupon: 16,
  inclusiveSignUp: 16,
  summerCoolness: 16,
  integralTree: 16,
  women: 16,
  multipleVote: 17,
};

export enum ActivityTypeZh {
  vieAnswer = '竞答',
  signUp = '报名',
  luckDraw = '抽奖',
  questionnaire = '问卷',
  vote = '投票',
  inclusiveYJWD = '有奖问答',
  walk = '健步走',
  inclusiveOther = '普惠',
  education = '困难帮扶',
  interestGroup = '兴趣小组',
  basicInfo = '基础',
  blueVest = '蓝背心',
  coupon = '普惠',
  inclusiveSignUp = '普惠报名',
  //南充竞赛
  competition = '南充竞赛',
  //趣味竞赛
  funCompetition = '趣味竞赛',
  //一岁一礼
  birthday = '一岁一礼',
  //志愿服务
  volunteerService = '志愿服务',
  friendship = '单身联谊',
  summerCoolness = '送清凉',
  // 种树
  integralTree = '积分种树',
  //玫瑰书香
  women = '玫瑰书香',
  multipleVote = '',
}

export const ActivityTypeApi = {
  basicInfo: baseInfo,
};

export enum ActivitySettingZh {
  signUp = '报名',
  vieAnswer = '竞答',
  luckDraw = '抽奖',
  questionnaire = '问卷',
  vote = '投票',
  inclusiveYJWD = '有奖问答',
  education = '金秋助学',
  interestGroup = '兴趣小组活动',
  coupon = '票券',
  inclusiveSignUp = '报名',
  birthday = '奖品',
  walk = '健步走',
  summerCoolness = '票券',
  multipleVote = '投稿',
}

// 预览路由
export const ActivityView = {
  vieAnswer: 'quiz',
  signUp: 'signUp',
  luckDraw: 'luckDraw',
  questionnaire: 'questionnaire',
  vote: 'vote',
  inclusiveYJWD: 'inclusiveYJWD',
  inclusiveOther: 'inclusiveOther',
  blueVest: 'blueVest',
  coupon: 'coupon',
  inclusiveSignUp: 'inclusiveSignUp',
  walk: 'walk',
  //南充竞赛
  competition: 'competition',
  //趣味竞赛
  funCompetition: 'funCompetition',
  //一岁一礼
  birthday: 'birthday',
  //志愿服务
  volunteerService: 'volunteerService',
  friendship: 'friendship',
  interestGroup: 'interestGroup',
  summerCoolness: 'summerCoolness',
  // 种树
  integralTree: 'integralTree',
  //玫瑰书香
  women: 'women',
  multipleVote: 'multipleVote',
};

//活动路由
export const ActivityRouter = {
  vieAnswer: 'quizActivity',
  signUp: 'signUpActivity',
  luckDraw: 'lotteryActivity',
  questionnaire: 'surveyActivity',
  vote: 'voteActivity',
  inclusiveYJWD: 'popQuiz',
  inclusiveOther: 'inclusiveOther',
  blueVest: 'blueVestActivity',
  coupon: 'grantPlan',
  inclusiveSignUp: 'grantPlan',
  //南充竞赛
  competition: 'competitionSignUpActivity',
  //趣味竞赛
  funCompetition: 'funCompetitionSignUpActivity',
  //一岁一礼
  birthday: 'birthday',
  //志愿服务
  volunteerService: 'volunteerServiceSignUpActivity',
  friendship: 'friendshipSignUpActivity',
  interestGroup: 'interestGroupActivity',
  summerCoolness: 'summerCoolnessActivity',
  integralTree: '/planting-trees',
  //玫瑰书香
  women: 'womenSignUpActivity',
  multipleVote: 'multipleVoteActivity',
};

//预览字体
export const ActivityText = {
  vieAnswer: '答题',
  signUp: '签到',
  luckDraw: '抽奖',
  questionnaire: '问卷调查',
  vote: '投票',
  inclusiveYJWD: 'inclusiveYJWD',
  inclusiveOther: '参与',
};

export interface formSettings {
  joinTotal?: Number;
  dailyTotal?: Number;
  questionNum?: Number;
  questionNumRandom?: Number;
  InputNumber?: Number;
  answerTime?: Number;
  questionList: Question[];
}

export interface Question {
  topicContent: string;
  optionType: string;
  score: MiniDecimal;
  options: AnswerType[];
  ifShow?: boolean;
  ifMust?: string;
  opusNo?: string; //编号
  opusContent?: string; //详情
  opusCover?: string; //封面
  opusName?: string; //名称
  prizeName?: string; //奖品名称'
  prizeType?: string; //奖品类型
  prizeIcon?: string; //图标';
  prizeImg?: string; //图片';
  prizeContent?: string; //奖品内容';
  prizeCount?: number; //奖品数量';
  percentage?: number; //中奖率';
  number?: string | number;
  answer?: string; //答案
  prizeEx?: number;
  useCount?: number;
  couponId;
  couponName;
}
export type RangeValue = [Dayjs, Dayjs];

export interface AnswerType {
  optionContent: string;
  correct: boolean;
  optionNo: number;
  selectCount?: number;
}

export interface VoteTypeConfig {
  opusType;
  userLimit;
  fileLimit;
  fileType;
}

export interface LuckDrawInfo {
  receiveStartTime;
  receiveEndTime;
  awardPoolName;
  dailyAwardCount;
  awardCountMax;
  prizeInfos: Question[];
}

export const props = {
  // 隐藏显示
  disabled: { type: Boolean, default: false },
};

export interface CardTotal {
  name: string;
  count: number;
  icon: string;
}

export interface TabsObj extends Recordable {
  key: ActivityType | string;
  tab: string;
  cardData?: CardTotal[];
  echartData?: number[][];
  dateTime?: RangeValue;
  dateArr?: string[];
  prizeList?: [];
  topicList?: [];
  // dateTimeArr;
}

export interface BasicProps {
  width: string;
  height: string;
  sourceData: [];
  dateArr: string[];
  pieData: [];
  topicList: [];
}
export const basicProps = {
  width: {
    type: String as PropType<string>,
    default: '100%',
  },
  height: {
    type: String as PropType<string>,
    default: '100%',
  },
  sourceData: {
    type: Object,
  },
  type: {
    type: String as PropType<string>,
  },
};

interface topic {
  topicInfoId: string;
  topicContent: string;
  options: AnswerType[];
  optionType: string;
}
