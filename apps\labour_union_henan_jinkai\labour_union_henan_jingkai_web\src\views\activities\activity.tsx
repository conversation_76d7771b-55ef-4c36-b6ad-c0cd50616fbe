import { Rate, Tooltip, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep, filter, map } from 'lodash-es';
import { nextTick } from 'vue';
import { ActivityType } from './activities.d';
import { todoList } from '@/api/activities';
import { uploadApi } from '@/api/sys/upload';
import { DescItem } from '@/components/Description';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import isBetween from 'dayjs/plugin/isBetween';
import { useUserStore } from '@/store/modules/user';
import { searchNextUnionForm } from '@/utils/searchNextUnion';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { formatToDate } from '@monorepo-yysz/utils';

export const tableColumns = (type?: string): BasicColumn[] => {
  return [
    {
      title: '活动名称',
      dataIndex: 'activityName',
      width: 160,
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '阅读量',
      dataIndex: 'readCount',
      width: 60,
      customRender({ text }) {
        return <span>{text || 0}</span>;
      },
    },
    {
      title: '参与范围',
      dataIndex: 'areaCode',
      width: 100,
      ifShow: false,
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '参与类型',
      dataIndex: 'customerType',
      width: 100,
      ifShow: false,
      customRender: ({ record }) => {
        const dictionary = useDictionary();
        const name = dictionary.getDictionaryMap.get(
          `customerType_${record.customerType}`
        )?.dictName;
        return <Tooltip title={name}>{name}</Tooltip>;
      },
    },
    {
      title: '活动日期',
      dataIndex: 'activityEndTime',
      width: 200,
      align: 'left',
      class: '!text-center',
      ifShow: type !== ActivityType.WALK,
      customRender: ({ record }) => {
        const { openingStartTime, openingEndTime, activityStartTime, activityEndTime } = record;
        const start = activityStartTime
          ? dayjs(activityStartTime).format('YYYY-MM-DD HH:mm:ss')
          : '';
        const end = activityEndTime ? dayjs(activityEndTime).format('YYYY-MM-DD HH:mm:ss') : '';
        const timeStart = openingStartTime ? openingStartTime : '';
        const timeEnd = openingEndTime ? openingEndTime : '';
        let isDateRange = false;
        let isTimeRange = false;
        if (start && end && timeStart && timeEnd) {
          dayjs.extend(isBetween);
          const day = dayjs();
          const firstDate = `${start}`;
          const lastDate = `${end}`;
          const firstTime = `${dayjs().format('YYYY-MM-DD')} ${timeStart}`;
          const lastTime = `${dayjs().format('YYYY-MM-DD')} ${timeEnd}`;

          isDateRange = day.isBetween(firstDate, lastDate, null, '[]');
          isTimeRange = day.isBetween(firstTime, lastTime, null, '[]');
        }

        const s = start ? formatToDate(start, 'YYYY-MM-DD') : '';
        const e = end ? formatToDate(end, 'YYYY-MM-DD') : '';

        return (
          <div>
            <div>
              <label>起止日期：</label>
              <span class={isDateRange ? 'text-green-500' : 'text-red-500'}>
                {s}~{e}
              </span>
            </div>
            <div>
              <div>
                <label>开放时间：</label>
                <span
                  class={
                    isDateRange
                      ? isTimeRange
                        ? 'text-green-500'
                        : 'text-yellow-500'
                      : 'text-red-500'
                  }
                >
                  {timeStart}~{timeEnd}
                </span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: '组织单位',
      dataIndex: 'companyName',
      width: 120,
    },
    {
      title: '置顶状态',
      dataIndex: 'stickyOption',
      width: 90,
      customRender: ({ text, record }) => {
        const dictionary = useDictionary();
        const name = dictionary.getDictionaryMap.get(`topOption_${text}`)?.dictName;
        if (text === 'TOP_TIME') {
          return (
            <Tooltip title={name + ':' + record.topTime}>
              <div>{name}</div>
              <div>{record.topTime}</div>
            </Tooltip>
          );
        }
        return <Tooltip title={name}>{name ?? '-'}</Tooltip>;
      },
    },
    // {
    //   title: '审核状态',
    //   dataIndex: 'auditState',
    //   width: 75,
    //   ifShow: type === ActivityType.INTEREST_GROUP,
    //   customRender: ({ record }) => {
    //     const dictionary = useDictionary();
    //     const name = dictionary.getDictionaryMap.get(
    //       `activityVerifyStatus_${record.auditState}`
    //     )?.dictName;
    //     return <Tooltip title={name}>{name ?? '-'}</Tooltip>;
    //   },
    // },
    {
      title: '活动状态',
      dataIndex: 'state',
      width: 150,
      customRender: ({ record }) => {
        const dictionary = useDictionary();
        if (record.auditState && record.auditState != 'pass') {
          const result = dictionary.getDictionaryMap.get(
            `activityVerifyStatus_${record.auditState}`
          )?.dictName;
          if (record.auditState !== 'wait' && record.auditOpinion) {
            return `${result}：${record.auditOpinion}`;
          }
          return result;
        }
        if (record.state === 'publish') {
          return record.progressMsg;
        }
        return dictionary.getDictionaryMap.get(`activityState_${record.state}`)?.dictName;
      },
    },
  ];
};

export const searchFormSchema = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'activityName',
      label: '活动名称',
      component: 'Input',
      colProps: { span: 6 },
      class: 'z-0',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入活动名称',
      },
    },
    {
      field: 'customerType',
      label: '参与类型',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      ifShow: false,
      componentProps: function () {
        return {
          fieldNames: { label: 'dictName', value: 'dictCode' },
          options: dictionary.getDictionaryOBJMap.get('customerType'),
        };
      },
    },
    {
      field: 'areaType',
      label: '参与用户区域',
      component: 'Select',
      ifShow: false,
      colProps: { span: 6 },
      componentProps: ({ formModel }) => {
        return {
          placeholder: '请选择参与用户区域',
          options: dictionary.getDictionaryOpt.get('areaType'),
          onChange: value => {
            if (value === '2') {
              formModel['areaCode'] = undefined;
            }
          },
        };
      },
    },
    {
      field: 'state',
      label: '发布状态',
      component: 'Select',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
      componentProps: ({}) => {
        return {
          options: dictionary.getDictionaryOpt.get('activityState'),
        };
      },
    },
    {
      field: 'auditState',
      label: '审核状态',
      component: 'Select',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
      componentProps: ({}) => {
        return {
          options: dictionary.getDictionaryOpt.get('activityVerifyStatus'),
        };
      },
    },
    {
      field: 'startEndDate',
      label: '活动起止日期',
      component: 'RangePicker',
      colProps: { span: 6 },
      componentProps: {
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD',
      },
    },

    ...searchNextUnionForm(),
  ];
};

export const joinColumn = (activityMode: string): BasicColumn[] => {
  const dictionary = useDictionary();

  const columns = [
    {
      dataIndex: 'autoId',
      defaultHidden: true,
      title: '主键',
    },
    {
      dataIndex: 'userName',
      title: '用户名称',
    },
    {
      dataIndex: 'phone',
      title: '手机号',
    },
    {
      dataIndex: 'companyName',
      title: '工会名称',
    },
    {
      dataIndex: 'createTime',
      title: activityMode === ActivityType.SURVEY ? '填写日期' : '报名时间',
      width: 150,
    },
    {
      dataIndex: 'createTime',
      title: '用时（秒）',
      width: 150,
      ifShow: activityMode === ActivityType.SURVEY,
    },
    {
      dataIndex: 'state',
      title: '审核状态',
      customRender: ({ text }) => {
        return (
          <span>{dictionary.getDictionaryMap.get(`activityVerifyStatus_${text}`)?.dictName}</span>
        );
      },
      ifShow: activityMode !== ActivityType.SURVEY,
    },
    {
      dataIndex: 'signInFlag',
      title: '签到状态',
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`activityAudit_${text}`)?.dictName}</span>;
      },
      ifShow: activityMode === ActivityType.VOLUNTEER_SERVICE,
    },
  ];
  return columns;
};

export const joinFormSchema = (activityMode: string): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'userName',
      label: '用户姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyName',
      label: '工会名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'userMobile',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'state',
      label: '审核状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('activityVerifyStatus'),
        };
      },
    },
    {
      field: 'searchText',
      label: '内容关键字',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const joinAuditFormSchema = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'state',
      label: '是否通过',
      component: 'RadioGroup',
      required: true,
      defaultValue: 'pass',
      componentProps: () => {
        const options = dictionary.getDictionaryOpt.get('activityVerifyStatus');
        const option = filter(cloneDeep(options), v => v.value !== 'wait');
        return {
          options: option as RadioGroupChildOption[],
        };
      },
    },
    {
      field: 'auditOpinion',
      label: '审核意见',
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入审核意见',
      },
    },
  ];
};

export const schema = (activityMode): DescItem[] => {
  const dictionary = useDictionary();
  const schema = [
    {
      field: 'userName',
      label: '用户名称',
    },
    {
      field: 'phone',
      label: '手机号码',
    },
    {
      field: 'companyName',
      label: '工会名称',
    },
    {
      field: 'createTime',
      label: activityMode === ActivityType.SURVEY ? '填写时间' : '报名时间',
    },
  ];
  if (activityMode === ActivityType.SURVEY) {
    return schema;
  }

  return [
    ...schema,
    {
      field: 'state',
      label: '审核状态',
      render: val => {
        const name = dictionary.getDictionaryMap.get(`activityVerifyStatus_${val}`)?.dictName;
        return <span>{name}</span>;
      },
    },
    {
      field: 'updateUser',
      label: '审核人',
    },
    {
      field: 'updateTime',
      label: '审核时间',
    },
    {
      field: 'auditOpinion',
      label: '审核意见',
    },
  ];
};

export const audit = (): DescItem[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'state',
      label: '审核状态',
      render: val => {
        const name = dictionary.getDictionaryMap.get(`activityVerifyStatus_${val}`)?.dictName;
        return <span>{name}</span>;
      },
    },
    {
      field: 'updateUser',
      label: '审核人',
    },
    {
      field: 'updateTime',
      label: '审核时间',
    },
    {
      field: 'auditOpinion',
      label: '审核意见',
    },
  ];
};

export const archiveColum = (type): BasicColumn[] => {
  return [
    {
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      dataIndex: 'activityName',
      title: '活动名称',
    },
    {
      dataIndex: 'companyName',
      title: '组织单位',
      width: 200,
    },
    {
      dataIndex: 'activityAddress',
      title: '举办地点',
      width: 300,
    },
    {
      dataIndex: 'createTime',
      title: '归档日期',
      width: 150,
    },
  ];
};

export const archiveSearchFormSchema = (): FormSchema[] => {
  return [
    {
      field: 'activityName',
      component: 'Input',
      label: '活动名称',
      colProps: {
        span: 6,
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入活动名称',
      },
    },
  ];
};

export const archiveFormSchema = (type, activityId): FormSchema[] => {
  const userStore = useUserStore();
  return [
    {
      field: 'activityId',
      component: 'ApiSelect',
      label: '活动名称',
      colProps: {
        span: 24,
      },
      ifShow({ disabled, values }) {
        return !values.activityName;
      },
      required: true,
      componentProps: ({ formActionType, formModel }) => {
        return {
          placeholder: '请选择活动',
          api: todoList,
          resultField: 'data',
          params: {
            pageSize: 0,
            activityCategory: type,
            activityId: activityId,
            companyId: userStore.getUserInfo.companyId,
          },
          immediate: true,
          alwaysLoad: true,
          onChange: (v, record) => {
            const { clearValidate } = formActionType;
            if (record) {
              const { companyName, activityStartTime, activityEndTime, activityAddress } = record;
              formModel['companyName'] = companyName;
              formModel['activityAddress'] = activityAddress;
              formModel['dateRange'] = [
                dayjs(activityStartTime).format('YYYY-MM-DD'),
                dayjs(activityEndTime).format('YYYY-MM-DD'),
              ];
            }
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.activityName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'activityName', value: 'activityId' },
        };
      },
    },
    {
      field: 'activityName',
      component: 'ShowSpan',
      label: '活动名称',
      colProps: {
        span: 24,
      },
      required: true,
      dynamicDisabled: true,
      ifShow({ values }) {
        return values.activityName;
      },
    },
    {
      field: 'companyName',
      component: 'ShowSpan',
      label: '组织单位',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'dateRange',
      component: 'RangePicker',
      label: '活动起止日期',
      colProps: {
        span: 12,
      },
      required: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD ',
      },
    },
    {
      field: 'activityAddress',
      component: 'Input',
      label: '举办地点',
      colProps: {
        span: 12,
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入举办地点',
      },
    },
    {
      field: 'filePath',
      component: 'Upload',
      label: '上传附件',
      colProps: {
        span: 12,
      },
      componentProps: {
        api: uploadApi,
        maxNumber: 3,
        uploadParams: {
          operateType: 16,
        },
      },
    },
    {
      field: 'activityHeld',
      component: 'InputTextArea',
      label: '举办情况',
      colProps: {
        span: 24,
      },
      required: true,
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入举办情况',
        showCount: true,
        maxlength: 500,
      },
    },
  ];
};

export function prizeFormItem(): FormSchema[] {
  const dictionary = useDictionary();
  return [
    {
      field: 'userName',
      component: 'Input',
      label: '用户姓名',
      colProps: {
        span: 6,
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入用户姓名',
      },
    },
    {
      field: 'userMobile',
      component: 'Input',
      label: '联系电话',
      colProps: {
        span: 6,
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入联系电话',
      },
    },
    {
      field: 'companyName',
      component: 'Input',
      label: '工会名称',
      colProps: {
        span: 6,
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'dateRange',
      label: '中奖日期',
      component: 'RangePicker',
      colProps: { span: 6 },
      componentProps: {
        showTime: false,
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'prizeName',
      component: 'Input',
      label: '奖品名称',
      colProps: {
        span: 6,
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'prizeType',
      label: '奖品类型',
      component: 'Select',
      colProps: {
        span: 6,
      },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('prize'),
      },
    },
    {
      field: 'couponUseState',
      label: '票券核销状态',
      component: 'Select',
      colProps: {
        span: 6,
      },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('couponUseState'),
      },
    },
    {
      field: 'assignState',
      label: '实物发放状态',
      component: 'Select',
      colProps: {
        span: 6,
      },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: [
          { label: '未发放', value: 'N' },
          { label: '已发放', value: 'Y' },
        ],
      },
    },
    {
      field: 'receiveState',
      label: '红包领取状态',
      component: 'Select',
      colProps: {
        span: 6,
      },
      rulesMessageJoinLabel: true,
      ifShow: false,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('receiveState'),
      },
    },
  ];
}

export function prizeColumns(): BasicColumn[] {
  const dictionary = useDictionary();
  return [
    {
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      dataIndex: 'userName',
      title: '用户姓名',
    },
    {
      dataIndex: 'companyName',
      title: '所属工会',
    },
    {
      dataIndex: 'prizeName',
      title: '奖品名称',
    },
    {
      dataIndex: 'prizeType',
      title: '奖品类型',
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`prize_${text}`)?.dictName}</span>;
      },
    },
    {
      dataIndex: 'prizeContent',
      title: '奖品内容',
    },
    {
      dataIndex: '',
      title: '奖品状态',
      width: 100,
      customRender({ text, record }) {
        let msg = '';
        let color = 'default'; // 默认颜色

        const { receiveState, assignState, couponUseState, prizeType } = record;

        switch (prizeType) {
          case '3': // 实物
            msg = assignState === 'Y' ? '已发放' : '未发放';
            color = assignState === 'Y' ? 'success' : 'default';
            break;
          case '5': // 红包
            if (receiveState === '0') {
              msg = '未领取';
              color = 'default';
            } else if (receiveState === '1') {
              msg = '已领取';
              color = 'success';
            } else {
              msg = '未到账';
              color = 'error';
            }
            break;
          case '7': // 票券
            msg = couponUseState === 'Y' ? '已使用' : '未使用';
            color = couponUseState === 'Y' ? 'success' : 'default';
            break;
        }
        if (msg) {
          return <Tag color={color}>{msg}</Tag>;
        }
        return <span>-</span>;
      },
    },
    {
      dataIndex: 'createTime',
      title: '中奖日期',
    },
  ];
}

export function modalAuditFormItem(type): FormSchema[] {
  const dictionary = useDictionary();
  if (type === 'give') {
    return [
      {
        field: 'remark',
        label: '备注信息',
        component: 'InputTextArea',
        rulesMessageJoinLabel: true,
        required: true,
      },
    ];
  }
  return [
    {
      field: 'state',
      label: '是否通过',
      component: 'RadioGroup',
      required: true,
      defaultValue: 'pass',
      componentProps: {
        options: filter(
          cloneDeep(dictionary.getDictionaryOpt.get('activityVerifyStatus')),
          v => v.value !== 'review'
        ) as RadioGroupChildOption[],
      },
    },
    {
      field: 'auditOpinion',
      label: '审核意见',
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入审核意见',
        maxlength: 200,
      },
    },
  ];
}

export function prizeModalFormItem(): FormSchema[] {
  const dictionary = useDictionary();
  return [
    {
      field: 'userName',
      label: '用户姓名',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'userMobile',
      label: '手机号',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'companyName',
      label: '工会名称',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'createTime',
      label: '中奖时间',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'prizeName',
      label: '奖品名称',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'prizeType',
      label: '奖品类型',
      component: 'Select',
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`prize`),
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'prizeContent',
      label: '奖品内容',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'receiveState',
      label: '红包发放状态',
      component: 'Select',
      ifShow({ values }) {
        return values.prizeType === '5';
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`receiveState`),
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'couponUseState',
      label: '核销状态',
      component: 'Select',
      ifShow({ values }) {
        return values.prizeType === '7';
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`couponUseState`),
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'useType',
      label: '票劵使用类型',
      component: 'Select',
      ifShow({ values }) {
        return values.prizeType === '7';
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`useType`),
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'consignee',
      label: '收货人姓名',
      component: 'Input',
      ifShow({ values }) {
        return (
          (values.prizeType === '3' && values.receiveType === '2') ||
          (values.prizeType === '7' && values.useType === 'post')
        );
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'consigneeMobile',
      label: '收货人联系电话',
      component: 'Input',
      ifShow({ values }) {
        return (
          (values.prizeType === '3' && values.receiveType === '2') ||
          (values.prizeType === '7' && values.useType === 'post')
        );
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'provinceCode',
      label: '收货地址',
      component: 'Input',
      ifShow({ values }) {
        return (
          (values.prizeType === '3' && values.receiveType === '2') ||
          (values.prizeType === '7' && values.useType === 'post')
        );
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'address',
      label: '地址详情',
      component: 'Input',
      ifShow({ values }) {
        return (
          (values.prizeType === '3' && values.receiveType === '2') ||
          (values.prizeType === '7' && values.useType === 'post')
        );
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'remark',
      label: '备注信息',
      component: 'InputTextArea',
      ifShow({ values }) {
        return values.prizeType === '3' && values.receiveType === '2';
      },
    },
    {
      field: 'receiveType',
      label: '领取方式',
      show: false,
      component: 'Input',
    },
  ];
}

export function commentColum(commentType: string): BasicColumn[] {
  const dictionary = useDictionary();
  const colums =
    commentType === 'comment'
      ? [
          {
            dataIndex: 'state',
            title: '审核状态',
            customRender: ({ text }) => {
              const dictName = dictionary.getDictionaryMap.get(
                `activityVerifyStatus_${text}`
              )?.dictName;
              return <span title={dictName}>{dictName}</span>;
            },
          },
          {
            dataIndex: 'auditOpinion',
            title: '审核意见',
          },
        ]
      : [
          {
            dataIndex: 'score',
            title: '评分',
            width: 180,
            customRender: ({ text }) => {
              return (
                <Rate
                  disabled={true}
                  value={text}
                />
              );
            },
          },
          {
            dataIndex: 'openState',
            title: '是否公开',
            width: 100,
            customRender: ({ text }) => {
              const dictName = dictionary.getDictionaryMap.get(`logicallyDelete_${text}`)?.dictName;
              return <span title={dictName}>{dictName}</span>;
            },
          },
        ];

  return [
    {
      dataIndex: 'activityName',
      title: '活动名称',
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      dataIndex: 'userName',
      title: commentType === 'comment' ? '评论人' : '评价人',
      width: 120,
    },
    {
      dataIndex: 'content',
      title: commentType === 'comment' ? '评论内容' : '评价内容',
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      dataIndex: 'createTime',
      title: commentType === 'comment' ? '评论时间' : '评价时间',
      width: 150,
    },
    ...colums,
  ];
}

export function commentSearchFormSchema(): FormSchema[] {
  const dictionary = useDictionary();
  return [
    {
      field: 'activityName',
      label: '活动名称',
      component: 'Input',
      colProps: { span: 6 },
      class: 'z-0',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入活动名称',
      },
    },
    {
      field: 'state',
      label: '审核状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('activityVerifyStatus'),
        };
      },
    },
  ];
}

export function commentAuditFormItem(): FormSchema[] {
  const dictionary = useDictionary();
  return [
    {
      field: 'state',
      label: '是否通过',
      component: 'RadioGroup',
      required: true,
      defaultValue: 'pass',
      componentProps: {
        options: filter(
          cloneDeep(dictionary.getDictionaryOpt.get('activityVerifyStatus')),
          v => v.value !== 'wait'
        ) as RadioGroupChildOption[],
      },
    },
    {
      field: 'auditOpinion',
      label: '审核意见',
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入审核意见',
        maxlength: 200,
      },
    },
  ];
}

export function formNumber(): FormSchema[] {
  return [
    {
      field: 'cycle',
      label: '期数',
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
      className: '!w-full',
    },
  ];
}

export function columnNumber(): BasicColumn[] {
  return [
    {
      title: '期数',
      dataIndex: 'cycle',
    },
    {
      title: '活动日期',
      dataIndex: 'activityEndTime',
      align: 'left',
      class: '!text-center',
      customRender: ({ record }) => {
        const { openingStartTime, openingEndTime, beginTime, endTime } = record;
        const start = beginTime ? dayjs(beginTime).format('YYYY-MM-DD HH:mm:ss') : '';
        const end = endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm:ss') : '';
        const timeStart = openingStartTime ? openingStartTime : '';
        const timeEnd = openingEndTime ? openingEndTime : '';
        let isDateRange = false;
        let isTimeRange = false;
        if (start && end && timeStart && timeEnd) {
          dayjs.extend(isBetween);
          const day = dayjs();
          const firstDate = `${start}`;
          const lastDate = `${end}`;
          const firstTime = `${dayjs().format('YYYY-MM-DD')} ${timeStart}`;
          const lastTime = `${dayjs().format('YYYY-MM-DD')} ${timeEnd}`;

          isDateRange = day.isBetween(firstDate, lastDate, null, '[]');
          isTimeRange = day.isBetween(firstTime, lastTime, null, '[]');
        }

        const s = start ? formatToDate(start, 'YYYY-MM-DD') : '';
        const e = end ? formatToDate(end, 'YYYY-MM-DD') : '';

        return (
          <div>
            <div>
              <label>起止日期：</label>
              <span class={isDateRange ? 'text-green-500' : 'text-red-500'}>
                {s}~{e}
              </span>
            </div>
            <div>
              <div>
                <label>开放时间：</label>
                <span
                  class={
                    isDateRange
                      ? isTimeRange
                        ? 'text-green-500'
                        : 'text-yellow-500'
                      : 'text-red-500'
                  }
                >
                  {timeStart}~{timeEnd}
                </span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      customRender({ text }) {
        const name = text === 'Y' ? '启用' : '禁用';
        return <span title={name}>{name}</span>;
      },
    },
  ];
}

export function modalNumber(): FormSchema[] {
  const dictionary = useDictionary();
  return [
    {
      field: 'cycle',
      required: true,
      label: '期数',
      component: 'InputNumber',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 1,
      },
      className: '!w-full',
    },
    {
      field: 'state',
      label: '是否启用',
      component: 'RadioGroup',
      colProps: { span: 24 },
      defaultValue: 'N',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
      },
      className: '!w-full',
    },
    {
      field: 'startEndDate',
      required: true,
      label: '活动起止日期',
      component: 'RangePicker',
      colProps: { span: 12 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD ',
      },
    },
    {
      field: 'dailyTime',
      required: true,
      label: '每日开放时间',
      component: 'TimeRangePicker',
      colProps: { span: 12 },
      componentProps: {
        placeholder: ['起始时间', '结束时间'],
        showNow: true,
        showTime: { showNow: true },
      },
    },
    {
      field: 'cover',
      required: true,
      label: '封面',
      component: 'CropperForm',
      colProps: { span: 24 },
    },
  ];
}

export function typeColumns(): BasicColumn[] {
  return [
    {
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      dataIndex: 'orderNum',
      title: '序号',
      width: 150,
    },
    {
      dataIndex: 'dictName',
      title: '类型名称',
    },
    {
      dataIndex: 'dictCode',
      title: '类型编码',
    },
  ];
}

export function typeFormItem(isUpdate: boolean): FormSchema[] {
  return [
    {
      field: 'orderNum',
      label: '排序',
      colProps: { span: 24 },
      component: 'InputNumber',
      className: '!w-full',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'dictCode',
      label: '类型编码',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      ifShow: !isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'dictName',
      label: '类型名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
  ];
}

export function answerRecordColumns(): BasicColumn[] {
  return [
    {
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      dataIndex: 'userName',
      title: '用户姓名',
      width: 150,
    },
    {
      dataIndex: 'phone',
      title: '联系电话',
      width: 150,
    },
    {
      dataIndex: 'companyName',
      title: '所属工会',
    },
    {
      dataIndex: 'totalScore',
      title: '得分',
      width: 150,
    },
    {
      dataIndex: 'totalTime',
      title: '用时（秒）',
      width: 150,
    },
    {
      dataIndex: 'accuracy',
      title: '正确率',
      width: 150,
    },
    {
      dataIndex: 'createTime',
      title: '答题时间',
      width: 150,
    },
  ];
}

export function answerRecordRankColumns(): BasicColumn[] {
  return [
    {
      dataIndex: 'userName',
      title: '用户姓名',
      width: 150,
    },
    {
      dataIndex: 'totalScore',
      title: '累计得分',
      width: 150,
    },
    {
      dataIndex: 'answerCount',
      title: '答题次数',
      width: 150,
    },
    {
      dataIndex: 'totalScore',
      title: '累计耗时（秒）',
      width: 150,
    },
  ];
}

export const answerRecordSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      component: 'Input',
      label: '用户姓名',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 6,
      },
    },
    {
      field: 'phone',
      component: 'Input',
      label: '联系电话',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 6,
      },
    },
  ];
};

export function voteRankColumns(): BasicColumn[] {
  return [
    {
      dataIndex: 'rank',
      title: '排名',
      width: 150,
    },
    {
      title: '作品编号',
      dataIndex: 'opusNo',
      width: 120,
    },
    {
      title: '作品名称',
      dataIndex: 'opusName',
    },
    {
      title: '作者姓名',
      dataIndex: 'userName',
      width: 120,
    },
    {
      title: '工会名称',
      dataIndex: 'companyName',
      width: 200,
    },
    {
      title: '总票数',
      dataIndex: 'votesNum',
      width: 100,
    },
    {
      title: '今日新增票券',
      dataIndex: 'todayCount',
      width: 100,
    },
  ];
}

export const voteRankSchemas = (voteTypes): FormSchema[] => {
  return [
    {
      field: 'opusNo',
      component: 'Input',
      label: '编号',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 6,
      },
    },
    {
      field: 'opusType',
      component: 'RadioGroup',
      label: '作品分类',
      defaultValue: voteTypes[0].recordId,
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      componentProps: () => {
        const option = map(cloneDeep(voteTypes), ({ recordId, opusType }) => {
          return { label: opusType, value: recordId };
        });
        return {
          options: option as RadioGroupChildOption[],
        };
      },
    },
    // {
    //   field: 'opusName',
    //   component: 'Input',
    //   label: '作品名称',
    //   rulesMessageJoinLabel: true,
    //   colProps: {
    //     span: 6,
    //   },
    // },
  ];
};
