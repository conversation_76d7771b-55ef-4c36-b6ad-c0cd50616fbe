<template>
  <ActivityTypeManage
    :type="{ groupCode: 'unionActivityType', groupName: '工会活动类型' }"
    :add="add"
    :modify="modify"
    :del="del"
  />
</template>

<script lang="ts" setup>
import ActivityTypeManage from '@/views/activities/ActivityTable/ActivityType.vue';
const add = '/unionActivityType/add';
const modify = '/unionActivityType/modify';
const del = '/unionActivityType/delete';
</script>
