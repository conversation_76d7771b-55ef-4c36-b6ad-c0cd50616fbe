<template>
  <ActivityArchive
    :type="ActivityType.UNION"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityArchive from '@/views/activities/ActivityTable/ActivityArchive.vue';
import { ActivityType } from '@/views/activities/activities.d';

const columnAuth = ['/archive/add', '/archive/view'];
const recordAuth = {
  view: '/archive/view',
};

const titleAuth = '/archive/add';
</script>
