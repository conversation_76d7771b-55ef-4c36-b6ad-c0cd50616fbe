<script setup lang="ts">

import {BasicModal, useModalInner} from "@/components/Modal";
import {computed, ref, unref} from "vue";
import ActivityComment from "@/views/activities/ActivityTable/ActivityComment.vue";

const columnAuth = ['/comment/audit'];

const recordAuth = {
  audit: '/comment/audit',
};

const titleAuth = '/comment/auditBatch';
const record = ref<Recordable>();
const activityId = ref('')
const title = computed(() => {
  return `${unref(record)?.activityName}-评论管理`;
});

const type = computed(()=>{
  return unref(record)?.activityCategory === 'inclusive' ? 'inclusive' : 'union'
})


const [registerModal, { setModalProps }] = useModalInner(async data => {
  record.value = data.record;
  activityId.value = data.record.activityId
  setModalProps({ confirmLoading: false });
});
</script>

<template>
  <BasicModal
      @register="registerModal"
      v-bind="$attrs"
      :title="title"
      :destroyOnClose="true"
  >
    <ActivityComment
        :type="type"
        :columnAuth="columnAuth"
        :activityId="activityId"
        :recordAuth="recordAuth"
        :titleAuth="titleAuth"
        commentType="comment"
    />
  </BasicModal>
</template>

<style scoped lang="less">

</style>
