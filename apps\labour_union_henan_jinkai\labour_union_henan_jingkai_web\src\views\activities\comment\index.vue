<template>
  <ActivityComment
    :type="ActivityType.UNION"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
    commentType="comment"
  />
</template>

<script lang="ts" setup>
import ActivityComment from '@/views/activities/ActivityTable/ActivityComment.vue';
import { ActivityType } from '@/views/activities/activities.d';

const columnAuth = ['/comment/audit'];

const recordAuth = {
  audit: '/comment/audit',
};

const titleAuth = '/comment/auditBatch';
</script>
