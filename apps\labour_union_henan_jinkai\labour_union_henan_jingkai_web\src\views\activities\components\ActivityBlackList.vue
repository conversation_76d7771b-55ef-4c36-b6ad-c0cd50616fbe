<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleBatchAdd" class="mr-2">
          批量添加
        </a-button>
        <a-button type="primary" danger @click="handleClearAll">清空黑名单</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
              :actions="[ {
                icon: 'fluent:delete-12-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
              },]"/>
        </template>
      </template>
    </BasicTable>

    <BasicModal
      v-bind="$attrs"
      @register="registerModal"
      :can-fullscreen="false"
      title="批量添加黑名单"
      width="50%"
      @ok="handleSubmit"
    >
      <BasicForm @register="registerForm" />
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
import {BasicTable, TableAction, useTable} from '@/components/Table';
import { BasicModal, useModal } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { actBlackList, saveBatchBlackList, delBlackList, clearBlackList } from '@/api/activities/statistics';
import {createVNode, nextTick, onMounted, ref, unref} from 'vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {message, Modal} from "ant-design-vue";
import {ExclamationCircleOutlined} from "@ant-design/icons-vue";


const props = defineProps({
  activityId: {
    type: String,
  },
  actName: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['reload']);

const params = ref<Recordable>({});

const { createMessage } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns:   [
    {
      title: '手机号/工会名称',
      dataIndex: 'content',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '添加日期',
      dataIndex: 'createTime',
    },
  ],
  showIndexColumn: false,
  api: actBlackList,
  beforeFetch: p => {
    params.value = { ...p, activityId: unref(props.activityId) };
    return unref(params);
  },
  formConfig: {
    labelWidth: 120,
    schemas: [
      {
        field: 'content',
        label: '手机号/工会名称',
        colProps: { span: 6 },
        component: 'Input',
        rulesMessageJoinLabel: true,
      },
    ],
    autoSubmitOnEnter: true,
  },
  immediate: false,
  useSearchForm: true,
  maxHeight: 510,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 120,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal,closeModal }] = useModal();

const [registerForm, { validateFields, resetFields }] = useForm({
  labelWidth: 100,
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'content',
      label: '黑名单内容',
      component: 'InputTextArea',
      componentProps: {
        rows: 10,
        placeholder: '请输入手机号或工会名称，每行一个，每行内容不超过100个字符',
      },
      required: true,
    },
    {
      field: 'remark',
      label: '备注',
      component: 'Input',
    },
  ],
});

function reloadAll() {
  reload({
    searchInfo: {
      activityId: unref(props.activityId),
    },
  });
}

async function handleBatchAdd() {
  resetFields();
  openModal();
}

async function handleSubmit() {
  const values = await validateFields();
  const lines = [...new Set(values.content.split('\n').filter(item => item.trim()))];

  if (!lines.length) {
    createMessage.warning('请输入有效的黑名单内容');
    return;
  }

  // 验证每行内容长度
  const invalidLines = lines.filter(line => line.length > 100);
  if (invalidLines.length > 0) {
    createMessage.warning('每行内容不能超过100个字符');
    return;
  }

  const {code,message} = await saveBatchBlackList({
    activityId: unref(props.activityId),
    list: lines,
    remark: values.remark,
  });

  if(code === 200){
    createMessage.success('批量添加成功');
    reload();
    closeModal()
  }else {
    createMessage.error(message);
  }
}

async function handleDelete(record: Recordable) {
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定删除 ? `,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      delBlackList({
        activityId: unref(props.activityId),
        autoId: record.autoId,
      }).then(res => {
        if (res.code === 200) {
          createMessage.success('清空成功');
          reload();
          closeModal()
        } else {
          message.error(`删除失败，${res.message}`);
        }
      });
    },
  });
}

async function handleClearAll() {
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定删除 ? `,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      clearBlackList({
        activityId: unref(props.activityId),
      }).then(res => {
        if (res.code === 200) {
          createMessage.success('操作成功');
          reload();
          closeModal()
        } else {
          message.error(`删除失败，${res.message}`);
        }
      });
    },
  });
}

onMounted(() => {
  nextTick(() => {
    emit('reload', reloadAll);
  });
});
</script>

<style scoped>
.mr-2 {
  margin-right: 8px;
}
</style>
