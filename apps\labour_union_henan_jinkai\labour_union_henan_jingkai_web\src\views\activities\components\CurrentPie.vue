<template>
  <div
    ref="chartRef"
    :style="{ height, width }"
  ></div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, onUnmounted, ref, Ref, unref, watch } from 'vue';
import { useECharts } from '@/hooks/web/useECharts';
import { basicProps } from '../activities.d';
import { EChartsOption } from '@monorepo-yysz/utils';

const props = defineProps({
  ...basicProps,
});

const clear = ref<Fn>();

const chartRef = ref<HTMLDivElement | null>(null);

const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

function setEchart(pieData) {
  setPie(pieData);
}

function setPie(dataSource) {
  unref(clear)?.();

  const options: EChartsOption = {
    tooltip: {
      trigger: 'item',
      formatter: tooltipFormatter,
    },
    legend: {
      top: '5%',
      left: 'center',
    },
    series: [
      {
        name: '基础数据',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        labelLine: {
          show: false,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: 'bold',
            formatter: ({ value, name }) => {
              return [`{num|${value ? value : '-'}}`, `{text|${name}}`].join('\n');
            },
            rich: {
              num: {
                fontSize: 25,
                fontWeight: 'bold',
              },
              text: { fontSize: 35, fontWeight: 'bold' },
            },
          },
        },
        data: dataSource || [],
      },
    ],
  };
  setOptions(options);

  //@ts-ignore
  const { clearLoop } = tools.loopShowTooltip(getInstance(), options, { loopSeries: true });
  clear.value = clearLoop;
}

function tooltipFormatter(params, textColor?: string) {
  const { data, marker, name, value } = params;
  return `<span class="${textColor ? textColor : 'text-white'}">${marker}${data.name || name}: ${
    data.value ? data.value : value ? value : '-'
  }</span>`;
}

onUnmounted(() => {
  //清除定时任务，解决echarts对象被提前销毁解决报错暂时trycatch捕获
  try {
    unref(clear)?.();
  } catch (e) {}
});

onMounted(() => {
  nextTick(() => {
    setEchart(props.pieData);
  });
});

watch(
  () => props.pieData,
  val => {
    setEchart(val);
  },
  {
    deep: true,
  }
);
</script>
