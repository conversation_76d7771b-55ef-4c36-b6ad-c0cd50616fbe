<template>
  <BasicTable @register="registerTable">
    <template #toolbar>
      <a-button
        type="primary"
        @click="handleDowen"
        :loading="spinning"
        >导出参与用户</a-button
      >
    </template>
  </BasicTable>
</template>

<script lang="ts" setup>
import { BasicTable, useTable } from '@/components/Table';
import { columns, formSchemas } from './data';
import { joinExport, joinList } from '@/api/activities/statistics';
import { nextTick, onMounted, ref, unref } from 'vue';
import dayjs from 'dayjs';
import { downloadByUrl } from '@monorepo-yysz/utils';

const props = defineProps({
  activityId: {
    type: String,
  },
  actName: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['reload']);

const params = ref<Recordable>({});

const spinning = ref<boolean>(false);

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: joinList,
  beforeFetch: p => {
    params.value = {
      ...p,
      sourceId: unref(props.activityId),
      orderBy: 'create_time',
      sortType: 'desc',
    };
    return unref(params);
  },
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  immediate: false,
  useSearchForm: true,
  maxHeight: 510,
  bordered: true,
  // actionColumn: {
  //   title: '操作',
  //   width: 330,
  //   dataIndex: 'action',
  //   fixed: undefined,
  // },
});

function handleDowen() {
  spinning.value = true;
  joinExport(unref(params)).then(res => {
    const url = window.URL.createObjectURL(res);
    const fileName = `${props.actName}参与用户${dayjs().format('YYYY-MM-DD HH:mm:ss')}`;

    downloadByUrl({
      url,
      fileName,
    });
    spinning.value = false;
  });
}

function reloadAll() {
  reload({
    searchInfo: {
      activityId: unref(props.activityId),
    },
  });
}

onMounted(() => {
  nextTick(() => {
    emit('reload', reloadAll);
  });
});
</script>
