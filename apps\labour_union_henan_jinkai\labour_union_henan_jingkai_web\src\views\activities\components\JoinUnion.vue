<template>
  <BasicTable @register="registerTable" />
</template>

<script lang="ts" setup>
import { BasicTable, useTable } from '@/components/Table';
import { unionColumns, unionFormSchemas } from './data';
import { countByUnion } from '@/api/activities/statistics';
import { nextTick, onMounted, ref, unref } from 'vue';

const props = defineProps({
  activityId: {
    type: String,
  },
  actName: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['reload']);

const params = ref<Recordable>({});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: unionColumns(),
  showIndexColumn: false,
  api: countByUnion,
  beforeFetch: p => {
    params.value = { ...p, sourceId: unref(props.activityId) };
    return unref(params);
  },
  formConfig: {
    labelWidth: 120,
    schemas: unionFormSchemas(),
    autoSubmitOnEnter: true,
  },
  immediate: false,
  useSearchForm: true,
  maxHeight: 510,
  bordered: true,
  // actionColumn: {
  //   title: '操作',
  //   width: 330,
  //   dataIndex: 'action',
  //   fixed: undefined,
  // },
});

function reloadAll() {
  reload({
    searchInfo: {
      activityId: unref(props.activityId),
    },
  });
}

onMounted(() => {
  nextTick(() => {
    emit('reload', reloadAll);
  });
});
</script>
