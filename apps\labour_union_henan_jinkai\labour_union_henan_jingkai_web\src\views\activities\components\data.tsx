import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { map } from 'lodash-es';

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '用户姓名',
      dataIndex: 'userName',
    },
    {
      title: '联系方式',
      dataIndex: 'phone',
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
    },
    {
      title: '注册区域',
      dataIndex: 'areaName',
    },
    {
      title: '参与渠道',
      dataIndex: 'platform',
      customRender({ text }) {
        const way = text?.split(',') || [];
        const name = map(way, v => (v === 'wx' ? '微信' : '川工之家APP'))?.join(',');
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  //const dictionary = useDictionary()

  return [
    {
      field: 'userName',
      label: '用户姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyName',
      label: '所属工会',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const unionColumns = (): BasicColumn[] => {
  return [
    {
      title: '工会名称',
      dataIndex: 'companyName',
    },
    {
      title: '数量',
      dataIndex: 'count',
    },
  ];
};

export const unionFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'companyName',
      label: '工会名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};
