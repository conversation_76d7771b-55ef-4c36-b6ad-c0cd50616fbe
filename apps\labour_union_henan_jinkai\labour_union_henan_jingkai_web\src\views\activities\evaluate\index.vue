<template>
  <ActivityComment
    :type="ActivityType.UNION"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    commentType="evaluate"
    titleAuth="/evaluate/publishBatch"
  />
</template>

<script lang="ts" setup>
import ActivityComment from '@/views/activities/ActivityTable/ActivityComment.vue';
import { ActivityType } from '@/views/activities/activities.d';

const columnAuth = ['/evaluate/publish', '/evaluate/apply'];

const recordAuth = {
  publish: '/evaluate/publish',
  apply: '/evaluate/apply',
};
</script>
