<template>
  <ActivityTable
    :activity-type="ActivityType.BIRTHDAY"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'
/*一岁一礼*/
const columnAuth = ref([
  '/birthdayLotteryActivity/modify',
  '/birthdayLotteryActivity/pushOrCut',
  '/birthdayLotteryActivity/sum',
  '/birthdayLotteryActivity/delete',
  '/birthdayLotteryActivity/audit',
  '/birthdayLotteryActivity/link',
  '/birthdayLotteryActivity/view',
])

const recordAuth = ref({
  modify: '/birthdayLotteryActivity/modify',
  pushOrCut: '/birthdayLotteryActivity/pushOrCut',
  sum: '/birthdayLotteryActivity/sum',
  delete: '/birthdayLotteryActivity/delete',
  audit: '/birthdayLotteryActivity/audit',
  link: '/birthdayLotteryActivity/link',
  view: '/birthdayLotteryActivity/view',
})

const titleAuth = ref('/lotteryActivity/add')
</script>
