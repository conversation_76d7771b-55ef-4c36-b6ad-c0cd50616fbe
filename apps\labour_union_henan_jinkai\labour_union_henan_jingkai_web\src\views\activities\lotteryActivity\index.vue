<template>
  <ActivityTable
    :activity-type="ActivityType.LOTTERY"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'

const columnAuth = ref([
  '/lotteryActivity/modify',
  '/lotteryActivity/pushOrCut',
  '/lotteryActivity/sum',
  '/lotteryActivity/delete',
  '/lotteryActivity/audit',
  '/lotteryActivity/link',
  '/lotteryActivity/view',
  '/lotteryActivity/comments',
  '/lotteryActivity/archives',
])

const recordAuth = ref({
  modify: '/lotteryActivity/modify',
  pushOrCut: '/lotteryActivity/pushOrCut',
  sum: '/lotteryActivity/sum',
  delete: '/lotteryActivity/delete',
  audit: '/lotteryActivity/audit',
  link: '/lotteryActivity/link',
  view: '/lotteryActivity/view',
  comments:'/lotteryActivity/comments',
  archives:'/lotteryActivity/archives',
})

const titleAuth = ref('/lotteryActivity/add')
</script>
