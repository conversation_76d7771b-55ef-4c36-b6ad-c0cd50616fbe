<template>
  <ActivityTable
    :activity-type="ActivityType.MULTIPLE_VOTE"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'

const columnAuth = ref([
  '/multipleVoteActivity/modify',
  '/multipleVoteActivity/pushOrCut',
  '/multipleVoteActivity/sum',
  '/multipleVoteActivity/delete',
  '/multipleVoteActivity/audit',
  '/multipleVoteActivity/link',
  '/multipleVoteActivity/view',
  '/multipleVoteActivity/comments',
  '/multipleVoteActivity/archives',
  '/multipleVoteActivity/opus',
])

const recordAuth = ref({
  modify: '/multipleVoteActivity/modify',
  pushOrCut: '/multipleVoteActivity/pushOrCut',
  sum: '/multipleVoteActivity/sum',
  delete: '/multipleVoteActivity/delete',
  audit: '/multipleVoteActivity/audit',
  link: '/multipleVoteActivity/link',
  view: '/multipleVoteActivity/view',
  comments:'/multipleVoteActivity/comments',
  archives:'/multipleVoteActivity/archives',
  opus:'/multipleVoteActivity/opus',
})

const titleAuth = ref('/multipleVoteActivity/add')
</script>
