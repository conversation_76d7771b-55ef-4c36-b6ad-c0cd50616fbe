<template>
  <BasicModal
    @register="registerModule"
    @success="handleSuccess"
    :title="title"
    :can-fullscreen="false"
    @ok="handleSuccess"
    :wrap-class-name="$style['opinion-audit']"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { modalCommentFormItem } from './data';
import { useUserStore } from '@/store/modules/user';

const user = useUserStore();

const emit = defineEmits(['register', 'success']);

const name = ref('');

const noticeId = ref<string>('');

const title = computed(() => {
  return `${unref(name)}--新增意见`;
});

const [registerModule, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  noticeId.value = '';
  if (data.record) {
    name.value = data.record.title;
    noticeId.value = data.record.noticeId;
  }

  setModalProps({
    confirmLoading: false,
  });
});

const [registerForm, { validate, resetFields }] = useForm({
  labelWidth: 100,
  schemas: modalCommentFormItem(),
  showActionButtonGroup: false,
});

async function handleSuccess() {
  try {
    const values = await validate();

    emit('success', {
      values: {
        ...values,
        noticeId: unref(noticeId),
        unionId: user.getUserInfo.companyId,
        companyName: user.getUserInfo.companyName,
      },
    });
  } catch (error) {
    setModalProps({
      confirmLoading: true,
    });
  }
}
</script>

<style lang="less" module>
.opinion-audit {
  :global {
    .ant-input-number {
      width: 100% !important;
    }

    .activity-mode {
      display: none;
    }
  }
}
</style>
