<template>
  <BasicModal
    @register="registerModule"
    :title="title"
    :can-fullscreen="false"
    :show-ok-btn="false"
    :wrap-class-name="$style['comment-view']"
  >
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicTable, useTable } from '@/components/Table';
import { BasicModal, useModalInner } from '@/components/Modal';
import { collectionFindList } from '@/api/opinionCollection';
import { commentColumns } from './data';

const name = ref('');

const noticeId = ref<string>('');

const unionId = ref<string>('');

const title = computed(() => {
  return `${unref(name)}--意见`;
});

const [registerModule, { setModalProps }] = useModalInner(async data => {
  if (data.record) {
    name.value = data.record.title;
    noticeId.value = data.record.noticeId;
    unionId.value = data.record.unionId;
    reload({
      searchInfo: {
        noticeId: unref(noticeId),
        unionId: unref(unionId),
      },
    });
  }
  setModalProps({
    confirmLoading: false,
  });
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: commentColumns(),
  beforeFetch: params => {
    params.noticeId = unref(noticeId);
    params.unionId = unref(unionId);
    return params;
  },
  searchInfo: {
    noticeId: unref(noticeId),
    unionId: unref(unionId),
  },
  useSearchForm: false,
  showTableSetting: false,
  bordered: true,
  api: collectionFindList,
  showIndexColumn: false,
});
</script>

<style lang="less" module>
.comment-view {
  :global {
    .ant-table-body {
      height: 51vh !important;
    }
  }
}
</style>
