<template>
  <BasicModal
    @register="registerModule"
    :title="title"
    :can-fullscreen="false"
    @ok="handleSuccess"
    :wrap-class-name="$style['opinion-audit']"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { modalAuditFormItem } from './data';

const emit = defineEmits(['register', 'success']);

const name = ref('');

const autoId = ref<number | undefined>(undefined);

const title = computed(() => {
  return `${unref(name)}--审核`;
});

const [registerModule, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  autoId.value = undefined;
  if (data.record) {
    name.value = data.record.title;
    autoId.value = data.record.autoId;
  }

  setModalProps({
    confirmLoading: false,
  });
});

const [registerForm, { validate, resetFields }] = useForm({
  labelWidth: 100,
  schemas: modalAuditFormItem(),
  showActionButtonGroup: false,
});

async function handleSuccess() {
  try {
    const values = await validate();

    emit('success', {
      values: {
        ...values,
        autoId: unref(autoId),
      },
    });
  } catch (error) {
    setModalProps({
      confirmLoading: true,
    });
  }
}
</script>

<style lang="less" module>
.opinion-audit {
  :global {
    .ant-form {
      height: 50vh !important;
    }

    .activity-mode {
      display: none;
    }
  }
}
</style>
