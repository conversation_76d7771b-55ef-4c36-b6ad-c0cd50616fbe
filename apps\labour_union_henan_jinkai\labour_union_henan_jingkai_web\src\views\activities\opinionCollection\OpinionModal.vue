<template>
  <BasicModal
    @register="registerModule"
    :title="title"
    :can-fullscreen="false"
    @ok="handleSuccess"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { useUserStore } from '@/store/modules/user';
import { modalFormItem } from './data';

const emit = defineEmits(['register', 'success']);

const user = useUserStore();

const disabled = ref(false);

const isUpdate = ref(false);

const name = ref('');

const autoId = ref<number | undefined>(undefined);

const title = computed(() => {
  return unref(disabled)
    ? `${unref(name)}--详情`
    : unref(isUpdate)
      ? `编辑--${unref(name)}`
      : `下发通知`;
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const [registerModule, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  autoId.value = undefined;

  if (unref(isUpdate)) {
    name.value = data.record.title;
    autoId.value = data.record.autoId;
    setFieldsValue({
      ...data.record,
    });
  }

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });
  setProps({
    disabled: unref(disabled),
  });
});

const [registerForm, { setProps, validate, resetFields, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: modalFormItem(),
  showActionButtonGroup: false,
});

async function handleSuccess() {
  try {
    const values = await validate();
    const { areaType, areaCode } = values;
    let ac = areaCode;
    if (areaType && areaType === '1') {
      ac = user.getUserInfo.areaName;
    } else if (areaType && areaType === '0') {
      ac = '南充市';
    } else {
      ac = areaCode ? areaCode?.join(',') : undefined;
    }
    emit('success', {
      values: {
        ...values,
        unionId: user.getUserInfo.companyId,
        companyName: user.getUserInfo.companyName,
        areaCode: ac,
        autoId: unref(autoId),
      },
    });
  } catch (error) {
    setModalProps({
      confirmLoading: true,
    });
  }
}
</script>
