import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { h, nextTick } from 'vue';
import { Tinymce } from '@/components/Tinymce/index';
import { cloneDeep, filter, map } from 'lodash-es';
import { ActivityType } from '../activities.d';
import { findByActivityCategory } from '@/api/opinionCollection';
import { Tooltip } from 'ant-design-vue';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

export const tableColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      dataIndex: 'autoId',
      defaultHidden: true,
      title: '主键',
    },
    {
      dataIndex: 'title',
      title: '标题',
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      dataIndex: 'areaCode',
      title: '参与区域',
      width: 170,
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      dataIndex: 'state',
      title: '审核状态',
      width: 150,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`opinionVerify_${text}`)?.dictName;
        return <span title={name}>{name}</span>;
      },
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
      width: 150,
    },
  ];
};

export const searchFormSchema = (): FormSchema[] => {
  return [
    {
      field: 'title',
      component: 'Input',
      label: '标题',
      colProps: {
        span: 6,
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入标题',
      },
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();

  return [
    {
      field: 'title',
      component: 'Input',
      label: '标题',
      required: true,
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入标题',
        maxlength: 100,
        showCount: true,
      },
    },
    {
      field: 'areaType',
      required: true,
      label: '参与区域',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: '1',
      componentProps: ({ formModel }) => {
        return {
          placeholder: '请选择参与区域',
          options: dictionary.getDictionaryOpt.get('areaType') as RadioGroupChildOption[],
          onChange: e => {
            if (e.target?.value === '2') {
              formModel['areaCode'] = undefined;
            }
          },
        };
      },
    },
    {
      field: 'areaCode',
      label: '区域选择',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      ifShow: ({ values }) => {
        return values.areaType === '2';
      },
      componentProps: {
        options: map(dictionary.getDictionaryOpt.get('regionCode'), t => {
          return { value: t.label, label: t.label };
        }) as RadioGroupChildOption[],
        mode: 'multiple',
        placeholder: '请选择参与区域',
      },
    },
    {
      field: 'activityContent',
      label: '活动内容描述',
      component: 'Input',
      required: true,
      render: ({ model, field, disabled }) => {
        return h(Tinymce, {
          value: model[field],
          onChange: (value: string) => {
            model[field] = value;
          },
          showImageUpload: false,
          options: {
            readonly: disabled,
          },
        });
      },
    },
  ];
};

export const modalAuditFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'state',
      label: '审核状态',
      component: 'RadioGroup',
      defaultValue: 'Y',
      required: true,
      componentProps: {
        options: filter(
          cloneDeep(dictionary.getDictionaryOpt.get('opinionVerify')),
          v => v.value !== 'N'
        ) as RadioGroupChildOption[],
      },
    },
    {
      field: 'activityId',
      component: 'ApiSelect',
      label: '活动主体',
      required: true,
      componentProps: ({ formActionType, formModel }) => {
        return {
          placeholder: '请选择活动',
          api: findByActivityCategory,
          resultField: 'data',
          params: {
            activityCategory: ActivityType.UNION,
          },
          onChange: (e, node) => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());

            formModel['activityMode'] = node.activityMode;
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.activityName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'activityName', value: 'activityId' },
        };
      },
    },
    {
      field: 'activityMode',
      component: 'Input',
      label: '活动小类',
      ifShow: true,
      dynamicDisabled: true,
      class: 'activity-mode',
    },
  ];
};

export const modalCommentFormItem = (): FormSchema[] => {
  return [
    {
      field: 'maxUserCount',
      label: '预计组织人数',
      required: true,
      component: 'InputNumber',
      componentProps: {
        min: 0,
        placeholder: '请输入预计组织人数',
      },
    },
    {
      field: 'sponsors',
      label: '活动赞助',
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 100,
        placeholder: '请输入活动赞助',
      },
    },
    {
      field: 'opinion',
      label: '活动修改建议',
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 500,
        placeholder: '请输入活动修改建议',
      },
    },
    {
      field: 'requirement',
      label: '其他需求',
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 100,
        placeholder: '请输入其他需求',
      },
    },
  ];
};

export const commentColumns = (): BasicColumn[] => {
  return [
    {
      dataIndex: 'autoId',
      defaultHidden: true,
      title: '主键',
    },
    {
      dataIndex: 'companyName',
      title: '工会名称',
    },
    {
      dataIndex: 'maxUserCount',
      title: '预计组织人数',
      width: 150,
    },
    {
      dataIndex: 'sponsors',
      title: '活动赞助',
    },
    {
      dataIndex: 'opinion',
      title: '活动修改建议',
    },
    {
      dataIndex: 'requirement',
      title: '其他需求',
    },
  ];
};
