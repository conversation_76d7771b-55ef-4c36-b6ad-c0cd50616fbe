<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/opinionCollection/add"
          >下发通知</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                disabled: record.state !== 'N',
                onClick: handleModify.bind(null, record),
                auth: '/opinionCollection/modify',
              },
              {
                icon: 'akar-icons:comment-add',
                label: '新增意见',
                type: 'primary',
                disabled: record.state !== 'N',
                onClick: handleComment.bind(null, record),
                auth: '/opinionCollection/comment',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                disabled: record.state !== 'N',
                onClick: handleAudit.bind(null, record),
                auth: '/opinionCollection/audit',
              },
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/opinionCollection/view',
              },
              {
                icon: 'fe:notice-push',
                label: '查看意见',
                type: 'default',
                onClick: handleCommentView.bind(null, record),
                auth: '/opinionCollection/commentView',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <OpinionModal
      @register="registerModal"
      width="50%"
      @success="handleSuccess"
    />
    <OpinionAuditModal
      @register="registerAuditModal"
      width="50%"
      @success="handleAuditSuccess"
    />
    <CommentModal
      @register="registerCommentModal"
      width="50%"
      @success="handleCommentSuccess"
    />
    <CommentView
      @register="registerCommentview"
      width="60%"
    />
  </div>
</template>

<script lang="ts" setup>
import { tableColumns, searchFormSchema } from './data';
import { noticeFindList, saveOrUpdate, add, bindActivity } from '@/api/opinionCollection';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import OpinionModal from './OpinionModal.vue';
import OpinionAuditModal from './OpinionAuditModal.vue';
import CommentModal from './CommentModal.vue';
import CommentView from './CommentView.vue';
import { Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { CheckCircleOutlined, CloseCircleFilled } from '@ant-design/icons-vue';

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: tableColumns(),
  authInfo: '/opinionCollection/add',
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema(),
    autoSubmitOnEnter: true,
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  api: noticeFindList,
  showIndexColumn: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',

    fixed: undefined,
    width: 430,
    auth: [
      '/opinionCollection/add',
      '/opinionCollection/modify',
      '/opinionCollection/comment',
      '/opinionCollection/audit',
      '/opinionCollection/view',
      '/opinionCollection/commentView',
    ],
  },
});

const [registerModal, { closeModal, openModal }] = useModal();

const [registerAuditModal, { closeModal: closeAuditModal, openModal: openAuditModal }] = useModal();

const [registerCommentModal, { closeModal: closeComment, openModal: openComment }] = useModal();

const [registerCommentview, { openModal: openCommentview }] = useModal();

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

function handleModify(record) {
  openModal(true, {
    isUpdate: true,
    disabled: false,
    record,
  });
}

function handleAudit(record) {
  openAuditModal(true, { record });
}

function handleView(record) {
  openModal(true, {
    isUpdate: true,
    disabled: true,
    record,
  });
}

function handleSuccess({ values }) {
  saveOrUpdate(values).then(res => {
    const { code, message } = res;
    if (code === 200) {
      Modal.success({
        title: '提示',
        icon: createVNode(CheckCircleOutlined),
        content: '操作成功!' || message,
        okText: '确认',
        closable: true,
      });
      reload();
      closeModal();
    } else {
      Modal.error({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: `操作失败!${message}`,
        okText: '确认',
        closable: true,
      });
    }
  });
}

function handleAuditSuccess({ values }) {
  bindActivity(values).then(res => {
    const { code, message } = res;
    if (code === 200) {
      Modal.success({
        title: '提示',
        icon: createVNode(CheckCircleOutlined),
        content: '审核成功!' || message,
        okText: '确认',
        closable: true,
      });
      reload();
      closeAuditModal();
    } else {
      Modal.error({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: `审核失败!${message}`,
        okText: '确认',
        closable: true,
      });
    }
  });
}

function handleComment(record) {
  openComment(true, { record });
}

function handleCommentView(record) {
  openCommentview(true, { record });
}

function handleCommentSuccess({ values }) {
  add(values).then(res => {
    const { code, message } = res;
    if (code === 200) {
      Modal.success({
        title: '提示',
        icon: createVNode(CheckCircleOutlined),
        content: '新增意见成功!' || message,
        okText: '确认',
        closable: true,
      });
      reload();
      closeComment();
    } else {
      Modal.error({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: `新增意见失败!${message}`,
        okText: '确认',
        closable: true,
      });
    }
  });
}
</script>
