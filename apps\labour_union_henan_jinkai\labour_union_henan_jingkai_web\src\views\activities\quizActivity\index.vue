<template>
  <ActivityTable
    :activity-type="ActivityType.QUIZ"
    :titleAuth="titleAuth"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'

const columnAuth = ref([
  '/quizActivity/modify',
  '/quizActivity/pushOrCut',
  '/quizActivity/sum',
  '/quizActivity/delete',
  '/quizActivity/audit',
  '/quizActivity/link',
  '/quizActivity/view',
  '/lotteryActivity/comments',
  '/lotteryActivity/archives',
])

const recordAuth = ref({
  modify: '/quizActivity/modify',
  pushOrCut: '/quizActivity/pushOrCut',
  sum: '/quizActivity/sum',
  delete: '/quizActivity/delete',
  audit: '/quizActivity/audit',
  link: '/quizActivity/link',
  view: '/quizActivity/view',
  comments:'/lotteryActivity/comments',
  archives:'/lotteryActivity/archives',
})

const titleAuth = ref('/quizActivity/add')
</script>
