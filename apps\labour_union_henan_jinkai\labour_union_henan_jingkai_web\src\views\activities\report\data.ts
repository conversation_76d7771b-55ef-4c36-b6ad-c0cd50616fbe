export const joinOpt = {
  title: {
    text: '',
    subtext: '',
    left: 'center',
  },
  legend: {
    show: true,
    top: '20px',
    right: '3%',
    itemWidth: 30, // 图例标记的图形宽度。
    //   itemGap: 20, // 图例每项之间的间隔。
    itemHeight: 10, //  图例标记的图形高度。
    textStyle: {
      // color: '#fff',
      fontSize: 14,
      padding: [0, 8, 0, 8],
    },
  },
  grid: {
    top: '50px',
    left: '4%',
    right: '4%',
    bottom: '10%',
  },
  tooltip: {
    trigger: 'axis',
  },
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
    minInterval: 1,
  },
  series: [
    {
      data: [],
      type: 'line',
      smooth: true, // 设置为光滑曲线
      name: '抽奖次数',
    },
    {
      data: [],
      type: 'line',
      name: '访问量',
      smooth: true, // 设置为光滑曲线
    },
    {
      data: [],
      type: 'line',
      name: '参与人次',
      smooth: true, // 设置为光滑曲线
    },
  ],
};

export const defaultOption = {
  title: {
    text: '',
    subtext: '',
    left: 'center',
  },
  legend: {
    show: true,
    top: '20px',
    right: '3%',
    itemWidth: 30, // 图例标记的图形宽度。
    //   itemGap: 20, // 图例每项之间的间隔。
    itemHeight: 10, //  图例标记的图形高度。
    textStyle: {
      // color: '#fff',
      fontSize: 14,
      padding: [0, 8, 0, 8],
    },
  },
  grid: {
    top: '50px',
    left: '4%',
    right: '4%',
    bottom: '15%',
  },
  tooltip: {
    trigger: 'axis',
  },
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
    minInterval: 1,
  },
  series: [],
};
