<template>
  <div
    class="w-full bg-[#e3e3e8] h-full overflow-y-auto pt-2 relative"
    :class="$style.report"
  >
    <div class="mx-[30vw] p-3 bg-[#fff] subpixel-antialiased tracking-wide">
      <div>
        <img
          :src="logo"
          class="w-[36px] h-[36px]"
        />
        <span class="ml-1">{{ detailRecord.companyName }}</span>
        <a-divider class="border-[#262626] mt-[20px]" />
      </div>
      <h1 class="text-center mb-5">{{ activityName }}分析报告</h1>

      <!-- 一、活动基本情况 -->
      <div class="w-full px-15">
        <h2 class="my-5">一、活动基本情况</h2>

        <p>
          本次
          <span class="text-red-500">{{ activityName }}</span>
          由
          <span class="text-red-500">{{ detailRecord.companyName }}</span>
          积极组织，于
          <span class="text-red-500">{{ detailRecord.startDate }}</span>
          至
          <span class="text-red-500">{{ detailRecord.endDate }}</span>
          通过川工之家APP南充频道开展，活动持续{{ detailRecord.days }}天。
        </p>
      </div>
      <!-- 二、活动效果分析 -->
      <div class="w-full px-15">
        <h2 class="my-5">二、活动效果分析</h2>
        <!-- 参与情况 -->
        <div class="w-full">
          <a-divider orientation="left"><h3>参与情况</h3></a-divider>

          <p
            >此次活动吸引了广大职工的积极参与，活动期间累计参与活动的工会组织
            <span class="text-red-500">{{ detailRecord.unionCount }}</span>
            个，参与人数
            <span class="text-red-500">{{ detailRecord.joinUser }}</span>
            人，参与人次
            <span class="text-red-500">{{ detailRecord.joinCount }}</span>
            人次，访问量
            <span class="text-red-500">{{ detailRecord.readCount }}</span>
            人次，得到了广大职工的积极响应和热情参与，提升了工会的网络影响力，通过网络平台的广泛传播，让更多人了解了南充市总工会的工作和职工的风采。
          </p>
          <ActivityAnalysis
            :sourceData="joinOption"
            class="w-full !h-[270px]"
          />
        </div>

        <!-- 奖品发放情况 -->
        <div
          class="w-full"
          v-if="detailRecord?.prizeFlag"
        >
          <a-divider orientation="left"><h3>奖品发放情况</h3></a-divider>

          <p
            >为了确保公平公正，所有奖品均通过随机抽选的方式进行分配，确保每位参与者都有机会获得奖品。本次活动预计发放
            <span class="text-red-500">{{ detailRecord.prizeCount }}</span>
            奖品，已成功发放
            <span class="text-red-500">{{ detailRecord.prizeAssignCount }}</span>
            份。通过奖品的激励让每一位参与者都能感受到活动的诚意和工会的关怀。</p
          >

          <ActivityAnalysis
            :sourceData="prizeOption"
            class="w-full !h-[270px]"
          />
        </div>

        <!-- 票券发放情况 -->
        <div
          class="w-full"
          v-if="detailRecord?.couponFlag"
        >
          <a-divider orientation="left"><h3>票券发放情况</h3></a-divider>

          <p>
            本次活动预计发放
            <span class="text-red-500">{{ detailRecord.issueCount }}</span>
            份普票票券，已成功发放
            <span class="text-red-500">{{ detailRecord.assignCount }}</span>
            份，已核销
            <span class="text-red-500">{{ detailRecord.usedCount }}</span>
            份。
          </p>

          <ActivityAnalysis
            :sourceData="prizeOption"
            class="w-full !h-[270px]"
          />
        </div>

        <!-- 新增用户趋势 -->
        <div
          class="w-full"
          v-if="unref(detailRecord)?.maxJoinUserNum > 0"
        >
          <a-divider orientation="left"><h3>新增用户趋势</h3></a-divider>

          <p>
            在活动期间
            <span class="text-red-500">{{ detailRecord.maxJoinUserDate }}</span>
            新增长用户最多（
            <span class="text-red-500">{{ detailRecord.maxJoinUserNum }}</span>
            人），这可能与活动推广的高峰期和用户活跃度有关。未来在制定活动计划时，应考虑在这些关键时间点加大宣传力度，以吸引更多的用户参与。同时，活动的持续性和用户粘性也是影响新增用户数量的重要因素，需要通过优化活动内容和增加用户互动来提高用户的持续参与度。
          </p>

          <ActivityAnalysis
            :sourceData="newUsersOption"
            class="w-full !h-[270px]"
          />
        </div>
      </div>
    </div>
    <a-float-button
      shape="circle"
      :style="{ right: '164px' }"
      @click="handleClick"
    >
      <template #icon>
        <CloudDownloadOutlined />
      </template>
    </a-float-button>
  </div>
</template>

<script lang="ts" setup>
import {
  exportActivityReport,
  getActivityReport,
  newUsersData,
  prizeData as prizeDataApi,
  statisticsByDate,
} from '@/api/activities';
import { useLoading } from '@/hooks/web/loading/useLoading';
import { onMounted, ref, unref } from 'vue';
import { useRoute } from 'vue-router';
import ActivityAnalysis from '@/views/activities/ActivityTable/ActivityAnalysis.vue';
import { defaultOption, joinOpt } from './data';
import { findIndex, isEmpty, map, max, merge } from 'lodash-es';
import logo from '@/assets/images/logo.png';
import { CloudDownloadOutlined } from '@ant-design/icons-vue';
import { downloadByUrl } from '@monorepo-yysz/utils';

const route = useRoute();

const activityName = ref<string>('');

const detailRecord = ref<Recordable>({ companyName: '' });

const joinOption = ref<Recordable>(joinOpt);

const prizeOption = ref<Recordable>({
  tooltip: {
    trigger: 'axis',
  },
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
  },
  series: [],
});

const newUsersOption = ref<Recordable>({
  tooltip: {
    trigger: 'axis',
  },
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      data: 0,
      type: 'line',
      name: '新增用户',
      smooth: true, // 设置为光滑曲线
    },
  ],
});

// down
function handleClick() {
  const chart1Flag = !isEmpty(unref(joinOption).xAxis.data);
  if (chart1Flag) {
    unref(detailRecord).chart1 = {
      categories: unref(joinOption).xAxis.data,
      seriesDatas: map(unref(joinOption).series, v => ({
        name: v.name,
        comboType: 'LINE',
        values: v.data,
      })),
    };
  }

  if (unref(detailRecord)?.prizeFlag || unref(detailRecord)?.couponFlag) {
    unref(detailRecord)[
      unref(detailRecord)?.prizeFlag ? 'chart2' : unref(detailRecord)?.couponFlag ? 'chart3' : ''
    ] = {
      categories: unref(prizeOption).xAxis.data,
      seriesDatas: map(unref(prizeOption).series, v => ({
        name: v.name,
        comboType: 'LINE',
        values: v.data,
      })),
    };
  }

  const chart4Flag = unref(detailRecord).maxJoinUserNum > 0;

  if (chart4Flag) {
    unref(detailRecord).chart4 = {
      categories: unref(newUsersOption).xAxis.data,
      seriesDatas: map(unref(newUsersOption).series, v => ({
        name: v.name,
        comboType: 'LINE',
        values: v.data,
      })),
    };
  }

  exportActivityReport({ ...unref(detailRecord), chart1Flag, chart4Flag }).then(response => {
    console.log(response);

    const url = window.URL.createObjectURL(response);
    downloadByUrl({
      url,
      fileName: `${unref(activityName) || ''}分析报告.docx`,
    });
  });
}

onMounted(async () => {
  useLoading(false);

  activityName.value = route.query.activityName as string;

  const activityId = route.query.activityId as string;

  // 基础
  detailRecord.value = await getActivityReport({ activityId });

  // 参与情况
  const joinData = (await statisticsByDate({
    activityId,
    activityStartTime:route.query.activityStartTime,
    activityEndTime:route.query.activityEndTime,
  })) as Recordable;

  joinOption.value = merge(unref(joinOption), {
    xAxis: { data: joinData.columnList },
    series: [
      { data: joinData.lotteryCountList },
      { data: joinData.readCountList },
      { data: joinData.joinCountList },
    ],
  });

  // 奖品 /票券发放情况
  if (unref(detailRecord)?.prizeFlag || unref(detailRecord)?.couponFlag) {
    const prizeData = (await prizeDataApi({
      activityId,
    })) as Recordable;

    prizeOption.value = merge(unref(prizeOption), defaultOption);

    unref(prizeOption).xAxis.data = prizeData.dataX;
    unref(prizeOption).series = prizeData.series || [];
  }
  // 新增人员
  const data = (await newUsersData({
    activityId,
  })) as Recordable;
  newUsersOption.value = merge(unref(newUsersOption), defaultOption);

  unref(newUsersOption).xAxis.data = data.dataX;
  unref(newUsersOption).series[0].data = data.dataY || [];

  unref(detailRecord).maxJoinUserNum = max(data.dataY) || 0;
  if (unref(detailRecord).maxJoinUserNum > 0) {
    const index = findIndex(data.dataY, v => v === unref(detailRecord).maxJoinUserNum);

    unref(detailRecord).maxJoinUserDate = data.dataX[index];
  }
});
</script>

<style lang="less" module>
.report {
  :global {
    p {
      text-indent: 2rem;
      line-height: 26px;
    }
  }
}
</style>
