<template>
  <ActivityTable
    :activity-type="ActivityType.COMPETITION"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'
/*南充竞赛*/
const columnAuth = ref([
  '/competitionSignUpActivity/modify',
  '/competitionSignUpActivity/pushOrCut',
  '/competitionSignUpActivity/sum',
  '/competitionSignUpActivity/delete',
  '/competitionSignUpActivity/join',
  '/competitionSignUpActivity/link',
  '/competitionSignUpActivity/view',
  '/competitionSignUpActivity/audit',
  '/competitionSignUpActivity/comments',
  '/competitionSignUpActivity/archives',
])

const recordAuth = ref({
  modify: '/competitionSignUpActivity/modify',
  pushOrCut: '/competitionSignUpActivity/pushOrCut',
  sum: '/competitionSignUpActivity/sum',
  delete: '/competitionSignUpActivity/delete',
  link: '/competitionSignUpActivity/link',
  view: '/competitionSignUpActivity/view',
  join: '/competitionSignUpActivity/join',
  audit: '/competitionSignUpActivity/audit',
  comments:'/competitionSignUpActivity/comments',
  archives:'/competitionSignUpActivity/archives',

})

const titleAuth = ref('/competitionSignUpActivity/add')
</script>
