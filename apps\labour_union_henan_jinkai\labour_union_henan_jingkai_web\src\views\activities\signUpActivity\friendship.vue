<template>
  <ActivityTable
    :activity-type="ActivityType.FRIENDSHIP"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'

const columnAuth = ref([
  '/friendshipSignUpActivity/modify',
  '/friendshipSignUpActivity/pushOrCut',
  '/friendshipSignUpActivity/sum',
  '/friendshipSignUpActivity/delete',
  '/friendshipSignUpActivity/join',
  '/friendshipSignUpActivity/link',
  '/friendshipSignUpActivity/view',
  '/funCompetitionSignUpActivity/audit',
  '/funCompetitionSignUpActivity/comments',
  '/funCompetitionSignUpActivity/archives',
])

const recordAuth = ref({
  modify: '/friendshipSignUpActivity/modify',
  pushOrCut: '/friendshipSignUpActivity/pushOrCut',
  sum: '/friendshipSignUpActivity/sum',
  delete: '/friendshipSignUpActivity/delete',
  link: '/friendshipSignUpActivity/link',
  view: '/friendshipSignUpActivity/view',
  join: '/friendshipSignUpActivity/join',
  audit: '/friendshipSignUpActivity/audit',
  comments:'/friendshipSignUpActivity/comments',
  archives:'/friendshipSignUpActivity/archives',

})

const titleAuth = ref('/friendshipSignUpActivity/add')
</script>
