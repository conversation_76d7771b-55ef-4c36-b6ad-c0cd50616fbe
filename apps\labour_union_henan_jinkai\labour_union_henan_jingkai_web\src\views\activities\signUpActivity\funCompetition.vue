<template>
  <ActivityTable
    :activity-type="ActivityType.FUN_COMPETITION"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'
/*趣味竞赛*/
const columnAuth = ref([
  '/funCompetitionSignUpActivity/modify',
  '/funCompetitionSignUpActivity/pushOrCut',
  '/funCompetitionSignUpActivity/sum',
  '/funCompetitionSignUpActivity/delete',
  '/funCompetitionSignUpActivity/join',
  '/funCompetitionSignUpActivity/link',
  '/funCompetitionSignUpActivity/view',
  '/funCompetitionSignUpActivity/audit',
  '/funCompetitionSignUpActivity/comments',
  '/funCompetitionSignUpActivity/archives',
])

const recordAuth = ref({
  modify: '/funCompetitionSignUpActivity/modify',
  pushOrCut: '/funCompetitionSignUpActivity/pushOrCut',
  sum: '/funCompetitionSignUpActivity/sum',
  delete: '/funCompetitionSignUpActivity/delete',
  link: '/funCompetitionSignUpActivity/link',
  view: '/funCompetitionSignUpActivity/view',
  join: '/funCompetitionSignUpActivity/join',
  audit: '/funCompetitionSignUpActivity/audit',
  comments:'/funCompetitionSignUpActivity/comments',
  archives:'/funCompetitionSignUpActivity/archives',
})

const titleAuth = ref('/funCompetitionSignUpActivity/add')
</script>
