<template>
  <ActivityTable
    :activity-type="ActivityType.SIGNUP"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'

const columnAuth = ref([
  '/signUpActivity/modify',
  '/signUpActivity/pushOrCut',
  '/signUpActivity/sum',
  '/signUpActivity/delete',
  '/signUpActivity/join',
  '/signUpActivity/link',
  '/signUpActivity/view',
  '/signUpActivity/comments',
  '/signUpActivity/archives',
])

const recordAuth = ref({
  modify: '/signUpActivity/modify',
  pushOrCut: '/signUpActivity/pushOrCut',
  sum: '/signUpActivity/sum',
  delete: '/signUpActivity/delete',
  link: '/signUpActivity/link',
  view: '/signUpActivity/view',
  join: '/signUpActivity/join',
  audit: '/signUpActivity/audit',
  comments:'/signUpActivity/comments',
  archives:'/signUpActivity/archives',
})

const titleAuth = ref('/signUpActivity/add')
</script>
