<template>
  <ActivityTable
    :activity-type="ActivityType.VOLUNTEER_SERVICE"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'
/*单身联谊*/
const columnAuth = ref([
  '/volunteerServiceSignUpActivity/modify',
  '/volunteerServiceSignUpActivity/pushOrCut',
  '/volunteerServiceSignUpActivity/sum',
  '/volunteerServiceSignUpActivity/delete',
  '/volunteerServiceSignUpActivity/join',
  '/volunteerServiceSignUpActivity/link',
  '/volunteerServiceSignUpActivity/view',
  '/volunteerServiceSignUpActivity/audit',
  '/volunteerServiceSignUpActivity/comments',
  '/volunteerServiceSignUpActivity/archives',
])

const recordAuth = ref({
  modify: '/volunteerServiceSignUpActivity/modify',
  pushOrCut: '/volunteerServiceSignUpActivity/pushOrCut',
  sum: '/volunteerServiceSignUpActivity/sum',
  delete: '/volunteerServiceSignUpActivity/delete',
  link: '/volunteerServiceSignUpActivity/link',
  view: '/volunteerServiceSignUpActivity/view',
  join: '/volunteerServiceSignUpActivity/join',
  comments:'/volunteerServiceSignUpActivity/comments',
  archives:'/volunteerServiceSignUpActivity/archives',
  audit: '/volunteerServiceSignUpActivity/audit',
})

const titleAuth = ref('/volunteerServiceSignUpActivity/add')
</script>
