<template>
  <ActivityTable
    :activity-type="ActivityType.WOMEN"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue';
import { ActivityType } from '../activities.d';
import { ref } from 'vue';
/*玫瑰书香*/
const columnAuth = ref([
  '/womenSignUpActivity/modify',
  '/womenSignUpActivity/pushOrCut',
  '/womenSignUpActivity/sum',
  '/womenSignUpActivity/delete',
  '/womenSignUpActivity/join',
  '/womenSignUpActivity/link',
  '/womenSignUpActivity/view',
  '/womenSignUpActivity/audit',
  '/womenSignUpActivity/comments',
  '/womenSignUpActivity/archives',
]);

const recordAuth = ref({
  modify: '/womenSignUpActivity/modify',
  pushOrCut: '/womenSignUpActivity/pushOrCut',
  sum: '/womenSignUpActivity/sum',
  delete: '/womenSignUpActivity/delete',
  link: '/womenSignUpActivity/link',
  view: '/womenSignUpActivity/view',
  join: '/womenSignUpActivity/join',
  audit: '/womenSignUpActivity/audit',
  comments: '/womenSignUpActivity/comments',
  archives: '/womenSignUpActivity/archives',
});

const titleAuth = ref('/womenSignUpActivity/add');
</script>
