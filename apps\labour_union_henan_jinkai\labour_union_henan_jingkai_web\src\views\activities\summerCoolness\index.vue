<template>
  <ActivityTable
    :activity-type="ActivityType.SUMMER_COOLNESS"
    :titleAuth="titleAuth"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'

const columnAuth = ref([
  '/summerCoolnessActivity/modify',
  '/summerCoolnessActivity/pushOrCut',
  '/summerCoolnessActivity/sum',
  '/summerCoolnessActivity/delete',
  '/summerCoolnessActivity/audit',
  '/summerCoolnessActivity/link',
  '/summerCoolnessActivity/view',
  '/summerCoolnessActivity/comments',
  '/summerCoolnessActivity/archives',
])

const recordAuth = ref({
  modify: '/summerCoolnessActivity/modify',
  pushOrCut: '/summerCoolnessActivity/pushOrCut',
  sum: '/summerCoolnessActivity/sum',
  delete: '/summerCoolnessActivity/delete',
  audit: '/summerCoolnessActivity/audit',
  link: '/summerCoolnessActivity/link',
  view: '/summerCoolnessActivity/view',
  comments:'/summerCoolnessActivity/comments',
  archives:'/summerCoolnessActivity/archives',
})

const titleAuth = ref('/summerCoolnessActivity/add')
</script>
