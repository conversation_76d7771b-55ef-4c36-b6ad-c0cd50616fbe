<template>
  <ActivityTable
    :activity-type="ActivityType.SURVEY"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'

const columnAuth = ref([
  '/surveyActivity/modify',
  '/surveyActivity/pushOrCut',
  '/surveyActivity/sum',
  '/surveyActivity/delete',
  '/surveyActivity/audit',
  '/surveyActivity/link',
  '/surveyActivity/view',
  '/surveyActivity/comments',
  '/surveyActivity/archives',
])

const recordAuth = ref({
  modify: '/surveyActivity/modify',
  pushOrCut: '/surveyActivity/pushOrCut',
  sum: '/surveyActivity/sum',
  delete: '/surveyActivity/delete',
  audit: '/surveyActivity/audit',
  link: '/surveyActivity/link',
  view: '/surveyActivity/view',
  comments:'/surveyActivity/comments',
  archives:'/surveyActivity/archives',
})

const titleAuth = ref('/surveyActivity/add')
</script>
