<template>
  <ActivityTable
    :activity-type="ActivityType.VOTE"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '../ActivityTable/index.vue'
import { ActivityType } from '../activities.d'
import { ref } from 'vue'

const columnAuth = ref([
  '/voteActivity/modify',
  '/voteActivity/pushOrCut',
  '/voteActivity/sum',
  '/voteActivity/delete',
  '/voteActivity/audit',
  '/voteActivity/link',
  '/voteActivity/view',
  '/voteActivity/comments',
  '/voteActivity/archives',
  '/voteActivity/opus',
])

const recordAuth = ref({
  modify: '/voteActivity/modify',
  pushOrCut: '/voteActivity/pushOrCut',
  sum: '/voteActivity/sum',
  delete: '/voteActivity/delete',
  audit: '/voteActivity/audit',
  link: '/voteActivity/link',
  view: '/voteActivity/view',
  comments:'/voteActivity/comments',
  archives:'/voteActivity/archives',
  opus:'/voteActivity/opus',
})

const titleAuth = ref('/voteActivity/add')
</script>
