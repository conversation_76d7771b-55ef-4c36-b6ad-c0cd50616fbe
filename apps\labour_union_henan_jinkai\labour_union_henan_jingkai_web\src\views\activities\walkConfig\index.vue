<template>
  <ActivityTable
    :activity-type="ActivityType.WALK"
    :titleAuth="titleAuth"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
  />
</template>
<script lang="ts" setup>
import ActivityTable from '@/views/activities/ActivityTable/index.vue';
import { ActivityType } from '@/views/activities/activities.d';
import { ref } from 'vue';

const columnAuth = ref([
  '/walkConfig/modify',
  '/walkConfig/pushOrCut',
  '/walkConfig/delete',
  '/walkConfig/view',
  '/walkConfig/sum',
  '/walkConfig/number',
]);

const recordAuth = ref({
  modify: '/walkConfig/modify',
  pushOrCut: '/walkConfig/pushOrCut',
  delete: '/walkConfig/delete',
  view: '/walkConfig/view',
  sum: '/walkConfig/sum',
  number: '/walkConfig/number',
});

const titleAuth = ref('/walkConfig/add');
</script>
