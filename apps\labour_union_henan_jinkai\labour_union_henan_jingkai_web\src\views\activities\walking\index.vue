<template>
  <ActivityTable
    :activity-type="ActivityType.WALK"
    :titleAuth="titleAuth"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
  />
</template>
<script lang="ts" setup>
import ActivityTable from '@/views/activities/ActivityTable/index.vue';
import { ActivityType } from '@/views/activities/activities.d';
import { ref } from 'vue';

const columnAuth = ref([
  '/walking/modify',
  '/walking/pushOrCut',
  '/walking/delete',
  '/walking/view',
]);

const recordAuth = ref({
  modify: '/walking/modify',
  pushOrCut: '/walking/pushOrCut',
  delete: '/walking/delete',
  view: '/walking/view',
});

const titleAuth = ref('/walking/add');
</script>
