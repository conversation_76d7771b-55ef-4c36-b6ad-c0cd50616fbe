<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :mask="true"
  >
    <Tabs
      v-model:activeKey="activeKey"
      @change="handleTabs"
      :destroyInactiveTabPane="true"
    >
      <Tabs.TabPane
        key="basic"
        tab="基本信息"
        :forceRender="true"
      >
        <BasicForm
          @register="registerForm"
          :class="disabledClass"
        >
          <template #productType="{ model, field }">
            <RadioGroup
              :disabled="disabled"
              type="primary"
              v-model:value="model[field]"
              @change="changeProductType(model, field)"
              :options="dictionary.getDictionaryOpt.get('productType') as CheckboxOptionType[]"
              placeholder="请选择商品类型"
            ></RadioGroup>
          </template>
          <template #integralPayment="{ model, field }">
            <RadioGroup
              :disabled="disabled"
              type="primary"
              v-model:value="model[field]"
              :options="integralPaymentOptions"
              placeholder="请选择领取方式"
            ></RadioGroup>
          </template>

          <template #sourceType="{ model, field }">
            <!-- :disabled="disabled" -->
            <RadioGroup
              :disabled="true"
              type="primary"
              v-model:value="model[field]"
              @change="changeSourceType(model)"
              :options="[
                { label: '自主提供', value: 'provide' },
                { label: '普惠商户', value: 'inclusive' },
              ]"
              placeholder="请选择商品来源"
            ></RadioGroup>
          </template>
          <template #consumeType="{ model, field }">
            <!-- :disabled="disabled" -->
            <RadioGroup
              :disabled="true"
              type="primary"
              v-model:value="model[field]"
              @change="changeConsumeType(model)"
              :options="[
                { label: '积分', value: 'integral' },
                { label: '人民币+积分', value: 'mix' },
              ]"
              placeholder="请选择商品来源"
            ></RadioGroup>
          </template>
          <template #button="{ model, field }">
            <a-input
              type="primary"
              @click="choiceCompany(model, field)"
              :disabled="disabled"
              v-model:value="model[field]"
              placeholder="请选择核销商户"
            ></a-input>
          </template>
          <template #productButton="{ model, field }">
            <a-input
              type="primary"
              @click="choiceCompanyAndProducts(model, field)"
              :disabled="disabled"
              v-model:value="model[field]"
              placeholder="请选择商品信息"
            ></a-input>
          </template>
          <template #picOnlyOne="{ model, field }">
            <CropperForm
              :operateType="58"
              :disabled="disabled"
              :value="model[field]"
              @change="e => (model[field] = e)"
              :imgSize="690 / 518"
            >
            </CropperForm>
          </template>
          <template #pic="{ model, field }">
            <UploadSimple
              @change="info => handleImges(info, model, field)"
              :some-file="model[field]"
              :disabled="disabled"
              :operate-type="58"
              :fix="field === 'productPublicityImg' ? 5 : 1"
            >
            </UploadSimple>
          </template>
        </BasicForm>
      </Tabs.TabPane>
      <Tabs.TabPane
        key="ggxx"
        tab="规格信息"
        :forceRender="true"
      >
        <div :class="$style.lottery">
          <div>
            <BasicTable @register="registerTable">
              <template #bodyCell="{ column, record }">
                <!-- <template v-if="column.key === 'productSubName'">
                  <a-input
                    :disabled="disabled"
                    v-model:value="record.productSubName"
                    placeholder="请输入规格名称"
                  ></a-input>
                </template>
                <template v-if="column.key === 'reserveType'">
                  <Select
                    :disabled="disabled"
                    v-model:value="record.reserveType"
                    :options="[
                      { label: '有限', value: 'limited' },
                      { label: '无限', value: 'unlimited' },
                    ]"
                    placeholder="请选择库存类型"
                  ></Select>
                </template>
                <template v-if="column.key === 'reserve'">
                  <InputNumber
                    :disabled="disabled"
                    v-if="record.reserveType === 'limited'"
                    v-model:value="record.reserve"
                    placeholder="请输入库存"
                    :min="0"
                    :max="99999"
                  >
                  </InputNumber>
                  <Tooltip
                    title="无"
                    v-if="record.reserveType === 'unlimited'"
                  >
                    <span>不限</span>
                  </Tooltip>
                </template>
                <template v-if="column.key === 'nowIntegral'">
                  <InputNumber
                    :disabled="disabled"
                    v-model:value="record.nowIntegral"
                    placeholder="请输入积分"
                    :min="1"
                    :max="99999"
                  >
                  </InputNumber>
                </template> -->
                <!-- <template v-if="column.key === 'nowPrice'">
                  <InputNumber
                    :disabled="disabled || canInput"
                    v-model:value="record.nowPrice"
                    placeholder="请输入价格"
                    :precision="2"
                    :step="0.1"
                    :min="0.0"
                    :max="99999.99"
                  >
                  </InputNumber>
                </template> -->
                <!-- <template v-if="column.key === 'productSubImg'">
                  <CropperForm
                    :operateType="23"
                    :disabled="disabled"
                    :value="record.productSubImg"
                    @change="e => (record.productSubImg = e)"
                  />
                </template> -->
                <template v-if="column.key === 'action'">
                  <TableAction
                    :actions="[
                      {
                        icon: 'fluent:delete-16-filled',
                        label: '删除',
                        type: 'primary',
                        danger: true,
                        disabled: disabled,
                        onClick: handleDelete.bind(null, record),
                        // ifShow: !disabled,
                      },
                    ]"
                  ></TableAction>
                </template>
              </template>
            </BasicTable>
          </div>
          <span class="flex justify-center items-center my-5px">
            <a-button
              v-if="!disabled"
              type="primary"
              shape="round"
              class="mr-1"
              @click="handleAdd"
              >添加</a-button
            >
          </span>
        </div>
      </Tabs.TabPane>
    </Tabs>
  </BasicModal>
  <CompanysModal
    @register="registerCompanyModal"
    :canFullscreen="false"
    width="50%"
    @success="handleSuccess"
  >
  </CompanysModal>
  <AddProductsModal
    @register="registerAddProductsModal"
    :canFullscreen="false"
    width="80%"
    @success="handleSuccessChioceProducts"
  >
  </AddProductsModal>
</template>
<script lang="ts" setup>
import CompanysModal from '@/views/businessInfo/productManagement/CompanysModal.vue';
import AddProductsModal from '@/views/businessInfo/productManagement/AddProductsModal.vue';
import { ref, computed, unref, createVNode, watch } from 'vue';
import { BasicModal, useModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { CropperForm } from '@/components/Cropper';
import { modalForm } from '@/views/businessInfo/productManagement/productManagement';
import { specificationsColumns } from './data';
import { getProductsIntroduces } from '@/api/productManagement';
import { list as getSpecifications } from '@/api/birthdayProduct/specifications';
import UploadSimple from '@/views/components/upload-simple/index.vue';
import { Modal, Tooltip, Select, RadioGroup, InputNumber, Tabs } from 'ant-design-vue';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { CloseCircleFilled } from '@ant-design/icons-vue';
import { useDictionary } from '@/store/modules/dictionary';
import { CheckboxOptionType } from 'ant-design-vue/lib';
import { includes } from 'lodash-es';
import { useMessage } from '@monorepo-yysz/hooks';

const column = computed(() => {
  return specificationsColumns(unref(record)?.consumeType);
});

const emit = defineEmits(['success', 'register']);

const dictionary = useDictionary();

const { createWarningModal } = useMessage();

const integralPaymentOptions = ref([]);
const model = ref<Recordable>({});
const field = ref('');
const isUpdate = ref(true);
const canInput = ref(true);
const autoId = ref('');
const disabled = ref(false);
const record = ref();
const companyName = ref(''); //商户名称
const sourceType = ref(''); //商品来源
const sourceProductId = ref(''); //商品id
const companyId = ref(''); //companyId
const contractPhone = ref(''); //联系电话
const address = ref(''); //商户地址
const addressCoordinate = ref(''); //商户坐标
const productCoverImg = ref(''); //商品封面图
const productPublicityImg = ref(''); //商品宣传图
const productName = ref(''); //商品名称
const productType = ref(''); //商品类型
const consumeType = ref(''); //消耗
const productIntroduce = ref(''); //商品简介
const isInclusive = ref(''); //是否是普惠

//

const activeKey = ref<string>('basic');

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.productName || ''}--详情`
    : unref(isUpdate)
      ? `编辑${unref(record)?.productName || ''}`
      : '新增积分商品';
});

const form = computed(() => {
  return modalForm(unref(isInclusive));
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

//注册商品表单
const [
  registerAddProductsModal,
  { openModal: openAddProductsModal, closeModal: closeAddProductsModal },
] = useModal();

//注册商户表单
const [registerCompanyModal, { openModal: openCompanyModal, closeModal: closeCompanyModal }] =
  useModal();

//注册列表
const [registerModal, { setModalProps }] = useModalInner(async data => {
  activeKey.value = 'basic';
  await resetFields();
  isUpdate.value = !!data?.isUpdate;
  disabled.value = !!data?.disabled;
  record.value = data.record;
  autoId.value = data.record?.autoId;
  companyName.value = data.record?.companyName;
  sourceType.value = data.record?.sourceType;
  sourceProductId.value = data.record?.sourceProductId;
  companyId.value = data.record?.companyId;
  contractPhone.value = data.record?.contractPhone;
  address.value = data.record?.address;
  addressCoordinate.value = data.record?.addressCoordinate;
  isInclusive.value = data.isInclusive;
  await setProps({ disabled: unref(disabled) });

  if (unref(isUpdate)) {
    const { productPublicityImg: m } = data.record;
    await setFieldsValue({
      ...data.record,
      productPublicityImg: m ? m.split(',') : [],
    });
  }

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });
});

//注册table
const [
  registerTable,
  { insertTableDataRecord, deleteTableDataRecord, getDataSource, setTableData, reload },
] = useTable({
  rowKey: 'orderNum',
  columns: column,
  useSearchForm: false,
  showTableSetting: false,
  isCanResizeParent: true,
  bordered: true,
  api: getSpecifications,
  pagination: false,
  showIndexColumn: false,
  immediate: false,
  actionColumn: {
    title: '操作',
    width: 100,
    dataIndex: 'action',
    fixed: undefined,
  },
});

function handleImges(info, model, field) {
  const { index, filePath } = info;
  if (field === 'productPublicityImg') {
    if (!model[field]) {
      model[field] = [];
    }
    model[field][index] = filePath;
  } else {
    model[field] = filePath;
  }
}

function handleDelete(record) {
  deleteTableDataRecord(record.orderNum);
}

//添加商品规格
function handleAdd() {
  const productSubIdList = getDataSource() || [];
  if (productSubIdList.length > 9) {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '一次最多添加10种规格',
      okText: '确认',
      closable: true,
    });
  } else {
    insertTableDataRecord({ orderNum: productSubIdList ? productSubIdList.length + 1 : 0 });
  }
}

//确认商户
function handleSuccess({ record }) {
  productCoverImg.value = undefined;
  productName.value = undefined;
  productType.value = undefined;
  companyName.value = record.companyName;
  companyId.value = record.companyId;
  contractPhone.value = record.contractPhone;
  address.value = record.address;
  addressCoordinate.value = record.addressCoordinate;
  closeCompanyModal();
}

//选择好商品后
function handleSuccessChioceProducts(record) {
  companyName.value = record.mainProduct.companyName;
  companyId.value = record.mainProduct.companyId;
  sourceProductId.value = record.mainProduct.productId;
  contractPhone.value = record.mainProduct.contractPhone;
  address.value = record.mainProduct.address;
  addressCoordinate.value = record.mainProduct.addressCoordinate;
  productCoverImg.value = record.mainProduct.productCoverImg;
  consumeType.value = record.mainProduct.consumeType;
  productPublicityImg.value = record.mainProduct.productPublicityImg;
  productName.value = record.mainProduct.productName;
  productType.value = record.mainProduct.productType;
  setTableData(record.productSubIdList);
  //当商品来源是普惠商户时
  getProductsIntroduces({ productId: record.mainProduct.productId }).then(res => {
    if (res.code === 200) {
      productIntroduce.value = res.data.productIntroduce;
      model.value['productIntroduce'] = unref(productIntroduce);
    }
  });
  closeAddProductsModal();
}

//监听商品来源
function changeSourceType(m) {
  //等于自主提供(为了避免从普惠商户改变,所以要清空数据)
  if ('provide' === m['sourceType']) {
    companyName.value = undefined;
    productCoverImg.value = undefined;
    productName.value = undefined;
    productType.value = undefined;
    setTableData([]);
    unref(model)['productPublicityImg'] = undefined;
    unref(model)['productIntroduce'] = undefined;
  }
}

//商品类型
function changeProductType(m, f) {
  if ('virtual' === m[f]) {
    integralPaymentOptions.value = [{ label: '线下核销', value: '2' }];
  } else {
    integralPaymentOptions.value = dictionary.getDictionaryOpt.get(
      'integralPayment'
    ) as CheckboxOptionType[];
  }
}

//消耗类型监听
function changeConsumeType(m) {
  if (m.consumeType === 'integral') {
    canInput.value = true;
    const data = getDataSource();
    for (let i = 0; i < data.length; i++) {
      data[i].nowPrice = undefined;
    }
  } else {
    canInput.value = false;
  }
}

//选择商户
function choiceCompany(m, f) {
  if (m.sourceType === undefined) {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '请先确认商品来源!',
      okText: '确认',
      closable: true,
    });
  } else {
    openCompanyModal(true, {
      sourceType: m.sourceType,
    });
    model.value = m;
    field.value = f;
  }
}

//直接选择商品方法
function choiceCompanyAndProducts(m, f) {
  openAddProductsModal(true, {
    sourceType: m.sourceType,
  });
  model.value = m;
  field.value = f;
}

//提交表单
async function handleSubmit() {
  try {
    const dataSource = getDataSource();
    //校验基本商品信息
    const values = await validate();
    //校验规格信息
    if (dataSource.find(value => !value.productSubName)) {
      Modal.warning({
        title: '提示',
        content: '规格名称不能为空',
      });
      return false;
    } else if (dataSource.find(value => !value.productSubImg)) {
      Modal.warning({
        title: '提示',
        content: '规格封面图不能为空',
      });
      return false;
    } else if (dataSource.find(value => !value.reserveType)) {
      Modal.warning({
        title: '提示',
        content: '库存类型不能为空',
      });
      return false;
    } else if (dataSource.find(value => !value.nowIntegral)) {
      Modal.warning({
        title: '提示',
        content: '消耗积分不能为空',
      });
      return false;
    } else if (dataSource.find(value => !value.nowPrice)) {
      if (!canInput.value) {
        Modal.warning({
          title: '提示',
          content: '商品价格不能为空',
        });
        return false;
      }
    }

    values.addressCoordinate = addressCoordinate.value;
    values.companyId = companyId.value;
    values.sourceProductId = sourceProductId.value;
    values.priceInfoList = dataSource;
    const { productPublicityImg } = values;
    if (values.sourceType === 'provide') {
      values.productPublicityImg = productPublicityImg ? productPublicityImg.join(',') : '';
    } else {
      values.productPublicityImg = productPublicityImg ? productPublicityImg : '';
    }
    setModalProps({ confirmLoading: true });
    emit('success', values);
    closeCompanyModal();
  } catch (e) {
    if (includes(e.message, 'The table instance')) {
      createWarningModal({ content: '请填写规格信息！' });
      activeKey.value = 'ggxx';
    }
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

//关闭弹窗
function handleCancel() {
  setTableData([]);
}

async function handleTabs(key) {
  key === 'ggxx' &&
    (await reload?.({
      searchInfo: {
        systemQueryType: 'manage',
        productId: unref(record)?.productId,
      },
    }));
}

watch(companyName, () => {
  //不知道干嘛 先注释
  if (model.value) {
    model.value[unref(field)] = unref(companyName);
    model.value['contractPhone'] = unref(contractPhone);
    model.value['address'] = unref(address);
    model.value['productCoverImg'] = unref(productCoverImg);
    if (model.value['sourceType'] != 'provide') {
      model.value['productPublicityImg'] = unref(productPublicityImg);
    }
    // model.value['productName'] = unref(productName);
    // model.value['consumeType'] = unref(consumeType);
    // model.value['productIntroduce'] = unref(productIntroduce)
    // model.value['productType'] = unref(productType);
    model.value['sourceProductId'] = unref(sourceProductId);
  }
});
</script>
<style lang="less" module>
.lottery {
  :global {
    .ant-select,
    .ant-input-number {
      width: 100% !important;
    }

    .ant-table-body {
      height: unset !important;
      max-height: unset !important;
    }
  }
}
</style>
