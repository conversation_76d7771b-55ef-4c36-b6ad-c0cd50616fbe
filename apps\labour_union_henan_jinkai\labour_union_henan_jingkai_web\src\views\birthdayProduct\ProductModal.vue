<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSuccess"
  >
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { BasicTable, useTable } from '/@/components/Table';
import { modalColumns, columnSchemas } from './data';
import { map } from 'lodash-es';
import { noBindingProduct } from '@/api/birthdayProduct';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const title = computed(() => {
  return '新增积分商品';
});

const [registerTable, { getSelectRows, setSelectedRowKeys, getSelectRowKeys, reload }] = useTable({
  rowKey: 'productId',
  columns: modalColumns(),
  showIndexColumn: false,
  api: noBindingProduct,
  formConfig: {
    labelWidth: 120,
    schemas: columnSchemas(),
    autoSubmitOnEnter: true,
  },
  clickToRowSelect: true,
  rowSelection: {
    type: 'checkbox',
  },
  pagination: false,
  immediate: false,
  searchInfo: {
    orderBy: 'create_time',
    sortType: 'desc',
    // systemQueryType: 'manage',
    // state: 'up',
  },
  useSearchForm: true,
  bordered: true,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  record.value = data.record;

  // const { data: dataList } = await list({ pageSize: 0 });

  // setSelectedRowKeys(map(dataList, v => v.productId));
  setSelectedRowKeys([]);
  reload();

  setModalProps({ confirmLoading: false });
});

function handleSuccess() {
  emit('success', { productIdList: map(getSelectRows(), v => v.productId) });
}
</script>
