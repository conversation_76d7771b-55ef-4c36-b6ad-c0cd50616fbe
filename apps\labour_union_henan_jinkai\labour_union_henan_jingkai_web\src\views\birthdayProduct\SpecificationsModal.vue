<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
  >
    <BasicTable @register="registerTable">
      <!-- <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增商品规格
        </a-button>

        <a-button
          type="primary"
          danger
          @click="handleDelete()"
          >批量删除</a-button
        >
      </template> -->
      <template
        v-if="!isMain"
        #toolbar
      >
      <a-button
          type="primary"
          @click="handleClick"
        >
          新增商品规格
        </a-button>
        <a-button
          type="primary"
          @click="handleMoreUp"
        >
          批量上架</a-button
        >
        <a-button
          type="primary"
          :danger ="true"  
          @click="handleMoreDown"
        >
          批量下架</a-button
        >
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '上架',
              disabled: record.saleState !== 'down',
              type: 'primary',
              onClick: handleUpSpecification.bind(null, record, 'up'),
              // auth: '/difficultEmployees/choice',
            },
            {
              icon: 'carbon:task-view',
              label: '下架',
              disabled: record.saleState !== 'up',
               type: 'primary',
              danger: true,
              onClick: handleUpSpecification.bind(null, record, 'down'),
              // auth: '/difficultEmployees/choice',
            },
          ]"
        />
      </template>
    </BasicTable>
    <SpecificationsSelectModal
      @register="registerSelectModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="60%"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref ,createVNode} from 'vue';
import { useModalInner, BasicModal, useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { specificationsColumns,querySpecifications } from './data';
import { deleteLine, list, saveApi , enableOrDisable } from '@/api/birthdayProduct/specifications';
import { useMessage } from '@monorepo-yysz/hooks';
import { message, Modal } from 'ant-design-vue';
import { map } from 'lodash-es';
import SpecificationsSelectModal from './SpecificationsSelectModal.vue';
import { CloseCircleFilled } from '@ant-design/icons-vue';

const { createConfirm, createSuccessModal, createErrorModal } = useMessage();

const emit = defineEmits(['register', 'success']);
const isUpOrDown = ref();
const productId = ref('');
const isMain = ref();
const isShow = ref();
const canSubmit = ref();
const record = ref<Recordable>();

const title = computed(() => {
  return `增加${unref(record)?.productName || ''}商品规格`;
});

const column = computed(() => {
  return specificationsColumns(unref(record)?.consumeType);
});
const rowSelection = computed(() => {
  return unref(isMain) ? undefined : {
    type: 'checkbox'
  };
});
const [registerSelectModal, { openModal, closeModal }] = useModal();

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: column,
  showIndexColumn: false,
  api: list,
  // formConfig: {
  //   labelWidth: 120,
  //   schemas: specificationsSchemas(),
  //   autoSubmitOnEnter: true,
  // },
  rowSelection: rowSelection,
  clickToRowSelect: true,
  beforeFetch: params => {
    params.productId = productId.value;
    params.systemQueryType = 'manage';
    return params;
  },
  searchInfo: { systemQueryType: 'manage' },
  formConfig: {
    labelWidth: 100,
    autoSubmitOnEnter: true,
    schemas: querySpecifications(),
  },
  pagination: false,
  useSearchForm: true,
  immediate: false,
  bordered: true,
  maxHeight:420,
  actionColumn: {
    title: '操作',
    width: 170,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: 'right',
  },
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await clearSelectedRowKeys();

  if (data.record) {
    productId.value = data.record.productId;
    isMain.value = data.isMain;
    isShow.value = data.isShow;
    canSubmit.value = data.canSubmit;
    record.value = data.record
  }
  await reload();
  setModalProps({ confirmLoading: false });
});

// 新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false, record: unref(record) });
}

// 删除
function handleDelete(record?: Recordable<any>) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record ? `${record?.productName || '当前数据'}` : '选中数据'}？`,
    onOk: function () {
      deleteLine({
        autoIdList: record ? [record?.autoId] : map(getSelectRows(), v => v.autoId),
      }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功！` });
          reload();
        } else {
          createErrorModal({ content: `删除失败！${message}。` });
        }
      });
    },
  });
}

function handleSuccess(values) {
  saveApi(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `新增成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `新增失败！${message}。`,
      });
    }
  });
}

//批量上架
function handleMoreUp() {
  const rows = getSelectRows();
  
  // 检查商品是否是上架状态
  const hasDownProducts = rows.some((row) => row.saleState === 'up');
  console.log(hasDownProducts);
  if (true === hasDownProducts) {
    Modal.warning({
      title: '提示', 
      icon: createVNode(CloseCircleFilled),
      content: '选中的商品规格中有已上架的商品规格！',
      okText: '确认',
      closable: true,
    });
    return false;
  }

  isUpOrDown.value = 'up';
  handleSubmitSpeciForm();
}

//批量下架
function handleMoreDown() {
const rows = getSelectRows();

  // 检查商品是否是上架状态
  const hasDownProducts = rows.some((row) => row.saleState === 'down');
  if (true === hasDownProducts) {
    Modal.warning({
      title: '提示', 
      icon: createVNode(CloseCircleFilled),
      content: '选中的商品规格中有已下架的商品规格！',
      okText: '确认',
      closable: true,
    });
    return false;
  }

  isUpOrDown.value = 'down';
  handleSubmitSpeciForm();
}

//批量提交主商品时候操作
function handleSubmitSpeciForm() {
  const temProductSubIdList = [];
  const rows = getSelectRows();
  if (!rows || rows.length === 0) {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '请选择至少一种规格上架(下架)！',
      okText: '确认',
      closable: true,
    });
    return false;
  } else {
    for (let i = 0; i < rows.length; i++) {
      temProductSubIdList.push(rows[i].productSubId);
    }
    const upParams = {};
    if (isMain.value === true) {
      upParams.operateProductType = 'birthday';
      upParams.operateType = 'up';
    } else {
      upParams.operateProductType = 'birthdaySub';
      if (isUpOrDown.value === 'up') {
        upParams.operateType = 'up';
      } else {
        upParams.operateType = 'down';
      }
    }
    upParams.productId = productId.value;
    upParams.productSubIdList = temProductSubIdList;
    enableOrDisable(upParams).then(res => {
    if (res.code === 200) {
      message.success(upParams.operateType === 'up' ? '商品上架成功' : '商品下架成功');
      reload();
    clearSelectedRowKeys();
    } else {
      message.error(upParams.operateType === 'up' ? '商品上架失败' : '商品下架失败');
    }
  });
  }
}
//商品规格单独上下架操作
function handleUpSpecification(record, arg1) {
  const upParams = {};
  const temList = [];
  temList.push(record.productSubId);
  upParams.operateProductType = 'birthdaySub';
  upParams.operateType = arg1;
  upParams.productId = productId.value;
  upParams.productSubIdList = temList;
  enableOrDisable(upParams).then(res => {
    if (res.code === 200) {
      message.success(upParams.operateType === 'up' ? '商品上架成功' : '商品下架成功');
      reload();
    } else {
      message.error(upParams.operateType === 'up' ? '商品上架失败' : '商品下架失败');
    }
  });
}

</script>
