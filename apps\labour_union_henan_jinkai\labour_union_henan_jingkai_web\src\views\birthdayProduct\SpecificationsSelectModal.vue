<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    :canFullscreen="false"
    @ok="handleSubmit"
    v-bind="$attrs"
  >
    <BasicTable @register="registerTable"> </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '@/components/Modal';
import { useTable, BasicTable } from '@/components/Table';
import { computed, ref } from 'vue';
// import { getSpecifications } from '@/api/productManagement';
import { noBindingProductInfo } from '@/api/birthdayProduct/specifications';
import { unref } from 'vue';
import { map } from 'lodash-es';
import { querySpecificationsColumns } from './data';

const emit = defineEmits(['success', 'register']);

const record = ref<Recordable>();
const productId = ref('');
const title = computed(() => {
  return `${unref(record)?.productName || ''}商品规格`;
});

const column = computed(() => {
  return querySpecificationsColumns(unref(record)?.consumeType);
});

const [registerModal, {}] = useModalInner(async data => {
  await clearSelectedRowKeys();
  console.log('data', data);
  if (data.record) {
    record.value = data.record;
    productId.value = data.record?.productId;
  }
  //   const { data: dataList } = await list({ pageSize: 0 });

  // setSelectedRowKeys(map(dataList, v => v.productId));
  await reload();
});

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  api: noBindingProductInfo,
  columns: column,
  beforeFetch: params => {
    params.productId = productId.value;
    return params;
  },
  formConfig: {
    labelWidth: 100,
    autoSubmitOnEnter: true,
    schemas: [
      {
        field: 'productSubName',
        label: '商品规格名称',
        component: 'Input',
        colProps: { span: 8 },
        rulesMessageJoinLabel: true,
      },
    ],
  },
  rowSelection: {
    type: 'checkbox',
  },
  maxHeight: 400,
  immediate: true,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: false,
});

function handleSubmit() {
  emit(
    'success',
    map(getSelectRows(), v => ({
      birthdayProductId:unref(record)?.birthdayProductId,
      birthdayProductInfoId: v.birthdayProductInfoId,
      productId: v.productId,
      productSubId: v.productSubId,
      discountIntegral: v.discountIntegral,
      discountPrice: v.discountPrice,
      saleState:'up'
    }))
  );
}
</script>
