import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useUserStore } from '/@/store/modules/user';
import { useDictionary } from '/@/store/modules/dictionary';
import { Image, InputNumber } from 'ant-design-vue';
import { saveApi } from '@/api/birthdayProduct/specifications';
import { useMessage } from '@monorepo-yysz/hooks';

const { createErrorModal } = useMessage();

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '商品名称',
      dataIndex: 'productName',
      width: 250,
    },
    {
      title: '商户名称',
      dataIndex: 'companyName',
      width: 200,
    },
    {
      title: '商品封面',
      dataIndex: 'productCoverImg',
      width: 90,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          />
        );
      },
    },
    {
      title: '商品类型',
      dataIndex: 'productType',
      width: 90,
      customRender: ({ text }) => {
        return (
          <span>{dictionary.getDictionaryMap.get(`productType_${text}`)?.dictName || ''}</span>
        );
      },
    },
    {
      title: '商品栏目',
      dataIndex: 'integralProductColumn',
      width: 90,
      customRender: ({ text }) => {
        return (
          <span>
            {dictionary.getDictionaryMap.get(`integralProductColumn_${text}`)?.dictName || ''}
          </span>
        );
      },
    },
    {
      title: '上架状态',
      dataIndex: 'state',
      width: 90,
      customRender: ({ text, record }) => {
        const { state } = record;
        return (
          <span class={state == 'up' ? 'text-green-500' : state == 'down' ? 'text-red-500' : ''}>
            {dictionary.getDictionaryMap.get(`saleEnable_${text}`)?.dictName}
          </span>
        );
      },
    },
  ];
};

export const modalColumns = (): BasicColumn[] => {
  const userStore = useUserStore();

  return [
    {
      title: '商品名称',
      dataIndex: 'productName',
      width: 250,
    },
    {
      title: '商户名称',
      dataIndex: 'companyName',
      width: 200,
    },
    {
      title: '商品封面',
      dataIndex: 'productCoverImg',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          />
        );
      },
    },
    {
      title: '所属工会',
      dataIndex: 'provideUnionName',
      width: 200,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'productName',
      label: '商品名称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'state',
      label: '上架状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('saleEnable'),
          placeholder: '请选择上架状态',
        };
      },
    },
  ];
};

export const columnSchemas = (): FormSchema[] => {
  return [
    {
      field: 'productName',
      label: '商品名称',
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请输入商品名称',
        autocomplete: 'off',
      },
    },

    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: { span: 3 },
      defaultValue: true,
    },
  ];
};

export const specificationsColumns = (consumeType): BasicColumn[] => {
  const userStore = useUserStore();
  const dictionary = useDictionary();

  return [
    {
      title: '规格名',
      dataIndex: 'productSubName',
      width: 150,
    },
    {
      title: '规格封面',
      dataIndex: 'productSubImg',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '现积分',
      dataIndex: 'nowIntegral',
      width: 100,
    },
    {
      title: '现价(元)',
      dataIndex: 'nowPrice',
      width: 100,
    },
    {
      title: '打折积分',
      dataIndex: 'discountIntegral',
      width: 100,
      // customRender({ record }) {
      //   return (
      //     <InputNumber
      //       value={record.discountIntegral}
      //       placeholder="请输入积分数"
      //       min={0}
      //       autocomplete="off"
      //       onChange={value => {
      //         saveApi([{ ...record, discountIntegral: value }]).then(({ code, message }) => {
      //           if (code === 200) {
      //             record.discountIntegral = value;
      //           } else {
      //             createErrorModal({ content: `现价修改失败！${message}` });
      //           }
      //         });
      //       }}
      //     />
      //   );
      // },
    },
    {
      title: '打折现价(元)',
      dataIndex: 'discountPrice',
      width: 100,
      // customRender({ record }) {
      //   return (
      //     <InputNumber
      //       value={record.discountPrice}
      //       placeholder="请输入金额"
      //       min={0}
      //       disabled={consumeType === 'integral'}
      //       autocomplete="off"
      //       onChange={value => {
      //         saveApi([{ ...record, discountPrice: value }]).then(({ code, message }) => {
      //           if (code === 200) {
      //             record.discountPrice = value;
      //           } else {
      //             createErrorModal({ content: `现价修改失败！${message}` });
      //           }
      //         });
      //       }}
      //     />
      //   );
      // },
    },
    {
      title: '销售量',
      dataIndex: 'saleNum',
      width: 100,
    },
    {
      title: '上架状态',
      dataIndex: 'saleState',
      width: 100,
      customRender: ({ text, record }) => {
        const { saleState } = record;
        return (
          <span
            class={saleState == 'up' ? 'text-green-500' : saleState == 'down' ? 'text-red-500' : ''}
          >
            {dictionary.getDictionaryMap.get(`saleEnable_${text}`)?.dictName}
          </span>
        );
      },
    },
    {
      title: '库存类型',
      dataIndex: 'reserveType',
      width: 100,
      customRender: ({ text }) => {
        return (
          <span>{dictionary.getDictionaryMap.get(`reserveType_${text}`)?.dictName || ''}</span>
        );
      },
    },
    {
      title: '库存量',
      dataIndex: 'reserve',
      width: 100,
      customRender: ({ text, record }) => {
        const { reserveType } = record;
        return <span>{reserveType === 'limited' ? text : '--'}</span>;
      },
    },
  ];
};

export const specificationsSchemas = (): FormSchema[] => {
  return [
    {
      field: 'productSubName',
      label: '商品规格名称',
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请输入商品规格名称',
        autocomplete: 'off',
      },
    },
  ];
};
export const querySpecifications = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'productSubName',
      label: '商品规格名称',
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请输入商品规格名称',
        autocomplete: 'off',
      },
    },
    {
      field: 'saleState',
      label: '上架状态',
      component: 'Select',
      colProps: { span: 8 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('saleEnable'),
          placeholder: '请选择上架状态',
        };
      },
    },
  ];
};

//查询积分时候规格列表
export const querySpecificationsColumns = (consumeType): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '规格名',
      dataIndex: 'productSubName',
      width: 150,
    },
    {
      title: '规格封面',
      dataIndex: 'productSubImg',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '现积分',
      dataIndex: 'nowIntegral',
      width: 100,
    },
    {
      title: '现价(元)',
      dataIndex: 'nowPrice',
      width: 100,
    },
    {
      title: '打折积分',
      dataIndex: 'discountIntegral',
      width: 100,
      customRender({ record }) {
        return (
          <InputNumber
            value={record.discountIntegral}
            placeholder="请输入积分数"
            min={0}
            max={record.nowIntegral}
            autocomplete="off"
            onChange={value => {
              record.discountIntegral = value;
            }}
          />
        );
      },
    },
    {
      title: '打折现价(元)',
      dataIndex: 'discountPrice',
      width: 100,
      customRender({ record }) {
        return (
          <InputNumber
            value={record.discountPrice}
            placeholder="请输入金额"
            min={0}
            max={record.nowPrice}
            disabled={consumeType === 'integral'}
            autocomplete="off"
            onChange={value => {
              record.discountPrice = value;
            }}
          />
        );
      },
    },
    {
      title: '销售量',
      dataIndex: 'saleNum',
      width: 100,
    },
    {
      title: '库存类型',
      dataIndex: 'reserveType',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`reserveType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '库存量',
      dataIndex: 'reserve',
      width: 100,
      customRender: ({ text, record }) => {
        const { reserveType } = record;
        return <span>{reserveType === 'limited' ? text : '--'}</span>;
      },
    },
  ];
};
