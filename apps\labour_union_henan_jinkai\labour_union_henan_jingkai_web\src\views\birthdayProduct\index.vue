<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/birthdayProduct/add"
        >
          新增积分商品
        </a-button>

        <a-button
          type="primary"
          danger
          @click="handleDelete()"
          auth="/birthdayProduct/batchDelete"
          >批量删除</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/birthdayProduct/view',
              },
              {
                icon: 'material-symbols:article-outline',
                label: '商品规格',
                type: 'default',
                onClick: handleSpecifications.bind(null, record),
                auth: '/birthdayProduct/price',
              },
              {
                icon: 'carbon:cut-out',
                label: '上架',
                type: 'primary',
                onClick: handleUpOrDownProduct.bind(null, record, 'up'),
                ifShow: record.state === 'down',
                disabled: record.state !== 'down',
                auth: '/birthdayProduct/up',
              },
              {
                icon: 'bx:log-out-circle',
                label: '下架',
                type: 'primary',
                danger: true,
                // onClick: handleUpOrDownProduct.bind(null, record, 'down'),
                onClick: handleDownProduct.bind(null, record),
                ifShow: record.state === 'up',
                auth: '/birthdayProduct/down',
                disabled: record.state !== 'up',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/birthdayProduct/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ProductModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="55%"
    />
    <ProductManagementModal
      @register="registerProductModal"
      :canFullscreen="false"
      width="70%"
    />
    <SpecificationsModal
      @register="registerSpecificationsModal"
      :canFullscreen="false"
      width="63%"
      @cancel="handleCancel"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import ProductModal from './ProductModal.vue';
import SpecificationsModal from './SpecificationsModal.vue';
import { list, deleteLine, saveApi, enableOrDisable } from '@/api/birthdayProduct';
import { useMessage } from '@monorepo-yysz/hooks';
import ProductManagementModal from './ProductManagementModal.vue';
import { getProductsIntroduces } from '@/api/productManagement';
import { map } from 'lodash-es';
import { createVNode} from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  getSpecifications
} from '@/api/productManagement';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload, getSelectRows }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  authInfo: ['/birthdayProduct/add','/birthdayProduct/batchDelete'],
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  rowSelection: {
    type: 'checkbox',
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 260,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/birthdayProduct/view',
      '/birthdayProduct/price',
      '/birthdayProduct/down',
      '/birthdayProduct/up',
      '/birthdayProduct/delete',
    ],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

const [registerProductModal, { openModal: openProduct }] = useModal();

const [registerSpecificationsModal, { openModal: openSpecifications }] = useModal();

// 新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//发布
function handleUpOrDownProduct(record, state) {
  openSpecifications(true, {
    record: record,
    isMain: true,
    isShow: false,
    canSubmit: false,
  });
  // const name = state === 'up' ? '上架' : '下架';
  // createConfirm({
  //   title: '信息',
  //   icon: createVNode(ExclamationCircleOutlined),
  //   content: `确定${name}?`,
  //   okText: '确认',
  //   cancelText: '取消',
  //   async onOk() {
  //     try {
  //       return await new Promise<void>(resolve => {
  //         enableOrDisableProduct({ autoId: record.autoId, state }).then(res => {
  //           if (res.code === 200) {
  //             createSuccessModal({ content: `${name}成功` });
  //           } else {
  //             createErrorModal({ content: `${name}失败,${res?.message}` });
  //           }
  //           reload();
  //           resolve();
  //         });
  //       });
  //     } catch {
  //       return console.log('Oops errors!');
  //     }
  //   },
  // });
}

// 详情
function handleView(record: Recordable<any>) {
  getProductsIntroduces({ productId: record.productId }).then(res => {
    if (res.code === 200) {
      record.productIntroduce = res.data.productIntroduce;
      openProduct(true, {
        record: record,
        isUpdate: true,
        disabled: true,
      });
    }
  });
}

// 删除
function handleDelete(record?: Recordable<any>) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record ? `${record?.productName || '当前数据'}` : '选中数据'}？`,
    onOk: function () {
      deleteLine({
        productIdList: record ? [record?.productId] : map(getSelectRows(), v => v.productId),
      }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功！` });
          reload();
        } else {
          createErrorModal({ content: `删除失败！${message}。` });
        }
      });
    },
  });
}

// 新增修改
function handleSuccess(values) {
  saveApi(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `新增成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `新增失败！${message}。`,
      });
    }
  });
}

function handleSpecifications(record: Recordable) {
  openSpecifications(true, { record });
}


//主商品下架
function handleDownProduct(record) {
  const specificationsIds = [];
  getSpecifications({ productId: record.productId, systemQueryType: 'manage',birthdayQueryFlag :true}).then(res => {
    if (res.code === 200) {
      for (let i = 0; i < res.data.length; i++) {
        specificationsIds.push(res.data[i].productSubId);
      }
    }
  });
  Modal.confirm({
    title: '主商品下架时,所有规格也会被下架',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要下架` + '"' + record.productName + '"' + '?',
    cancelText: '取消',
    okText: '确认',
    async onOk() {
      const downParams = {
        operateProductType: 'birthday',
        productId: record.productId,
        operateType: 'down',
        productSubIdList: specificationsIds,
      };
      try {
        return await new Promise<void>(resolve => {
          enableOrDisable(downParams).then(res => {
            if (res.code === 200) {
              message.success('一岁一礼主商品下架成功');
            } else {
              message.error('一岁一礼商品下架失败');
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}
// 添加取消处理函数
function handleCancel() {
  reload();
}
</script>
