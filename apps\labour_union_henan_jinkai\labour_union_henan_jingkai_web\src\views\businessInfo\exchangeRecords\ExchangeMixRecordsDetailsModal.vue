<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { mixModalFormItem } from './data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.xx || ''}详情`
      : `编辑${unref(record)?.xx || ''}`
    : '新增xx';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return mixModalFormItem();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
      productName: data.record?.snapshotVo?.transProductSnapshot?.productInfoList[0]?.priceListInfo[0]?.productName,
      productSubName: data.record?.snapshotVo?.transProductSnapshot?.productInfoList[0]?.priceListInfo[0]?.productSubName,
      productCoverImg: data.record?.snapshotVo?.transProductSnapshot?.productInfoList[0]?.productCoverImg,
      productSubImg: data.record?.snapshotVo?.transProductSnapshot?.productInfoList[0]?.priceListInfo[0]?.productSubImg,
      receiverName: data.record?.snapshotVo?.receiveSnapshot?.receiverName,
      receiverPhone: data.record?.snapshotVo?.receiveSnapshot?.receiverPhone,
      detailArea: data.record?.snapshotVo?.receiveSnapshot?.detailArea,
      detailAddress: data.record?.snapshotVo?.receiveSnapshot?.detailAddress,
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
