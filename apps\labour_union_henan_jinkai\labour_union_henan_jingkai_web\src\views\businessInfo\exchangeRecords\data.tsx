import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useUserStore } from '/@/store/modules/user';
import { useDictionary } from '/@/store/modules/dictionary';
import { userInfoSearch, productSearch } from '@/api/productManagement/integralExchangeRecord';
import { getSpecifications } from '@/api/productManagement';
import { Image, Input } from 'ant-design-vue';

export const columns = (consumeType: string): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '兑换人',
      dataIndex: 'userName',
    },
    {
      title: '商品名',
      customRender({ record }) {
        const productName = consumeType === 'integral' ? record?.productName : record?.snapshotVo?.transProductSnapshot?.productInfoList[0]?.priceListInfo[0]?.productName;
        return <span title={productName}>{productName}</span>;
      },
    },
    {
      title: '规格名',
      customRender({ record }) {
        const productSubName = consumeType === 'integral' ? record?.productSubName : record?.snapshotVo?.transProductSnapshot?.productInfoList[0]?.priceListInfo[0]?.productSubName;
        return <span title={productSubName}>{productSubName}</span>;
      },
    },
    {
      title: '兑换时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '规格封面',
      width: 100,
      customRender: ({ record }) => {
        const text = consumeType === 'integral' ? record?.productSubImg : record?.snapshotVo?.transProductSnapshot?.productInfoList[0]?.priceListInfo[0]?.productSubImg;
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '领取方式',
      dataIndex: 'integralPayment',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`integralPayment_${text}`)?.dictName || '线上发货';
        return <span title={name}>{name}</span>;
      },
      width: 150,
    },
    {
      title: '状态',
      dataIndex: '',
      customRender({ record }) {
        let name = '';
        if (consumeType === 'integral') {
             if ('1' === record?.integralPayment) {
          name =
            dictionary.getDictionaryMap.get(`transOrderState_${record?.deliveryStatus}`)
              ?.dictName || '';
        } else {
          name = dictionary.getDictionaryMap.get(`qrCodeState_${record?.state}`)?.dictName || '';
        }
        } else {
          name =
          dictionary.getDictionaryMap.get(`transOrderState_${record?.orderState}`)?.dictName || '';
        }
        return <span title={name}>{name}</span>;
      },
      width: 150,
    },
  ];
};

export const modalColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '',
      dataIndex: '',
    },
    {
      title: '',
      dataIndex: '',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (consumeType: string): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: 'userId',
      label: '兑换人',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          api: userInfoSearch,
          resultField: 'data',
          params: { consumeType },
          alwaysLoad: true,
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.userName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'userName', value: 'userId' },
        };
      },
    },
    {
      field: 'productId',
      label: '商品名',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          api: productSearch,
          resultField: 'data',
          params: { consumeType },
          alwaysLoad: true,
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.productName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'productName', value: 'productId' },
        };
      },
    },
    // {
    //   field: 'productSubId',
    //   label: '规格名',
    //   component: 'ApiSelect',
    //   colProps: { span: 6 },
    //   rulesMessageJoinLabel: true,
    //   componentProps: ({ formActionType }) => {
    //     return {
    //       api: getSpecifications,
    //       resultField: 'data',
    //       params: { productId: productId ? productId : '', systemQueryType: 'manage' },
    //       alwaysLoad: true,
    //       immediate: true,
    //       showSearch: true,
    //       filterOption: (input: string, option: any) => {
    //         return option.productSubName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    //       },
    //       fieldNames: { label: 'productSubName', value: 'productSubId' },
    //     };
    //   },
    // },

    // {
    //   field: '1' === integralPayment ? 'deliveryStatus' : 'state',
    //   label: '状态',
    //   colProps: { span: 6 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   componentProps: function () {
    //     return {
    //       options:
    //         '1' === integralPayment
    //           ? [
    //               { label: '待发货', value: 'deliver' },
    //               { label: '待收货', value: 'receive' },
    //               { label: '交易成功', value: 'over' },
    //             ]
    //           : dictionary.getDictionaryOpt.get('qrCodeState'),
    //     };
    //   },
    // },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: '',
      label: '',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get(''),
        };
      },
    },
  ];
};

export const columnSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: '',
      label: '',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get(''),
        };
      },
    },
  ];
};

export const mixModalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();
  return [
    {
      field: '',
      label: '兑换信息',
      component: 'Divider',
    },
    {
      field: 'userName',
      label: '兑换人',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'createTime',
      label: '兑换时间',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'productName',
      label: '商品名',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'productSubName',
      label: '规格名',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
    },
    {
      field: 'productCoverImg',
      label: '商品封面图',
      colProps: { span: 12 },

      component: 'CropperForm',
    },
    {
      field: 'productSubImg',
      label: '规格封面',
      colProps: { span: 12 },

      component: 'CropperForm',
    },
    {
      field: 'orderState',
      label: '状态',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options:
            dictionary.getDictionaryOpt.get('transOrderState'),
        };
      },
    },
    {
      field: '',
      label: '收货信息',
      component: 'Divider',
    },
    {
      field: 'receiverName',
      label: '收货人姓名',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'receiverPhone',
      label: '收货人电话',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
    },
    {
      field: 'detailArea',
      label: '所在地区',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
    },
    {
      field: 'detailAddress',
      label: '详细地址',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '发货信息',
      component: 'Divider',
    },
    {
      field: 'transportName',
      label: '物流公司',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 50,
        showCount: true,
      },
    },
    {
      field: 'transportNumber',
      label: '物流单号',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 50,
        showCount: true,
      },
    },
  ];
};