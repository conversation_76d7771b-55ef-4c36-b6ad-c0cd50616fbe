<template>
  <div>
    <Tabs v-model:active-key="activeKey" type="card" :destroyInactiveTabPane="false" @change="tableChange"
      v-show="false">
      <TabPane key="integral" tab="积分-兑换记录" />
      <TabPane key="mix" tab="人民币+积分-兑换记录" />
    </Tabs>

    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <ExchangeRecordsDetailsModal @register="registerExchangeRecordsDetailsModal" @success="handleSuccess"
      :can-fullscreen="false" width="50%" />
    <ExchangeMixRecordsDetailsModal @register="registerMixExchangeRecordsDetailsModal" @success="handleSuccess"
      :can-fullscreen="false" width="50%" />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { columns, formSchemas } from './data';
import { useMessage } from '@monorepo-yysz/hooks';
import ExchangeRecordsDetailsModal from '../productManagement/ExchangeRecordsDetailsModal.vue';
import ExchangeMixRecordsDetailsModal from './ExchangeMixRecordsDetailsModal.vue';

import {
  list,
  findOrderVoList,
  view,
  deleteLine,
  saveOrUpdate,
} from '@/api/productManagement/integralExchangeRecord';
import { Tabs, TabPane, Select, Spin } from 'ant-design-vue'
import { computed, onMounted, ref, unref } from 'vue';

//积分
const integral = ref<string>('integral')
//人民币+积分
const mix = ref<string>('mix')

//tab 切换
const activeKey = ref<string>(unref(integral))

const schemas = computed(() => {
  return formSchemas(unref(activeKey));
});

const newsColumns = computed(() => {
  return columns(unref(activeKey));
});

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload, setProps }] = useTable({
  rowKey: 'autoId',
  columns: newsColumns,
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: schemas,
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  // searchInfo: {
  //   orderBy: "a.create_time",
  //   sortType: "desc"
  // },
  useSearchForm: true,
  bordered: true,
  immediate: false,
  actionColumn: {
    title: '操作',
    width: 150,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerExchangeRecordsDetailsModal, { openModal, closeModal }] = useModal();
const [registerMixExchangeRecordsDetailsModal, { openModal: openMixModal, closeModal: closeMixModal }] = useModal();


async function tableChange(pagination) {
  switch (pagination) {
    case unref(integral):
      setProps({
        api: list,
        searchInfo: {
          orderBy: "a.create_time",
          sortType: "desc"
        }
      })
      break
    case unref(mix):
      setProps({
        api: findOrderVoList,
        searchInfo: {
          orderSource: "IntegralShoppingCenter"
        }
      })
      break
  }
  reload()
}

onMounted(() => {
  tableChange(unref(integral))
})

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record) {
  view({ ...record }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

//详情
function handleView(record) {
  switch (unref(activeKey)) {
    case unref(integral):
      view({ recordId: record?.recordId }).then(({ data }) => {
        openModal(true, {
          isUpdate: true,
          disabled: true,
          record: { ...data, userName: record?.userName },
        });
      });
      break
    case unref(mix):
      openMixModal(true, {
        isUpdate: true,
        disabled: true,
        record: record,
      });
      break
  }
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.title}`,
    onOk: function () {
      deleteLine({ ...record }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ values, isUpdate }) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
