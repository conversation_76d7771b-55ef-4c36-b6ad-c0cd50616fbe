<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="true"
    :canFullscreen="false"
    @cancel="handleCancel"
    @ok="handleAddProductsForm"
  >
    <!-- <div :class="$style['industry-set']"> -->
      <div >
      <Row>
        <Col
          :span="4"
          class="!p-16px tree-col"
        >
          <BasicTree
            :search="true"
            expandOnSearch
            showLine
            v-model:selectedKeys="selectedKeys"
            :treeData="treeData"
            :fieldNames="{ title: 'productName', key: 'productId' }"
          />
        </Col>
        <Col :span="20">
          <BasicTable
            @register="registerTable"
            :clickToRowSelect="false"
          >
            <template #action="{ record }">
              <TableAction
                :actions="[
                  {
                    icon: 'carbon:task-view',
                    label: '详情',
                    type: 'default',
                    onClick: handleDetails.bind(null, record),
                    auth: '/liveRecord/view',
                  },
                ]"
              />
            </template>
          </BasicTable>
        </Col>
      </Row>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicTable, TableAction, useTable } from '@/components/Table';
import { treeProductList } from '@/api/productManagement'; //查询主商品
import { getSpecifications } from '@/api/productManagement'; //查询商品规格
import {
  querySpecifications,
  querypuhuiColumns,
} from '@/views/businessInfo/productManagement/productManagement';
import { Col, Modal, Row } from 'ant-design-vue';
import { TreeDataItem } from 'ant-design-vue/lib/tree';
import { BasicTree } from '@/components/Tree';
import { computed, createVNode, nextTick, onMounted, reactive, ref, unref, watch } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { CloseCircleFilled } from '@ant-design/icons-vue';

const title = computed(() => {
  return '商品信息';
});

const treeData = ref<TreeDataItem[]>([]);

const selectedKeys = ref<string[] | number[]>([]);
const companyId = ref('');

const column = computed(() => {
  return querypuhuiColumns();
});

const emit = defineEmits(['success', 'register']);
const params = reactive({
  systemQueryType: 'manage',
  productId: unref(selectedKeys)[0] || undefined,
});

const [registerModal, { closeModal: closeModal }] = useModalInner(async data => {
  if (data.record) {
    companyId.value = data.record.companyId;
  }
  await initTree();
});

const [registerTable, { reload, getSelectRows,setSelectedRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: unref(column),
  showIndexColumn: false,
  api: getSpecifications,
  formConfig: {
    labelWidth: 120,
    schemas: querySpecifications(),
    autoSubmitOnEnter: true,
  },
  searchInfo: params,
  // authInfo: props.toolbarAuth,
  useSearchForm: true,
  immediate: false,
  bordered: true,
  maxHeight: 415,
  actionColumn: {
    ifShow: false,
    title: '操作',
    // width: 180,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
    // auth: [props.columnAuth.modify, props.columnAuth.delete],
  },
  rowSelection: {
    type: 'checkbox',
  },
});

watch(
  selectedKeys,
  newVal => {
    params.productId = newVal[0] || undefined;
    nextTick().then(() => {
      reload();
    });
    //重新选择商品时 清空选择的规格信息
    setSelectedRows([])
  },
  { deep: true }
);

//查询主商品
async function initTree() {
  treeData.value = (await treeProductList({ pageSize: 0, systemQueryType: 'manage' })) || [];
  unref(treeData) &&
    unref(treeData).length > 0 &&
    (selectedKeys.value = [unref(treeData.value[0].productId)]);
}

//提交选择好商品后提交
function handleAddProductsForm() {
  const rows = getSelectRows();
  if (!rows || rows.length === 0) {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '请选择至少一种需要上架的商品！',
      okText: '确认',
      closable: true,
    });
    return false;
  } else {
    const record = {};
    for (let i = 0; i < treeData.value.length; i++) {
      if (selectedKeys.value[0] === treeData.value[i].productId) {
        record.mainProduct = treeData.value[i];
      }
    }
    //该处的规格是查询普惠商品出来的 需要将autoId 置为null 
    rows.forEach(item => {
  item.autoId = null;
});
    record.productSubIdList = rows;
    console.log(record, 'record');
    
    emit('success', record);
    clearSelectedRowKeys();
  }
}

//关闭时执行
function handleCancel() {
  closeModal();
}
</script>

<style lang="less" module>
.industry-set {
  :global {
    .tree-col {
      .scrollbar__wrap {
        margin-bottom: 0 !important;
      }

      .ant-tree {
        height: 78vh;
        width: 100%;
        overflow: auto;

        .ant-tree-list-holder-inner {
          padding-top: 10px !important;

          .ant-tree-treenode {
            margin-bottom: 5px !important;

            .anticon {
              @apply text-blue-500 ! text-23px;
            }
          }
        }
      }
    }
  }
}
</style>
