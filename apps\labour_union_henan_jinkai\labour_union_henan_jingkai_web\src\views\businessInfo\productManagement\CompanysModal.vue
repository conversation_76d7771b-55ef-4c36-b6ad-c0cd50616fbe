<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :canFullscreen="false"
    @cancel="handleCancel"
  >
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '选择',
              type: 'default',
              onClick: handleChoiceCompany.bind(null, record),
              ifShow: !show,
            },
          ]"
        />
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '@/components/Modal';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { computed, ref } from 'vue';
import { useUserStore } from '@/store/modules/user';
import { findList } from '@/api/productManagement';
import {
  queryCompanyList,
  queryCompanyParams,
} from '@/views/businessInfo/productManagement/productManagement';
const userStore = useUserStore();

const emit = defineEmits(['success', 'register']);

const sourceType = ref(''); //商品来源
const show = ref(false); //是否展示
const title = computed(() => {
  return `核销商户选择`;
});

const [registerModal, { closeModal: closeModal }] = useModalInner(async date => {
  sourceType.value = date?.sourceType;
  if (date?.sourceType === 'inclusive') {
    show.value = true;
  } else {
    show.value = false;
  }
  await clearSelectedRowKeys();
  await reload();
});

const [registerTable, { reload, getForm, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  api: findList,
  columns: queryCompanyList(),
  beforeFetch: params => {
    params.companyType = 'merchant';
    params.pid = 0;
    return params;
  },
  formConfig: {
    labelWidth: 90,
    autoSubmitOnEnter: true,
    schemas: queryCompanyParams(),
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
    // auth: ['/difficultEmployees/choice']
  },
  maxHeight: 400,
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
});

//选择按钮操作
function handleChoiceCompany(record) {
  emit('success', { record: record });
}

//关闭时执行
function handleCancel() {
  getForm()?.resetFields();
}
</script>

<style scoped></style>
