<template>
  <BasicModal @register="registerModal" v-bind="$attrs" :title="title" @ok="handleSubmit">
    <BasicTable @register="registerTable" :clickToRowSelect="true" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { BasicTable, useTable } from '@/components/Table';
import {
  queryCompanyList,
  queryCompanyParams,
} from '@/views/businessInfo/productManagement/productManagement';
import { findList } from '@/api/productManagement';
// import { isEmpty } from 'lodash-es';

const props = defineProps({
  parentAutoId: { type: Number, default: undefined },
  name: { type: String, default: '选择资源' },
});

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const title = computed(() => {
  return props.name;
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  record.value = data.record;
  setModalProps({ confirmLoading: false });
  await nextTick();
  await redoHeight();
  await clearSelectedRowKeys();
});

const columns = computed(() => {
  return queryCompanyList();
});

const schemas = computed(() => {
  return queryCompanyParams();
});

const [registerTable, {  reload,redoHeight, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'companyId',
  columns: columns,
  showIndexColumn: false,
  api: findList,
  formConfig: {
    labelWidth: 120,
    schemas: schemas,
    autoSubmitOnEnter: true,
    submitOnChange: true,
    showAdvancedButton: false,
  },
  beforeFetch: params => {
    params.companyType = 'merchant';
    params.pageSize = 0;
    params.pid = props.parentAutoId;
    
  },
  rowSelection: {
    type: 'checkbox',
  },
  maxHeight: 364,
  useSearchForm: true,
  bordered: true,
  immediate: false,
  pagination: false,
  // actionColumn: {
  //   title: '操作',
  //   dataIndex: 'action',
  //   fixed: undefined,
  // },
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const selected = getSelectRows();
    // if (isEmpty(selected)) {
    //   return false;
    // }
    emit('success', {
      selected
    });
    clearSelectedRowKeys()
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

defineExpose({
  reload
});
</script>
