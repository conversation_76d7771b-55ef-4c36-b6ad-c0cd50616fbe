<template>
  <div>
    <a-input
      v-model:value="strValue"
      :placeholder="placeholder"
      :title="strValue"
      :disabled="true"
    >
      <template #suffix>
        <a-button
          type="primary"
          @click="handleOpenModal"
          v-if="disabled"
        >
          {{ name }}
        </a-button>
      </template>
    </a-input>
    <CompanysModalChildModal
      ref="modalRef"
      @register="registerResource"
      @success="handleResourceSuccess"
      :can-fullscreen="false"
      :parentAutoId="parentAutoId"
      :name="name"
      width="70%"
    />
  </div>
</template>

<script lang="ts" setup>
import { useModal } from '@/components/Modal';
import CompanysModalChildModal from './CompanysModalChildModal.vue';
import { ref, watch, nextTick } from 'vue';

const props = defineProps({
  value: { type: String },
  parentAutoId: { type: Number, default: undefined },
  name: { type: String, default: '选择数据' },
  disabled: { type: Boolean, default: true },
});

const emit = defineEmits(['change']);

const strValue = ref<string>();
const placeholder = ref<string>('请' + props.name);

const [registerResource, { openModal, closeModal }] = useModal();

const modalRef = ref();

// 资源
function handleResourceSuccess({ selected }) {
  emit('change', { selected });
  closeModal();
}

// 修改打开弹窗的处理函数
async function handleOpenModal() {
  openModal(true);
  await nextTick();
  // 直接调用子组件的 reload 方法
  modalRef.value?.reload?.();
}

watch(
  () => props.value,
  () => {
    strValue.value = props.value;
  },
  { deep: true, immediate: true }
);
</script>
