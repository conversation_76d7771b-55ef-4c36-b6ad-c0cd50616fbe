<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
  >
    <BasicTable
      @register="registerTable"
      :clickToRowSelect="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              // {
              //   icon: 'fa6-solid:pen-to-square',
              //   label: '编辑',
              //   type: 'primary',
              //   onClick: handleEdit.bind(null, record),
              // },
              // {
              //   icon: 'fluent:delete-16-filled',
              //   label: '删除',
              //   type: 'primary',
              //   danger: true,
              //   onClick: handleDelete.bind(null, record),
              // },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ExchangeRecordsDetailsModal
      @register="registerExchangeRecordsDetailsModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
    <ExchangeMixRecordsDetailsModal @register="registerMixExchangeRecordsDetailsModal" @success="handleSuccess"
      :can-fullscreen="false" width="50%" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { useModalInner, BasicModal, useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { modalColumns, columnSchemas } from './productManagement';
import { useUserStore } from '@/store/modules/user';
import ExchangeRecordsDetailsModal from './ExchangeRecordsDetailsModal.vue';
import ExchangeMixRecordsDetailsModal from '../exchangeRecords/ExchangeMixRecordsDetailsModal.vue';
import {
  list,
  view,
  deleteLine,
  saveOrUpdate,
  findOrderVoList
} from '@/api/productManagement/integralExchangeRecord';
import { useMessage } from '@monorepo-yysz/hooks';

defineEmits(['register']);

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const userStore = useUserStore();

//积分
const integral = ref<string>('integral')
//人民币+积分
const mix = ref<string>('mix')

const record = ref<Recordable>();

const title = computed(() => {
  return `${unref(record)?.productName || ''}--兑换记录`;
});

const schema = computed(() => {
  return columnSchemas(unref(record)?.productId, unref(record)?.integralPayment,unref(record)?.consumeType);
});

const columns = computed(() => {
  return modalColumns(unref(record)?.consumeType);
});

const [registerTable, { reload,setProps }] = useTable({
  rowKey: 'autoId',
  columns: columns,
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: schema,
    autoSubmitOnEnter: true,
  },
  beforeFetch(params) {
    params.productId = unref(record)?.productId;
    return params;
  },
  // searchInfo: {
  //   orderBy: "a.create_time",
  //   sortType: "desc"
  // },
  maxHeight: 410,
  immediate: false,
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerExchangeRecordsDetailsModal, { openModal: openModal, closeModal: closeModal }] =
  useModal();
  const [registerMixExchangeRecordsDetailsModal, { openModal: openMixModal, closeModal: closeMixModal }] = useModal();

const [registerModal, { setModalProps }] = useModalInner(async data => {
  record.value = data.record;
  tableChange(unref(record)?.consumeType);
  setModalProps({ confirmLoading: false });
});

async function tableChange(pagination) {
  switch (pagination) {
    case unref(integral):
      setProps({
        api: list,
        searchInfo: {
          orderBy: "a.create_time",
          sortType: "desc"
        }
      })
      break
    case unref(mix):
      setProps({
        api: findOrderVoList,
        searchInfo: {
          orderSource: "IntegralShoppingCenter"
        }
      })
      break
  }
  reload()
}

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record) {
  view({ ...record }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

//详情
function handleView(record) {
  switch (unref(record)?.consumeType) {
    case unref(integral):
      view({ recordId: record?.recordId }).then(({ data }) => {
        openModal(true, {
          isUpdate: true,
          disabled: true,
          record: { ...data, userName: record?.userName },
        });
      });
      break
    case unref(mix):
      openMixModal(true, {
        isUpdate: true,
        disabled: true,
        record: record,
      });
      break
  }
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.title}`,
    onOk: function () {
      deleteLine({ ...record }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ values, isUpdate }) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
