<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicTable
      @register="registerTable"
      :clickToRowSelect="true"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { BasicTable, useTable } from '@/components/Table';
import { modalFormItemNews, newsFormSchema } from './data';
import { getNewsList } from '@/api/news';
import { isArray, isEmpty } from 'lodash-es';

const props = defineProps({
  externalLink: { type: String, default: undefined },
  name: { type: String,default:'选择资源' },
});

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const title = computed(() => {
  return props.name;
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await nextTick();
  redoHeight();
  clearSelectedRowKeys();
  record.value = data.record;
  setModalProps({ confirmLoading: false });
});


const columns = computed(() => {
  return modalFormItemNews();
});

const schemas = computed(() => {
  return newsFormSchema();
});

const [registerTable, { redoHeight, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns:columns,
  showIndexColumn: false,
  api: getNewsList,
  formConfig: {
    labelWidth: 120,
    schemas: schemas,
    autoSubmitOnEnter: true,
    submitOnChange: false,
    showAdvancedButton: false,
  },
  beforeFetch: params => {
    params.newsPublishStatus = '10'
    return params;
  },
  rowSelection: {
    type: 'radio',
  },
  maxHeight: 364,
  useSearchForm: true,
  bordered: true,
  // actionColumn: {
  //   title: '操作',
  //   dataIndex: 'action',
  //   fixed: undefined,
  // },
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const selected = getSelectRows();

    if (isEmpty(selected)) {
      return false;
    }

    emit('success', {
      ...getSelectRows()[0],
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
