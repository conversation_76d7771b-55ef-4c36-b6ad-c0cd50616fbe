<template>
  <div>
    <a-input
      v-model:value="strValue"
      :placeholder="placeholder"
      :title="strValue"
      :disabled="true"
    >
      <template #suffix>
        <a-button
          type="primary"
          v-if="disabled"
          @click="openModal(true)"
        >
          {{ name }}
        </a-button>
      </template>
    </a-input>
    <ResourcesModal
      @register="registerResource"
      @success="handleResourceSuccess"
      :can-fullscreen="false"
      :externalLink="externalLink"
      :name="name"
      width="70%"
    />
  </div>
</template>

<script lang="ts" setup>
import { useModal } from '@/components/Modal';
import ResourcesModal from './ResourcesModal.vue';
import { ref, watch } from 'vue';

const props = defineProps({
  value: { type: String },
  externalLink: { type: String, default: undefined },
  name: { type: String, default: '选择资源' },
  disabled: { type: Boolean, default: true },
});

const emit = defineEmits(['change']);
const strValue = ref<string>();
const placeholder = ref<string>('请' + props.name);

const [registerResource, { openModal, closeModal }] = useModal();

// 资源
function handleResourceSuccess({ newsTitle, newsId }) {
  strValue.value = newsTitle;
  emit('change', { newsTitle, newsId });

  closeModal();
}

watch(
  () => props.value,
  () => {
    strValue.value = props.value;
  },
  { deep: true, immediate: true }
);
</script>
