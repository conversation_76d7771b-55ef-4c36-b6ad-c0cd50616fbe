import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
import { Image } from 'ant-design-vue';
import { findVoList } from '@/api/liveType/liveType';
import { nextTick } from 'vue';
import { getIsHardWorkerList } from '@/api/educationAid/hardWorkerAudit';
import { getNzOrZdyTree } from '@/api/messageSend';
import { list } from '@/api/system/userLabel';
import { unionNextLevel } from '@/api';
import { uploadApi } from '@/api/sys/upload';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { validatePositiveInteger, validateZeroPositiveInteger } from '@monorepo-yysz/utils';
import dayjs from 'dayjs';
import { filter, cloneDeep } from 'lodash-es';
import { maxNumber } from '@/api/commonMessage/homeLaunch';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '标题',
      dataIndex: 'title',
    },

    {
      title: '图片',
      dataIndex: 'launchPicUrl',
      width: 200,
      customRender: ({ record }) => {
        return record.launchPicUrl ? (
          <Image
            src={userStore.getPrefix + record.launchPicUrl}
            width={40}
            height={40}
          />
        ) : (
          ''
        );
      },
    },

    // {
    //   title: '消息类型',
    //   dataIndex: 'mesType',
    //   customRender: ({ text }) => {
    //     return <span>{dictionary.getDictionaryMap.get(`newIndexMesType_${text}`)?.dictName}</span>;
    //   },
    // },

    {
      title: '接收人类型',
      dataIndex: 'receivePersonType',
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`indexUserType_${text}`)?.dictName}</span>;
      },
    },

    {
      title: '推送次数',
      dataIndex: 'launchLimitNum',
      width: 80,
      customRender({ text, record }) {
        const name = record.launchLimit === 'n' ? '无限制' : text;
        return <span title={name}>{name}</span>;
      },
    },

    {
      title: '状态',
      dataIndex: 'launchStatus',
      width: 80,
      customRender({ text }) {
        const name = text === 'y' ? '启用' : '禁用';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '排序',
      dataIndex: 'sort',
    },
    // {
    //     title: '显示时间',
    //     dataIndex: 'picShowTime',
    //     width: 80,
    //     customRender({record}) {
    //         return <span>{record.picShowTime}秒</span>
    //     },
    // },

    {
      title: '所属工会',
      dataIndex: 'companyName',
    },

    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      format: 'date|YYYY-MM-DD HH:mm',
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  return [
    {
      field: 'title',
      label: '标题',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },

    // {
    //   field: 'mesType',
    //   label: '消息类型',
    //   colProps: { span: 5 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   componentProps: function () {
    //     return {
    //       options: dictionary.getDictionaryOpt.get('newIndexMesType'),
    //     };
    //   },
    // },

    {
      field: 'receivePersonType',
      label: '接收人类型',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('indexUserType'),
        };
      },
    },

    {
      field: 'launchStatus',
      label: '状态',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: [
          { label: '启用', value: 'y' },
          { label: '禁用', value: 'n' },
        ],
      },
    },
  ];
};
export const modalFormItem = (receivePersonType: String, isUpdate: boolean): FormSchema[] => {
  // export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  var companyId = userStore.getUserInfo.companyId;
  return [
    {
      field: 'title',
      label: '标题',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        placeholder: '请输入推送标题',
        autocomplete: 'off',
        maxlength: 50,
        showCount: true,
      },
    },

    {
      field: 'sort',
      label: '排序',
      colProps: { span: 12 },
      component: 'InputNumber',
      rules: [{ required: true, validator: validatePositiveInteger, trigger: ['change', 'blur'] }],
      componentProps: {
        placeholder: '请输入排序号',
      },
    },

    // {
    //   field: 'mesType',
    //   label: '消息类型',
    //   colProps: { span: 12 },
    //   required: true,
    //   component: 'Select',
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get(`newIndexMesType`),
    //   },
    // },

    {
      field: 'receivePersonType',
      label: '接收人类型',
      colProps: { span: 24 },
      required: true,
      component: 'RadioGroup',
      slot: 'receivePersonType',
    },

    {
      field: 'receivePersonArea',
      label: '接收人区域',
      colProps: { span: 24 },
      ifShow({ values }) {
        return values.receivePersonType === 'ZCYH';
      },
      component: 'RadioGroup',
      // componentProps: {
      //     options: dictionary.getDictionaryOpt.get(`regionCode`) as RadioGroupChildOption[],
      //   //
      // },
      componentProps: ({}) => {
        let areaCode = userStore.getUserInfo.areaCode;
        let options = [] as RadioGroupChildOption[];
        if (areaCode === '511300') {
          options = dictionary.getDictionaryOpt.get(`regionCode`) as RadioGroupChildOption[];
        } else {
          options = filter(
            cloneDeep(dictionary.getDictionaryOpt.get('regionCode')),
            v => v.value === areaCode
          ) as RadioGroupChildOption[];
        }
        return {
          options,
        };
      },
    },

    {
      field: 'customLabelCode',
      component: 'ApiSelect',
      label: '标签选择',
      colProps: { span: 12 },
      ifShow({ values }) {
        return values.receivePersonType === 'ZDYBQ';
      },
      itemProps: {
        autoLink: true,
      },
      componentProps: ({ formActionType }) => {
        return {
          mode: 'multiple',
          placeholder: '请选择标签',
          api: list,
          resultField: 'data',
          params: {
            pageSize: 0,
            labelState: 'y',
            labelType: 'custom',
          },
          // alwaysLoad: true,
          // immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'labelName', value: 'labelCode' },
        };
      },
    },
    {
      field: 'builtInLabelCode',
      component: 'ApiSelect',
      label: '标签选择',
      colProps: { span: 12 },
      ifShow({ values }) {
        return values.receivePersonType === 'NZ';
      },
      itemProps: {
        autoLink: true,
      },
      componentProps: ({ formActionType }) => {
        return {
          mode: 'multiple',
          placeholder: '请选择标签',
          api: list,
          resultField: 'data',
          params: {
            pageSize: 0,
            labelState: 'y',
            labelType: 'builtIn',
          },
          // alwaysLoad: true,
          // immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'labelName', value: 'labelCode' },
        };
      },
    },

    {
      field: 'receiveUserDTOList',
      label: '接收人',
      component: 'ApiSelect',
      // required: true,
      ifShow({ values }) {
        return values.receivePersonType === 'GHGB';
      },
      slot: 'receiverDTOList',
    },

    //  认证会员
    {
      field: 'receiveRangeCompanyIdByMember',
      component: 'ApiSelect',
      label: '范围选择',
      colProps: { span: 12 },
      ifShow({ values }) {
        return values.receivePersonType === 'RZHY';
      },
      itemProps: {
        autoLink: true,
      },
      componentProps: ({ formActionType }) => {
        return {
          mode: 'multiple',
          placeholder: '请选择工会,非必填',
          api: unionNextLevel,
          resultField: 'data.data',
          params: {
            unionId: companyId,
          },
          // alwaysLoad: true,
          immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.c0100.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'c0100', value: 'id' },
        };
      },
    },

    {
      field: 'launchPicUrl',
      label: '图片',
      colProps: { span: 24 },
      component: 'Upload',
      helpMessage: '选择[图片]时,推荐尺寸为750*400',
      componentProps: {
        uploadParams: {
          operateType: 7,
        },
        maxSize: 10,
        maxNumber: 1,
        api: uploadApi,
        accept: ['image/*'],
      },
      required: true,
      rulesMessageJoinLabel: true,
    },

    {
      field: 'textPopupContent',
      label: '弹窗文字内容',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      ifShow({ values }) {
        return values.mesType === 'WZTC';
      },
      componentProps: {
        maxlength: 20,
        showCount: true,
      },
      rulesMessageJoinLabel: true,
    },

    {
      field: 'showBeginTime',
      label: '开始时间',
      colProps: { span: 12 },
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
      ifShow({ values }) {
        return values.mesType !== 'SRZF';
      },
      required: true,
      rulesMessageJoinLabel: true,
    },

    {
      field: 'showEndTime',
      label: '结束时间',
      colProps: { span: 12 },
      component: 'DatePicker',
      componentProps: ({ formModel }) => {
        return {
          disabledDate: current => {
            return current && current < dayjs(formModel.showBeginTime).subtract(0, 'day');
          },
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          showNow: false,
        };
      },
      ifShow({ values }) {
        return values.mesType !== 'SRZF' && values.mesType !== 'WZTC';
      },
      required: true,
      rulesMessageJoinLabel: true,
    },

    {
      field: 'launchLimit',
      label: '限制推送',
      colProps: { span: 12 },
      component: 'RadioGroup',
      ifShow({ values }) {
        return values.receivePersonType !== 'FWYH';
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`YesOrNo`) as RadioGroupChildOption[],
      },
      required: true,
    },
    {
      field: 'launchLimitNum',
      label: '限制次数',
      colProps: { span: 12 },
      component: 'InputNumber',
      ifShow({ values }) {
        return values.launchLimit === 'y' && values.receivePersonType !== 'FWYH';
      },
      componentProps: {
        max: 10,
        min: 0,
      },
      rules: [
        { required: false, validator: validateZeroPositiveInteger, trigger: ['change', 'blur'] },
      ],
      className: '!w-full',
      suffix: '注:最大推送次数 10',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'isJump',
      label: '是否外链',
      colProps: { span: 12 },
      component: 'RadioGroup',

      componentProps: {
        options: dictionary.getDictionaryOpt.get(`YesOrNo`) as RadioGroupChildOption[],
      },
      required: true,
    },

    {
      field: 'jumpType',
      label: '跳转方式',
      colProps: { span: 12 },
      component: 'RadioGroup',
      helpMessage: '选择[内置]时,页面链接配置新闻信息,选择[外部]时,页面链接配置完整可访问url',
      ifShow({ values }) {
        return values.isJump === 'y';
      },
      componentProps({ formModel }) {
        return {
          options: dictionary.getDictionaryOpt.get(`skipType`) as RadioGroupChildOption[],
          onChange: () => {
            formModel['jumpLinkUrl'] = undefined;
            formModel['dataName'] = undefined;
            formModel['dataNameActive'] = undefined;
            formModel['jumpRouter'] = undefined;
          },
        };
      },
      required: true,
    },
    {
      field: 'jumpRemind',
      label: '跳转提醒',
      colProps: { span: 12 },
      ifShow({ values }) {
        return values.isJump === 'y';
      },
      component: 'RadioGroup',
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`YesOrNo`) as RadioGroupChildOption[],
      },
      required: true,
    },

    {
      field: 'isWithToken',
      label: '携带用户信息',
      colProps: { span: 12 },
      ifShow({ values }) {
        return values.isJump === 'y';
      },
      component: 'RadioGroup',
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`specialUserInfo`) as RadioGroupChildOption[],
      },
      required: true,
    },
    {
      field: 'jumpRouter',
      label: '跳转类型',
      colProps: { span: 12 },
      ifShow({ values }) {
        return values.isJump === 'y' && values.jumpType === 'builtIn';
      },
      component: 'RadioGroup',
      componentProps({ formModel }) {
        return {
          options: dictionary.getDictionaryOpt.get(`jumpRouter`) as RadioGroupChildOption[],
          onChange: () => {
            formModel['jumpLinkUrl'] = undefined;
            formModel['dataName'] = undefined;
            formModel['dataNameActive'] = undefined;
          },
        };
      },
      required: true,
    },
    {
      field: 'encryptionFlag',
      label: '是否加密用户信息',
      component: 'RadioGroup',
      colProps: {
        span: 12,
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
      },
      ifShow({ values }) {
        return values.isWithToken === 'carry';
      },
    },

    {
      field: 'jumpLinkUrl',
      label: '链接地址',
      colProps: { span: 24 },
      component: 'Input',
      ifShow({ values }) {
        return values.isJump === 'y' && 'external' === values.jumpType;
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'dataName',
      label: '链接地址',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return (
          values.isJump === 'y' && 'builtIn' === values.jumpType && 'news' === values.jumpRouter
        );
      },
      slot: 'newRecordSelect',
    },
    {
      field: 'dataNameActive',
      label: '链接地址',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return (
          values.isJump === 'y' && 'builtIn' === values.jumpType && 'active' === values.jumpRouter
        );
      },
      // },
      slot: 'activeRecordSelect',
    },
  ];
};

//观看记录页面
export const recordColumns = (): BasicColumn[] => {
  return [
    {
      title: '用户姓名',
      dataIndex: 'userName',
    },

    {
      title: '首次观看时间',
      dataIndex: 'createTime',
    },

    {
      title: '最近观看时间',
      dataIndex: 'updateTime',
    },
  ];
};

//观看记录搜索
export const recordSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '用户姓名',
      component: 'Input',
      colProps: {
        span: 8,
      },
    },
  ];
};

export const modalFormItemNews = (): BasicColumn[] => {
  return [
    {
      title: '新闻标题',
      dataIndex: 'newsTitle',
      ellipsis: true,
    },
    {
      title: '新闻栏目',
      dataIndex: 'categoryName',
      width: 200,
    },
    {
      title: '发布时间',
      dataIndex: 'publishTime',
      width: 150,
      customRender: ({ record, text }) => {
        if ('10' === record.newsPublishStatus) {
          return <span>{text}</span>;
        } else {
          return <span>{'--'}</span>;
        }
      },
    },
  ];
};

export const newsFormSchema = (): FormSchema[] => {
  return [
    {
      field: 'newsTitle',
      label: '新闻标题',
      component: 'Input',
      colProps: { span: 8 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: { span: 4 },
      defaultValue: true,
    },
  ];
};
