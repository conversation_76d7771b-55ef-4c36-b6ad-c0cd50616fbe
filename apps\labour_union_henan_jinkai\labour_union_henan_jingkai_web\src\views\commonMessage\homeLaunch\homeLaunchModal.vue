<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :can-fullscreen="false"
    :wrap-class-name="$style['recruit-modal']"
  >
    <BasicTable
      @register="registerTable"
      v-if="ifViewList"
    />

    <Row>
      <Col :span="24">
        <BasicForm
          @register="registerForm"
          :class="disabledClass"
          v-if="!ifViewList"
        >
          <template #receiverDTOList="{ model, field }">
            <a-button
              type="primary"
              v-if="!disabled"
              @click="handleGB"
              class="ml-1"
              >选择干部</a-button
            >
            <div class="w-full pt-1 pl-1">
              <span
                v-for="item in model[field]"
                class="mb-1 inline-block"
              >
                <Tag
                  color="cyan"
                  :closable="!disabled"
                  @close="handleTagClose(item)"
                  >{{ `${item.userName}` }}</Tag
                >
              </span>
            </div>
          </template>
          <template #newRecordSelect="{ model, field }">
            <ResourcesSelect
              :value="model[field]"
              externalLink="y"
              name="选择数据"
              :disabled="!disabled"
              @change="resource => handleResources(resource, model)"
            />
          </template>
          <template #activeRecordSelect="{ model, field }">
            <ResourcesSelectActive
              :value="model[field]"
              name="选择数据"
              :disabled="!disabled"
              @change="resource => handleResourcesActive(resource, model)"
            />
          </template>
          <template #receivePersonType="{ model, field }">
            <div class="mb-1">
              <RadioGroup
                v-model:value="model[field]"
                @change="handleChange(model, model[field])"
                name="radioGroup"
                :options="dictionary.getDictionaryOpt.get('indexUserType')"
              />
            </div>
          </template>
        </BasicForm>
      </Col>
    </Row>
    <ReceiveUserModal
      @register="registerGBModal"
      @success="handleReceiveSuccess"
      :checkable="false"
      :ifHomeLaunch="true"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal, useModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from './data';
import { BasicTable, useTable } from '/@/components/Table';
import { recordList } from '@/api/commonMessage/homeLaunch';
import { Col, Row, Tag, RadioGroup } from 'ant-design-vue';
import { useDictionary } from '@/store/modules/dictionary';
import ResourcesSelect from './ResourcesSelect.vue';
import ResourcesSelectActive from '../../news/channelNews/ResourcesSelect.vue';
import ReceiveUserModal from '@/views/commonMessage/messageSending/ReceiveUserModal.vue';
import { map, split, join, filter, isEmpty } from 'lodash-es';
import { find as cadreFindApi } from '@/api/cadre';
import { maxNumber } from '@/api/commonMessage/homeLaunch';

// -----------------------------工会干部相关-------------------------
const emit = defineEmits(['register', 'success', 'selectInfo']);

const tabs = ref<Recordable[]>();

const dictionary = useDictionary();

const receivePersonTypeDic = ref<Recordable[]>();

const receivePersonType = ref<String>('');

// -----------------------------页面字段----------------------------------
const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const ifViewList = ref(false);

const configTitle = ref('');

const configLaunchId = ref('');

const title = computed(() => {
  return unref(ifViewList)
    ? `${configTitle.value || ''}--观看记录`
    : unref(isUpdate)
      ? unref(disabled)
        ? `${unref(record)?.title || ''}--详情`
        : `编辑--${unref(record)?.title || ''}`
      : '新增首页推送配置';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(unref(receivePersonType), unref(isUpdate));
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps, getFieldsValue }] = useForm(
  {
    labelWidth: 120,
    schemas: form,
    showActionButtonGroup: false,
  }
);

const [registerTable] = useTable({
  rowKey: 'autoId',
  columns: [
    {
      title: '用户姓名',
      dataIndex: 'userName',
    },
    {
      title: '观看时间',
      dataIndex: 'createTime',
    },
  ],
  showIndexColumn: false,
  searchInfo: {},
  api: recordList,
  immediate: false,
  beforeFetch: params => {
    return { ...params, launchId: unref(configLaunchId) };
  },
  formConfig: {
    labelWidth: 120,
    schemas: [
      {
        field: 'userName',
        label: '用户姓名',
        colProps: { span: 8 },
        component: 'Input',
        rulesMessageJoinLabel: true,
      },
    ],
    autoSubmitOnEnter: true,
  },
  maxHeight: 420,
  useSearchForm: true,
  bordered: true,
});

const [registerGBModal, { openModal }] = useModal();

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  tabs.value = dictionary.getDictionaryOpt.get('mesUserType');

  receivePersonTypeDic.value = dictionary.getDictionaryOpt.get('indexUserType');

  record.value = data.record;

  configTitle.value = data.configTitle;

  configLaunchId.value = data.launchId;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  ifViewList.value = !!data.viewList;

  if (unref(isUpdate)) {
    const {
      showBeginTime,
      showEndTime,
      launchPicUrl,
      receiveRangeUserId,
      dataName,
      builtInLabelCode,
      customLabelCode,
      receiveRangeCompanyIdByMember,
    } = data.record;

    await setFieldsValue({
      ...data.record,
      dateTime: showBeginTime && showEndTime ? [showBeginTime, showEndTime] : undefined,
      launchPicUrl: launchPicUrl ? launchPicUrl.split(',') : [],
      dataNameActive: dataName ? dataName : undefined,
      builtInLabelCode: builtInLabelCode ? builtInLabelCode : [],
      customLabelCode: customLabelCode ? customLabelCode : [],
      receiveRangeCompanyIdByMember: receiveRangeCompanyIdByMember
        ? receiveRangeCompanyIdByMember
        : [],
    });

    !isEmpty(receiveRangeUserId) &&
      Promise.all(
        map(split(receiveRangeUserId, ','), async v => await cadreFindApi({ id: v }, true))
      ).then(async responseArr => {
        await setFieldsValue({
          receiveUserDTOList: map(responseArr, (v: Recordable) => ({
            userAccount: v.contractPhone,
            userName: v.cadreName,
            souceId: v.unionId,
            cadreId: v.cadreId,
          })),
        });
      });
  } else {
    const { data } = await maxNumber();
    setFieldsValue({ sort: data });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const {
      launchPicUrl,
      receiveUserDTOList,
      customLabelCode,
      builtInLabelCode,
      receiveRangeCompanyIdByMember,
      ...values
    } = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        launchPicUrl: launchPicUrl ? launchPicUrl.join(',') : undefined,
        customLabelCode: customLabelCode ? customLabelCode.join(',') : undefined,
        builtInLabelCode: builtInLabelCode ? builtInLabelCode.join(',') : undefined,
        receiveRangeCompanyIdByMember: receiveRangeCompanyIdByMember
          ? receiveRangeCompanyIdByMember.join(',')
          : undefined,
        receiveRangeUserId: join(
          map(receiveUserDTOList, (v: Recordable) => v.cadreId),
          ','
        ),
        mesType:'PDSX'
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

// 删除单个
async function handleTagClose(item) {
  const { receiveUserDTOList } = await validate();

  await setFieldsValue({
    receiveUserDTOList: filter(receiveUserDTOList, (v: Recordable) => v.cadreId !== item.cadreId),
  });
}

function handleGB() {
  if (unref(isUpdate)) {
    const { receiveUserDTOList } = getFieldsValue();
    openModal(true, {
      type: 'GHGB',
      contact: receiveUserDTOList,
      isUpdate: unref(isUpdate),
    });
  } else {
    openModal(true, {
      type: 'GHGB',
    });
  }
}

// 内部资源
function handleResources({ newsTitle, newsId }, model) {
  model['dataName'] = newsTitle;
  model['jumpLinkUrl'] = newsId;
}

// 内部资源
function handleResourcesActive({ activityName, activityId }, model) {
  model['dataNameActive'] = activityName;
  model['jumpLinkUrl'] = activityId;
  model['dataName'] = activityName;
}

function handleReceiveSuccess({ receiveUserDTOList }) {
  setFieldsValue({ receiveUserDTOList });
}

function handleChange(m, value) {
  receivePersonType.value = '空值';
  m.receivePersonArea = undefined;
  // 处理选中值的更改
  switch (value) {
    case 'GHGB':
      receivePersonType.value = 'GHGB';
      break;
    case 'RZHY':
      receivePersonType.value = 'RZHY';
      break;
    case 'FWYH':
    case 'ZEYH':
    case 'LMGJ':
    case 'KNZG':
    case 'XJYXTHY':
    case 'ZBDYYH':
    case 'NZ':
      break;
    default:
      break;
  }
}
</script>

<style lang="less" module>
.mes {
  :global {
    .no-pointer {
      .ant-tree-switcher_close {
        display: none !important;
      }
    }

    .ant-tree {
      height: 45vh !important;
      overflow: auto;
    }
  }
}
.recruit-modal {
  :global {
    .ant-input-number {
      width: 100% !important;
    }
  }
}
</style>
