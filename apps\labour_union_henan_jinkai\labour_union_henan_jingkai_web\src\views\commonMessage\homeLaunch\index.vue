<template>
  <div :class="$style['message-start']">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/homeLaunch/add"
        >
          新增首页推送配置
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                auth: '/homeLaunch/view',
                onClick: handleView.bind(null, record, false),
              },

              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                auth: '/homeLaunch/edit',
                ifShow: record.launchStatus === 'n',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon:
                  record.launchStatus === 'y'
                    ? 'material-symbols:lock-open'
                    : 'material-symbols:lock',
                label: record.launchStatus === 'y' ? '禁用' : '启用',
                type: 'primary',
                auth: '/homeLaunch/isEnable',
                onClick: handleConfirm.bind(null, record),
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                auth: '/homeLaunch/delete',
                onClick: handleDelete.bind(null, record),
              },
              {
                icon: 'carbon:task-view',
                label: '观看记录',
                type: 'default',
                auth: '/homeLaunch/watchRecord',
                onClick: handleView.bind(null, record, true),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <MsgStartModal
      @register="registerModal"
      @success="handleSuccess"
      width="50%"
    />

    <ViewRecordModal
      @register="registerRecordModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import MsgStartModal from './homeLaunchModal.vue';
import ViewRecordModal from './viewRecordModal.vue';
import {
  list,
  view,
  saveOrUpdate,
  saveOrUpdateByDTO,
  deleteLine,
  recordList,
} from '@/api/commonMessage/homeLaunch';
import { useMessage } from '@monorepo-yysz/hooks';
import { useUserStore } from '@/store/modules/user';

const userStore = useUserStore();
const { createConfirm, createErrorModal, createSuccessModal } = useMessage();
const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  beforeFetch: params => {
    params.orderBy = 'create_time';
    params.sortType = 'desc';
    return { ...params };
  },
  authInfo: '/homeLaunch/add',
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    actionColOptions: { span: 3 },
  },
  searchInfo: {
    companyId: userStore.getUserInfo.companyId,
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 400,
    dataIndex: 'action',

    fixed: undefined,
    auth: [
      '/homeLaunch/add',
      '/homeLaunch/view',
      '/homeLaunch/delete',
      '/homeLaunch/edit',
      '/homeLaunch/watchRecord',
    ],
    align: 'left',
    class: '!text-center',
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

//观看记录
const [registerRecordModal, { openModal: openRecordModal }] = useModal();

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record) {
  view({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

//详情
function handleView(record, flg) {
  if (flg) {
    recordList({ launchId: record.launchId }).then(({ data }) => {
      openRecordModal(true, {
        configTitle: record.title,
        launchId: record.launchId,
        record: data,
        viewList: true,
        isUpdate: false,
        disabled: true,
      });
    });
  } else {
    view({ autoId: record.autoId }).then(({ data }) => {
      openModal(true, { isUpdate: true, disabled: true, record: data });
    });
  }
}

function handleConfirm(record) {
  const name = record.launchStatus === 'y' ? '禁用' : '启用';
  createConfirm({
    iconType: 'warning',
    content: `请确定${name}--${record.title}?`,
    onOk: function () {
      saveOrUpdate({
        autoId: record.autoId,
        launchStatus: record.launchStatus === 'y' ? 'n' : 'y',
      }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({
            content: `${name}成功`,
          });
          reload();
          closeModal();
        } else {
          createErrorModal({
            content: `${name}失败! ${message}`,
          });
        }
      });
    },
  });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除--${record.title}?`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ values, isUpdate }) {
  saveOrUpdateByDTO(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>

<style lang="less" module>
.message-start {
  :global {
    .ant-image-img {
      height: 100%;
    }
  }
}
</style>
