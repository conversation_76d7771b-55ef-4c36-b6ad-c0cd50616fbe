<template>
  <BasicModal
    @register="registerModal"
    :show-ok-btn="false"
    :show-cancel-btn="false"
    v-bind="$attrs"
    :title="title"
  >
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicTable, useTable } from '@/components/Table';
import { computed, ref, unref } from 'vue';
import { recordColumns, recordSchemas } from './data';
import { recordList } from '@/api/commonMessage/homeLaunch';

const record = ref<Recordable>();

const configTitle = ref('');

const configLaunchId = ref('');

//标题名
const title = computed(() => {
  return `${configTitle.value}--观看记录`;
});

const [registerModal, {}] = useModalInner(async data => {
  await clearSelectedRowKeys();

  configLaunchId.value = data.launchId;

  record.value = data.record;

  configTitle.value = data.configTitle;

  if (data.record) {
    reload({
      searchInfo: {},
    });
  }
});

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: recordColumns(),
  beforeFetch: params => {
    params.orderBy = 'create_time';
    params.sortType = 'desc';
    return { ...params, launchId: unref(configLaunchId) };
  },
  // searchInfo: {
  //   launchId: unref(configLaunchId),
  // },
  formConfig: {
    labelWidth: 80,
    autoSubmitOnEnter: true,
    schemas: recordSchemas(),
  },
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  api: recordList,
  showIndexColumn: false,
  canResize: true,
  canColDrag: true,
  maxHeight: 420,
});
</script>

<style scoped></style>
