<template>
  <div class="!pl-2 !pt-2">
    <Tabs
      v-model:active-key="activeKey"
      type="card"
    >
      <TabPane
        key="count"
        tab="短信统计"
      >
        <div>
          <Descriptions bordered>
            <Descriptions.Item label="总条数（条）">{{ total }}</Descriptions.Item>
            <Descriptions.Item label="剩余数量（条）">{{ surplus }}</Descriptions.Item>
          </Descriptions>
          <BasicTable @register="registerTable" />
        </div>
        <!-- <div>
          <BasicTable @register="templateLog" :clickToRowSelect="false" />
        </div> -->
      </TabPane>
      <TabPane
        key="log"
        tab="短信记录"
      >
        <BasicTable @register="logTable"> </BasicTable>
      </TabPane>
    </Tabs>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { BasicTable, useTable } from '@/components/Table';
import { Tabs, Descriptions } from 'ant-design-vue';
import {
  columns,
  columnCount,
  formSchemasSending,
} from '@/views/commonMessage/messageLogg/messageLogg';
import { getSmsList, getTotal } from '@/api/messageSend';

const TabPane = Tabs.TabPane;

const activeKey = ref('count');

const dataSource = ref<Recordable[]>([]);

const total = ref(0);

const surplus = ref(0);

const [registerTable, {}] = useTable({
  columns: columns,
  showIndexColumn: false,
  dataSource: dataSource,
  pagination: false,
  useSearchForm: false,
  bordered: true,
});

// const [templateLog, { }] = useTable({
//   columns: columnsCount(),
//   showIndexColumn: false,
//   api: countType,
//   isCanResizeParent: true,
//   pagination: false,
//   useSearchForm: false,
//   bordered: true,
// })

const [logTable, {}] = useTable({
  rowKey: 'autoId',
  columns: columnCount(),
  showIndexColumn: false,
  api: getSmsList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemasSending(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
});

onMounted(async () => {
  const { messageCountList, allMessageNmu, surplusNum } = await getTotal();

  dataSource.value = messageCountList;
  surplus.value = surplusNum;
  total.value = allMessageNmu;
});
</script>
