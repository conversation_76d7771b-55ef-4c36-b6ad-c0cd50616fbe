import dayjs from 'dayjs'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'

export const columnSms = (): BasicColumn[] => {
  const dictionary = useDictionary()
  return [
    {
      title: '接收人名称',
      dataIndex: 'userName',
      width: 100,
    },
    {
      title: '短信状态',
      dataIndex: 'mesStatus',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`smsStatus_${text}`)?.dictName}</span>
      },
    },
  ]
}

export const columnSys = (): BasicColumn[] => {
  const dictionary = useDictionary()
  return [
    {
      title: '接收人名称',
      dataIndex: 'userName',
      width: 100,
    },
    {
      title: '阅读状态',
      dataIndex: 'readFlag',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`readFlag_${text}`)?.dictName}</span>
      },
    },
  ]
}

export const columns: BasicColumn[] = [
  {
    title: '消息类型',
    dataIndex: 'mesTypeName',
    width: 100,
  },
  {
    title: '使用数量（条）',
    dataIndex: 'useCount',
    width: 100,
  },
]

export const columnsCount = (): BasicColumn[] => {
  const dictionary = useDictionary()
  return [
    {
      title: '模板类型',
      dataIndex: 'smsTypeCode',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`messageType_${text}`)?.dictName}</span>
      },
    },
    {
      title: '使用量(条)',
      dataIndex: 'countType',
      width: 100,
    },
  ]
}

export const columnCount = (): BasicColumn[] => {
  const dictionary = useDictionary()

  return [
    {
      title: '消息标题',
      dataIndex: 'mesTitle',
      width: 100,
    },
    {
      title: '接收人姓名',
      dataIndex: 'userName',
      width: 100,
    },

    {
      title: '接收人手机号',
      dataIndex: 'userAccount',
      width: 100,
    },
    {
      title: '消息状态',
      dataIndex: 'mesStatus',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`smsStatus_${text}`)?.dictName}</span>
      },
    },
    {
      title: '消息内容',
      dataIndex: 'mesContent',
      width: 100,
    },
  ]
}

export const formSchemasSending = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '接收人姓名',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'userAccount',
      label: '接收人电话',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
    {
      field: 'findTime',
      label: '月份',
      component: 'MonthPicker',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
      className: '!w-full',
      componentProps: {
        valueFormat: 'YYYY-MM',
        defaultValue: dayjs(),
      },
    },
  ]
}
