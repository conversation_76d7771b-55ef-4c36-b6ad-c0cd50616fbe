<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref, computed, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { appForm } from './messageSending';

const emit = defineEmits(['success', 'register']);

const record = ref<Recordable>();

const title = computed(() => {
  return `${unref(record)?.mesTitle || ''}消息审核`;
});

const [registerForm, { resetFields, validate }] = useForm({
  labelWidth: 120,
  schemas: appForm,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  setModalProps({
    confirmLoading: false,
  });

  record.value = data.record;
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });
    // TODO custom api
    emit('success', {
      values: { ...values, id: unref(record)?.autoId },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
