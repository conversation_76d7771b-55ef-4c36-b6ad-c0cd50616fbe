<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    v-model="registerModal"
  >
    <Tabs
      :activeKey="activeKey"
      @change="changeTabs"
    >
      <TabPane
        key="sms"
        tab="短信详情"
        forceRender
      >
        <BasicTable @register="smsInfoTable" />
      </TabPane>
      <TabPane
        key="sys"
        tab="站内信详情"
        forceRender
      >
        <BasicTable @register="sysInfoTable" />
      </TabPane>
    </Tabs>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, unref } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicTable, useTable } from '@/components/Table';
import { Tabs } from 'ant-design-vue';
import { columnSms, columnSys } from '@/views/commonMessage/messageLogg/messageLogg';
import { getSysDetailList, querySys } from '@/api/messageSend';

const emit = defineEmits(['register']);

const TabPane = Tabs.TabPane;

const activeKey = ref('sms');

const record = ref<Recordable>();

const mainAutoId = ref(undefined);

const autoId = ref<number[]>([]);

const title = computed(() => {
  return `${unref(record)?.mesTitle || ''}--消息发送详情`;
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  activeKey.value = 'sms';

  autoId.value = data.ids;

  record.value = data.record;

  mainAutoId.value = data.record.autoId;

  reload({
    searchInfo: {
      autoId: mainAutoId.value,
      saveTableName: unref(record)?.saveTableName,
      mesTypeCode: 'mes',
    },
  });

  setModalProps({
    confirmLoading: false,
    showOkBtn: false,
  });
});

const [smsInfoTable, { reload }] = useTable({
  columns: columnSms(),
  showIndexColumn: false,
  api: getSysDetailList,
  beforeFetch: params => {
    params.autoId = mainAutoId.value;
    params.saveTableName = unref(record)?.saveMesTableName;
    params.mesTypeCode = 'mes';
    return params;
  },
  useSearchForm: false,
  bordered: true,
  immediate: false,
});

const [sysInfoTable, { reload: reloadSys }] = useTable({
  columns: columnSys(),
  showIndexColumn: false,
  api: querySys,
  beforeFetch: params => {
    params.autoId = mainAutoId.value;
    params.saveTableName = unref(record)?.saveTableName;
    params.mesTypeCode = 'sys';
    return params;
  },
  useSearchForm: false,
  bordered: true,
  immediate: false,
  maxHeight: 450,
});

async function changeTabs(value) {
  activeKey.value = value;
  await nextTick();
  if (value === 'sys') {
    reloadSys({
      searchInfo: {
        autoId: mainAutoId.value,
        saveTableName: unref(record)?.saveTableName,
        mesTypeCode: 'sys',
      },
    });
  } else {
    reload({
      searchInfo: {
        autoId: mainAutoId.value,
        saveTableName: unref(record)?.saveMesTableName,
        mesTypeCode: 'mes',
      },
    });
  }
}
</script>
