<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
    :wrap-class-name="$style.mes"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
      <template #receiverDTOList="{ model, field }">
        <div class="p-2">
          <span
            v-for="item in model[field]"
            class="mb-1 inline-block span-tag"
          >
            <a-tag
              color="cyan"
              :closable="!disabled"
              @close="handleTagClose(item, true)"
              >{{ `${item.userName}(${item.userAccount})` }}</a-tag
            >
          </span>
        </div>
      </template>
      <template #contactType="{ model, field }">
        <div class="p-2">
          <span
            v-for="item in model[field]"
            class="mb-1 inline-block span-tag"
          >
            <a-tag
              color="cyan"
              :closable="!disabled"
              @close="handleTagClose(item, false)"
              >{{ `${item.name || ''}` }}</a-tag
            >
          </span>
        </div>
      </template>
    </BasicForm>

    <ReceiveUserModal
      @register="registerReceiveModal"
      @success="handleReceiveSuccess"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { modalForm } from './messageSending';
import { detailList } from '@/api/messageSend';
import { filter, isEmpty, join, map, split } from 'lodash-es';
import { useDictionary } from '@/store/modules/dictionary';
import dayjs from 'dayjs';
import ReceiveUserModal from './ReceiveUserModal.vue';
import { userLabelManageGetByEntity } from '@/api/system/userLabel';
import { unionFind } from '@/api';

const emit = defineEmits(['success', 'register']);

const dictionary = useDictionary();

const tabs = ref<Recordable[]>();

const isUpdate = ref(true);

const autoId = ref();

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.mesTitle || ''}详情`
    : unref(isUpdate)
      ? `编辑${unref(record)?.mesTitle || ''}`
      : '消息发送';
});

const schemas = computed(() => {
  return modalForm(unref(isUpdate), openModal);
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 140,
  schemas: schemas,
  showActionButtonGroup: false,
});

const [registerReceiveModal, { openModal }] = useModal();

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  tabs.value = dictionary.getDictionaryOpt.get('mesUserType');

  tabs.value?.forEach(item => {
    item.treeData = [];
  });

  isUpdate.value = !!data?.isUpdate;

  record.value = data.record;

  disabled.value = !!data.disabled;

  if (unref(isUpdate)) {
    autoId.value = data.record.autoId;

    // 反查大类型
    if (unref(record)?.typeParams) {
      switch (unref(record)?.receiveType) {
        case 'ZDY':
        case 'NZ':
          Promise.all(
            map(
              split(unref(record)?.typeParams, ','),
              async v => await userLabelManageGetByEntity({ labelCode: v })
            )
          ).then(async responseArr => {
            await setFieldsValue({
              contactType: map(responseArr, v => ({ souceId: v.labelCode, name: v.labelName })),
            });
          });
          break;
        case 'GHGB':
        case 'GHXX':
          Promise.all(
            map(split(unref(record)?.typeParams, ','), async v => await unionFind({ id: v }, true))
          ).then(async responseArr => {
            await setFieldsValue({
              contactType: map(responseArr, (v: Recordable) => ({
                souceId: v.recordId,
                name: v.labourUnionName,
              })),
            });
          });
          break;
        default:
          break;
      }
    }

    const contactList = await detailList({
      autoId: unref(record)?.autoId,
      tableName: unref(record)?.saveTableName,
      dataType: 'alone',
    });

    await setFieldsValue({
      ...data.record,
      sendTime: dayjs(data.record.sendTime),
      contact: contactList,
    });
  }

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });

  setProps({ disabled: unref(disabled) });
});

// 取值
function handleReceiveSuccess({ receiveType, receiveUserDTOList, typeParams }) {
  switch (receiveType) {
    case 'ZDY':
    case 'NZ':
      Promise.all(
        map(typeParams, async v => await userLabelManageGetByEntity({ labelCode: v }))
      ).then(async responseArr => {
        await setFieldsValue({
          contactType: map(responseArr, v => ({ name: v.labelName, souceId: v.labelCode })),
          contact: receiveUserDTOList,
        });
      });
      break;
    case 'GHGB':
    case 'GHXX':
      Promise.all(map(typeParams, async v => await unionFind({ id: v }, true))).then(
        async responseArr => {
          await setFieldsValue({
            contactType: map(responseArr, (v: Recordable) => ({
              name: v.labourUnionName,
              souceId: v.recordId,
            })),
            contact: receiveUserDTOList,
          });
        }
      );
      break;
    default:
      break;
  }
}

// 删除单个
async function handleTagClose(item, ifUser) {
  const { contactType, contact } = await validate();

  await setFieldsValue(
    ifUser
      ? {
          contact: filter(contact, (v: Recordable) => v.userAccount !== item.userAccount),
        }
      : {
          contactType: filter(contactType, (v: Recordable) => v.souceId !== item.souceId),
        }
  );
}

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const { contact, contactType, ...values } = await validate();

    setModalProps({ confirmLoading: true });

    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...values,
        autoId: isUpdate.value ? autoId.value : undefined,
        saveTableName: isUpdate.value ? unref(record)?.saveTableName : undefined,
        receiveUserDTOList: contact,
        typeParams: isEmpty(contactType)
          ? undefined
          : join(
              map(contactType, v => v.souceId),
              ','
            ),
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" module>
.mes {
  :global {
    .no-pointer {
      .ant-tree-switcher_close {
        display: none !important;
      }
    }

    .ant-tree {
      height: 45vh !important;
      overflow: auto;
    }
    .span-tag {
      .ant-tag-hidden {
        display: inline-block;
      }
    }
  }
}
</style>
