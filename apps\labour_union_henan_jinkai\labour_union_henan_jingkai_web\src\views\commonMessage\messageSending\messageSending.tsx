import { ref, unref } from 'vue';
import { queryMesTemplate } from '@/api/messageSend';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { Button, Select } from 'ant-design-vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { cloneDeep, filter } from 'lodash-es';
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '标题',
      dataIndex: 'mesTitle',
      width: 100,
    },
    {
      title: '消息类型',
      dataIndex: 'sendType',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`smsType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '发送类型',
      dataIndex: 'mesTypeCode',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`sendType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '发送时间',
      dataIndex: 'sendTime',
      width: 100,
    },
    {
      title: '编辑时间',
      dataIndex: 'updateTime',
      sorter: true,
      width: 100,
    },
    {
      title: '消息状态',
      dataIndex: 'mesStatus',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`messageStatus_${text}`)?.dictName}</span>;
      },
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'mesTitle',
      label: '消息标题',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
    {
      field: 'mesStatus',
      label: '消息状态',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
            // options: dictionary.getDictionaryOpt.get('messageStatus') as RadioGroupChildOption[],
            options: filter(cloneDeep(dictionary.getDictionaryOpt.get('messageStatus') as RadioGroupChildOption[],),v => (v.value !== 'STATUS06' && v.value !== 'STATUS07'))
        };
      },
    },

    {
      field: 'mesTypeCode',
      label: '发送类型',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('sendType'),
        };
      },
    },
  ];
};

export const modalForm = (isUpdate, openModal: Fn): FormSchema[] => {
  const dictionary = useDictionary();
  const options = ref<Recordable[]>([]);

  return [
    {
      label: '信息标题',
      field: 'mesTitle',
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'mesGroupCode',
      label: '选择类型',
      rulesMessageJoinLabel: true,
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      componentProps: ({ formModel }) => {
        return {
          options: dictionary.getDictionaryOpt.get('messageType'),
          async onChange(value) {
            if (formModel.ifTemp) {
              options.value = await queryMesTemplate({ templateType: value });
            }
          },
        };
      },
    },
    {
      field: 'sendTime',
      label: '指定发送时间',
      required: true,
      component: 'DatePicker',
      rulesMessageJoinLabel: true,
      colProps: { span: 12 },
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
      },
    },
    {
      field: 'ifTemp',
      label: '是否使用模板',
      rulesMessageJoinLabel: true,
      component: 'RadioGroup',
      defaultValue: false,
      ifShow({ values }) {
        return values.mesGroupCode;
      },
      componentProps: ({ formModel }) => {
        return {
          options: [
            { label: '是', value: true },
            { label: '否', value: false },
          ],
          onChange: async e => {
            if (e.target.checked) {
              options.value = await queryMesTemplate({ templateType: formModel.mesGroupCode });
            }
          },
        };
      },
    },
    {
      field: 'template',
      label: '选择模板',
      rulesMessageJoinLabel: true,
      required: true,
      ifShow({ values }) {
        return values.ifTemp;
      },
      component: 'Select',
      componentProps: ({ formModel }) => {
        return {
          options: unref(options),
          onChange(_, { mesTemplateContent, sysTemplateContent }: Recordable) {
            formModel.mesContent = mesTemplateContent;
            formModel.sysContent = sysTemplateContent;
          },
          fieldNames: { label: 'templateTitle', value: 'templateTitle' },
        };
      },
    },
    {
      field: 'mesTypeCode',
      label: '发送类型',
      required: true,
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: 'sys',
      rulesMessageJoinLabel: true,
      dynamicDisabled: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('sendType') as RadioGroupChildOption[],
        };
      },
    },
    {
      field: 'sendType',
      label: '消息类型',
      required: true,
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      defaultValue: 'app',
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('smsType') as RadioGroupChildOption[],
        };
      },
    },
    {
      field: 'receiveType',
      label: '接收人类型',
      component: 'Select',
      colProps: { span: 12 },
      required: true,
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return values.receiveType !== 'YWTS';
      },
      defaultValue: 'GHGB',
      render({ model, field, disabled }) {
        const { createWarningModal } = useMessage();

        return (
          <Select
            value={model[field]}
            options={dictionary.getDictionaryOpt.get('mesUserType')}
            placeholder="请选择接收人类型"
            onSelect={value => {
              model[field] = value;
              openModal?.(true, {
                type: model[field],
                contact: model['contact'],
                contactType: model['contactType'],
                isUpdate,
              });
            }}
            onChange={value => {
              model[field] = value;
              model['contact'] = undefined;
              model['contactType'] = undefined;
            }}
            disabled={disabled}
          >
            {{
              suffixIcon: () =>
                disabled ? null : (
                  <Button
                    shape="circle"
                    type="primary"
                    size={`small`}
                    icon={<SearchOutlined />}
                    onClick={() => {
                      if (!model[field]) {
                        createWarningModal({ content: '请选择接收人类型！' });
                        return;
                      }

                      openModal?.(true, {
                        type: model[field],
                        contact: model['contact'],
                        contactType: model['contactType'],
                        isUpdate,
                      });
                    }}
                  />
                ),
            }}
          </Select>
        );
      },
    },
    {
      label: '短信内容',
      field: 'mesContent',
      required: true,
      component: 'InputTextArea',
      ifShow({ values }) {
        return values.mesTypeCode?.includes('mes');
      },
      rulesMessageJoinLabel: true,
    },
    {
      label: '系统消息内容',
      field: 'sysContent',
      required: true,
      component: 'InputTextArea',
      ifShow({ values }) {
        return values.mesTypeCode?.includes('sys');
      },
      rulesMessageJoinLabel: true,
      // render: ({ model, field, disabled }) => {
      //   return h(Tinymce, {
      //     value: model[field],
      //     onChange: (value: string) => {
      //       model[field] = value;
      //     },
      //     showImageUpload: false,
      //     options: {
      //       readonly: disabled,
      //     },
      //     operateType: 3,
      //   });
      // },
    },

    // {
    //   field: 'isAll',
    //   label: '是否选择全部',
    //   component: 'RadioGroup',
    //   colProps: { span: 12 },
    //   show: ({ values }) => values?.receiveType,
    //   defaultValue: 'y',
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get(`YesOrNo`) as RadioGroupChildOption[],
    //   },
    // },
    // {
    //   field: 'typeParams',
    //   label: '选择接收人',
    //   component: 'Input',
    //   colProps: { span: 12 },
    //   defaultValue: 'y',
    //   render({}) {
    //     return <ReceiveUser />;
    //   },
    // },
    {
      label: '接收人',
      field: 'contact',
      component: 'ShowSpan',
      ifShow: ({ values }) => !!values.contact,
      slot: 'receiverDTOList',
    },
    {
      label: '全选接收类型/工会',
      field: 'contactType',
      component: 'ShowSpan',
      ifShow: ({ values }) => !!values.contactType,
      slot: 'contactType',
    },
    {
      field: 'mesStatus',
      label: '消息状态',
      component: 'RadioGroup',
      dynamicDisabled: isUpdate,
      ifShow({ values }) {
        return values.mesStatus;
      },
      componentProps: {
        // options: dictionary.getDictionaryOpt.get('messageStatus') as RadioGroupChildOption[],
        options: filter(cloneDeep(dictionary.getDictionaryOpt.get('messageStatus') as RadioGroupChildOption[],),v => (v.value !== 'STATUS06' && v.value !== 'STATUS07'))
      },
    },
    {
      field: 'appRemarks',
      label: '审核意见',
      component: 'InputTextArea',
      dynamicDisabled: isUpdate,
      ifShow({ values }) {
        return values.appRemarks;
      },
      rulesMessageJoinLabel: true,
    },
  ];
};

export const appForm: FormSchema[] = [
  {
    field: 'operator',
    label: '审核状态',
    required: true,
    component: 'RadioGroup',
    componentProps: {
      options: [
        {
          label: '通过',
          value: 'PASS',
        },
        {
          label: '拒绝',
          value: 'REFUSE',
        },
        {
          label: '驳回',
          value: 'RETURN',
        },
      ],
    },
  },
  {
    field: 'operatorReason',
    label: '审核意见',
    // required: true,
    component: 'InputTextArea',
    rulesMessageJoinLabel: true,
  },
];
