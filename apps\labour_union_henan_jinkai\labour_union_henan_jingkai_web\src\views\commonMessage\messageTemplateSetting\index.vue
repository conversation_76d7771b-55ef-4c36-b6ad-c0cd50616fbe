<template>
  <div>
    <BasicTable
      @register="registerTable"
      :clickToRowSelect="false"
    >
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/messageTemplateSetting/add"
        >
          新增模板</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/messageTemplateSetting/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/messageTemplateSetting/modify',
              },
              {
                icon:
                  record.enableType === 'y'
                    ? 'material-symbols:lock-open'
                    : 'material-symbols:lock',
                label: record.enableType === 'y' ? '禁用' : '启用',
                type: 'primary',
                onClick: handleConfirm.bind(null, record),
                auth: '/messageTemplateSetting/modify',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/messageTemplateSetting/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <MessageModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { createVNode } from 'vue';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { Modal } from 'ant-design-vue';
import MessageModal from './messageModal.vue';
import { CloseCircleFilled, CheckCircleOutlined } from '@ant-design/icons-vue';
import { useModal } from '@/components/Modal';
import { list, saveOrUpdate, deleteMessageTemplate } from '@/api/commonMessage';
import { columns, formSchemas } from './messageTemplateSetting';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm } = useMessage();

const [registerTable, { reload, updateTableDataRecord }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  authInfo: ['/messageTemplateSetting/add'],
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',

    fixed: undefined,
    auth: [
      '/messageTemplateSetting/modify',
      '/messageTemplateSetting/view',
      '/messageTemplateSetting/delete',
    ],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确定删除${record.templateTitle}?`,
    onOk: function () {
      deleteMessageTemplate(record.autoId).then(res => {
        const { code, message } = res;
        if (code === 200) {
          Modal.success({
            title: '提示',
            icon: createVNode(CheckCircleOutlined),
            content: '删除成功！' || message,
            okText: '确认',
            closable: true,
          });
          reload();
        } else {
          Modal.error({
            title: '提示',
            icon: createVNode(CloseCircleFilled),
            content: `删除失败！${message} `,
            okText: '确认',
            closable: true,
          });
        }
      });
    },
  });
}

function handleEdit(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: false,
  });
}

function handleView(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: true,
  });
}

function handleConfirm(record) {
  const name = record.enableType === 'y' ? '禁用' : '启用';
  createConfirm({
    iconType: 'warning',
    content: `请确定${name}${record.templateTitle}?`,
    onOk: function () {
      apiSubmit(
        {
          autoId: record.autoId,
          enableType: record.enableType === 'y' ? 'n' : 'y',
        },
        name
      );
    },
  });
}

function apiSubmit(values, name) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      Modal.success({
        title: '提示',
        icon: createVNode(CheckCircleOutlined),
        content: `${name}成功!` || message,
        okText: '确认',
        closable: true,
      });
      updateTableDataRecord(values.autoId, values);
      reload();
      closeModal();
    } else {
      Modal.error({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: `${name}失败!${message}`,
        okText: '确认',
        closable: true,
      });
    }
  });
}

function handleSuccess({ values, isUpdate }) {
  const name = isUpdate ? '修改' : '新增';
  apiSubmit(values, name);
}
</script>
