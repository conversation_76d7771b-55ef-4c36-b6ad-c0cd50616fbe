<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, unref } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { modalForm } from './messageTemplateSetting';

const emit = defineEmits(['success', 'register']);

const isUpdate = ref(true);

const autoId = ref('');

const disabled = ref(false);

const record = ref<Recordable>();

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.templateTitle || ''}详情`
    : unref(isUpdate)
      ? `编辑${unref(record)?.templateTitle || ''}`
      : '新增消息模板';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 100,
  schemas: modalForm(),
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  isUpdate.value = !!data?.isUpdate;

  disabled.value = !!data?.disabled;

  record.value = data.record;

  if (unref(isUpdate)) {
    autoId.value = data.record.autoId;
    await setFieldsValue({
      ...data.record,
    });
  }

  setProps({
    disabled: unref(disabled),
  });

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });
    // TODO custom api
    emit('success', {
      isUpdate: unref(isUpdate),
      values: { ...values, autoId: isUpdate.value ? autoId.value : undefined },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
