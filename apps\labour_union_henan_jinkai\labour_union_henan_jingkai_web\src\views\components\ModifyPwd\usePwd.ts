import { RuleObject } from 'ant-design-vue/lib/form';
import { computed, Ref, unref } from 'vue';
import { validatePassWithOthers } from '@monorepo-yysz/utils';

export function useRules(formData?: Recordable) {
  const getPasswordFormRule = computed(() => createRule('请输入密码'));

  const validateConfirmPassword = (password: string) => {
    return async (_: RuleObject, value: string) => {
      if (!value) {
        return Promise.reject('请输入密码');
      }
      if (value !== password) {
        return Promise.reject('两次输入密码不一致');
      }
      return Promise.resolve();
    };
  };

  const getFormRules = computed((): { [k: string]: RuleObject | RuleObject[] } => {
    const passwordFormRule = unref(getPasswordFormRule);

    return {
      pwd: [...passwordFormRule, { validator: validatePassWithOthers }],
      rePassword: [
        { validator: validateConfirmPassword(formData?.pwd), trigger: ['change', 'blur'] },
      ],
    };
  });

  return { getFormRules };
}

function createRule(message: string): RuleObject[] {
  return [
    {
      required: true,
      message,
      trigger: ['change', 'blur'],
    },
  ];
}

export function useFormVal<T extends Object = any>(formRef: Ref<any>) {
  async function validForm() {
    const form = unref(formRef);
    if (!form) return;
    const data = await form.validate();
    return data as T;
  }

  async function reset() {
    const form = unref(formRef);
    if (!form) return;
    await form.resetFields();
  }

  return { validForm, reset };
}
