<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { RadioGroupChildOption } from 'ant-design-vue/lib/radio/Group';
import { cloneDeep, filter } from 'lodash-es';
import { useDictionary } from '@/store/modules/dictionary';

const emit = defineEmits(['register', 'success']);

const dictionary = useDictionary();

const record = ref<Recordable>();

const batchList = ref<string[]>([]);

const title = computed(() => {
  return '审核信息';
});

const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
  labelWidth: 120,
  schemas: [
    {
      field: 'auditState',
      label: '审核状态',
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      defaultValue: 'PASS',
      required: true,
      componentProps: {
        options:
          (filter(
            cloneDeep(dictionary.getDictionaryOpt.get('auditState')),
            v => v.value !== 'WAIT'
          ) as RadioGroupChildOption[]) || [],
      },
    },
    {
      field: 'auditRemark',
      label: '审核备注',
      component: 'InputTextArea',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        rows: 3,
      },
    },
  ],
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  batchList.value = data.batchList;

  setFieldsValue({
    ...data.record,
  });

  setModalProps({ confirmLoading: false });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();

    emit('success', {
      values: {
        ...values,
        batchList: unref(batchList),
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
