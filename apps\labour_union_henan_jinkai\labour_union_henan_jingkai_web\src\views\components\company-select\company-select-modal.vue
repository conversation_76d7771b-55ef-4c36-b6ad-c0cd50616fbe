<template>
  <BasicModal
    v-bind="$attrs"
    :title="'选择工会'"
    :canFullscreen="false"
    @register="registerModal"
    @ok="handleOk"
    @cancel="handleCancel"
    :wrapClassName="$style['company-select-modal']"
  >
    <BasicTable
      @register="registerTable"
      class="!p-0"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <Button
            type="primary"
            size="small"
            @click="handleSelect(record)"
          >
            选择
          </Button>
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Button } from 'ant-design-vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicColumn, BasicTable, FormSchema, useTable } from '@/components/Table';
import { unionInfoList } from '@/api';

const emit = defineEmits(['register', 'success']);

const selectedRecord = ref<Recordable | null>(null);

const columns: BasicColumn[] = [
  {
    title: '工会名称',
    dataIndex: 'companyName',
    key: 'companyName',
    width: 200,
  },
];

const searchFormSchema: FormSchema[] = [
  {
    field: 'companyName',
    label: '工会名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入工会名称',
    },
    colProps: { span: 8 },
  },
];

const [registerModal, { closeModal }] = useModalInner(async data => {
  selectedRecord.value = null;
  // 重新加载数据
  await reload();
});

const [registerTable, { reload }] = useTable({
  rowKey: 'companyId',
  columns,
  api: unionInfoList,
  formConfig: {
    labelWidth: 80,
    schemas: searchFormSchema,
    autoSubmitOnEnter: true,
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: false,
  maxHeight: 380,
  actionColumn: {
    title: '操作',
    key: 'action',
    width: 100,
    fixed: 'right',
  },
});

// 选择工会
function handleSelect(record: Recordable) {
  selectedRecord.value = record;
  handleOk();
}

// 确认选择
function handleOk() {
  if (selectedRecord.value) {
    emit('success', {
      companyId: selectedRecord.value.companyId,
      companyName: selectedRecord.value.companyName,
      record: selectedRecord.value,
    });
    closeModal();
  }
}

// 取消选择
function handleCancel() {
  selectedRecord.value = null;
  closeModal();
}
</script>

<style lang="less" module>
.company-select-modal {
  :global {
    .ant-pagination-options {
      .ant-select {
        width: auto !important;
      }
    }
  }
}
</style>
