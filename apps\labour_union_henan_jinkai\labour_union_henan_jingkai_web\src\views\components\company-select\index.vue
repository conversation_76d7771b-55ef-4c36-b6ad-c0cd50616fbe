<template>
  <div>
    <Input
      v-model:value="strValue"
      placeholder="选择工会"
      :title="strValue"
      @change="handleInput"
      :disabled="disabled"
      readonly
    >
      <template #suffix>
        <Button
          type="primary"
          size="small"
          v-if="!disabled"
          @click="handleChange"
          >选择工会</Button
        >
      </template>
    </Input>
    <CompanySelectModal
      @register="registerModal"
      width="70%"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, withDefaults } from 'vue';
import { Input, Button } from 'ant-design-vue';
import { useModal } from '@/components/Modal';
import CompanySelectModal from './company-select-modal.vue';

defineOptions({ name: 'CompanySelect' });

interface CompanySelectEmits {
  (e: 'change', value: { companyId: string; companyName: string; record: Recordable }): void;
  (e: 'input', value: string): void;
}

interface CompanySelectProps {
  value?: string;
  disabled?: boolean;
}

const emit = defineEmits<CompanySelectEmits>();

const props = withDefaults(defineProps<CompanySelectProps>(), {
  value: '',
  disabled: false,
});

const strValue = ref<string>('');

const [registerModal, { openModal, closeModal }] = useModal();

function handleChange() {
  openModal(true, {});
}

function handleInput(e: Event) {
  const target = e.target as HTMLInputElement;
  emit('input', target.value);
}

function handleSuccess({
  companyId,
  companyName,
  record,
}: {
  companyId: string;
  companyName: string;
  record: Recordable;
}) {
  strValue.value = companyName;

  emit('change', {
    companyId,
    companyName,
    record,
  });
  closeModal();
}

watch(
  () => props.value,
  () => {
    strValue.value = props.value;
  },
  { deep: true, immediate: true }
);
</script>
