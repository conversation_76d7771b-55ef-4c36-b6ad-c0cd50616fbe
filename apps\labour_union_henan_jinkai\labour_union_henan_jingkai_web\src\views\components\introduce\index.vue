<template>
  <div
    class="w-full h-full p-5 !pb-1"
    :class="$style.introduce"
  >
    <div class="w-full h-full bg-hex-[#fff] rounded-t-xl content-clazz">
      <div
        class="h-40px rounded-t-xl"
        :style="{ backgroundColor: '#ECF4FF' }"
      >
        <span class="text-left flex items-center p-2 text-17px h-full">
          <Icon
            icon="line-md:home-twotone"
            :size="20"
            class="introduce-title"
          />
          <span class="m-1 introduce-title">{{ record?.title || '' }}</span>
        </span>
      </div>
      <div
        :style="{ height: 'calc(100% - 40px)' }"
        class="w-full p-2 flex justify-center items-center"
      >
        <div
          class="bg-no-repeat w-1200px h-380px"
          :style="{
            backgroundImage: `url(${introduce})`,
            backgroundSize: '100%',
            backgroundPosition: '40% 50%',
          }"
        >
          <div class="h-88/100 pl-[20%] pt-6 pr-5 overflow-auto">
            <div class="text-24px pb-2 introduce-title">{{ record?.title || '' }}</div>
            <div v-html="record?.menuDesc || ''"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import introduce from '@/assets/images/introduce.png';
import { Persistent } from '@/utils/cache/persistent';
import { COMMON_RECORD } from '@monorepo-yysz/enums';
import { Icon } from '@monorepo-yysz/ui';

import { find } from 'lodash-es';
import { usePermissionStore } from '@/store/modules/permission';

const permissionStore = usePermissionStore();

const record = ref<Recordable>();

onMounted(() => {
  const route = Persistent.getLocal(COMMON_RECORD) as Recordable;
  record.value = find(permissionStore.getAllPermList, v => v.path === route?.path);
});
</script>

<style lang="less" module>
.introduce {
  :global {
    .introduce-title {
      color: @primary-color;
    }
    .content-clazz {
      border: 1px solid #b3d5ff;
    }
  }
}
</style>
