<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="closeModal"
    okText="需要"
    :showCancelBtn="false"
    :showOtherBtn="true"
    @other="handleCancel"
    otherText="不需要"
  >
    <div class="font-bold mb-20px"> 提交内容包含敏感信息,是否需要重新修改？ </div>
    <div
      class="mb-20px"
      v-if="sensitiveWords && sensitiveWords.length"
      ><span class="font-bold">内容：</span>{{ sensitiveWords }}
    </div>
    <div v-if="sensitiveImages && sensitiveImages.length">
      <span class="font-bold">图片：</span
      ><Image
        class="!w-200px"
        v-for="(item, index) in sensitiveImages"
        :key="index"
        :src="item"
      />
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { Image } from 'ant-design-vue';

const emit = defineEmits(['success', 'register', 'cancel']);
const sensitiveImages = ref([]);
const sensitiveWords = ref([]);
const title = computed(() => {
  return '敏感内容校验提醒';
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
  sensitiveWords.value = data?.data?.sensitiveWords || [];
  sensitiveImages.value = data?.data?.sensitiveImages || [];
  setModalProps({
    confirmLoading: false,
    showOkBtn: true,
  });
});

async function handleCancel() {
  try {
    emit('success', {
      filter: false,
    });
    closeModal();
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" module>
.channel-topic-modal {
  :global {
    .ant-input-number,
    .ant-picker {
      width: 100% !important;
    }
  }
}
</style>
