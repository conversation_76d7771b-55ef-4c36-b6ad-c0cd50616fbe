<template>
  <video
    ref="videoPlayer"
    :id="`videoPlayer-${id}`"
    class="w-full h-full video-js"
  ></video>
</template>

<script lang="ts" setup>
import { nextTick, onUnmounted, onMounted, ref, unref } from 'vue';
import videoJS from 'video.js';
import { useRoute } from 'vue-router';
import { useUserStore } from '/@/store/modules/user';
// import 'videojs-contrib-hls';
import zhCN from 'video.js/dist/lang/zh-CN.json';
import 'video.js/dist/video-js.css';

videoJS.addLanguage('zh-CN', zhCN);

const route = useRoute();

const { getPrefix } = useUserStore();

const videoPlayer = ref();

const player = ref();

const props = defineProps({
  url: String,
  videoType: { type: String, default: 'video/mp4' },
  id: { type: String, default: 'video-js' },
});

onMounted(() => {
  const url = route?.query?.url ? ((getPrefix + route?.query?.url) as string) : props.url;
  const videoType = route?.query?.videoType ? (route?.query?.videoType as string) : props.videoType;

  if (!url) {
    return false;
  }
  nextTick(() => {
    player.value = videoJS(
      `videoPlayer-${props.id}`,
      {
        language: 'zh-CN',

        html5: {
          hls: {
            enableLowInitialPlaylist: true,
            smoothQualityChange: true,
          },
        },
        autoplay: false,
        controls: true,
        preload: 'auto', //定义视频加载模式
        sources: [
          {
            src: url,
            type: videoType, //'application/x-mpegURL',
          },
        ],
      },
      function onPlayerReady() {
        // console.log(player.value)
      }
    );
  });
});

onUnmounted(() => {
  unref(player)?.dispose();
});
</script>
