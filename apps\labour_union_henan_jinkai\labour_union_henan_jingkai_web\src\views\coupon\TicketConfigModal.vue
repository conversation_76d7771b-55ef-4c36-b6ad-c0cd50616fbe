<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="`${$style['ticket-config']} ${disabledClass}`"
    >
      <template #useScope="{ model, field }">
        <RadioGroup
          v-model:value="useScope"
          :options="dictionary.getDictionaryOpt.get('useScope') as RadioGroupChildOption[]"
          @change="e => changeUseScope(e, model, field)"
          :disabled="disabled"
        />
      </template>
    </BasicForm>
    <div v-show="false">
      <BasicTable
        class="!h-auto"
        @register="registerProductTable"
        :canResize="false"
        :maxHeight="200"
      >
        <template
          v-if="!disabled"
          #toolbar
        >
          <a-button
            type="primary"
            @click="handleChooseUnion(useScope)"
            >选择{{ useScope === 'merchant' ? '商户' : '商品' }}</a-button
          >
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  icon: 'fluent:delete-16-filled',
                  label: '删除',
                  type: 'primary',
                  danger: true,
                  ifShow: !disabled,
                  onClick: handleDeleteProduct.bind(null, record),
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <UnionListModal
      :canFullscreen="false"
      width="60%"
      @register="registerUnionListModal"
      @success="handleSuccess"
    >
    </UnionListModal>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicForm, useForm } from '/@/components/Form';
import UnionListModal from './UnionListModal.vue';
import { formSchema } from './data';
import { useModal, useModalInner, BasicModal } from '/@/components/Modal';
import { BasicTable, TableAction, useTable } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import { message, RadioGroup } from 'ant-design-vue';
import { getTicketDetail, saveOrUpdate } from '@/api/coupon';
import { useMessage } from '@monorepo-yysz/hooks';
import { uniqBy } from 'lodash-es';
import dayjs from 'dayjs';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

const record = ref<Recordable>();
const dictionary = useDictionary();
const disabled = ref(false);
const isUpdate = ref(false);
const useScope = ref();
const disableEdit = ref(false);

const dataSource = ref('');
const emit = defineEmits(['register', 'reloadTable']);

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return formSchema(unref(disabled), unref(disableEdit));
});

const title = ref('');
const [registerForm, { validate, resetFields, setFieldsValue, setProps }] = useForm({
  labelWidth: 160,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerUnionListModal, { openModal }] = useModal();

const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
  await resetFields();
  record.value = {};
  dataSource.value = data?.record?.dataSource;
  disabled.value = data?.state === 'view';
  isUpdate.value = data?.state === 'edit';
  //进来先将 disableEdit 设置为false 避免
  disableEdit.value = false;
  setProductTableDate([]);
  switch (data?.state) {
    case 'view':
      break;
    case 'edit':
      disableEdit.value = data.record?.assignCount !== 0;
      title.value = `编辑--${data.record?.couponName || ''}`;
      break;
    default:
      break;
  }
  if (data?.state !== 'add') {
    //获取票券详情
    getTicketDetail({ couponId: data.record.couponId }).then(res => {
      let { code, data: couponInfo } = res;

      if (code === 200) {
        title.value = `${couponInfo.couponName || ''}--详情`;
        // 领取 使用时间
        couponInfo.receiveDateRange =
          couponInfo.assignStartTime && couponInfo.assignEndTime
            ? [couponInfo.assignStartTime, couponInfo.assignEndTime]
            : [];

        couponInfo.useDateRange =
          couponInfo.useStartTime && couponInfo.useEndTime
            ? [couponInfo.useStartTime, couponInfo.useEndTime]
            : [];

        // 适用范围
        useScope.value = couponInfo.useScope;
        if (useScope.value == 'merchant') {
          setProductTableDate(couponInfo?.extendMap?.merchant ?? []);
        } else if (useScope.value == 'product') {
          setProductTableDate(couponInfo?.extendMap?.product ?? []);
        }

        // 指定日期
        couponInfo.weekLimit = couponInfo.weekLimit ? couponInfo.weekLimit.split(',') : [];
        couponInfo.specifyDate = couponInfo.specifyDate ? couponInfo.specifyDate.split(',') : [];
        //处理折扣比例
        couponInfo.discountPercent = Math.floor(couponInfo?.discountPercent * 10) / 10;

        record.value = couponInfo;
        setFieldsValue(couponInfo);
      }
    });
  } else {
    title.value = '新增票券';
    setFieldsValue(data.record);
  }
  setProps({ disabled: unref(disabled) });
  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

// 适用范围选择
const changeUseScope = (e, model, field) => {
  model[field] = e;
  model['extendList'] = undefined;
  setProductTableDate([]);
};
// 商品商户表格注册
const [
  registerProductTable,
  {
    deleteTableDataRecord: deleteProduct,
    setTableData: setProductTableDate,
    getDataSource: getProductDataSource,
  },
] = useTable({
  rowKey: 'sourceId',
  columns: [
    {
      title: `${useScope.value === 'merchant' ? '商户' : '商品'}名称`,
      dataIndex: 'sourceName',
    },
  ],
  useSearchForm: false,
  bordered: true,
  pagination: false,
  showIndexColumn: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
  },
});

function handleChooseUnion(type) {
  openModal(true, { type });
}

// 删除商品
function handleDeleteProduct({ sourceId }) {
  deleteProduct(sourceId);
}

// 设置表格数据
function handleSuccess(list, type) {
  switch (type) {
    case 'merchant':
      setProductTableDate(uniqBy([...getProductDataSource(), ...list], t => t.sourceId));
      break;
    case 'product':
      setProductTableDate(uniqBy([...getProductDataSource(), ...list], t => t.sourceId));
      break;
    default:
      break;
  }
}
const { createSuccessModal, createErrorModal } = useMessage();

// 提交
async function handleSubmit() {
  try {
    let { receiveDateRange, useDateRange, generateDate, activityId, ...values } = await validate();
    // return
    // 领取 使用时间
    receiveDateRange =
      receiveDateRange && receiveDateRange.length
        ? [
            dayjs(receiveDateRange[0]).format('YYYY-MM-DD'),
            dayjs(receiveDateRange[1]).format('YYYY-MM-DD'),
          ]
        : undefined;
    useDateRange =
      useDateRange && useDateRange.length
        ? [dayjs(useDateRange[0]).format('YYYY-MM-DD'), dayjs(useDateRange[1]).format('YYYY-MM-DD')]
        : undefined;
    //派发方式：领取  设置一个默认的活动id
    if (values.dataSource === 'birthday' && values.grantType === 'receive') {
      values.activityId = 'birthdayCoupon';
    }
    // if (!getProductDataSource()?.length) {
    //   createErrorModal({ content: `请选择指定商户或商品` });
    //   return;
    // }

    const params = {
      ...unref(record),
      ...values,
      //useScope: unref(useScope),
      // 领取日期
      assignStartTime: receiveDateRange ? receiveDateRange[0] : undefined,
      assignEndTime: receiveDateRange ? receiveDateRange[1] : undefined,
      // 使用日期
      useStartTime: useDateRange ? useDateRange[0] : undefined,
      useEndTime: useDateRange ? useDateRange[1] : undefined,
      // 指定商户商品工会人员
      extendList: [...getProductDataSource()],
      // 指定日期
      weekLimit: values.weekLimit?.join(',') || undefined,
      specifyDate: values.specifyDate?.join(',') || undefined,
    };

    saveOrUpdate(params).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({
          content: `配置成功`,
          onOk: () => {
            closeModal();
            emit('reloadTable');
          },
        });
        // goBack()
      } else {
        createErrorModal({ content: `配置失败!${message}` });
      }
    });
  } catch (error) {
    message.warning('请完善必填信息');
  }
}
</script>

<style lang="less" module>
.ticket-config {
  :global {
    // padding: 20px;

    .ant-input-number,
    .ant-input-number-affix-wrapper {
      width: 100% !important;
    }

    .form-action {
      border: none !important;
    }
  }
}

.ticket {
  :global {
    .ant-table-body {
      max-height: 400px !important;
      height: auto !important;
    }
  }
}
</style>
