<template>
  <BasicModal
    :canFullscreen="false"
    :title="title"
    show-ok-btn
    v-bind="$attrs"
    @ok="handleOk"
    @register="registerModal"
    @visible-change="visibleChange"
  >
    <BasicTable @register="registerTable"></BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicTable, useTable } from '@/components/Table';
import { computed, ref, unref } from 'vue';
import { unionColumns, unionFormSchemas } from './data';
// 商户商品列表
import { findList, treeProductList } from '@/api/productManagement';

const emit = defineEmits(['success', 'register']);
const type = ref('');
const modalVisible = ref(null);
const title = computed(() => {
  switch (unref(type)) {
    case 'merchant':
      return '选择商户';
    case 'product':
      return '选择商品';
    default:
      return;
  }
});
const columns = computed(() => {
  return unionColumns(unref(type));
});
const formSchemas = computed(() => {
  return unionFormSchemas(unref(type));
});

const visibleChange = visible => {
  modalVisible.value = visible;
};
const [registerModal, { closeModal }] = useModalInner(async data => {
  await clearSelectedRowKeys();
  type.value = data.type;
  let api = null as any;
  switch (unref(type)) {
    case 'merchant':
      api = findList;
      break;
    case 'product':
      api = treeProductList;
      break;
    default:
      break;
  }
  setProps({
    api,
    pagination: {
      current: 1,
    },
  });

  // 清空表单搜索条件
  getForm().resetFields();
  reload();
});
const rowKey = computed(() => {
  switch (unref(type)) {
    case 'merchant':
      return 'companyId';
    case 'product':
      return 'productId';
    default:
      break;
  }
});
const [registerTable, { reload, getSelectRows, clearSelectedRowKeys, setProps, getForm }] =
  useTable({
    rowKey: rowKey,
    columns: columns,
    beforeFetch: params => {
      switch (unref(type)) {
        case 'merchant':
        //  params.queryCompanyId = '6650f8e054af46e7a415be50597a99d5';
            params.pid = 0
          break;
        case 'product':
          params.systemQueryType = 'manage';
          break;
        default:
          break;
      }
      return { ...params, nextLevelFlag: 'true', systemQueryType: 'manage' };
    },
    formConfig: {
      labelWidth: 120,
      actionColOptions: {
        span: 4,
      },
      autoSubmitOnEnter: true,
      schemas: formSchemas as any,
    },
    immediate: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: true,
    showIndexColumn: true,
    maxHeight: 420,
    rowSelection: {
      type: 'checkbox',
    },
  });
// 提交
function handleOk() {
  const rows = getSelectRows();
  const sourceType = unref(type);
  const list = rows?.map(t => {
    const { productId, productName, companyId, companyName } = t;
    switch (sourceType) {
      case 'merchant':
        return { sourceId: companyId, sourceName: companyName, sourceType };
      case 'product':
        return {
          sourceId: productId,
          sourceName: productName,
          sourceType,
          remark: companyId,
        };
      default:
        break;
    }
  });
  emit('success', list, sourceType);
  closeModal();
}
</script>

<style scoped></style>
