import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { Image, Progress } from 'ant-design-vue';
import { map } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { validateIntegerAndOneDecimal, validateIntegerAndTwoDecimal } from '@monorepo-yysz/utils';
import { searchNextUnionForm } from '@/utils/searchNextUnion';
import { findSimpleList } from '@/api/activities';
import { simpleList } from '@/api/coupon';
import dayjs from 'dayjs';
import {unionInfoList} from "@/api";


// 判断当前是否在领取日期内
const isWithinAssignDate = (assignStartTime, assignEndTime, specifyDate, weekLimit, issueCount, assignCount) => {
  if (!assignStartTime) return false;

  const today = dayjs();
  // 检查时间范围
  const isBefore = dayjs().diff(dayjs(assignStartTime + ' 00:00:00'));
  const isAfter = dayjs().diff(dayjs(assignEndTime + ' 23:59:59'));
  if (isBefore < 0 || isAfter > 0) {
    return false;
  }
  if (issueCount <= assignCount) {
    return false
  }
  // 检查每月指定日期（需要处理空值）
  if (specifyDate) {
    const currentDay = today.date().toString(); // 获取当月第几天（1-31）
    const validDays = specifyDate.split(',').map(d => d.trim());
    if (!validDays.includes(currentDay)) return false;
  }

  // 检查每周星期（需要转换周日为7）
  if (weekLimit) {
    const weekDay = today.day(); // dayjs周日为0，周一为1
    const chinaWeekDay = weekDay === 0 ? 7 : weekDay; // 转换为国内习惯（1-7）
    const validWeeks = weekLimit.split(',').map(w => w.trim());
    if (!validWeeks.includes(chinaWeekDay.toString())) return false;
  }
  return true;
};
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '票券名称',
      dataIndex: 'couponName',
    },
    {
      title: '票券类型',
      dataIndex: 'couponType',
      width: 120,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`ticketType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '领取日期',
      dataIndex: 'receiveDateRange',
      width: 180,
      customRender: ({ record }) => {
        const { assignStartTime, assignEndTime } = record;
        return (
          <span>
            {assignStartTime && assignEndTime ? assignStartTime + '-' + assignEndTime : '-'}
          </span>
        );
      },
    },
    {
      title: '发放总数',
      dataIndex: 'receiveTimeRange',
      width: 150,
      customRender: ({ record }) => {
        const { issueCount, assignCount, usedCount } = record;
        return (
          <div style={{ textAlign: 'left' }}>
            <div>发放总数：{issueCount}</div>
            <div>已领取数：{assignCount}</div>
            <div>已核销数：{usedCount}</div>
          </div>
        );
      },
    },
    {
      title: '每日发行量',
      dataIndex: '',
      resizable: true,
      width: 200,
      customRender: ({ record }) => {
        const { dailyIssueCount = 0, todayAssignCount = 0, assignStartTime, assignEndTime, specifyDate, weekLimit, issueCount, assignCount } = record
        // 无领取日期配置时显示 '-'
        if (!assignStartTime || !dailyIssueCount) {
          return <div>-</div>;
        }
        const isValidDate = isWithinAssignDate(assignStartTime, assignEndTime, specifyDate, weekLimit, issueCount, assignCount);
        const remaining = isValidDate ? dailyIssueCount - todayAssignCount : 0;
        return (
          <div style={{ 'text-align': 'left' }}>
            <div>每日发行：{dailyIssueCount}</div>
            {isValidDate && <div>今日剩余：{remaining}</div>}
            {(
              <Progress
                percent={Number(((remaining / dailyIssueCount) * 100).toFixed(2))}
                format={percent => `${percent}%`}
                strokeColor="#40c340de"
                size="small"
              />
            )}
          </div>
        );
      },
    },
    {
      title: '组织单位',
      dataIndex: 'companyName',
      width: 150,
    },
    {
      title: '活动名称',
      dataIndex: 'activityName',
      width: 150,
      customRender: ({ text }) => {
        return text ?? '-';
      },
    },
    {
      title: '票券状态',
      dataIndex: 'state',
      width: 80,
      customRender: ({ text }) => {
        return <span>{text === 'Y' ? '已发布' : '待发布'}</span>;
      },
      ifShow: false,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'couponName',
      component: 'Input',
      label: '票券名称',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 6,
      },
    },
    ...searchNextUnionForm(),
  ];
};

export const formSchema = (disabled, disableEdit: boolean): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: '',
      label: '基本信息',
      colProps: { span: 24 },
      component: 'Divider',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'couponName',
      component: 'Input',
      label: '票券名称',
      required: true,
      rulesMessageJoinLabel: true,
      colProps: {
        span: 24,
      },
    },
    {
      field: 'useScope',
      component: 'RadioGroup',
      label: '适用范围',
      defaultValue: 'merchant',
      rulesMessageJoinLabel: true,
      required: true,
      show:false,
      colProps: {
        span: 12,
      },
     // slot: 'useScope',
    },
    {
      field: 'extendIds',
      required: true,
      label: '适用商家',
      component: 'ApiSelect',
      colProps: { span: 24 },
      componentProps: function ({formModel}) {
        return {
          placeholder: '请选择商家',
          api: unionInfoList,
          params: {
            authSystem: '', //OPEN
            pageSize: 0,
          },
          mode: 'multiple',
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          onChange(value,nodes){
            formModel['extendList'] = nodes?.map(t=>({sourceId:t.companyId,sourceName:t.companyName,sourceType:'merchant',}))
          },
          fieldNames: { label: 'companyName', value: 'companyId' },
        }
      },
    },
    {
      field:'extendList',
      component:'ShowSpan',
      show:false,
    },
    {
      field: 'issueCount',
      component: 'InputNumber',
      label: '预发放总量',
      colProps: {
        span: 12,
      },
      required: true,
      componentProps: {
        placeholder: '请输入预发放总量',
        min: 0,
        step: 1,
      },
    },
    {
      field: 'dailyIssueCount',
      component: 'InputNumber',
      label: '每日发放数量',
      colProps: {
        span: 12,
      },
      required: false,
      componentProps: {
        placeholder: '请输入每日发行数量',
        min: 0,
        step: 1,
      },
    },
    {
      field: 'couponType',
      component: 'RadioGroup',
      label: '优惠类型',
      rulesMessageJoinLabel: true,
      defaultValue: 'fullDecrement',
      required: true,
      dynamicDisabled: disableEdit || disabled,
      colProps: {
        span: 12,
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('ticketType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'amountLimit',
      component: 'InputNumber',
      label: '门槛金额(元)',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      dynamicDisabled: disableEdit || disabled,
      rules: [
        { required: true, validator: validateIntegerAndTwoDecimal, trigger: ['change', 'blur'] },
      ],
      ifShow({ values }) {
        return values.couponType === 'fullDecrement' || values.couponType === 'discount';
      },
    },
    {
      field: 'discountAmount',
      component: 'InputNumber',
      label: ({ values }) => values.couponType === 'noLimit' ? '票券金额' : '满减金额(元)',
      rules: [
        { required: true, validator: validateIntegerAndTwoDecimal, trigger: ['change', 'blur'] },
      ],
      componentProps: {
        placeholder: '请输入金额',
      },
      colProps: {
        span: 12,
      },
      dynamicDisabled: disableEdit || disabled,
      ifShow({ values }) {
        return values.couponType === 'fullDecrement' || values.couponType === 'noLimit';
      },
    },
    {
      field: 'discountPercent',
      component: 'InputNumber',
      label: '折扣比例',
      rules: [
        { required: true, validator: validateIntegerAndOneDecimal, trigger: ['change', 'blur'] },
      ],
      dynamicDisabled: disableEdit || disabled,
      rulesMessageJoinLabel: true,
      suffix: '例如:7折就输入7，7.5折输入7.5',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.couponType === 'discount';
      },
    },
    {
      field: 'discountAmount',
      component: 'InputNumber',
      label: '最大折扣金额(元)',
      // rules: [
      //   { required: false, validator: validateIntegerAndTwoDecimal, trigger: ['change', 'blur'] },
      // ],
      rules: [
        {
          required: false, validator: (rule, value) => {
            //有值才校验
            if (value) {
              // 校验
              if (!/^([1-9]\d*(\.\d{1,2})?|([0](\.([0][1-9]|[1-9]\d{0,1}))))$/.test(value)) {
                // callback(new Error('请输入正数,最多保留2位小数'))
                return Promise.reject('请输入正数,最多保留2位小数');
              }
            }
            // 不满足条件时不进行校验
            return Promise.resolve();

          }, trigger: ['change', 'blur']
        },
      ],
      dynamicDisabled: disableEdit || disabled,
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.couponType === 'discount';
      },
    },
    {
      field: 'grantType',
      component: 'RadioGroup',
      label: '派发方式',
      rulesMessageJoinLabel: true,
      required: true,
      dynamicDisabled: disableEdit || disabled,
      colProps: {
        span: 12,
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('grantType') as RadioGroupChildOption[],
      },
    },
    {
      field: '',
      label: '领取规则',
      colProps: { span: 24 },
      component: 'Divider',
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return ['receive'].includes(values.grantType);
      },
    },
    {
      field: 'areaCode',
      label: '参与用户区域',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      ifShow({ values }) {
        return values.grantType === 'receive' && values.dataSource === 'birthday';
      },
      componentProps: {
        options: map(dictionary.getDictionaryOpt.get('regionCode'), t => {
          return { value: t.label, label: t.label };
        }) as RadioGroupChildOption[],
        placeholder: '请选择参与区域',
      },
    },
    {
      field: 'userLimit',
      component: 'InputNumber',
      label: '个人最大领取次数',
      colProps: {
        span: 12,
      },
      required: true,
      ifShow({ values }) {
        return ['receive'].includes(values.grantType);
      },
      componentProps: {
        placeholder: '请输入个人最大领取次数',
        min: 0,
        step: 1,
      },
    },
    {
      field: 'userDailyLimit',
      component: 'InputNumber',
      label: '个人每日领取次数',
      colProps: {
        span: 12,
      },
      required: true,
      ifShow({ values }) {
        return values.grantType === 'receive' && values.dataSource === 'coupon';
      },
      componentProps: {
        placeholder: '请输入个人每日领取次数',
        min: 0,
        step: 1,
      },
    },

    {
      field: 'receiveDateRange',
      label: '领取日期',
      component: 'RangePicker',
      colProps: { span: 12 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      required: true,
      ifShow({ values }) {
        return ['receive'].includes(values.grantType);
      },
    },
    {
      field: 'specifyDate',
      label: '限每月领取日期',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        options: Array.from({ length: 31 }, (_, index) => {
          return { value: index + 1, label: index + 1 + '日' };
        }),
        mode: 'multiple',
      },
      required: false,
      ifShow({ values }) {
        return values.grantType === 'receive' && values.dataSource === 'coupon';
      },
    },
    {
      field: 'weekLimit',
      component: 'Select',
      label: '限每周领取星期',
      rulesMessageJoinLabel: true,
      required: false,
      colProps: {
        span: 12,
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('week'),
        mode: 'multiple',
      },
      ifShow({ values }) {
        return values.grantType === 'receive' && values.dataSource === 'coupon';
      },
    },
    {
      field: 'integralFlag',
      label: '设置积分',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: 'N',
      dynamicDisabled: disableEdit || disabled,
      ifShow({ values }) {
        return ['receive'].includes(values.grantType);
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
      },
    },
    {
      field: 'integralThreshold',
      label: '参与积分门槛',
      dynamicDisabled: disableEdit || disabled,
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: { min: 0 },
      ifShow: ({ values }) => {
        return values.integralFlag === 'Y'
      },
    },
    {
      field: 'integralOperateType',
      label: '积分操作类型',
      component: 'RadioGroup',
      colProps: { span: 12 },
      dynamicDisabled: disableEdit || disabled,
      required: true,
      defaultValue: 'DEC',
      ifShow: ({ values }) => {
        return values.integralFlag === 'Y'
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('IntegralOperateType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'integralScore',
      label: '奖励/消耗分值',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: { min: 0 },
      dynamicDisabled: disableEdit || disabled,
      required: true,
      ifShow: ({ values }) => {
        return values.integralFlag === 'Y'
      },
    },
    {
      field: '',
      label: '使用规则',
      colProps: { span: 24 },
      component: 'Divider',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'useDateRange',
      label: '使用日期',
      component: 'RangePicker',
      colProps: { span: 12 },
      dynamicDisabled: disableEdit || disabled,
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      required: true,
    },
    {
      field: 'expireDays',
      component: 'InputNumber',
      label: '领取后指定天数内使用',
      required: false,
      dynamicDisabled: disableEdit || disabled,
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      componentProps: {
        min: 0,
        step: 1,
      },
    },
    {
      field: 'useType',
      component: 'RadioGroup',
      label: '使用类型',
      rulesMessageJoinLabel: true,
      required: true,
      defaultValue: 'offline',
      colProps: {
        span: 12,
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('useType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'couponDesc',
      component: 'InputTextArea',
      label: '票券描述',
      colProps: {
        span: 24,
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'dataSource',
      component: 'Input',
      label: '',
      show: false,
    },
  ];
};

//选择所属工会弹框筛选条件
export const unionFormSchemas = (type): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'productName',
      label: '商品名称',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow: type === 'product',
    },
    {
      field: 'companyName',
      label: '商户名称',
      colProps: { span: 10 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow: type === 'merchant',
    },
    {
      field: 'areaCode',
      label: '所属区县',
      colProps: { span: 8 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          options: dictionary.getDictionaryOpt.get('regionCode'),
        };
      },
      ifShow: type === 'merchant',
    },
  ];
};

//筛选工会列表
export const unionColumns = (type): BasicColumn[] => {
  const userStore = useUserStore();
  const dictionary = useDictionary();
  return [
    {
      title: '商品样图',
      dataIndex: 'productCoverImg',
      ifShow: type === 'product',
      customRender({ text }) {
        return (
          <Image
            src={userStore.getPrefix + text}
            class={`!w-100px`}
          />
        );
      },
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      ifShow: type === 'product',
    },
    {
      title: '商户名称',
      dataIndex: 'companyName',
      ifShow: type === 'merchant',
    },
    {
      title: '所属区县',
      dataIndex: 'areaCode',
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`regionCode_${text}`)?.dictName}</span>;
      },
      ifShow: type === 'merchant',
    },
  ];
};

export const recordColumns = (flag: boolean): BasicColumn[] => {
  return [
    {
      title: '用户姓名',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '活动名称',
      dataIndex: 'activityName',
      width: 150,
    },
    {
      title: '票券名称',
      dataIndex: 'couponName',
      width: 150,
    },
    // {
    //   title: '券码',
    //   dataIndex: 'shortRecordId',
    //   width: 150,
    // },
    {
      title: '领取时间',
      dataIndex: 'createTime',
      width: 120,
    },
    {
      title: '使用状态',
      dataIndex: 'state',
      width: 80,
      customRender: ({ text, record }) => {
        if (text === 'Y') {
          return <span style={{ color: 'green' }}>已使用</span>;
        }
        if (record.useEndTime < dayjs().format('YYYY-MM-DD')) {
          return <span style={{ color: '#666' }}>已过期</span>;
        }
        return <span style={{ color: '#f6bb1b' }}>待使用</span>;
      },
    },
    {
      title: '核销商户',
      dataIndex: 'checkCompanyName',
      width: 120,
    },
    {
      title: '核销人',
      dataIndex: 'checkUserName',
      width: 120,
    },
    {
      title: '核销时间',
      dataIndex: 'checkTime',
      width: 120,
    },
  ];
};

export const recordModalForm = (useType): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'userName',
      label: '用户姓名',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'companyName',
      label: '工会名称',
      colProps: { span: 24 },
      component: 'ShowSpan',
    },
    {
      field: 'couponName',
      label: '票券名称',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'activityName',
      label: '活动名称',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'couponType',
      component: 'ShowSpan',
      label: '优惠类型',
      render({ values }) {
        return dictionary.getDictionaryMap.get(`ticketType_${values.couponType}`)?.dictName || '';
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'amountLimit',
      component: 'ShowSpan',
      label: '门槛金额(元)',
      colProps: {
        span: 12,
      },
      rules: [
        { required: true, validator: validateIntegerAndTwoDecimal, trigger: ['change', 'blur'] },
      ],
      ifShow({ values }) {
        return values.couponType === 'fullDecrement' || values.couponType === 'discount';
      },
    },
    {
      field: 'discountAmount',
      component: 'ShowSpan',
      label: ({ values }) => values.couponType === 'noLimit' ? '票券金额' : '满减金额(元)',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.couponType === 'fullDecrement' || values.couponType === 'noLimit';
      },
    },
    {
      field: 'discountPercent',
      component: 'ShowSpan',
      label: '折扣比例',
      rulesMessageJoinLabel: true,

      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.couponType === 'discount';
      },
    },
    {
      field: 'discountAmount',
      component: 'ShowSpan',
      label: '最大折扣金额(元)',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.couponType === 'discount';
      },
    },
    // {
    //   field: 'shortRecordId',
    //   label: '券码',
    //   colProps: { span: 12 },
    //   component: 'ShowSpan',
    // },
    {
      field: 'createTime',
      label: '领取日期',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'useEndTime',
      label: '有效期',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'state',
      label: '使用状态',
      colProps: { span: 12 },
      component: 'ShowSpan',
      render({ values }) {
        if (values.useEndTime < dayjs().format('YYYY-MM-DD')) {
          return <span>已过期</span>;
        }
        return <span>{values.state === 'Y' ? '已使用' : '待使用'}</span>;
      },
    },
    {
      field: 'checkUserName',
      label: '核销人',
      colProps: { span: 12 },
      component: 'ShowSpan',
      ifShow: 'post' !== useType,
    },
    {
      field: 'checkCompanyName',
      label: '核销商户',
      colProps: { span: 12 },
      component: 'ShowSpan',
      ifShow: 'post' !== useType,
    },
    {
      field: 'checkTime',
      label: '核销时间',
      colProps: { span: 12 },
      component: 'ShowSpan',
      ifShow: 'post' !== useType,
    },
    {
      field: 'receiverName',
      label: '收货人姓名',
      colProps: { span: 12 },
      component: 'ShowSpan',
      ifShow: 'post' === useType,
    },
    {
      field: 'receiverPhone',
      label: '收货人电话',
      colProps: { span: 12 },
      component: 'ShowSpan',
      ifShow: 'post' === useType,
    },
    {
      field: 'detailArea',
      label: '所在地区',
      colProps: { span: 12 },
      component: 'ShowSpan',
      ifShow: 'post' === useType,
    },
    {
      field: 'detailAddress',
      label: '详细地址',
      colProps: { span: 12 },
      component: 'ShowSpan',
      ifShow: 'post' === useType,
    },
  ];
};

export const recordFormSchemas = (couponId: string, activityId: string): FormSchema[] => {
  const useUser = useUserStore();
  return [
    {
      field: 'userName',
      label: '用户姓名',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
    {
      field: 'phone',
      label: '联系电话',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
    {
      field: 'activityId',
      label: '活动名称',
      component: 'ApiSelect',
      colProps: { span: 6 },
      ifShow: !activityId,
      componentProps: () => {
        return {
          placeholder: '请选择活动',
          api: findSimpleList,
          resultField: 'data',
          params: {
            pageSize: 0,
            queryCompanyId: useUser.getUserInfo.companyId,
            orderBy: 'auto_id',
            sortType: 'desc',
            nextLevelFlag: true,
          },
          immediate: true,
          onChange: () => { },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.activityName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'activityName', value: 'activityId' },
        };
      },
    },
    {
      field: 'couponId',
      label: '票券名称',
      component: 'ApiSelect',
      colProps: { span: 6 },
      ifShow: !couponId,
      componentProps: () => {
        return {
          placeholder: '请选择票券',
          api: simpleList,
          resultField: 'data',
          params: {
            pageSize: 0,
            queryCompanyId: useUser.getUserInfo.companyId,
            orderBy: 'auto_id',
            sortType: 'desc',
            nextLevelFlag: true,
          },
          immediate: true,
          onChange: () => { },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.couponName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'couponName', value: 'couponId' },
        };
      },
    },
    {
      field: 'receiveDateRange',
      label: '领取日期',
      component: 'RangePicker',
      colProps: { span: 6 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'destroyDateRange',
      label: '核销日期',
      component: 'RangePicker',
      colProps: { span: 6 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'checkCompanyName',
      label: '核销商户',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
    {
      field: 'useState',
      label: '使用状态',
      component: 'Select',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
      componentProps: function () {
        return {
          options: [
            { label: '待使用', value: '1' },
            { label: '已使用', value: '2' },
            { label: '已过期', value: '3' },
          ],
        };
      },
    },
  ];
};

export const exportColumn = () => {

  return [
    {
      label: '活动名称',
      value: 'activityName',
    },
    {
      label: '票券名称',
      value: 'couponName',
    },
    {
      label: '用户姓名',
      value: 'userName',
    },
    {
      label: '联系电话',
      value: 'phone',
    },
    // {
    //   label: '券码',
    //   value: 'shortRecordId',
    // },
    {
      label: '工会名称',
      value: 'companyName',
    },
    {
      label: '领取时间',
      value: 'createTime',
    },
    {
      label: '使用类型',
      value: 'useType',
    },
    {
      label: '核销状态',
      value: 'state',
    },
    {
      label: '核销人',
      value: 'checkUserName',
    },
    {
      label: '核销人所属商户名称',
      value: 'checkCompanyName',
    },
    {
      label: '核销时间',
      value: 'checkTime',
    },
    {
      label: '收货人姓名',
      value: 'receiverName',
    },
    {
      label: '收货人电话',
      value: 'receiverPhone',
    },
    {
      label: '所在地区',
      value: 'detailArea',
    },
    {
      label: '详细地址',
      value: 'detailAddress',
    },
  ]

}

