<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick()"
          auth="/couponManage/add"
          >新增票券配置</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth:'/couponManage/view'
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                //disabled: record.assignCount !== 0,
                onClick: handleEdit.bind(null, record),
                auth:'/couponManage/edit'
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: record.state === 'N' ? '发布' : '撤销',
                type: 'primary',
                ifShow: props.dataSource === 'birthday' && record.grantType === 'receive',
                onClick: handleUpdateState.bind(null, record),
                auth:'/couponManage/push'
              },
              {
                icon: 'ri:coupon-line',
                label: '领取记录',
                type: 'primary',
                // ifShow: record.assignCount === 0,
                onClick: handleRecord.bind(null, record),
                auth: '/couponManage/record'
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                disabled: record.assignCount !== 0,
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth:'/couponManage/delete'
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <TicketConfigModal
      @register="registerModal"
      width="80%"
      :canFullscreen="false"
      @reloadTable="reloadTable"
    />
    <!--  领取记录-->
    <BasicModal
      v-model:open="visible"
      :destroyOnClose="true"
      :title="title"
      width="80%"
      :showOkBtn="false"
      :can-fullscreen="false"
    >
      <RecordList :couponId="couponId"/>
    </BasicModal>
  </div>
</template>
<script lang="ts" setup>
import { BasicTable, TableAction, useTable } from '/@/components/Table';
import { deleteLine, list, updateState } from '@/api/coupon';
import { columns, formSchemas } from './data';
import { useMessage } from '@monorepo-yysz/hooks';
import RecordList from './record/index.vue';
import TicketConfigModal from './TicketConfigModal.vue';
import { BasicModal, useModal } from '/@/components/Modal';
import { ref } from 'vue';

const props = defineProps({
  dataSource: { type: String, default: 'coupon' },
});

const title = ref('领取记录');
const couponId = ref('');
const visible = ref<boolean>(false);
const { createSuccessModal, createErrorModal, createConfirm } = useMessage();
const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  beforeFetch: params => {
    params.sortType = 'desc';
    params.orderBy = 'create_time';
    params.dataSource = props.dataSource;
    return params;
  },
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 450,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/couponManage/delete', '/couponManage/edit', '/couponManage/record', '/couponManage/push',
    '/couponManage/view','/couponManage/add']
  },
});

const [registerModal, { openModal }] = useModal();

function handleClick(record, state = 'add') {
  openModal(true, {
    state,
    record: {
      ...record,
      dataSource: props.dataSource,
    },
  });
}
function reloadTable() {
  reload();
}
function handleEdit(record) {
  handleClick(record, 'edit');
}

function handleView(record) {
  handleClick(record, 'view');
}

function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除”${record.ticketName}“`,
    onOk: function () {
      deleteLine({ ticketId: record.ticketId }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleRecord(record) {
  couponId.value = record.couponId;
  title.value = `${record.couponName}-领取记录`;
  visible.value = true;
}

async function handleUpdateState(record) {
  const state = record.state === 'Y' ? 'N' : 'Y';

  const { code } = await updateState({ couponId: record.couponId });
  if (code === 200) {
    record.state = state;
    createSuccessModal({ content: `${state === 'Y' ? '发布' : '撤销'}成功` });
  }
}
</script>

<style lang="less" module>
.pick-detail {
  :global {
    .ant-table-body {
      max-height: 400px !important;
      height: 400px !important;
    }
  }
}
</style>
