<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleDowen"
          :loading="spinning"
          auth="/couponRecord/view"
          >导出</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth:'/couponRecord/export'
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <RecordModal
      @register="registerModal"
      width="70%"
      :canFullscreen="false"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, TableAction, useTable } from '/@/components/Table';
import { exportRecord, recordList } from '@/api/coupon';
import { recordColumns, recordFormSchemas, exportColumn } from '@/views/coupon/data';
import RecordModal from '@/views/coupon/record/RecordModal.vue';
import { useModal } from '@/components/Modal';
import { computed, ref, unref } from 'vue';
import dayjs from 'dayjs';
import { downloadByUrl } from '@monorepo-yysz/utils';
import { useMessage } from '@monorepo-yysz/hooks';
const { createErrorModal } = useMessage();
const [registerModal, { openModal }] = useModal();
const props = defineProps({
  couponId: { type: String },
  activityId: { type: String },
});
const spinning = ref<boolean>(false);
const columns = computed(() => {
  return recordColumns(!!props.couponId);
});
const exportParams = ref<Recordable>({});

const schemas = computed(() => {
  return recordFormSchemas(props.couponId, props.activityId);
});

const exportColumnList = computed(() => {
  return exportColumn();
});

const [registerTable] = useTable({
  rowKey: 'autoId',
  columns: columns,
  api: recordList,
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: schemas,
    autoSubmitOnEnter: true,
    autoAdvancedLine:2,
    submitOnChange:true,
  },
  beforeFetch(p) {
    const { receiveDateRange, destroyDateRange, ...params } = p;
    params.orderBy = 'auto_id';
    params.sortType = 'desc';
    if (props.couponId) {
      params.couponId = props.couponId;
    }
    if (props.activityId) {
      params.activityId = props.activityId;
    }
    if (receiveDateRange?.length) {
      params.receiveStartTime = receiveDateRange[0] + ' 00:00:00';
      params.receiveEndTime = receiveDateRange[1] + ' 23:59:59';
    }
    if (destroyDateRange?.length) {
      params.destroyStartTime = destroyDateRange[0] + ' 00:00:00';
      params.destroyEndTime = destroyDateRange[1] + ' 23:59:59';
    }
    if(!props.couponId && !props.activityId){
      params.dataSource = 'coupon'
    }

    exportParams.value = params;
    return params;
  },
  useSearchForm: true,
  bordered: true,
  maxHeight: props.couponId ? 300 : props.activityId ? 350 : null,
  actionColumn: {
    title: '操作',
    width: 100,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/couponRecord/view', '/couponRecord/export']
  },
});
function handleView(record) {
  openModal(true, {
    record,
  });
}

function handleDowen() {
  const { activityId, couponId } = unref(exportParams);
  if (!activityId && !couponId) {
    createErrorModal({ content: '请选择指定活动或票券进行导出' });
    return;
  }
  spinning.value = true;
  //获取导出字段
  exportRecord({ ...unref(exportParams), exportColumn:exportColumnList.value }).then(res => {
    const url = window.URL.createObjectURL(res);
    const fileName = `领取记录-${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    downloadByUrl({
      url,
      fileName,
    });
    spinning.value = false;
  });
}
</script>
