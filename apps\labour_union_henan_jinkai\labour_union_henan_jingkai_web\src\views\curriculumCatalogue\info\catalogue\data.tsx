
import dayjs from 'dayjs';
import { useDictionary } from '@/store/modules/dictionary';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useUserStore } from '@/store/modules/user';
import { cloneDeep, filter } from 'lodash-es';
import D0MPurify from 'dompurify'
import { uploadApi } from '@/api/sys/upload';
import { h } from 'vue';
import { Tinymce } from '@/components/Tinymce';
const dictionary = useDictionary();
const userStore = useUserStore();
//列表
export const modelColumns = (): BasicColumn[] => {
    return [
        {
            title: '课程名称',
            dataIndex: 'catalogueName',
        },
        {
            title: '类型',
            dataIndex: 'catalogueType',//0直播1视频2图文
            customRender({ text }) {
                const name = text === 'live' ? '直播' : text === 'video' ? '视频': '图文';
                return <span title={name}>{name}</span>;
            },
        },
        {
          dataIndex: 'publishStatus',
          title: '发布状态',
          customRender({ record }) {
            // return text == 'n' ? '未发布' : '已发布';
            if(record.catalogueType === 'live'){
              const name = record?.isOpenLive == 'notStart' ? '-未开播' : record?.isOpenLive == 'onGoing' ? '-进行中':'-已结束'
              return <div>{record.publishStatus == 'n' ? '未发布' : '已发布'}{name}</div>
            }else{
              return record.publishStatus == 'n' ? '未发布' : '已发布'
            }
          },
        },
        // {
        //     dataIndex: 'catalogueIntroduce',
        //     title: '课程目录简介',
        //     customRender({ text }) {
        //         return<span vHtml={D0MPurify.sanitize(text)}></span>
        //       },
        //   },
    ]
}

//弹框筛选条件
export const modelSchemas = (): FormSchema[] => {
    return [
        {
            field: 'catalogueName',
            label: '课程名称',
            colProps: { span: 7 },
            component: 'Input',
            rulesMessageJoinLabel: true,
        },
        // {
        //     field: 'catalogueType',
        //     label: '课程类型',
        //     component: 'Select',
        //     colProps: { span: 7 },
        //     rulesMessageJoinLabel: true,
        //     componentProps: function () {
        //       return {
        //         showSearch: true,
        //         filterOption: (input: string, option: any) => {
        //           return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        //         },
        //         options: dictionary.getDictionaryOpt.get('catalogueType'),
        //       };
        //     },
        // },
        // {
        //     field: 'nextLevelFlag',
        //     component: 'Checkbox',
        //     label: '包含下级',
        //     colProps: { span: 3 },
        //     defaultValue: true,
        // },
    ]
}
export const typeFormItem = (): FormSchema[] => {
    return [
        
        {
            field: 'curriculumBizId',
            label: '课程id',
            colProps: { span: 12 },
            component: 'Input',
            rulesMessageJoinLabel: true,
            show:false
        },
        {
            field: 'catalogueName',
            label: '课程名称',
            colProps: { span: 24 },
            component: 'Input',
            required: true,
            rulesMessageJoinLabel: true,
            componentProps:function(){
              return{
                maxlength: 200,
                showCount:true
              }
            }
        },
        {
            field: 'catalogueType',
            label: '课程类型',
            component: 'Select',
            required: true,
            colProps: { span: 24 },
            rulesMessageJoinLabel: true,
            componentProps: function () {
              return {
                showSearch: true,
                filterOption: (input: string, option: any) => {
                  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                },
                options: dictionary.getDictionaryOpt.get('catalogueType'),
              };
            },
        },
        {
          field: 'liveStartTime',
          label: '直播开始时间',
          colProps: { span: 12 },
          ifShow: ({ values }) => {
              return (values.catalogueType === "live" );
          },
          component: 'DatePicker',
          componentProps: {
              showTime: true,
              format: 'YYYY-MM-DD HH:mm',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
          },
          required: true,
          rulesMessageJoinLabel: true,
      },
      {
          field: 'liveEndTime',
          label: '直播结束时间',
          colProps: { span: 12 },
          ifShow: ({ values }) => {
              return (values.catalogueType === "live" );
          },
          component: 'DatePicker',
          componentProps: {
              showTime: true,
              format: 'YYYY-MM-DD HH:mm',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
          },
          required: true,
          rulesMessageJoinLabel: true,
      },
      {
          field: 'livePlayBack',
          label: '直播地址',
          colProps: { span: 24 },
          component: 'Input',
          rulesMessageJoinLabel: true,
          required: true,
          ifShow: ({ values }) => {
              return (values.catalogueType === "live" );
          },
      },
      {
        field: 'catalogueVideo',
        label: '课程视频',
        required: true,
        rulesMessageJoinLabel: true,
        component: 'Upload',
        ifShow: ({ values }) => {
            return (values.catalogueType === "video"  );
        },
        componentProps: {
          api: uploadApi,
          uploadParams: {
            operateType: 68,
          },
          maxNumber: 1,
          maxSize: 100,
          accept: ['video/*'],
        },
      },
    //   {
    //     field: 'catalogueVideoTime',
    //     label: '课程视频时长',
    //     colProps: { span: 24 },
    //     required: true,
    //     component: 'Input',
    //     ifShow: ({ values }) => {
    //         return (values.catalogueType === "video" );
    //     },
    //     rulesMessageJoinLabel: true,
    // },
        {
            field: 'catalogueIntroduce',
            component: 'Input',
            label: '课程目录简介',
            required: true,
            rulesMessageJoinLabel: true,
            colProps: {
                span: 24,
              },
            render: ({ model, field, disabled }) => {
              return h(Tinymce, {
                value: model[field],
                onChange: (value: string) => {
                  model[field] = value;
                },
                showImageUpload: false,
                operateType: 68,
                options: {
                  readonly: disabled,
                },
              });
            },
          },
          {
            field: 'graphic',
            component: 'Input',
            label: '图文介绍',
            required: true,
            rulesMessageJoinLabel: true,
            colProps: {
                span: 24,
              },
            ifShow: ({ values }) => {
                return (values.catalogueType === "photo" );
            },
            render: ({ model, field, disabled }) => {
              return h(Tinymce, {
                value: model[field],
                onChange: (value: string) => {
                  model[field] = value;
                },
                showImageUpload: false,
                operateType: 68,
                options: {
                  readonly: disabled,
                },
              });
            },
          },
          
        // {
        //     field: 'liveReturnBack',
        //     label: '直播回放地址',
        //     colProps: { span: 12 },
        //     component: 'Input',
        //     rulesMessageJoinLabel: true,
        //     ifShow: ({ values }) => {
        //         return (values.catalogueType === "live" );
        //     },
        // },
        {
            field: 'liveCover',
            label: '直播封面图',
            colProps: { span: 12 },
            component: 'CropperForm',
            ifShow: ({ values }) => {
                return (values.catalogueType === "live" );
            },
            componentProps: function () {
              return {
                operateType: 68,
                imgSize:1000/400
              };
            },
            rulesMessageJoinLabel: true,
            required: true,
          },
          // {
          //   field: 'isOpenLive',
          //   label: '是否开播',
          //   colProps: { span: 12 },
          //   component: 'Select',
          //   rulesMessageJoinLabel: true,
          //   ifShow: ({ values }) => {
          //       return (values.catalogueType === "live" );
          //   },
          //   componentProps: function () {
          //     return {
          //       options: [
          //         { label: '是', value: 'y' },
          //         { label: '否', value: 'n' },
          //       ],
          //     }
          //   },
          // },
    ]
}


  