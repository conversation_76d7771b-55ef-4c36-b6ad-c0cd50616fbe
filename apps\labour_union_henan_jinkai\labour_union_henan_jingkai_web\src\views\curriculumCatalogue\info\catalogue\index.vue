<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增课程目录
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                //   auth: '/employeeMessageType/modify',
                disabled: record.publishStatus === 'y'
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                //   auth: '/employeeMessageType/delete',
              },
              {
                icon:
                  record.publishStatus === 'n'
                    ? 'material-symbols:lock-open'
                    : 'material-symbols:lock',
                label: record.publishStatus === 'n' ? '发布' : '撤销',
                type: 'primary',
                onClick: handleConfirm.bind(null, record),
                // auth: '/liveInfo/publish',
              },
              {
                icon:
                  record.isOpenLive === 'notStart'
                    ? 'material-symbols:auto-read-pause-rounded'
                    : 'material-symbols:auto-read-play',
                label: record.isOpenLive === 'notStart' ? '开播' : '关播',
                type: 'primary',
                onClick: handleOpen.bind(null, record),
                ifShow: record.catalogueType === 'live',
                // auth: '/liveInfo/openLive',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
  <TypeModal
    @register="registerListModal"
    :canFullscreen="false"
    width="50%"
    @success="handleModel"
  ></TypeModal>
  <OpenOrClsoeLiveModel
    @register="registerCloseModal"
    @success="handleCloseSuccess"
    :can-fullscreen="false"
    width="40%"
  >
  </OpenOrClsoeLiveModel>
</template>

<script lang="ts" setup>
import { computed, nextTick, unref, ref } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useTable, BasicTable, TableAction } from '/@/components/Table';
import { modelColumns, modelSchemas } from './data';
import {
  curriculumCatalogue,
  deleteCatalogue,
  curriculumCatalogueSave,
  openStatus,
  publishStatus,
} from '@/api/curriculumInfo/index';
import TypeModal from './typeModal.vue';
const emit = defineEmits(['register', 'success']);
import { useMessage } from '@monorepo-yysz/hooks';
import { useModal } from '@/components/Modal';
import OpenOrClsoeLiveModel from '../../../linkManagement/openOrClsoeLiveModel.vue';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();
const [registerCloseModal, { openModal: openLiveModal, closeModal: closeLiveModal }] = useModal();

const catalogueName = ref(null);
const curriculumBizId = ref(null);
const title = computed(() => {
  return `${unref(catalogueName)}的课程目录`;
});
const [registerModal, {}] = useModalInner(async data => {
  catalogueName.value = data.catalogueName;
  curriculumBizId.value = data.curriculumBizId;
  await nextTick();
  await reload({
    // searchInfo:{
    //     curriculumBizId: data.curriculumBizId
    // }
  });
  await clearSelectedRowKeys();
});

const [registerTable, { clearSelectedRowKeys, reload }] = useTable({
  rowKey: 'catalogueId',
  api: curriculumCatalogue,
  columns: modelColumns(),
  maxHeight: 430,
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: modelSchemas(),
  },
  afterFetch: data => {
    const userData = data;
    return userData && userData.length > 0 ? userData : [];
  },
  beforeFetch(params) {
    params.curriculumBizId = unref(curriculumBizId);
    return params;
  },
  actionColumn: {
    title: '操作',
    width: 360,
    dataIndex: 'action',
    // slots: { customRender: 'action' },
    fixed: undefined,
    // auth: ['/difficultEmployees/choice']
  },
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
  indexColumnProps: { width: 90 },
});

//列表信息
const [registerListModal, { openModal, closeModal }] = useModal();
//新增
function handleClick() {
  openModal(true, { isUpdate: false });
}
//编辑
function handleEdit(record) {
  openModal(true, { isUpdate: true, record });
}
//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.catalogueName}`,
    onOk: function () {
      deleteCatalogue(record.catalogueId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload({
            searchInfo: {
              curriculumBizId: curriculumBizId.value,
            },
          });
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}
//选择按钮操作
function handleModel(record) {
  
  if (record.values.catalogueVideo) {
    record.values.catalogueVideo = record.values.catalogueVideo.toString();
  }
  record.values.curriculumBizId = curriculumBizId.value;
  curriculumCatalogueSave(record.values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${record.isUpdate ? '编辑' : '新增'}成功`,
      });
      reload({
        searchInfo: {
          curriculumBizId: curriculumBizId.value,
        },
      });
      closeModal();
    } else {
      createErrorModal({
        content: `${record.isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}

function handleConfirm(record) {
  const name = record.publishStatus === 'n' ? '发布' : '撤销';
  createConfirm({
    iconType: 'warning',
    content: `请确定${name}?`,
    onOk: function () {
      publishStatus({
        catalogueId: record.catalogueId,
        publishStatus: record.publishStatus === 'y' ? 'n' : 'y',
      }).then(({ code, message }) => {
        if (code === 200) {
          reload();
        } else {
          createErrorModal({
            content: `${record.publishStatus === 'n' ? '发布' : '撤销'}失败! ${message}`,
          });
        }
      });
    },
  });
}

function handleOpen(record) {
  const name = record.isOpenLive === 'notStart' ? '开播' : '关播';
  if (record.isOpenLive === 'notStart') {
    createConfirm({
      iconType: 'warning',
      content: `请确定${name}吗?`,
      onOk: function () {
        openStatus({
          catalogueId: record.catalogueId,
          isOpenLive: 'onGoing',
        }).then(({ code, message }) => {
          if (code === 200) {
            reload();
          } else {
            createErrorModal({
              content: `${record.isOpenLive === 'notStart' ? '开播' : '关播'}失败! ${message}`,
            });
          }
        });
      },
    });
  } else {
    openLiveModal(true, { isUpdate: true, disabled: true, record: record });
  }
}

//回放地址回调
function handleCloseSuccess({ isUpdate, values }) {
  console.log(values.livePlayReturn);
  openStatus({
    catalogueId: values.catalogueId,
    isOpenLive: (values.isOpenLive = 'hasEnd'),
    livePlayReturn: values.livePlayReturn,
  }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `链接回放地址添加成功`,
      });
      reload();
      closeLiveModal();
    } else {
      createErrorModal({
        content: `链接回放地址添加失败! ${message}`,
      });
    }
  });
}
</script>
