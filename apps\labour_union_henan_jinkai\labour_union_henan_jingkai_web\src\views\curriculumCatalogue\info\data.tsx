import { Tooltip } from 'ant-design-vue';
import { useDictionary } from '@/store/modules/dictionary';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useUserStore } from '@/store/modules/user';
import { h } from 'vue';
import { Tinymce } from '@/components/Tinymce';
import { Image } from 'ant-design-vue';
import { uploadApi } from '@/api/sys/upload';
const dictionary = useDictionary();
const userStore = useUserStore();
export function typeColumns(): BasicColumn[] {
  return [
    {
      dataIndex: 'companyName',
      title: '工会名称',
    },
    {
      dataIndex: 'curriculumName',
      title: '课堂名称',
      ellipsis:true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      dataIndex: 'currTypeId',
      title: '课堂栏目',
      customRender: ({ text }) => {
        return dictionary.getDictionaryMap.get(`courseSection_${text}`)?.dictName;
      },
    },
    {
      title: '课程封面图',
      dataIndex: 'curriculumCover',
      customRender: ({ record }) => {
        return record.curriculumCover ? (
          <Image
            src={userStore.getPrefix + record.curriculumCover}
            width={40}
            height={40}
          />
        ) : (
          ''
        );
      },
    },
    // {
    //   dataIndex: 'curriculumIntroduce',
    //   title: '课堂简介',
    //   customRender({ text }) {
    //       return<span vHtml={D0MPurify.sanitize(text)}></span>
    //     },
    // },
    {
      dataIndex: 'publishStatus',
      title: '发布状态',
      customRender({ text }) {
        return dictionary.getDictionaryMap.get(`CataloguePublishStatus_${text}`)?.dictName;
      },
    },
    {
      dataIndex: 'publishTime',
      title: '发布时间',
    },
  ];
}
export function typeFormItem(isUpdate: boolean): FormSchema[] {
  return [
    {
      field: 'curriculumName',
      label: '课堂名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        maxlength: 40,
        showCount: true,
      },
    },
    {
      field: 'currTypeId',
      label: '课堂栏目',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          options: dictionary.getDictionaryOpt.get('courseSection'),
        };
      },
    },
    {
      field: 'introduceImage',
      label: '简介图片',
      colProps: { span: 24 },
      component: 'Upload',
      componentProps: {
        uploadParams: {
          operateType: 68,
        },
        maxSize: 10,
        maxNumber: 1,
        api: uploadApi,
        accept: ['image/*'],
      },
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'curriculumCover',
      label: '课程封面图',
      colProps: { span: 24 },
      component: 'CropperForm',
      componentProps: function () {
        return {
          operateType: 70,
          imgSize:1000/400
        };
      },
      rulesMessageJoinLabel: true,
      required: true,
    },
    {
      field: 'curriculumIntroduce',
      component: 'Input',
      label: '课程简介',
      required: true,
      rulesMessageJoinLabel: true,
      colProps: {
        span: 24,
      },
      render: ({ model, field, disabled }) => {
        return h(Tinymce, {
          value: model[field],
          onChange: (value: string) => {
            model[field] = value;
          },
          showImageUpload: false,
          operateType: 68,
          options: {
            readonly: disabled,
          },
        });
      },
    },
  ];
}

export function searchSchemas(): FormSchema[] {
  return [
    {
      field: 'curriculumName',
      component: 'Input',
      label: '课堂名称',
      colProps: { span: 6 },
    },
    {
      field: 'currTypeId',
      component: 'Select',
      label: '课堂栏目',
      colProps: { span: 6 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('courseSection'),
        };
      },
    },
    // {
    //     field: 'queryCompanyId',
    //     label: '下级工会',
    //     colProps: { span: 6 },
    //     component: 'Select',
    //     rulesMessageJoinLabel: true,
    //     ifShow: userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5',
    //     componentProps: function () {
    //       return {
    //         options: filter(
    //           cloneDeep(dictionary.getDictionaryOpt.get(`unionsInfo`)),
    //           v => v.value !== '6650f8e054af46e7a415be50597a99d5'
    //         ),
    //       };
    //     },
    //   },
    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: { span: 3 },
      defaultValue: true,
    },
  ];
}
