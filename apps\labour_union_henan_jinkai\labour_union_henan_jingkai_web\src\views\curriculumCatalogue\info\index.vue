<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/curriculum/add"
        >
          新增课堂信息
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/curriculum/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/curriculum/modify',
                disabled: record.publishStatus === 'pass'
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/curriculum/delete',
                // disabled: record.publishStatus === 'pass'
              },
              {
                icon: 'bx:log-out-circle',
                label: record.publishStatus === 'pass' ? '撤销' : '发布',
                type: 'primary',
                onClick: handlePublish.bind(null, record),
                auth: '/curriculum/publish',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '课程目录',
                type: 'primary',
                onClick: handleCatalogue.bind(null, record),
                auth: '/curriculum/catalList',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <TypeModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="40%"
    >
    </TypeModal>
    <Catalogue
      @register="registerCataModal"
      :can-fullscreen="false"
      width="55%"
    >
    </Catalogue>
  </div>
</template>

<script lang="ts" setup>
import TypeModal from './typeModal.vue';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { typeColumns, searchSchemas } from './data';
import { computed, unref, useAttrs, createVNode } from 'vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  curriculumInfoFindList,
  saveOrUpdateByDTO,
  deleteList,
  publishCurr,
  getVoByDto
} from '@/api/curriculumInfo/index';
import Catalogue from './catalogue/index.vue';
const attrs = useAttrs();

const type = computed<any>(() => {
  return attrs.type;
});


const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: typeColumns(),
  authInfo: '/curriculum/add',
  showIndexColumn: false,
  api: curriculumInfoFindList,
  beforeFetch: params => {
    params.groupCode = unref(type)?.groupCode;
    return params;
  },
  formConfig: {
    labelWidth: 120,
    schemas: searchSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  bordered: true,
  useSearchForm: true,
  actionColumn: {
    title: '操作',
    width: 460,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/curriculum/view',
      '/curriculum/modify',
      '/curriculum/publish',
      '/curriculum/catalList',
      '/curriculum/delete',
    ],
  },
});
const [registerCataModal, { openModal: openCataModal }] = useModal();

//新增
function handleClick() {
  openModal(true, { isUpdate: false });
}

//编辑
function handleEdit(record) {
  openModal(true, { isUpdate: true, record });
}
//详情
function handleView(record) {
  getVoByDto({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}
//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.curriculumName}`,
    onOk: function () {
      deleteList(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ isUpdate, values }) {
  if (values.curriculumIntroduce) {
    values.curriculumIntroduce = values.curriculumIntroduce.toString();
  }
  if (values.introduceImage) {
    values.introduceImage = values.introduceImage.toString();
  }

  saveOrUpdateByDTO(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
//发布
function handlePublish(record) {
  const name = record.publishStatus === 'pass' ? '撤销' : '发布';
  const publishStatus = record.publishStatus === 'pass' ? 'refuse' : 'pass';

  createConfirm({
    iconType: 'warning',
    content: `请确认要${name}${record.curriculumName}`,
    onOk: function () {
      publishCurr({ autoId: record.autoId, publishStatus }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `${name}成功` });
          reload();
        } else {
          createErrorModal({ content: `${name}失败，${message}` });
        }
      });
    },
  });
}
//课程目录
function handleCatalogue(record) {
  openCataModal(true, {
    curriculumBizId: record.curriculumBizId,
    catalogueName: record.curriculumName,
  });
}
</script>
