<template>
  <Card
    class="border !rounded-10px"
    :class="$style['card-template']"
    v-bind="$attrs"
  >
    <template
      #extra
      v-if="ifExtra"
    >
      <radio-group
        v-model:value="selected"
        button-style="outline"
        @change="handleChange"
      >
        <radio-button
          v-for="item in extra"
          :value="item.value"
          class="!mx-2 !border-[#227EFF] !rounded-6px"
        >
          {{ item.name }}
        </radio-button>
      </radio-group>
    </template>
    <template #title>
      <div class="flex relative">
        <img
          :src="titleIcon"
          class="absolute left-0 w-[60px] h-[31px]"
        />
        <div
          class="pl-33px text-[#111111]"
          style="font-family: Source Han Sans CN"
          >{{ title }}</div
        >
      </div>
    </template>
    <template
      #[item]="data"
      v-for="item in Object.keys($slots)"
    >
      <slot
        :name="item"
        v-bind="data || {}"
      ></slot>
    </template>
  </Card>
</template>

<script lang="ts" setup>
import { Card, RadioGroup, Radio } from 'ant-design-vue';
import { ref } from 'vue';
import titleIcon from '@/assets/images/dashboard/title-icon.png';

const RadioButton = Radio.Button;

const props = defineProps({
  title: { type: String },
  ifExtra: { type: Boolean, default: false },
  select: { type: String, default: 'area' },
});

const emit = defineEmits(['change']);

const selected = ref<string>(props.select);

const extra = ref<Recordable[]>([
  { value: 'area', name: '区域' },
  { value: 'week', name: '近七日' },
]);

function handleChange(e) {
  emit('change', { type: e.target.value });
}
</script>

<style lang="less" module>
.card-template {
  :global {
    height: 100%;

    .ant-card-head {
      padding: 7px 12px 7px 12px !important;
      border-bottom: none !important;
      min-height: 31px !important;

      .ant-card-extra {
        margin-right: 20px;

        .ant-radio-button-wrapper {
          border-left-width: thin !important;
          @apply text-[#227EFF];

          &::before {
            width: 0 !important;
          }
        }

        .ant-radio-button-wrapper-checked {
          background: #227eff !important;
          color: #ffffff !important;
        }
      }
    }

    .ant-card-body {
      padding: 0 !important;
      height: calc(100% - 42px);
    }
  }
}
</style>
