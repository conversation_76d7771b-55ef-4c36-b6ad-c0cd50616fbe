<template>
  <CardTemplate :title="`平台资讯流量统计${unionName}`">
    <div :class="$style['news-visit']">
      <div class="flex justify-center items-start px-[15px] w-full">
        <div
          v-for="item in newsStatistic"
          class="w-full px-5px h-full flex items-center justify-center"
        >
          <div class="!flex justify-center items-center w-[72px] h-full">
            <img
              :src="item.icon"
              class="inline w-full h-[56px]"
            />
          </div>
          <div
            :span="14"
            class="pl-2"
          >
            <Statistic
              :value="item.number"
              :valueStyle="{
                fontSize: '26px',
                fontWeight: 500,
                color: '#1371F7',
                fontFamily: 'Source Han Sans CN MEDIUM',
              }"
              v-if="item.number && item.number !== 0"
            />
            <div
              v-else
              class="text-24px text-[#0A3660] font-bold text-start"
              >—</div
            >

            <span class="text-[#0A3660] text-14px">{{ item.title }}</span>
          </div>
        </div>
      </div>
      <div class="w-full flex justify-center items-center">
        <div
          v-for="item in news"
          class="w-1/4 h-full"
        >
          <div class="relative flex justify-center items-center w-full h-[195px]">
            <img
              :src="item.icon"
              class="h-full w-[129px] absolute bottom-0"
            />
            <div class="absolute top-[28px] w-[91px] h-[32px] left-[8px]">
              <img
                :src="item.icon2"
                class="w-full h-full"
              />
              <span
                class="absolute top-0 flex items-center justify-center w-full left-0 text-[#fff] h-4/5"
              >
                {{ item.title }}
              </span>
            </div>
            <div
              class="text-left w-full pl-[19px]"
              :class="item.contentClass"
            >
              <div>
                累计：
                <span class="text-[16px] font-bold z-10 relative">
                  {{ item.total && item.total !== 0 ? item.total : '-' }}
                </span>
              </div>
              <div>
                昨日：
                <span class="text-[16px] font-bold z-10 relative">
                  {{ item.yesterday && item.yesterday !== 0 ? item.yesterday : '-' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </CardTemplate>
</template>

<script lang="ts" setup>
import CardTemplate from '../CardTemplate.vue';
import { Statistic } from 'ant-design-vue';
import { inject, ref, unref, watch } from 'vue';
import {
  collection,
  comment,
  good,
  iconNews,
  iconNews2,
  share,
  iconShare,
  iconCollection,
  iconComment,
  iconGood,
} from '../../../image';
import { newsStatistics } from '@/api/big';
import { merge } from 'lodash-es';

const newsStatistic = ref<Recordable[]>([
  { title: '总发布量(条)', icon: iconNews, number: 0 },
  { title: '总阅读量(人次)', icon: iconNews2, number: 0 },
]);

const news = ref<Recordable[]>([
  {
    title: '点赞量(次)',
    total: 0,
    yesterday: 0,
    icon: good,
    icon2: iconGood,
    contentClass: 'text-[#3251B1]',
  },
  {
    title: '收藏量(次)',
    total: 0,
    yesterday: 0,
    icon: collection,
    icon2: iconCollection,
    contentClass: 'text-[#381EB2]',
  },
  {
    title: '分享量(次)',
    total: 0,
    yesterday: 0,
    icon: share,
    icon2: iconShare,
    contentClass: 'text-[#1C9070]',
  },
  {
    title: '评论量(条)',
    total: 0,
    yesterday: 0,
    icon: comment,
    icon2: iconComment,
    contentClass: 'text-[#EF8486]',
  },
]);

const unionCode = inject('userUnionCode');

const unionName = inject('userUnionName');

async function init() {
  const {
    readingQuantity,
    yesterdayCollectTotal,
    shareTotal,
    collectTotal,
    yesterdayLikeTotal,
    commentTotal,
    yesterdayShareTotal,
    yesterdayCommentTotal,
    releaseVolume,
    likeTotal,
  } = await newsStatistics({
    companyId:
      unref(unionCode) !== '6650f8e054af46e7a415be50597a99d5' ? unref(unionCode) : undefined,
  });

  newsStatistic.value = merge(unref(newsStatistic), [
    { number: releaseVolume || 0 },
    { number: readingQuantity || 0 },
  ]);

  news.value = merge(unref(news), [
    {
      total: likeTotal || 0,
      yesterday: yesterdayLikeTotal || 0,
    },
    {
      total: collectTotal || 0,
      yesterday: yesterdayCollectTotal || 0,
    },
    {
      total: shareTotal || 0,
      yesterday: yesterdayShareTotal || 0,
    },
    {
      total: commentTotal || 0,
      yesterday: yesterdayCommentTotal || 0,
    },
  ]);
}

watch(
  () => unionCode,
  async () => {
    init();
  },
  { deep: true }
);
</script>

<style lang="less" module>
.news-visit {
  :global {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    height: 100%;
    padding: 5px;
  }
}
</style>
