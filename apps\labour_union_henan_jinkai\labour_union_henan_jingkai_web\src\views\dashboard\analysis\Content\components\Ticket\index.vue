<template>
  <CardTemplate :title="`平台普惠票券发放统计${unionName}`">
    <div :class="$style.ticket">
      <div class="px-25px mb-15px">
        <div
          class="flex justify-center items-start border-transparent rounded-10px bg-[rgba(61,160,255,0.06)] divide-x divide-[#E5E5E5]"
        >
          <div
            v-for="item in statistic"
            class="w-full px-5px h-[56px] h-full py-[6px]"
          >
            <div class="!flex justify-center items-center flex-col">
              <Statistic
                :value="item.number"
                :valueStyle="{
                  fontSize: '26px',
                  fontWeight: 500,
                  color: '#0A3660',
                  fontFamily: 'Source Han Sans CN MEDIUM',
                }"
                v-if="item.number && item.number !== 0"
              />

              <div
                v-else
                class="text-26px text-hex-666666 font-bold text-center"
                >—</div
              >

              <span class="text-hex-666666 text-14px">{{ item.title }}</span>
            </div>
          </div>
        </div>
      </div>
      <div
        ref="chartRef"
        class="h-[19.9vh]"
      />
    </div>
  </CardTemplate>
</template>

<script lang="ts" setup>
import { inject, Ref, ref, unref, watch } from 'vue';
import CardTemplate from '../CardTemplate.vue';
import { Statistic } from 'ant-design-vue';
import { useECharts } from '@/hooks/web/useECharts';
import { useEchartsTool } from '@monorepo-yysz/hooks';
import { couponInfo } from '@/api/big';
import { map, merge } from 'lodash-es';

const statistic = ref<Recordable[]>([
  { title: '历史总发布量(张)', number: 0 },
  { title: '历史总领取量(张)', number: 0 },
  { title: '历史总核销量(张)', number: 0 },
]);

const chartRef = ref<HTMLDivElement | null>(null);

const xAxis = ref<string[]>([]);

const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

const { lineFormatter } = useEchartsTool(getInstance);

const unionCode = inject('userUnionCode');
const unionName = inject('userUnionName');
watch(
  () => unionCode,
  async () => {
    const {
      assignCount,
      publishCount,
      usedCount,
      publishCountList,
      assignCountList,
      usedCountList,
      columnList,
    } = await couponInfo({
      companyId:
        unref(unionCode) != '6650f8e054af46e7a415be50597a99d5' ? unref(unionCode) : undefined,
    });

    xAxis.value = columnList;

    statistic.value = merge(unref(statistic), [
      { number: Number(publishCount || 0) },
      { number: Number(assignCount || 0) },
      { number: Number(usedCount || 0) },
    ]);

    setLineEcharts([publishCountList || [], assignCountList || [], usedCountList || []]);
  },
  { deep: true }
);

function setLineEcharts(dataSource) {
  setOptions({
    tooltip: {
      show: true,
      trigger: 'axis',
      formatter: params => lineFormatter(params),
    },
    legend: {
      show: true,
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      right: 7,
    },
    xAxis: {
      type: 'category',
      boundaryGap: ['20%', '30%'],
      data: unref(xAxis),
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: [
      {
        name: '张',
        nameTextStyle: {
          fontSize: 12,
          color: '#999999',
        },
        type: 'value',
        max: 'dataMax',
        min: 0,
        axisLine: {
          show: true,
          lineStyle: { color: '#E3ECFA' },
        },
        axisLabel: {
          color: '#999999',
        },
        minInterval: 1,
        splitArea: {
          show: true,
          areaStyle: {
            color: ['#EDF6FF', '#edf6ffbf', '#edf6ff7a', '#edf6ff45', '#edf6ff2e', '', '', ''], //只需要前几个有颜色
          },
        },
      },
      {
        name: '',
        axisLine: {
          show: true,
          lineStyle: { color: '#E3ECFA' },
        },
        type: 'value',
      },
    ],
    grid: { left: '2%', right: '2%', top: '16%', bottom: '1%', containLabel: true },
    series: [
      {
        smooth: true,
        data: map(dataSource[0], v => (v > 0 ? v : '-')),
        type: 'line',
        name: '发布量',
        symbol: 'circle',
        itemStyle: {
          color: '#1E84FF',
        },
      },
      {
        smooth: true,
        data: map(dataSource[1], v => (v > 0 ? v : '-')),
        name: '领取量',
        type: 'line',
        symbol: 'circle',
        itemStyle: {
          color: '#35C572',
        },
      },
      {
        smooth: true,
        data: map(dataSource[2], v => (v > 0 ? v : '-')),
        name: '核销量',
        type: 'line',
        symbol: 'circle',
        itemStyle: {
          color: '#FFAB09',
        },
      },
    ],
  });
}
</script>

<style lang="less" module>
.ticket {
  :global {
    height: 100%;
    padding: 5px;
  }
}
</style>
