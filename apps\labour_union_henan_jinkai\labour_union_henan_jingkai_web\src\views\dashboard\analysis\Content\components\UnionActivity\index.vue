<template>
  <CardTemplate
    :title="cardTitle"
    @change="handleChange"
    :select="dataRange"
  >
    <div :class="$style.activity">
      <div class="px-16">
        <div
          class="flex justify-evenly items-start border-transparent rounded-10px bg-[#F5F5F5] divide-x divide-[#E5E5E5] py-[10px]"
        >
          <div
            v-for="item in indexs"
            class="mx-2 w-1/3"
          >
            <div class="flex justify-center item-center text-16px text-[#0A3660]">
              <span class="inline-flex items-center">{{ item.name }}：</span>
              <Statistic
                :value="item.number"
                :valueStyle="{
                  fontSize: '20px !important',
                  color: '#0A3660 !important',
                  fontWeight: 'bold',
                  fontFamily: 'Source Han Sans CN MEDIUM',
                }"
                v-if="item.number && item.number !== 0"
              />
              <div
                v-else
                class="text-20px text-[#0A3660] text-center"
                style="font-family: 'Source Han Sans CN MEDIUM'"
                >—</div
              >
            </div>
          </div>
        </div>
      </div>
      <radio-group
        class="right-15 absolute z-10 flex top-63px"
        v-model:value="searchType"
        button-style="outline"
      >
        <radio-button
          v-for="item in extra"
          :value="item.value"
          class="!mx-2 !border-[#227EFF] !rounded-6px"
        >
          {{ item.name }}
        </radio-button>
      </radio-group>
      <div
        class="h-[22vh] relative top-10px"
        ref="echartRef"
      />
    </div>
  </CardTemplate>
</template>

<script lang="ts" setup>
import CardTemplate from '../CardTemplate.vue';
import { computed, inject, Ref, ref, unref, watch } from 'vue';
import { Statistic } from 'ant-design-vue';
import { useECharts } from '/@/hooks/web/useECharts';
import { flatMap, map, merge } from 'lodash-es';
import { activityInfo } from '@/api/big';
import { useEchartsTool } from '@monorepo-yysz/hooks';
import { RadioGroup, Radio } from 'ant-design-vue';

const RadioButton = Radio.Button;

const props = defineProps({
  title: { type: String, default: '工会活动数据统计' },
  activityType: { type: String, default: 'union' },
  dataRange: { type: String, default: 'halfYear' },
});

const indexs = ref<Recordable[]>([
  { name: '发布总数(场次)', number: 0 },
  // { name: '总访问量(人次)', number: 0 },
  { name: '参与总数(人)', number: 0 },
]);

const xAxis = ref<string[]>([]);

const echartRef = ref<HTMLDivElement | null>(null);

const originData = ref<Recordable[]>([]);

const searchType = ref<string>(props.dataRange);

const cardTitle = computed(() => {
  return `${props.title}${unref(unionName)}`;
});

const { setOptions, getInstance, echarts } = useECharts(echartRef as Ref<HTMLDivElement>);

const { lineFormatter } = useEchartsTool(getInstance);

const unionCode = inject('userUnionCode');
const unionName = inject('userUnionName');

const extra = ref<Recordable[]>([
  { value: 'area', name: '区域' },
  { value: 'halfYear', name: '近半年' },
]);

async function init() {
  const { publishCount, readCount, readCountList, publishCountList, columnList } =
    await activityInfo({
      dataRange: unref(searchType),
      activityCategory: props.activityType,
      companyId:
        unref(unionCode) !== '6650f8e054af46e7a415be50597a99d5' ? unref(unionCode) : undefined,
    });

  let series: Recordable[] = [];

  const legend: Recordable = {};

  xAxis.value = columnList;

  indexs.value = merge(unref(indexs), [
    { number: publishCount || 0 },
    // { number: readCount || 0 },
    { number: readCount || 0 },
  ]);

  if (unref(searchType) === 'area') {
    originData.value = [
      {
        name: '发布量',
        color: ['#49b1fe', '#02f1fe'],
        data: publishCountList || [],
        bar: true,
        type: 'bar',
        offset: -11.8,
        yAxisIndex: 0,
      },
      {
        name: '参与量',
        color: ['#31dae1', '#61f4f9'],
        data: readCountList || [],
        bar: true,
        type: 'bar',
        offset: 11.8,
        yAxisIndex: 1,
      },
    ];

    series = flatMap(
      map(unref(originData), v => {
        return [
          {
            name: v.name, // 头部
            type: 'pictorialBar',
            symbolSize: [20, 10],
            symbolOffset: [v.offset, -2],
            symbolPosition: 'end',
            z: 3,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                { offset: 0, color: v.color[1] },
                { offset: 1, color: v.color[0] },
              ]),
              opacity: 1,
            },
            yAxisIndex: v.yAxisIndex,
            data: map(v.data, v => (v > 0 ? v : '-')),
          },
          {
            data: map(v.data, v => (v > 0 ? v : '-')),
            type: v.type,
            name: v.name,
            barWidth: 20,
            yAxisIndex: v.yAxisIndex,
            itemStyle: {
              borderRadius: 5,
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                { offset: 0, color: v.color[1] },
                { offset: 1, color: v.color[0] },
              ]),
            },
          },
        ];
      })
    );
    legend.data = [
      { name: '发布量', itemStyle: { color: '#49b1fe' } },
      { name: '参与量', itemStyle: { color: '#31dae1' } },
    ];
  } else {
    originData.value = [
      {
        name: '发布量',
        color: ['#48B2FE', '#c6dcfc7e'],
        data: publishCountList || [],
        bar: true,
        type: 'line',
        yAxisIndex: 0,
        symbol:
          'image://data:image/png;base64,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',
      },
      {
        name: '参与量',
        color: ['#9f8cec', '#e8e4fb7e'],
        data: readCountList || [],
        bar: true,
        type: 'line',
        yAxisIndex: 1,
        symbol:
          'image://data:image/png;base64,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',
      },
    ];

    series = map(unref(originData), v => {
      return {
        symbol: v.symbol,
        symbolSize: 15,
        data: map(v.data, d => (d > 0 ? d : '-')),
        name: v.name,
        type: v.type,
        itemStyle: {
          color: 'transparent',
          opacity: 0.5,
        },
        yAxisIndex: v.yAxisIndex,
        areaStyle: {
          opacity: 0.5,
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            {
              offset: 0,
              color: v.color[1],
            },
            {
              offset: 1,
              color: v.color[0],
            },
          ]),
        },
        emphasis: {
          focus: 'series',
        },
      };
    });
    legend.data = [
      { name: '发布量', itemStyle: { color: '#49b1fe' } },
      { name: '参与量', itemStyle: { color: '#9f8cec' } },
    ];
  }

  setLineEcharts(series, legend);
}

function setLineEcharts(series, legend) {
  setOptions(
    {
      tooltip: {
        show: true,
        trigger: 'axis',
        formatter: params => lineFormatter(params),
      },
      legend: {
        show: true,
        icon: 'circle',
        itemWidth: 10,
        itemHeight: 10,
        right: 290,
        ...legend,
      },
      xAxis: {
        type: 'category',
        boundaryGap: ['20%', '30%'],
        data: unref(xAxis) || [],
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        z: 4,
      },
      yAxis: [
        {
          name: '场',
          nameTextStyle: { color: '#999999' },
          type: 'value',
          max: 'dataMax',
          min: 0,
          axisLine: {
            show: true,
            lineStyle: { color: '#E6E6E6' },
          },
          axisLabel: {
            color: '#999999',
          },
          minInterval: 1,
          splitNumber: 4,
        },
        {
          name: '人',
          nameTextStyle: { color: '#999999' },
          type: 'value',
          max: 'dataMax',
          min: 0,
          axisLine: {
            show: true,
            lineStyle: { color: '#E6E6E6' },
          },
          axisLabel: {
            color: '#999999',
          },
          minInterval: 1,
          splitNumber: 4,
        },
      ],
      grid: { left: '2%', right: '2%', top: '20%', bottom: '1%', containLabel: true },
      series,
    },
    true
  );
}

function handleChange({ type }) {
  searchType.value = type;
}

watch([() => unref(unionCode), searchType], () => {
  init();
});
</script>

<style lang="less" module>
.activity {
  :global {
    height: 100%;
    padding: 5px;
    position: relative;
    .ant-radio-button-wrapper {
      border-left-width: thin !important;
      max-height: 26px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #227eff;

      &::before {
        width: 0 !important;
      }
    }

    .ant-radio-button-wrapper-checked {
      background: #227eff !important;
      color: #ffffff !important;
    }
  }
}
</style>
