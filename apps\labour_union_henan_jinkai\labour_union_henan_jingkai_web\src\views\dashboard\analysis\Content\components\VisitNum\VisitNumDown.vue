<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :showOkBtn="false"
    :canFullscreen="false"
  >
    <div
      class="w-full h-[350px]"
      ref="echartRef"
    >
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed, Ref, nextTick } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useECharts } from '@/hooks/web/useECharts';
import { EChartsOption } from '@monorepo-yysz/utils';
import { viewSubordinatesSummary } from '@/api/big';

const emit = defineEmits(['register', 'success']);

const title = computed(() => {
  return '区县频道访问量';
});

const echartRef = ref<HTMLDivElement | null>(null);

const { setOptions, getInstance } = useECharts(echartRef as Ref<HTMLDivElement>);

const [registerModal, { setModalProps }] = useModalInner(async data => {
  initData();

  setModalProps({ confirmLoading: false });
});

async function initData() {
  const { nameList, countList } = await viewSubordinatesSummary();

  await nextTick();
  const option: EChartsOption = {
    tooltip: {
      trigger: 'axis',
    },
    xAxis: [
      {
        type: 'category',
        data: nameList,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        name: '单位：人次',
        nameTextStyle: {
          fontSize: 12,
          color: '#999999',
        },
        type: 'value',
        splitNumber: 4,
        splitLine: {
          lineStyle: {
            color: '#E3ECFA',
          },
        },
        axisLine: {
          show: true,
          lineStyle: { color: '#E3ECFA' },
        },
        axisLabel: {
          color: '#999999',
        },
        minInterval: 1,
        splitArea: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '访问量',
        type: 'line',
        data: countList,
        lineStyle: {
          width: 3,
          color: {
            type: 'linear',

            colorStops: [
              {
                offset: 0,
                color: '#28D9E0', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#48D8BF', // 100% 处的颜色
              },
            ],
          },
          shadowColor: 'rgba(72,216,191, 0.3)',
          shadowBlur: 5,
          shadowOffsetY: 20,
        },
        symbol: 'circle',
        itemStyle: {
          color: '#28D9E0',
          borderWidth: 5,
          borderColor: '#28D9E0',
        },
        smooth: true,
      },
    ],
  };
  setOptions(option);
}
</script>
