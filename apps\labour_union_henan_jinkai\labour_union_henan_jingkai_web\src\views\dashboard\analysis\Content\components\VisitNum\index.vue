<template>
  <CardTemplate title="平台历史访问统计">
    <div :class="$style.visit">
      <div class="flex justify-center items-start px-[15px] mb-15px">
        <div
          v-for="item in statistic"
          class="w-full px-5px h-full relative"
        >
          <div class="rounded-8px flex bg-[rgba(61,160,255,0.06)] py-[6px] pl-[11px]">
            <div class="!flex justify-center items-center w-[72px] h-full">
              <img
                :src="item.icon"
                class="inline w-full h-[56px]"
              />
            </div>
            <div
              :span="14"
              class="w-[calc(100%-72px)] pl-2"
            >
              <Statistic
                :value="item.number"
                :valueStyle="{
                  fontSize: '26px',
                  fontWeight: 500,
                  color: 'rgba(19, 113, 247, 1)',
                  fontFamily: 'Source <PERSON> Sans CN MEDIUM',
                }"
                v-if="item.number && item.number !== 0"
              />
              <div
                v-else
                class="text-24px text-[rgba(44,80,114,1)] font-bold text-start"
                >—</div
              >

              <span class="text-[rgba(44,80,114,1)] text-14px">{{ item.title }}</span>
            </div>
          </div>
          <div
            class="absolute right-1 top-0 text-[12px] text-[#1371F7] visit-down pl-3 pr-1 py-[3px] cursor-pointer"
            v-if="item.ifShowDown"
            @click="openModal(true, {})"
          >
            查看下级 >>
          </div>
        </div>
      </div>
      <div
        ref="echartRef"
        class="h-[19.9vh]"
      />
    </div>
    <VisitNumDown
      @register="registerModal"
      width="53%"
    />
  </CardTemplate>
</template>

<script lang="ts" setup>
import { Ref, ref, onMounted, unref } from 'vue';
import CardTemplate from '../CardTemplate.vue';
import { useECharts } from '@/hooks/web/useECharts';
import { iconVisit, appVisit } from '../../../image';
import { Statistic } from 'ant-design-vue';
import { useEchartsTool } from '@monorepo-yysz/hooks';
import { visitLatelySummary } from '@/api/big';
import { map, merge } from 'lodash-es';
import { useModal } from '@/components/Modal';
import VisitNumDown from './VisitNumDown.vue';

const echartRef = ref<HTMLDivElement | null>(null);

const statistic = ref<Recordable[]>([
  {
    title: 'APP频道总访问量(人次)',
    number: 0,
    icon: appVisit,
    class: 'rounded-l-none',
    ifShowDown: true,
  },
  {
    title: '工作平台总访问量(人次)',
    number: 0,
    icon: iconVisit,
    class: 'rounded-r-none',
    ifShowDown: false,
  },
]);

const xAxis = ref<string[]>([]);

const [registerModal, { openModal }] = useModal();

const { setOptions, getInstance } = useECharts(echartRef as Ref<HTMLDivElement>);

const { lineFormatter } = useEchartsTool(getInstance);

onMounted(async () => {
  const { chartData, h5VisitCount, manageVisitCount } = await visitLatelySummary();
  merge(unref(statistic), [
    {
      number: h5VisitCount || 0,
    },
    {
      number: manageVisitCount || 0,
    },
  ]);
  xAxis.value = chartData[0] || [];
  setLineEcharts([chartData[2], chartData[1]]);
});

function setLineEcharts(dataSource) {
  setOptions({
    tooltip: {
      show: true,
      trigger: 'axis',
      formatter: params => lineFormatter(params),
    },
    legend: {
      show: true,
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      right: 7,
    },
    xAxis: {
      type: 'category',
      boundaryGap: ['20%', '20%'],
      data: unref(xAxis),
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      min: 0,
    },
    yAxis: [
      {
        name: '人次',
        nameTextStyle: {
          fontSize: 12,
          color: '#999999',
        },
        axisLine: {
          show: true,
          lineStyle: { color: '#E3ECFA' },
        },
        axisLabel: {
          color: '#999999',
        },
        type: 'value',
        max: 'dataMax',
        min: 0,
        splitArea: {
          show: true,
          areaStyle: {
            color: ['#EDF6FF', '#edf6ffbf', '#edf6ff7a', '#edf6ff45', '#edf6ff2e', '', '', ''], //只需要前几个有颜色
          },
        },
        minInterval: 1,
      },
      {
        name: '',
        axisLine: {
          show: true,
          lineStyle: { color: '#E3ECFA' },
        },
        type: 'value',
      },
    ],
    grid: { left: '2%', right: '2%', top: '16%', bottom: '1%', containLabel: true },
    series: [
      {
        symbol: 'circle',
        smooth: true,
        data: map(dataSource[1], v => (v > 0 ? v : '-')),
        name: 'APP频道',
        type: 'line',
        itemStyle: {
          color: '#28D9E0',
        },
      },
      {
        smooth: true,
        data: map(dataSource[0], v => (v > 0 ? v : '-')),
        type: 'line',
        name: '工作平台',
        itemStyle: {
          color: '#1E84FF',
        },
        symbol: 'circle',
      },
    ],
  });
}
</script>

<style lang="less" module>
.visit {
  :global {
    height: 100%;
    padding: 5px;

    .visit-down {
      background: hsla(215, 100%, 57%, 0.2);
      border-radius: 0px 4px 0px 20px;
    }
  }
}
</style>
