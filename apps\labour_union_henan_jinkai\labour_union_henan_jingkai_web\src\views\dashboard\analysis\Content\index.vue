<template>
  <div :class="$style.content">
    <Row class="h-1/2">
      <Col
        :span="8"
        class="px-6px enter-x"
      >
        <Visit />
      </Col>
      <Col
        :span="8"
        class="px-6px enter-x"
      >
        <Ticket />
      </Col>
      <Col
        :span="8"
        class="px-6px enter-x"
      >
        <NewsAnalysis />
      </Col>
    </Row>
    <Row class="h-1/2 pt-8px">
      <Col
        :span="12"
        class="px-6px enter-x"
      >
        <InclusiveActivity />
      </Col>
      <Col
        :span="12"
        class="px-6px enter-x"
      >
        <UnionActivity />
      </Col>
    </Row>
  </div>
</template>

<script lang="ts" setup>
import { Row, Col } from 'ant-design-vue';
import Visit from './components/VisitNum/index.vue';
import NewsAnalysis from './components/NewsAnalysis/index.vue';
import Ticket from './components/Ticket/index.vue';
import InclusiveActivity from './components/InclusiveActivity/index.vue';
import UnionActivity from './components/UnionActivity/index.vue';
</script>

<style lang="less" module>
.content {
  :global {
    padding-top: 8px;
    height: calc(100% - 126px);
  }
}
</style>
