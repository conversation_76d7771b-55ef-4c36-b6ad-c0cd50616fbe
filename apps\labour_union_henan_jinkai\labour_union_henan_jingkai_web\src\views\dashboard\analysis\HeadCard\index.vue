<template>
  <div :class="$style['head-card']">
    <div
      v-for="item in datas"
      class="w-1/6 enter-x !px-6px h-full"
    >
      <Card
        :loading="loading"
        class="!rounded-10px h-full bg-no-repeat bg-transparent"
        :style="{
          backgroundImage: `url(${item.icon})`,
          backgroundSize: '100% 100%',
        }"
      >
        <div
          class="text-18px"
          :style="item.style"
        >
          <span>{{ item.title }}（{{ item.unit }}）</span>
          <Statistic
            :value="item.number"
            :valueStyle="item.style"
          />
        </div>
      </Card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Card, Statistic } from 'ant-design-vue';
import { inject, ref, unref, watch } from 'vue';
import { bgUser, bgActive, bgCadre, bgRegister, bgSystem, bgUnionUser } from '../image';
import { getUnionUserCountByCompanyId } from '@/api';

defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
});

const datas = ref<Recordable[]>([]);
const unionCode = inject('userUnionCode');

watch(
  () => unionCode,
  async () => {
    //参数解释  hyrzl--会员认证率   hyzhl--会员转化率hyzhl
    const { registerCount, userCount, cadreCount, unionCount, activetyCount, checkedUserCount } =
      await getUnionUserCountByCompanyId({ companyId: unref(unionCode) });

    datas.value = [
      {
        title: '注册用户',
        icon: bgRegister,
        number: registerCount || 0,
        style: { color: '#64528f', fontFamily: 'Source Han Sans CN MEDIUM' },
        unit: '人',
      },
      {
        title: '工会会员',
        icon: bgUser,
        number: userCount || 0,
        style: { color: '#2b6f88', fontFamily: 'Source Han Sans CN MEDIUM' },
        unit: '人',
      },
      {
        title: '认证会员',
        icon: bgUnionUser,
        number: checkedUserCount || 0,
        style: { color: '#4D558C', fontFamily: 'Source Han Sans CN MEDIUM' },
        unit: '人',
      },
      {
        title: '活跃用户',
        icon: bgActive,
        number: activetyCount || 0,
        style: { color: '#0A498D', fontFamily: 'Source Han Sans CN MEDIUM' },
        unit: '人',
      },
      {
        title: '工会干部',
        icon: bgCadre,
        number: cadreCount || 0,
        style: { color: '#9E6800', fontFamily: 'Source Han Sans CN MEDIUM' },
        unit: '人',
      },
      {
        title: '工会组织',
        icon: bgSystem,
        number: unionCount || 0,
        style: { color: '#0A498D', fontFamily: 'Source Han Sans CN MEDIUM' },
        unit: '个',
      },
    ];
  },
  { deep: true }
);
</script>

<style lang="less" module>
.head-card {
  :global {
    display: flex;
    width: 100%;
    height: 126px;

    .ant-statistic {
      .ant-statistic-content {
        color: unset;
        font-size: 30px !important;
        font-family: MicrosoftYaHei-Bold;
      }
    }
  }
}
</style>
