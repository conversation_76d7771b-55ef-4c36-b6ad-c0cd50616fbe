<template>
  <div :class="`${$style.dashboard} h-full`">
    <!-- <HeadCard :loading="loading" />
    <Content :loading="loading" /> -->
    暂时屏蔽
  </div>
</template>

<script lang="ts" setup>
import { onMounted, provide, ref } from 'vue';
import HeadCard from './HeadCard/index.vue';
import Content from './Content/index.vue';
import { userUnionCode } from '@/api';
import { useDictionary } from '@/store/modules/dictionary';

const loading = ref(true);

const unionCode = ref<string>('');

const unionName = ref<string>('');

provide('userUnionCode', unionCode);

provide('userUnionName', unionName);

onMounted(async () => {
  const dict = useDictionary();
  const data = await userUnionCode();

  const name = dict.getDictionaryMap.get(`unionInformation_${data}`)?.dictName || '';
  unionName.value = data === '6650f8e054af46e7a415be50597a99d5' ? '【全市】' : `【${name}】`;
  unionCode.value = data;
});

setTimeout(() => {
  loading.value = false;
}, 1500);
</script>

<style lang="less" module>
.dashboard {
  :global {
    padding: 13px 13px 10px 13px;

    .line {
      background-image: url(/@/assets/images/dashboard/line.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 100%;
    }
  }
}
</style>
