
import { BasicColumn, FormSchema } from '@/components/Table';
export function typeColumns(): BasicColumn[] {
    return [
      {
        dataIndex: 'typeName',
        title: '栏目名称',
      },
      {
        dataIndex: 'sort',
        title: '排序',
      },
      {
        dataIndex: 'typeState',
        title: '是否启用',
        customRender({ text }) {
          const title = text  ? '是' : '否'
          const color = text === true ? '#67C23A' : '#F56C6C';
          return <span title={title} style={{color}}>{title}</span>;
        },
      },
    ];
  }

export const formSchemas = (columnType): FormSchema[] => {
  return [
    {
      field: 'typeState',
      label: '是否启用',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '是', value: true },
            { label: '否', value: false },
          ],
        }
      },
    },
  ]
};

  export function typeFormItem(isUpdate: boolean): FormSchema[] {
    return [
      {
        field: 'autoId',
        label: 'id',
        colProps: { span: 24 },
        component: 'InputNumber',
        className: '!w-full',
        required: true,
        rulesMessageJoinLabel: true,
        ifShow:false
      },
      {
        field: 'typeName',
        label: '类型名称',
        colProps: { span: 24 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
        componentProps: {
          showCount: true,
          maxlength: 20 ,
        },
      },
      {
        field: 'sort',
        label: '排序',
        colProps: { span: 24 },
        component: 'InputNumber',
        className: '!w-full',
        required: true,
        rulesMessageJoinLabel: true,
      },
      {
        field: 'typeState',
        label: '是否启用',
        component: 'Switch',
        required: true,
        rulesMessageJoinLabel: true,
        colProps: {
          span: 24,
        },
        defaultValue: true,
        componentProps: {
          checkedValue: true,
          checkedChildren: '是',
        },
      },
    ];
  }
 
  