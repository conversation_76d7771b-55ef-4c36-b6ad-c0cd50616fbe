<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/employeeMessageType/add"
        >
          新增栏目类型
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/employeeMessageType/edit',
              },
              {
                  icon: record.typeState ?'icon-park-outline:remind-disable':'icon-park-outline:remind',
                  label: record.typeState ?'禁用':'启用',
                  type: 'primary',
                  danger: record.typeState,
                  onClick: EnableDisable.bind(null,record),
                  auth: '/employeeMessageType/disable',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/employeeMessageType/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <TypeModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
    >
    </TypeModal>
  </div>
</template>

<script lang="ts" setup>
import TypeModal from './typeModal.vue';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { typeColumns,formSchemas } from './data';
import { useMessage } from '@monorepo-yysz/hooks';
import { questionTypeFindList, questionType, deleteQuestionType,changeState } from '@/api/message';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: typeColumns(),
  authInfo: ['/employeeMessageType/add'],
  showIndexColumn: false,
  api: questionTypeFindList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/employeeMessageType/edit',
      '/employeeMessageType/disable',
      '/employeeMessageType/delete',
    ],
  },
});

//新增
function handleClick() {
  openModal(true, { isUpdate: false });
}

//编辑
function handleEdit(record) {
  openModal(true, { isUpdate: true, record });
}

//启用禁用
function EnableDisable(record) {
  const text = !record.typeState ? '启用' : '禁用';
  const typeState = !record.typeState;

  createConfirm({
    iconType: 'warning',
    content: `请确认要${text}${record.typeName}吗`,
    onOk: function () {
      changeState({ autoId: record.autoId, typeState:typeState }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `${text}成功` });
          reload();
        } else {
          createErrorModal({ content: `${text}失败，${message}` });
        }
      });
    },
  });
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.typeName}`,
    onOk: function () {
      deleteQuestionType(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ isUpdate, values }) {
  if (!isUpdate) {
    //   const { groupCode, groupName } = unref(type);
    //   values.groupCode = groupCode;
    //   values.groupName = groupName;
  }
  questionType(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
