<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :canFullscreen="false"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts">
import { defineComponent, ref, unref } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { Row, Col, Divider, RadioGroup } from 'ant-design-vue';
import { useForm, BasicForm } from '@/components/Form';
import { auditForm } from './data'

export default defineComponent({
  name: 'AuditModal',
  components: { BasicModal, Row, Col, Divider, BasicForm, RadioGroup },
  emits: ['register', 'success', 'cancel'],
  setup(_, { emit }) {
    const autoId = ref([]);

    const title = ref('');

    const sourceId = ref('');

    const [registerForm, { resetFields, validate }] = useForm({
      labelWidth: 100,
      schemas: auditForm(),
      showActionButtonGroup: false,
    });

    const [registerModal, {setModalProps }] = useModalInner(async data => {
      await resetFields();
      autoId.value = data.autoId;
      title.value = `审核留言`;
      setModalProps({ confirmLoading: false });
    });

    async function handleSubmit() {
      const values = await validate();
      emit('success', {
        values: { ...values, autoIdList: unref(autoId) },
      });
    }

    return {
      registerModal,
      registerForm,
      handleSubmit,
      title,
    };
  },
});
</script>
