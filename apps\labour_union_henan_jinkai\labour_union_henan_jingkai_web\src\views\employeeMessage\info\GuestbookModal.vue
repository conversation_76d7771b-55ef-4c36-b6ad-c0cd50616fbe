<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const columnType = ref('');

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.userName || ''}--详情`
      : `回复${unref(record)?.userName || ''}留言`
    : `公开${unref(record)?.userName || ''}留言`;
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(unref(columnType), unref(disabled));
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;
  columnType.value = data.columnType;

  if (unref(isUpdate)) {
    const { guestbookReplyList,img } = data.record;

    setFieldsValue({
      ...data.record,
      replyContent:
        guestbookReplyList && guestbookReplyList.length > 0
          ? guestbookReplyList[0].replyContent
          : '',
      img:img && img.length > 0 ? img.split(',') : []
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();

    emit('success', {
      values: {
        autoId: unref(record)?.employeeMessageReplyVO?.autoId,
        employeeMessageId: unref(record)?.employeeMessageId,
        content: values.content,
        replyState: values.replyState,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
