import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import {questionTypeFindList} from '@/api/message/index'
import { cloneDeep, filter } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
import { uploadApi } from '@/api/sys/upload';
const dictionary = useDictionary();
const userStore = useUserStore();
import { nextTick } from 'vue';
export const columns = (columnType): BasicColumn[] => {
  const dictionary = useDictionary()
  return [
    {
      title: '用户名',
      dataIndex: 'userName',
      width: 120,
    },
    {
      title: '所属区域',
      dataIndex: 'areaName',
      width: 100,
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
      width: 180,
    },
    {
      title: '留言类型',
      dataIndex: 'employeeMessageTypeValue',
      width: 120
    },
    {
      title: '留言内容',
      dataIndex: 'describes',
      width: 250,
    },
    {
      title: '是否公开',
      dataIndex: 'publicityState',
      width: 100,
      customRender({ text }) {
        const title = text ? '是' : '否'
        return (
          <span title={title} class={`${text ? 'text-green-500' : 'text-red-500'}`}>
            {title}
          </span>
        )
      },
    },
    {
      title: '是否回复',
      dataIndex: 'replyState',
      width: 100,
      customRender({ text }) {
        const title = text ? '是' : '否'
        return (
          <span title={title} class={`${text ? 'text-green-500' : 'text-red-500'}`}>
            {title}
          </span>
        )
      },
    },
    {
      title: '处理单位',
      dataIndex: 'replayCompanyName',
      width: 130,
    },
    {
      title: '留言时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ]
}

export const formSchemas = (columnType): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'employeeMessageTypeId',
      label: '留言类型',
      colProps: { span: 6 },
      component: 'ApiSelect',
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          placeholder: '请选择留言类型',
          api: questionTypeFindList,
          resultField: 'data',
          params: {
            pageSize: 10,
            pageNum: 1,
          },
          alwaysLoad: true,
          immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.typeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'typeName', value: 'employeeMessageTypeId' },
        };
      },
    },
    // {
    //   field: 'auditState',
    //   label: '审核状态',
    //   component: 'Select',
    //   colProps: { span: 6 },
    //   rulesMessageJoinLabel: true,
    //   componentProps: function () {
    //     return {
    //       options: dictionary.getDictionaryOpt.get('employeeMessageAudit'),
    //     };
    //   },
    // },
    {
      field: 'replyState',
      label: '是否回复',
      colProps: { span: 6},
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '否', value: false },
            { label: '是', value: true },
          ],
        }
      },
    },
    {
      field: 'publicityState',
      label: '是否公开',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '是', value: true },
            { label: '否', value: false },
          ],
        }
      },
    },
    {
      field: 'queryCompanyId',
      label: '下级工会',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      ifShow: userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5',
      componentProps: function () {
        return {
          options: filter(
            cloneDeep(dictionary.getDictionaryOpt.get(`unionsInfo`)),
            v => v.value !== '6650f8e054af46e7a415be50597a99d5'
          ),
        };
      },
    },
    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: {
        span: 3,
      },
      defaultValue: true,
    },
  ]
}

export const modalFormItem = (isUpdate,disabled): FormSchema[] => {
  return [
    {
      field: '',
      label: '基础信息',
      component: 'Divider',
      componentProps: {
        style: {
          fontWeight: 600,
        },
      },
    },
    {
      field: 'userName',
      label: '用户名',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'phone',
      label: '联系方式',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'employeeMessageTypeValue',
      label: '留言类型',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'createTime',
      label: '留言时间',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'describes',
      label: '留言内容',
      colProps: { span: 24 },
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      componentProps: {
        disabled:true,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    {
      field: 'img',
      label: '图片',
      colProps: { span: 24 },
      component: 'Upload',
      rulesMessageJoinLabel: true,
      componentProps: {
        disabled:true,
        api: uploadApi,
        maxNumber: 3,
        uploadParams: {
          operateType: 166,
        },
      },
    },
    {
      field: 'areaName',
      label: '所属区域',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'companyName',
      label: '所属工会',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'publicityState',
      label: '是否公开',
      colProps: { span: 12 },
      component: 'RadioGroup',
      ifShow() {
        return disabled;
      },
      componentProps: {
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
    },
    {
      field: '',
      label: '回复信息',
      component: 'Divider',
      componentProps: {
        style: {
          fontWeight: 600,
        },
      },
    },
    {
      field: 'content',
      label: '回复内容',
      colProps: { span: 24 },
      component: 'InputTextArea',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        disabled:false,
        maxlength: 500,
        showCount: true,
      },
    },
    {
      field: 'replayCompanyName',
      label: '处理单位',
      colProps: { span: 24 },
      component: 'ShowSpan',
      ifShow() {
        return disabled;
      },
    }
  ]
}
export const auditForm = (): FormSchema[] => {
  return [
    {
      field: 'auditState',
      label: '审核状态',
      component: 'RadioGroup',
      required: true,
      componentProps: {
        options: [
          { label: '通过', value: 'pass' },
          { label: '未通过', value: 'refuse' },
        ]
      },
    },
    {
      field: 'remark',
      label: '审核意见',
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入审核意见',
        showCount: true,
        maxlength: 300,
      },
    },
  ];
};