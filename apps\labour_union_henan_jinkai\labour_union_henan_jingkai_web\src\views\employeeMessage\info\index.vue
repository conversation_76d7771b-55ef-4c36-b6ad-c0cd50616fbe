<template>
  <div>
    <!-- 留言管理 -->
    <BasicTable @register="registerTable">
<!--      <template #toolbar>-->
<!--        <a-button-->
<!--          type="primary"-->
<!--          @click="handleAudit(null)"-->
<!--          auth="/employeeMessage/batchAudit"-->
<!--          >批量审核</a-button-->
<!--        >-->
<!--      </template>-->
      <template #bodyCell="{ column, record }">
        <TableAction
          v-if="column.dataIndex === 'action'"
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              auth: '/employeeMessage/view',
            },
            {
              icon: 'mdi:message-reply-text',
              label: '回复',
              type: 'primary',
              onClick: handleApply.bind(null, record),
              // disabled: record.auditState !== 'pass',
              auth: '/employeeMessage/reply',
            },
            // {
            //   icon: 'ant-design:audit-outlined',
            //   label: '审核',
            //   type: 'primary',
            //   disabled: record.auditState !== 'wait',
            //   onClick: handleAudit.bind(null, record),
            //   auth: '/employeeMessage/audit',
            // },
            {
              icon: record.publicityState ? 'material-symbols:lock-open' : 'material-symbols:lock',
              label: record.publicityState ? '不公开' : '公开',
              type: 'primary',
              danger: record.publicityState,
              onClick: handleOpen.bind(null, record),
              // disabled: record.auditState !== 'pass' || userStore.getUserInfo.companyId !== '6650f8e054af46e7a415be50597a99d5',
              auth: '/employeeMessage/disable',
            },
            {
              icon: 'fluent:delete-16-filled',
              label: '删除',
              type: 'primary',
              danger: true,
              auth: '/employeeMessage/delete',
              onClick: handleDelete.bind(null, record),
            },
          ]"
        />
      </template>
    </BasicTable>
    <GuestbookModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
    <AuditModal
      @register="registerAudit"
      width="50%"
      @success="handleSubmit"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { columns, formSchemas } from './data';
import GuestbookModal from './GuestbookModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { computed, unref, createVNode } from 'vue';
import {
  questionFindList,
  questionVoByDto,
  changePublicityState,
  deleteLine,
  saveOrUpdateByDTO,
  auditSave,
} from '@/api/message/index';
import AuditModal from './AuditModal.vue';
import { Modal } from 'ant-design-vue';
import { map } from 'lodash-es';
import { CloseCircleFilled } from '@ant-design/icons-vue';
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();
const props = defineProps({
  columnType: {
    type: String,
    default: 'TXTH',
  },
});
const [registerAudit, { openModal: openAudit, closeModal: closeAuditModal }] = useModal();

const column = computed(() => {
  return columns();
});

const formSchema = computed(() => {
  return formSchemas(props.columnType);
});

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();
const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: unref(column),
  showIndexColumn: false,
  api: questionFindList,
  // authInfo: ['/employeeMessage/batchAudit'],
  formConfig: {
    labelWidth: 120,
    schemas: unref(formSchema),
    autoSubmitOnEnter: true,
  },
  // rowSelection: {
  //   type: 'checkbox',
  //   getCheckboxProps: record => ({
  //     disabled: record.auditState !== 'wait',
  //   }),
  // },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 250,
    dataIndex: 'action',
    fixed: undefined,
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
    auth: [
      '/employeeMessage/view',
      '/employeeMessage/reply',
      // '/employeeMessage/audit',
      '/employeeMessage/disable',
      '/employeeMessage/delete',
    ],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

//公开
function handleOpen(record) {
  const text = !record.publicityState ? '公开' : '取消';
  const publicityState = record.publicityState ? false : true;

  createConfirm({
    iconType: 'warning',
    content: `请确认要${text}${record.userName}的留言`,
    onOk: function () {
      changePublicityState({ autoId: record.autoId, publicityState }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `${text}成功` });
          reload();
        } else {
          createErrorModal({ content: `${text}失败，${message}` });
        }
      });
    },
  });
}
//审核
function handleAudit(record: Nullable<Recordable>) {
  let arr: Recordable[] = [];
  if (record) {
    arr.push(record.autoId);
  } else {
    const rows = getSelectRows();
    if (!rows || rows.length === 0) {
      Modal.warning({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: '请选择至少一条数据进行审核！',
        okText: '确认',
        closable: true,
      });
      return false;
    }

    arr = map(rows, v => v.autoId);
  }
  openAudit(true, { record, autoId: arr });
}

//回复
function handleApply(record) {
  questionVoByDto({ autoId: record.autoId }).then(({ data }) => {
    data.content = data.employeeMessageReplyVO?.content;
    (data.PicUrl = data.file ? data.file.split(',') : []),
      openModal(true, {
        isUpdate: true,
        disabled: false,
        record: data,
        columnType: props.columnType,
      });
  });
}

//详情
function handleView(record) {
  questionVoByDto({ autoId: record.autoId }).then(({ data }) => {
    data.content = data.employeeMessageReplyVO?.content;
    data.replayCompanyName = data.employeeMessageReplyVO?.replayCompanyName;
    (data.PicUrl = data.file ? data.file.split(',') : []),
      openModal(true, {
        isUpdate: true,
        disabled: true,
        record: data,
        columnType: props.columnType,
      });
  });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.userName}的留言`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}
//审核
function handleSubmit({ values, isUpdate }) {
  auditSave(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `审核成功`,
      });
      reload();
      closeModal();
      closeAuditModal();
      clearSelectedRowKeys();
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      });
    }
  });
}
//留言回复
function handleSuccess({ values, isUpdate }) {
  if (values.replyState) {
    values.autoId = undefined;
  }
  saveOrUpdateByDTO(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `留言成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `留言失败! ${message}`,
      });
    }
  });
}
</script>
