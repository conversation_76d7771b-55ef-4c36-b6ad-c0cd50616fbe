<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/groupStudy/view',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                // disabled:record.auditStatus!=='wait',
                disabled: record.auditStatus != 'wait',
                onClick: handleAudit.bind(null, record),
                auth: '/groupStudy/auditInfo',
              }
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <TypeModal
      @register="registerModal"
      :canFullscreen="false"
      width="60%"
    >
    </TypeModal>
    <AuditModal
      @register="registerAuditModal"
      :canFullscreen="false"
      width="60%"
      @success="handleAuditModel"
    >
    </AuditModal>
  </div>
</template>

<script lang="ts" setup>
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { typeColumns, searchSchemas } from './data';
import { computed, unref, useAttrs } from 'vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  groupInfoFindList,
  auditGroups,
  getVoByDto,
} from '@/api/curriculumInfo/groupsStudy';
import TypeModal from '../info/typeModal.vue';
import AuditModal from './auditModal.vue';
const attrs = useAttrs();

const type = computed<any>(() => {
  return attrs.type;
});

const { createErrorModal, createSuccessModal } = useMessage();

const [registerModal, { openModal}] = useModal();
const [registerAuditModal, { openModal: openAuditModal, closeModal: closeAuditModal }] = useModal();


const [registerTable, { reload }] = useTable({
  rowKey: 'groupId',
  columns: typeColumns(),
  authInfo: ['/groupStudy/add'],
  showIndexColumn: false,
  api: groupInfoFindList,
  beforeFetch: params => {
    params.groupCode = unref(type)?.groupCode;
    return params;
  },
  formConfig: {
    labelWidth: 120,
    schemas: searchSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  bordered: true,
  useSearchForm: true,
  actionColumn: {
    title: '操作',
    width: 480,
    dataIndex: 'action',
    // slots: { customRender: 'action' },
    fixed: undefined,
    auth: [
      '/groupStudy/view',
      '/groupStudy/modify',
      '/groupStudy/auditInfo',
      '/groupStudy/auditMember',
      '/groupStudy/delete',
      '/groupStudy/shareList',
    ],
  },
});
//审核小组
function handleAudit(record) {
  openAuditModal(true, { isUpdate: true, record });
}

//详情
function handleView(record) {
  getVoByDto({ groupId: record.groupId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

//小组审核
function handleAuditModel(record) {
  auditGroups(record.values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `审核成功`,
      });
      reload();
      closeAuditModal();
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      });
    }
  });
}

</script>
