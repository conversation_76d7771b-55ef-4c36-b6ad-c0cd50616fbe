import { useDictionary } from '@/store/modules/dictionary';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useUserStore } from '@/store/modules/user';
import { cloneDeep, filter } from 'lodash-es';
import { h } from 'vue';
import { Tinymce } from '@/components/Tinymce';
import { Image } from 'ant-design-vue';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { Tooltip } from 'ant-design-vue';
const dictionary = useDictionary();
const userStore = useUserStore();
export function typeColumns(): BasicColumn[] {
    return [
      {
        dataIndex: 'companyName',
        title: '工会名称',
      },
      {
        dataIndex: 'groupName',
        title: '小组名称',
        ellipsis: true,
        customRender: ({ text }) => {
          return <Tooltip title={text}>{text}</Tooltip>;
        },
      },
      {
        dataIndex: 'currTypeId',
        title: '小组栏目',
        customRender: ({ text }) => {
          return dictionary.getDictionaryMap.get(`courseSection_${text}`)?.dictName;
        },
      },
      {
        dataIndex: 'maxPeople',
        title: '最大人数',
        width:100
      },
      
      
      // {
      //   title: '小组封面',
      //   dataIndex: 'groupCover',
      //   customRender: ({ record }) => {
      //       return record.groupCover ? (
      //         <Image
      //           src={userStore.getPrefix + record.groupCover}
      //           width={40}
      //           height={40}
      //         />
      //       ) : (
      //         ''
      //       );
      //     },
      // },
      // {
      //   dataIndex:'groupUserName',
      //   title:'小组审核人'
      // },
      {
        dataIndex: 'groupSource',
        title: '小组来源',
        customRender: ({ text }) => {
          return dictionary.getDictionaryMap.get(`dataSources_${text}`)?.dictName
        },
      },
      {
        dataIndex: '',
        title: '待审核（小组/分享）', 
        customRender: ({ record }) => {
            return (
              <div>
                <div>{(record.waitSum ? record.waitSum:'0' )}/{(record.waitShardingSum ? record.waitShardingSum:'0' )}</div>
              </div>
            );
          },
      },
      // {
      //   title: '审核状态',
      //   dataIndex: 'auditStatus',
      //   width: 120,
      //   customRender: ({ text, record }) => {
      //     const { auditStatus } = record;
      //     return (
      //       <span
      //         class={
      //           auditStatus == 'pass'
      //             ? 'text-green-500'
      //             : auditStatus == 'refuse'
      //               ? 'text-red-500'
      //               : ''
      //         }
      //       >
      //         {dictionary.getDictionaryMap.get(`groupStudyStatus_${text}`)?.dictName}
      //       </span>
      //     );
      //   },
      // },
      {
        dataIndex: 'createUser',
        title: '创建人',
      },
      {
        dataIndex: 'createTime',
        title: '创建时间',
      },
    ];
  }
export function searchSchemas (): FormSchema[] {
    return [
        {
            field: 'groupName',
            component: 'Input',
            label: '小组名称',
            colProps: { span: 6 },
          },
          // {
          //   field: 'auditStatus',
          //   label: '审核状态',
          //   component: 'Select',
          //   colProps: { span: 6 },
          //   rulesMessageJoinLabel: true,
          //   componentProps: {
          //     options: filter(
          //       cloneDeep(dictionary.getDictionaryOpt.get('groupStudyStatus')) as RadioGroupChildOption[]
          //     ),
          //   },
          // },        
          {
            field: 'currTypeId',
            component: 'Select',
            label: '小组栏目',
            colProps: { span: 6 },
            componentProps: function () {
              return {
                options: dictionary.getDictionaryOpt.get('courseSection'),
              };
            },
          },
          {
            field: 'nextLevelFlag',
            component: 'Checkbox',
            label: '包含下级',
            colProps: { span: 3 },
            defaultValue: true,
          },
    ]
}
export const typeFormItem = (isUpdate:Boolean,disabled:Boolean): FormSchema[] => {
    return [
         {
            field: 'groupSource',
            label: '数据来源',
            colProps: { span: 12 },
            component: 'Input',
            required: true,
            rulesMessageJoinLabel: true,
            ifShow:false
        },
        {
            field: 'groupName',
            label: '小组名称',
            colProps: { span: 12 },
            component: 'Input',
            required: true,
            rulesMessageJoinLabel: true,
            componentProps:{
              maxlength:30,
              showCount:true
            }
        },
        {
          field: 'maxPeople',
          label: '最大人数',
          colProps: { span: 12 },
          component: 'InputNumber',
          required: true,
          rulesMessageJoinLabel: true,
          componentProps: {
            min: 0,
            max:999,
            placeholder: '请输入最大人数',
          },
        },
        {
          field: 'currTypeId',
          label: '小组栏目',
          component: 'Select',
          required: true,
          colProps: { span: 12 },
          rulesMessageJoinLabel: true,
          componentProps: function () {
            return {
              showSearch: true,
              filterOption: (input: string, option: any) => {
                return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              },
              options: dictionary.getDictionaryOpt.get('courseSection'),
            };
          },
        },
        {
          field: 'reason',
          label: '申请理由',
          colProps: { span: 12 },
          component: 'Input',
          rulesMessageJoinLabel: true,
          ifShow: ({values}) => values.groupSource === 'staffIdentification'
          
        },
        {
          field: 'groupUserName',
          label: '小组审核人',
          colProps: { span: 12 },
          component: 'Input',
          rulesMessageJoinLabel: true,
          required: true,
          slot:'nameButton',
          ifShow:!disabled
        },
        {
          field: 'groupPhone',
          label: '审核人电话',
          colProps: { span: 12 },
          component: 'Input',
          rulesMessageJoinLabel: true,
          required: true,
          show:false
        },
        {
          field: 'groupUserId',
          label: '小组审核人Id',
          colProps: { span: 12 },
          component: 'Input',
          rulesMessageJoinLabel: true,
          required: true,
          show:false
        },
        {
          field: 'groupUserName',
          label: '小组审核人',
          colProps: { span: 12 },
          component: 'Input',
          rulesMessageJoinLabel: true,
          required: true,
          ifShow:disabled
        },
        {
          field: 'groupIntroduce',
          label: '小组简介',
          colProps: {
              span: 24,
            },
          component: 'InputTextArea',
          componentProps: {
            showCount: true,
            maxlength: 200,
            autoSize: { minRows: 2, maxRows: 5 },
          },
        },
        {
          field: 'groupCover',
          label: '小组封面',
          colProps: { span: 12 },
          component: 'CropperForm',
          componentProps: function () {
            return {
              operateType: 70,
            };
          },
          rulesMessageJoinLabel: true,
          required: true,
        },
        // {
        //   field: 'groupIntroduce',
        //   component: 'Input',
        //   label: '小组简介',
        //   required: true,
        //   rulesMessageJoinLabel: true,
        //   colProps: {
        //       span: 24,
        //     },
        //   render: ({ model, field, disabled }) => {
        //     return h(Tinymce, {
        //       value: model[field],
        //       onChange: (value: string) => {
        //         model[field] = value;
        //       },
        //       showImageUpload: false,
        //       operateType: 68,
        //       options: {
        //         readonly: disabled,
        //       },
        //     });
        //   },
        // },
        {
          field: '',
          label: '审核信息',
          component: 'Divider',
          ifShow: disabled
        },
        {
          field: 'auditStatus',
          label: '审核状态',
          component: 'Select',
          required: true,
          colProps: { span: 12 },
          rulesMessageJoinLabel: true,
          ifShow: disabled,
          componentProps: function () {
            return {
              options: dictionary.getDictionaryOpt.get('commonAudit'),
            };
          },
        },
        {
          field: 'auditUserName',
          label: '审核人',
          component: 'Input',
          colProps: { span: 12 },
          ifShow: disabled
        },
        {
          field: 'auditRemarks',
          label: '审核意见',
          ifShow: disabled,
          rulesMessageJoinLabel: true,
          colProps: { span: 24 },
          component: 'InputTextArea',
          componentProps: {
            showCount: true,
            maxlength: 200,
            autoSize: { minRows: 1, maxRows: 5 },
          },
        },
    ]
}
//审核
export const auditModalForm = (disabled): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'groupId',
      label: '小组id',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow:false
  },
    {
      field: 'auditStatus',
      label: '审核状态',
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      required: true,
      componentProps({ formModel }) {
        return {
          options: filter(
            cloneDeep(dictionary.getDictionaryOpt.get('groupStudyStatus')) as RadioGroupChildOption[],
            v => v.value !== 'wait'&&v.value !=="cancel"
          ),
          onChange:() => {
            formModel['auditRemarks'] = undefined;
          }
        }
      },
      ifShow:!disabled
    },
    {
      field: 'auditRemarks',
      label: '审核意见',
      // required: function ({ values }) {
      //   if (values.auditStatus == 'refuse') {
      //     return true;
      //   } else {
      //     return false;
      //   }
      // },
      // rulesMessageJoinLabel: true,
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 200,
      },
    },
  ];
};