<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/groupStudy/add"
        >
          新增学习小组
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/groupStudy/view',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                // disabled:record.auditStatus!=='wait',
                ifShow: record.auditStatus == 'wait',
                onClick: handleAudit.bind(null, record),
                auth: '/groupStudy/auditInfo',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/groupStudy/modify',
                disabled:record.groupSource ==='staffIdentification',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/groupStudy/delete',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '成员审核',
                type: 'primary',
                onClick: handleListAudit.bind(null, record),
                disabled: record.auditStatus !== 'pass',
                auth: '/groupStudy/auditMember',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '分享管理',
                type: 'primary',
                onClick: handleListShare.bind(null, record),
                disabled: record.auditStatus !== 'pass',
                auth: '/groupStudy/shareList',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <TypeModal
      @register="registerModal"
      :canFullscreen="false"
      width="60%"
      @success="handleModel"
    >
    </TypeModal>
    <AuditModal
      @register="registerAuditModal"
      :canFullscreen="false"
      width="60%"
      @success="handleAuditModel"
    >
    </AuditModal>
    <listAuditModal
      @register="registerListAuditModal"
      :canFullscreen="false"
      width="60%"
      @success="handleAuditModel"
      @flush = "handleFlushModel"
    >
    </listAuditModal>
    <listShareModal
      @register="registerListShareModal"
      :canFullscreen="false"
      width="60%"
      @success="handleShareModel"
    >
    </listShareModal>
  </div>
</template>

<script lang="ts" setup>
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { typeColumns, searchSchemas } from './data';
import { computed, unref, useAttrs } from 'vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  groupInfoFindList,
  updateLogicDeleteById,
  saveOrUpdateByDTO,
  auditGroups,
  getVoByDto,
} from '@/api/curriculumInfo/groupsStudy';
import { auditExperience } from '@/api/curriculumInfo/experienceSharding';
import TypeModal from './typeModal.vue';
import AuditModal from './auditModal.vue';
import listAuditModal from './listAuditModal/index.vue';
import listShareModal from './listShareModal/index.vue';
const attrs = useAttrs();

const type = computed<any>(() => {
  return attrs.type;
});



const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();
const [registerAuditModal, { openModal: openAuditModal, closeModal: closeAuditModal }] = useModal();
const [registerListAuditModal, { openModal: openListAuditModal }] = useModal();
const [registerListShareModal, { openModal: openListShareModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'groupId',
  columns: typeColumns(),
  authInfo: ['/groupStudy/add'],
  showIndexColumn: false,
  api: groupInfoFindList,
  beforeFetch: params => {
    params.groupCode = unref(type)?.groupCode;
    params.auditStatus = 'pass'
    return params;
  },
  formConfig: {
    labelWidth: 120,
    schemas: searchSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  bordered: true,
  useSearchForm: true,
  actionColumn: {
    title: '操作',
    width: 480,
    dataIndex: 'action',
    // slots: { customRender: 'action' },
    fixed: undefined,
    auth: [
      '/groupStudy/view',
      '/groupStudy/modify',
      '/groupStudy/auditInfo',
      '/groupStudy/auditMember',
      '/groupStudy/delete',
      '/groupStudy/shareList',
    ],
  },
});
//审核小组
function handleAudit(record) {
  openAuditModal(true, { isUpdate: true, record });
}
//新增
function handleClick() {
  openModal(true, { isUpdate: false });
}

//编辑
function handleEdit(record) {
  openModal(true, { isUpdate: true, record });
}
//详情
function handleView(record) {
  getVoByDto({ groupId: record.groupId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.groupName}`,
    onOk: function () {
      updateLogicDeleteById(record.groupId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

//编辑-新增
function handleModel(record) {
  saveOrUpdateByDTO(record.values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${record.isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${record.isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
//小组审核
function handleAuditModel(record) {
  auditGroups(record.values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `审核成功`,
      });
      reload();
      closeAuditModal();
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      });
    }
  });
}
function handleFlushModel(){
  reload();
}
//心得分享审核
function handleShareModel(record) {
  auditExperience(record.values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `审核成功`,
      });
      reload();
      closeAuditModal();
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      });
    }
  });
}
//小组成员审核
function handleListAudit(record) {
  openListAuditModal(true, { isUpdate: true, record });
}

//心得分享审核
function handleListShare(record) {
  openListShareModal(true, { isUpdate: true, record });
}
</script>
