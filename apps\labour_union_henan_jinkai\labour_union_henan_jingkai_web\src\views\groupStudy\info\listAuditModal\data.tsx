import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';

const dictionary = useDictionary();
//列表
export const modelColumns = (): BasicColumn[] => {
  return [
    {
      title: '申请人',
      dataIndex: 'addUserName',
    },
    {
      title: '成员身份',
      dataIndex: 'isLeader',
      customRender: ({text}) => {
        return text === 'y' ? '组长' : '组员'
      }
    },
    {
      title: '申请理由',
      dataIndex: 'addCause',
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      customRender: ({ text }) => {
        return (
          <span
            class={`${text === 'pass' ? 'text-green-500' : text === 'refuse' ? 'text-red-500' : ''}`}
          >
            {dictionary.getDictionaryMap.get(`groupStudyStatus_${text}`)?.dictName || ''}
          </span>
        );
      },
    },
  ]
}
  
//选择弹框筛选条件
export const modelSchemas = (): FormSchema[] => {
  return [
    {
      field: 'addUserName',
      label: '申请人',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'auditStatus',
      label: '审核状态',
      colProps: { span: 8},
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('groupStudyStatus'),
        };
      },
    },
  ]
}