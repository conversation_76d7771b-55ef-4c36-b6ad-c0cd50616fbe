<template>
    <BasicModal
      @register="registerModal"
      v-bind="$attrs"
      :title="title"
      :show-ok-btn="false"
      :canFullscreen="false"
    >
      <BasicTable @register="registerTable">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction 
              :actions="[
                {
                  icon: 'ant-design:audit-outlined',
                  label: '审核',
                  type: 'default',
                  onClick: handleModel.bind(null, record),
                  //ifShow:record.auditStatus=='wait',
                  disabled:record.isLeader == 'y' || record.auditStatus !=='wait'
                },
                {
                  icon: 'fluent:delete-16-filled',
                  label: '删除',
                  type: 'primary',
                  danger: true,
                  onClick: handleDelete.bind(null, record),
                  disabled:record.isLeader == 'y',
                },
              ]"
            />
          </template>
         
        </template>
      </BasicTable>
    </BasicModal>
    <AuditModal  
        @register="registerAuditModal"
        :canFullscreen="false"
        width="60%"
        @success="handleAuditModel"
        >
      </AuditModal>
  </template>
  
  <script lang="ts" setup>
  import { useModal } from '@/components/Modal';
  import { computed,ref,nextTick } from 'vue'
  import { useModalInner, BasicModal } from '/@/components/Modal'
  import { useTable, BasicTable, TableAction } from '/@/components/Table'
  import { modelColumns, modelSchemas } from './data'
  import { groupsInsertRecord,deleteInsertRecord,auditInsertGroups } from '@/api/curriculumInfo/groupsStudy';
  import { useMessage } from '@monorepo-yysz/hooks';
  import AuditModal from './../auditModal.vue';
  const emit = defineEmits(['register', 'success','flush'])
  
  const title = computed(() => {
    return '小组成员审核'
  })
  const groupBizId=ref(null)
  const [registerModal, {}] = useModalInner(async (data) => {
    groupBizId.value=data.record.groupBizId;
    await reload();
    await nextTick();
    await clearSelectedRowKeys();
   
  })
  const { createConfirm, createErrorModal, createSuccessModal } = useMessage();
  const [registerTable, { clearSelectedRowKeys,reload }] = useTable({
    rowKey: 'autoId',
    api: groupsInsertRecord,
    columns: modelColumns(),
    maxHeight: 400,
    beforeFetch: params => {
     params.groupBizId=groupBizId.value;
     return params
    },
    formConfig: {
      labelWidth: 120,
      autoSubmitOnEnter: true,
      schemas: modelSchemas(),
    },
    afterFetch: data => {
        const userData = data
        return userData && userData.length > 0 ? userData : []
    },
    actionColumn: {
      title: '操作',
      width: 200,
      dataIndex: 'action',
      // slots: { customRender: 'action' },
      fixed: undefined,
      // auth: ['/difficultEmployees/choice']
    },
    // immediate: true,
    useSearchForm: true,
    // showTableSetting: false,
    bordered: true,
    showIndexColumn: true,
    indexColumnProps: { width: 90 },
  })
  const [registerAuditModal, { openModal:openAuditModal,closeModal:closeAuditModal }] = useModal();
  //选择按钮操作
  function handleModel(record) {
    openAuditModal(true, { isUpdate: true,record });
  }
  //删除
  function handleDelete(record){
    createConfirm({
      iconType: 'warning',
      content: `请确认要删除${record.addUserName}`,
      onOk: function () {
        deleteInsertRecord(record.autoId).then(({ code, message }) => {
          if (code === 200) {
            createSuccessModal({ content: `删除成功` });
            reload();
            
          } else {
            createErrorModal({ content: `删除失败，${message}` });
          }
        });
      },
    });
  }
  //小组成员审核
function handleAuditModel(record) {
  auditInsertGroups(record.values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `审核成功`,
      });
      reload();
      closeAuditModal();
      emit('flush');
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      });
    }
  });
}
  </script>
  