import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { Tooltip } from 'ant-design-vue';

const dictionary = useDictionary();
//列表
export const modelColumns = (): BasicColumn[] => {
  return [
    {
      title: '回复人',
      dataIndex: 'userName',
    },
    {
      title: '回复内容',
      dataIndex: 'content',
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '回复时间',
      dataIndex: 'createTime',
    },
    {
      title: '公开状态',
      dataIndex: 'publicityStatus',
      customRender: ({ text }) => {
        return text == 'n' ? '未公开' : '已公开';
      },
    },
  ]
}
  
//选择弹框筛选条件
export const modelSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '回复人',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}