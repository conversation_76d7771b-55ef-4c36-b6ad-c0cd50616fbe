import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { Tooltip } from 'ant-design-vue';

const dictionary = useDictionary();
//列表
export const modelColumns = (): BasicColumn[] => {
  return [
    {
      title: '分享人',
      dataIndex: 'userName',
    },
    {
      title: '分享内容',
      dataIndex: 'describes',
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '分享时间',
      dataIndex: 'createTime',
    },
    {
      title: '公开状态',
      dataIndex: 'publicityStatus',
      customRender: ({text}) => {
        return text == 'y' ? '公开': '撤回'
      }
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      customRender: ({ text }) => {
        return (
          <span
            class={`${text === 'pass' ? 'text-green-500' : text === 'refuse' ? 'text-red-500' : ''}`}
          >
            {dictionary.getDictionaryMap.get(`newsCommentAuditState_${text}`)?.dictName || ''}
          </span>
        );
      },
    },
  ]
}
  
//选择弹框筛选条件
export const modelSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '分享人',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'auditStatus',
      label: '审核状态',
      colProps: { span: 8},
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsCommentAuditState'),
        };
      },
    },
  ]
}