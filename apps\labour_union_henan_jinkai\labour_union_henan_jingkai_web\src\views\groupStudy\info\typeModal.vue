<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" :class="disabledClass"> 
      <template #nameButton="{ model, field }">
        <a-input
          type="primary"
          @click="choiceModel(model, field)"
          v-model:value="model[field]"
          placeholder="请选择小组审核人"
          autocomplete="off"
          readonly
        ></a-input>
      </template>
    </BasicForm>
  </BasicModal>
  <modelListModals
    @register="registerListModal"
    :canFullscreen="false"
    width="60%"
    @success="handleModel" 
  />
</template>

<script lang="ts" setup>
import { ref, unref, computed ,watch} from 'vue';
import { useModalInner, BasicModal,useModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { typeFormItem } from './data';
import modelListModals from '../../workStar/info/modelList.vue';
const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const isUpdate = ref(false);
const disabled = ref(false);

const formItem = computed(() => {
  return typeFormItem(unref(isUpdate),unref(disabled));
});

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.groupName || ''}-详情`
      : `编辑${unref(record)?.groupName || ''}`
    : '新增学习小组'
});

const [registerForm, { resetFields, validate, setFieldsValue,setProps }] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  disabled.value = !!data?.disabled;
  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({ ...data.record });
  }
  setModalProps({ confirmLoading: false ,showOkBtn: !unref(disabled) });
  
  setProps({ disabled: unref(disabled) });
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
const model = ref<Recordable>();
const field = ref<Recordable>();
//所有信息
const [registerListModal, { openModal ,closeModal :closeModelModal }] = useModal();
//选择人员列表
function choiceModel(m,f) {
  openModal(true);
  model.value = m;
  field.value = f;
}
const modelRecord=ref<Recordable>()
function handleModel({record}) {
  modelRecord.value=record
  closeModelModal()
}
watch(modelRecord, () => {
  if (modelRecord.value) {
    model.value[unref(field)] = unref(modelRecord).userName;
    model.value['groupUserId'] = unref(modelRecord).userId;
    model.value['groupPhone'] = unref(modelRecord).phone;
  }
});
</script>
