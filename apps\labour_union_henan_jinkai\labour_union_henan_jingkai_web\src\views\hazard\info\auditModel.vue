<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { auditModalForm } from './data';

const emit = defineEmits(['success', 'register']);

const autoId = ref('');

const hazardExplain = ref('');
const isBatch = ref('')

const title = computed(() => {
  return `${unref(hazardExplain) || ''}--隐患审核`;
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {


  await resetFields();
  setModalProps({
    confirmLoading: false,
  });

  autoId.value = data.record.autoId;
  isBatch.value = data.record.isBatch;
  hazardExplain.value = data.record.hazardExplain;

  setFieldsValue({
    state: data.record.state,
    remark: data.record.remark,
  });
});

const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  labelWidth: 120,
  schemas: auditModalForm(),
  showActionButtonGroup: false,
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });
    emit('success', {
      ...values,
      autoId: autoId.value,
    });
    closeModal();
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
