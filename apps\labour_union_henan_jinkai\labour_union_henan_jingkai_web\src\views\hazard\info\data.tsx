import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { uploadApi } from '/@/api/sys/upload'
import { useUserStore } from '/@/store/modules/user'
import { useDictionary } from '@/store/modules/dictionary';
import { Tooltip } from 'ant-design-vue';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '举报问题描述',
      dataIndex: 'hazardExplain',
    },
    {
      title: '被举报单位',
      dataIndex: 'hazardCompany',
    },
    {
      title: '所属区县',
      dataIndex: 'hazardArea', 
      customRender: ({ text }) => {
        return (
          <span>{dictionary.getDictionaryMap.get(`regionCode_${text}`)?.dictName}</span>
        );
      }
    },
    {
      title: '举报人',
      dataIndex: 'reportUser',
    },
    {
      title: '举报时间',
      dataIndex: 'createTime',
      width: 160,
    },
    {
      title: '区县级审核信息',
      dataIndex: 'areaAuditState',
      customRender: ({ record, text }) => {
        const areaAuditState = dictionary.getDictionaryMap.get(
          `newsCommentAuditState_${text}`
        )?.dictName;
        return (
          <Tooltip
            title={
              <div>
                <div style={{ textAlign: 'left' }}>{`审核工会:${record?.areaAuditCompanyName || ''}`}</div>
            <div style={{ textAlign: 'left' }}>{`审核状态:${areaAuditState || ''}`}</div>
            <div style={{ textAlign: 'left' }}>{`审核人:${record?.areaAuditUserName || ''}`}</div>
            <div style={{ textAlign: 'left' }}>{`审核时间:${record?.areaAuditTime || ''}`}</div>
              </div>}>
            
          {record?.areaAuditCompanyName || '--'}
          </Tooltip>
        );
      },
    },
    {
      title: '市级审核信息',
      dataIndex: 'unionAuditState',
      customRender: ({ record, text }) => {
        const unionAuditState = dictionary.getDictionaryMap.get(
          `newsCommentAuditState_${text}`
        )?.dictName;
        return (
          <Tooltip title={<div>
            <div style={{ textAlign: 'left' }}>{`审核工会:${record?.unionAuditCompanyName || ''}`}</div>
            <div style={{ textAlign: 'left' }}>{`审核状态:${unionAuditState || ''}`}</div>
            <div style={{ textAlign: 'left' }}>{`审核人:${record?.unionAuditUserName || ''}`}</div>
            <div style={{ textAlign: 'left' }}>{`审核时间:${record?.unionAuditTime || ''}`}</div>
          </div>}>
            {record?.unionAuditCompanyName || '--'}
          </Tooltip>
        );
      },
    },
    
  ]
}

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      field: 'hazardExplain',
      label: '举报问题描述',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'hazardArea',
      label: '所属区县',
      colProps: { span: 6 },
      component: 'Select',
      ifShow: userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('regionCode'),
        };
      },
    },
    {
      field: 'areaAuditState',
      label: '区县级审核状态',
      colProps: { span: 6 },
      component: 'Select',
      ifShow: userStore.getUserInfo.companyId !== '6650f8e054af46e7a415be50597a99d5',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsCommentAuditState'),
        };
      },
    },
    {
      field: 'unionAuditState',
      label: '市级审核状态',
      colProps: { span: 6 },
      component: 'Select',
      ifShow: userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsCommentAuditState'),
        };
      },
    },
  ]
}

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'hazardExplain',
      label: '举报问题描述',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'hazardImage',
      label: '隐患照片',
      colProps: { span: 24 },
      slot: 'albumImgs',
      rulesMessageJoinLabel: true,
      required: true,
    },
    {
      field: 'hazardAddress',
      label: '举报问题所在地',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'hazardArea',
      label: '所属区县',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('regionCode'),
        };
      },
    },
    {
      field: 'reportUser',
      label: '举报人姓名',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'reportIdCard',
      label: '举报人身份证号码',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'annexFile',
      label: '附件',
      colProps: { span: 24 },
      component: 'Upload',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        api: uploadApi,
        maxNumber: 1,
        uploadParams: {
          operateType: 16,
        },
      },
    },
    {
      component: 'Divider',
      label: '区县级审核信息',
      field: '1',
    },
    {
      field: 'areaAuditState',
      label: '区县级审核状态',
      colProps: { span: 24 },
      component: 'Select',
      required: true,
      
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsCommentAuditState'),
        };
      },
    },
    {
      field: 'areaAuditUserName',
      label: '区县级审核人',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'areaAuditRemark',
      label: '区县级审核备注',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'areaAuditCompanyName',
      label: '区县级审核工会',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'areaAuditTime',
      label: '区县级审核时间',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      component: 'Divider',
      label: '市级审核信息',
      field: '1',
      ifShow: ({ values }) => {
        return (
          values.areaAuditState === 'pass' 
        );
      },
    },
    {
      field: 'unionAuditState',
      label: '市级审核状态',
      colProps: { span: 24 },
      component: 'Select',
      required: true,
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => {
        return (
          values.areaAuditState === 'pass' 
        );
      },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsCommentAuditState'),
        };
      },
    },
    {
      field: 'unionAuditUserName',
      label: '市级审核人',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      ifShow: ({ values }) => {
        return (
          values.areaAuditState === 'pass' 
        );
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'unionAuditRemark',
      label: '市级审核备注',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      ifShow: ({ values }) => {
        return (
          values.areaAuditState === 'pass' 
        );
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'unionAuditCompanyName',
      label: '市级审核工会',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      ifShow: ({ values }) => {
        return (
          values.areaAuditState === 'pass' 
        );
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'unionAuditTime',
      label: '市级审核时间',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      ifShow: ({ values }) => {
        return (
          values.areaAuditState === 'pass' 
        );
      },
      rulesMessageJoinLabel: true,
    },
    

  ]
}

export const auditModalForm = (): FormSchema[] => {
  return [
    {
      field: 'operateType',
      label: '审核结果',
      component: 'RadioGroup',
      required: true,
      componentProps: {
        options: [
          { label: '通过', value: 'pass' },
          { label: '驳回', value: 'refuse' },
        ],
      },
    },
    {
      field: 'remark',
      label: '审核意见',
      required: false,
      component: 'InputTextArea',
      componentProps: {
        maxlength: 255,
        showCount: true,
      },
    },
  ];
};
