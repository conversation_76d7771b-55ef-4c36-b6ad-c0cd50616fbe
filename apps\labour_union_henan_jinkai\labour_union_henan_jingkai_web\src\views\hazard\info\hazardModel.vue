<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :wrap-class-name="$style['recruit-modal']"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    <template #albumImgs="{ model, field }">
        <div class="p-2">
          <Image
            :width="150"
            v-for="item in model[field]"
            :key="item"
            :src="item.indexOf('http') < 0 ? imageUrl + item : item"
          >
          </Image>
        </div>
      </template>
    </BasicForm>
    
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { Image } from 'ant-design-vue';
import { modalFormItem } from './data';
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const imageUrl = userStore.getPrefix; //图片前缀

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.hazardExplain || ''}--详情`
      : `编辑--${unref(record)?.hazardExplain || ''}`
    : '新增举报信息';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    // const { showIcon,clickIcon,iconCode } = data.record
    if (record.value?.hazardImage && !Array.isArray(record.value.hazardImage)) {
      record.value.hazardImage = record.value.hazardImage.split(',');
    }
    setFieldsValue({
      ...data.record,
    });
  } 

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    // const { showIcon,clickIcon,iconCode } = values

    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" module>
.recruit-modal {
  :global {
    .ant-input-number {
      width: 100% !important;
    }
  }
}
</style>
