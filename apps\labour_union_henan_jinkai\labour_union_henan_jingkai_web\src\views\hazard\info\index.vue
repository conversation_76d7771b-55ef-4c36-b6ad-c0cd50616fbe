<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                disabled:record.areaAuditState !== 'wait'&& record.unionAuditState !== 'wait' ,
                onClick: handleAudit.bind(null, record),
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <HazardModel
      @register="registerModal"
      :can-fullscreen="false"
      width="50%"
    />
    <AuditModel
      @register="auditModal"
      @success="handleAuditSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
  
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import HazardModel from './hazardModel.vue';
import AuditModel from './auditModel.vue';
import { list,  deleteLine, auditHazard } from '@/api/hazard/index';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  // searchInfo: { orderBy: 'sort_number', sortType: 'desc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 260,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

const [auditModal, { openModal:openAuditModal, closeModal:closeAuditModal }] = useModal();


//详情
function handleView(record) {
  openModal(true, { isUpdate: true, disabled: true, record: record });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.hazardExplain}?`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

// 审核
function handleAudit(record) {
  openAuditModal(true, { isUpdate: true, disabled: true, record: record });
}

// 审核
function handleAuditSuccess(values) {
  const { autoId, ...params } = values
  auditHazard({
    todoValueList: typeof values.autoId === 'number' ?[values.autoId] : values.autoId ,
    ...params,
  }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({ content: '处理成功' })
      reload();
      closeAuditModal();
    } else {
     createErrorModal({ content: `${message}` })
    }
  });
}
</script>
