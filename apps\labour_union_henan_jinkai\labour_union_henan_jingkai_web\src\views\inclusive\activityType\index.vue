<template>
  <ActivityTypeManage
    :type="{ groupCode: 'inclusiveActivityType', groupName: '普惠活动类型' }"
    :add="add"
    :modify="modify"
    :del="del"
  />
</template>

<script lang="ts" setup>
import ActivityTypeManage from '/@/views/activities/ActivityTable/ActivityType.vue'
const add = '/inclusiveActivityType/add'
const modify = '/inclusiveActivityType/modify'
const del = '/inclusiveActivityType/delete'
</script>
