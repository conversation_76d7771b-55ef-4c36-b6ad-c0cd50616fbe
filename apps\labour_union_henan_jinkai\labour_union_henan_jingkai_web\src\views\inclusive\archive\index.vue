<template>
  <ActivityArchive
    :type="ActivityType.INCLUSIVE"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityArchive from '/@/views/activities/ActivityTable/ActivityArchive.vue'
import { ActivityType } from '/@/views/activities/activities.d'

const columnAuth = ['/inclusive/add', '/inclusive/view']
const recordAuth = {
  view: '/inclusive/view',
}

const titleAuth = '/inclusive/add'
</script>
