<template>
  <ActivityComment
    :type="ActivityType.INCLUSIVE"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
    commentType="comment"
  />
</template>

<script lang="ts" setup>
import ActivityComment from '/@/views/activities/ActivityTable/ActivityComment.vue'
import { ActivityType } from '/@/views/activities/activities.d'

const columnAuth = ['/inclusive/audit']

const recordAuth = {
  audit: '/inclusive/audit',
}

const titleAuth = '/inclusive/auditBatch'
</script>
