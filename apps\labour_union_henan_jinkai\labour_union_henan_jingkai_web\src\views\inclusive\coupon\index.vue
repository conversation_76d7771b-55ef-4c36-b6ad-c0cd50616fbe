<template>
  <ActivityTable
    :activity-type="ActivityType.COUPON"
    :titleAuth="titleAuth"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
  />
</template>
<script lang="ts" setup>
import ActivityTable from '/@/views/activities/ActivityTable/index.vue'
import { ActivityType } from '/@/views/activities/activities.d'
import { ref } from 'vue'

const columnAuth = ref([
  '/inclusiveOther/modify',
  '/inclusiveOther/pushOrCut',
  '/inclusiveOther/delete',
  '/inclusiveOther/view',
  '/inclusiveOther/top',
  '/inclusiveOther/sum',
  '/inclusiveOther/audit',
  '/inclusiveOther/comments',
  '/inclusiveOther/archives',
])

const recordAuth = ref({
  modify: '/inclusiveOther/modify',
  pushOrCut: '/inclusiveOther/pushOrCut',
  delete: '/inclusiveOther/delete',
  view: '/inclusiveOther/view',
  top: '/inclusiveOther/top',
  sum: '/inclusiveOther/sum',
  comments:'/inclusiveOther/comments',
  archives:'/inclusiveOther/archives',
  audit: '/inclusiveOther/audit',

})

const titleAuth = ref('/inclusiveOther/add')
</script>
