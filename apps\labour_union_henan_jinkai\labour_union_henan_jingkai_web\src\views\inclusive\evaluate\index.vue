<template>
  <ActivityComment
    :type="ActivityType.INCLUSIVE"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    commentType="evaluate"
    titleAuth="/inclusive/evaluate/publishBatch"
  />
</template>

<script lang="ts" setup>
import ActivityComment from '/@/views/activities/ActivityTable/ActivityComment.vue'
import { ActivityType } from '/@/views/activities/activities.d'

const columnAuth = ['/inclusive/evaluate/publish', '/inclusive/evaluate/apply']

const recordAuth = {
  publish: '/inclusive/evaluate/publish',
  apply: '/inclusive/evaluate/apply',
}
</script>
