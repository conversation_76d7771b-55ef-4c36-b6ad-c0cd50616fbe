<template>
  <ActivityTable
    :activity-type="ActivityType.INCLUSIVE_YJWD"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '/@/views/activities/ActivityTable/index.vue'
import { ActivityType } from '/@/views/activities/activities.d'
import { ref } from 'vue'

const columnAuth = ref([
  '/popQuiz/modify',
  '/popQuiz/pushOrCut',
  '/popQuiz/sum',
  '/popQuiz/delete',
  '/popQuiz/link',
  '/popQuiz/audit',
  '/popQuiz/view',
  '/popQuiz/top',
])

const recordAuth = ref({
  modify: '/popQuiz/modify',
  pushOrCut: '/popQuiz/pushOrCut',
  sum: '/popQuiz/sum',
  delete: '/popQuiz/delete',
  link: '/popQuiz/link',
  view: '/popQuiz/view',
  audit: '/popQuiz/audit',
  top: '/popQuiz/top',
})

const titleAuth = ref('/popQuiz/add')
</script>
