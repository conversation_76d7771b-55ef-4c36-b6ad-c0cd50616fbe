<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
    :wrapClassName="$style['rule-modal']"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref, computed, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { modalForm } from './integrationRule';

const emit = defineEmits(['success', 'register']);

const isUpdate = ref(true);

const ruleId = ref('');

const ruleName = ref('');

const ruleType = ref('custom');

const ruleCode = ref('ruleCode');

const disabled = ref(false);

const title = computed(() => {
  return unref(disabled)
    ? `${unref(ruleName)}--详情`
    : unref(isUpdate)
      ? `修改--${unref(ruleName)}`
      : '新增积分规则';
});

const form = computed(() => {
  return modalForm(unref(isUpdate), unref(ruleType),unref(ruleCode));
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 150,
  schemas: form,
  showActionButtonGroup: false,
  actionColOptions: {
    span: 24,
  },
  alwaysShowLines: 1,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  isUpdate.value = !!data?.isUpdate;

  disabled.value = !!data?.disabled;

  ruleCode.value = data.record.ruleCode;

  ruleType.value = 'custom';

  if (unref(isUpdate)) {
    ruleType.value = data.record.ruleType;
    ruleId.value = data.record.ruleId;
    ruleName.value = data.record.ruleName;
    await setFieldsValue({
      ...data.record,
    });
  }

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });
  setProps({
    disabled: unref(disabled),
  });
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });
    emit('success', {
      isUpdate: unref(isUpdate),
      values: { ...values, ruleId: isUpdate.value ? ruleId.value : undefined },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" module>
.rule-modal {
  :global {
    .ant-input-number {
      width: 100% !important;
    }
  }
}
</style>
