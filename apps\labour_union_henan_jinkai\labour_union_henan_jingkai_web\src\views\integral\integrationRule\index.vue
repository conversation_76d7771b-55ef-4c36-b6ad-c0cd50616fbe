<template>
  <div>
    <BasicTable
      @register="registerTable"
      :clickToRowSelect="false"
    >
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/integrationRule/add"
          v-if="false"
          >新增积分规则</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/integrationRule/circleView',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/integrationRule/modify',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <RuleModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import RuleModal from '@/views/integral/integrationRule/RuleModal.vue';
import { useModal } from '@/components/Modal';
import { list, saveOrUpdate, update } from '@/api/integral';
import { columns, formSchemas } from '@/views/integral/integrationRule/integrationRule';
import { useMessage } from '@monorepo-yysz/hooks';

const { createMessage } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: '/integrationRule/add',
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/integrationRule/modify', '/integrationRule/circleView'],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

function handleEdit(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: false,
  });
}

function handleView(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: true,
  });
}
//增加判断方法
function handleSuccess({ isUpdate, values }) {
  const saveMethod = isUpdate ? update : saveOrUpdate;
  saveMethod(values).then(res => {
    const { code, message } = res;

    if (code === 200) {
      createMessage.success('操作成功');
      closeModal();
      reload();
    } else {
      createMessage.error(`操作失败!${message}`);
    }
  });
}
</script>
