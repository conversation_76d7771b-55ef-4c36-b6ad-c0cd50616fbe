<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="modalTitle"
    :canFullscreen="false"
    width="70%"
    :footer="null"
  >
    <!-- 发放信息概览 -->
    <div class="mb-4 p-4 bg-gray-50 rounded">
      <a-row :gutter="16">
        <a-col :span="6">
          <div class="text-sm text-gray-600">发放名称</div>
          <div class="font-medium">{{ distributionInfo.distributionName }}</div>
        </a-col>
        <a-col :span="6">
          <div class="text-sm text-gray-600">发放分值</div>
          <div class="font-medium text-green-600">+{{ distributionInfo.integralScore }}</div>
        </a-col>
        <a-col :span="6">
          <div class="text-sm text-gray-600">发放人数</div>
          <div class="font-medium">{{ distributionInfo.distributionCount }}</div>
        </a-col>
        <a-col :span="6">
          <div class="text-sm text-gray-600">成功/失败</div>
          <div class="font-medium">
            <a-tag color="success">{{ distributionInfo.successCount }}</a-tag>
            /
            <a-tag color="error">{{ distributionInfo.failCount }}</a-tag>
          </div>
        </a-col>
      </a-row>
      <a-row
        :gutter="16"
        class="mt-2"
      >
        <a-col :span="12">
          <div class="text-sm text-gray-600">发放时间</div>
          <div class="font-medium">{{ distributionInfo.createTime }}</div>
        </a-col>
        <a-col :span="12">
          <div class="text-sm text-gray-600">发放备注</div>
          <div class="font-medium">{{ distributionInfo.distributionRemark || '-' }}</div>
        </a-col>
      </a-row>
    </div>

    <!-- 明细列表 -->
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicTable, useTable } from '@/components/Table';
import { detailColumns, detailSearchFormSchema } from './data';
import { distributionIntegralDetail, distributionIntegralRecordList } from '@/api/integral/send';
import { useMessage } from '@monorepo-yysz/hooks';

defineOptions({ name: 'IntegralDetailModal' });

const { createMessage } = useMessage();
const modalTitle = ref('积分发放明细');
const distributionInfo = reactive({
  distributionName: '',
  integralScore: 0,
  distributionCount: 0,
  successCount: 0,
  failCount: 0,
  createTime: '',
  distributionRemark: '',
});

let currentDistributionId = ref();

const [registerTable, { reload }] = useTable({
  api: distributionIntegralRecordList,
  columns: detailColumns,
  formConfig: {
    labelWidth: 80,
    schemas: detailSearchFormSchema,
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    actionColOptions: { span: 6 },
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
  rowKey: 'autoId',
  beforeFetch: params => {
    return {
      ...params,
      distributionId: currentDistributionId,
    };
  },
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  setModalProps({ confirmLoading: false });

  if (data?.record) {
    currentDistributionId = data.record.distributionId;
    modalTitle.value = `积分发放明细 - ${data.record.distributionName}`;

    try {
      // 获取发放详情
      const detailResult = await distributionIntegralDetail({
        autoId: data.record.autoId,
      });

      if (detailResult.code === 200 && detailResult.data) {
        Object.assign(distributionInfo, detailResult.data);
      }

      // 刷新明细列表
      await reload();
    } catch (error) {
      console.error('获取发放详情失败:', error);
      createMessage.error('获取发放详情失败');
    }
  }
});
</script>

<style lang="less" scoped>
.ant-tag {
  margin: 0 2px;
}
</style>
