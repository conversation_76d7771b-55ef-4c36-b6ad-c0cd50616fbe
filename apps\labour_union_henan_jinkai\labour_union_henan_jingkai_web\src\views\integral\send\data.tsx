import { simpleList } from '@/api/userGroup';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { Tag } from 'ant-design-vue';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { h } from 'vue';

const dictionary = useDictionary();

// 列表列配置
export const columns: BasicColumn[] = [
  {
    title: '发放名称',
    dataIndex: 'distributionName',
    width: 200,
  },
  {
    title: '发放类型',
    dataIndex: 'distributionType',
    width: 120,
    customRender: ({ text }) => {
      const name = dictionary.getDictionaryMap.get(`integralDistributionType_${text}`)?.dictName;
      return name || text;
    },
  },

  // operate_type
  {
    title: '积分类型',
    dataIndex: 'operateType',
    width: 100,
    customRender: ({ text }) => {
      const name = dictionary.getDictionaryMap.get(`IntegralOperateType_${text}`)?.dictName;
      return name || text;
    },
  },
  {
    title: '发放分值',
    dataIndex: 'integralScore',
    width: 100,
    customRender: ({ text, record }) => {
      return (
        <Tag color={record?.operateType === 'INC' ? 'green' : 'red'}>
          {record?.operateType === 'INC' ? `+${text}` : `-${text}`}
        </Tag>
      );
    },
  },

  {
    title: '成功数',
    dataIndex: 'successCount',
    width: 100,
    customRender: ({ text }) => {
      return h(Tag, { color: 'success' }, () => text);
    },
  },
  {
    title: '失败数',
    dataIndex: 'failCount',
    width: 100,
    customRender: ({ text }) => {
      return text > 0 ? h(Tag, { color: 'error' }, () => text) : text;
    },
  },

  {
    title: '发放时间',
    dataIndex: 'createTime',
    width: 160,
  },
  {
    title: '发放备注',
    dataIndex: 'distributionRemark',
    width: 200,
    ellipsis: true,
  },
];

// 搜索表单配置
export const searchFormSchema: FormSchema[] = [
  {
    field: 'distributionName',
    label: '发放名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入发放名称',
    },
    colProps: { span: 6 },
  },
  {
    field: 'distributionType',
    label: '发放类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择发放类型',
      options: dictionary.getDictionaryOpt.get('integralDistributionType') || [],
    },
    colProps: { span: 6 },
  },

  {
    field: 'dateRange',
    label: '发放时间',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
    },
    colProps: { span: 8 },
  },
];

// 积分发放表单配置
export const integralFormSchema: FormSchema[] = [
  {
    field: 'distributionName',
    label: '发放名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入发放名称',
      maxlength: 100,
    },
    colProps: { span: 24 },
  },
  {
    field: 'distributionType',
    label: '发放类型',
    component: 'RadioGroup',
    required: true,
    defaultValue: 'manualInput',
    componentProps: {
      options:
        (dictionary.getDictionaryOpt.get('integralDistributionType') as RadioGroupChildOption[]) ||
        [],
    },
    colProps: { span: 24 },
  },
  {
    field: 'directionalCode',
    label: '定向群体',
    ifShow: ({ values }) => values.distributionType === 'appointGroup',
    required: ({ values }) => values.distributionType === 'appointGroup',
    component: 'ApiSelect',
    colProps: { span: 24 },
    componentProps: ({ formModel }) => {
      return {
        placeholder: '请选择指定群体',
        api: simpleList,
        params: {
          pageSize: 0,
        },
        mode: 'multiple',
        resultField: 'data',
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.groupName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        onChange(value, nodes) {
          formModel['extendList'] = nodes?.map(t => ({
            sourceId: t.groupId,
            sourceName: t.groupName,
          }));
        },
        fieldNames: { label: 'groupName', value: 'groupId' },
      };
    },
  },
  {
    field: 'directionalName',
    label: '定向群体名称',
    component: 'Input',
    ifShow: ({ values }) => values.distributionType === 'appointGroup',
    show: false,
    colProps: { span: 12 },
  },
  {
    field: 'phones',
    label: '手机号码',
    component: 'InputTextArea',
    required: true,
    ifShow: ({ values }) => values.distributionType === 'manualInput',
    componentProps: {
      placeholder: '请输入手机号码，多个用英文逗号',
      rows: 4,
    },
    colProps: { span: 24 },
  },
  {
    field: 'integralScore',
    label: '发放分值',
    component: 'InputNumber',
    required: true,
    componentProps: {
      placeholder: '请输入发放分值',
      min: 0,
      max: 10000,
    },
    colProps: { span: 12 },
  },
  {
    field: 'operateType',
    label: '积分类型',
    component: 'RadioGroup',
    required: true,
    defaultValue: 'INC',
    componentProps: {
      options:
        (dictionary.getDictionaryOpt.get('IntegralOperateType') as RadioGroupChildOption[]) || [],
    },
    colProps: { span: 12 },
  },
  {
    field: 'distributionRemark',
    label: '发放备注',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入发放备注',
      rows: 3,
      maxlength: 200,
    },
    colProps: { span: 24 },
  },
];

// 明细列表列配置
export const detailColumns: BasicColumn[] = [
  {
    title: '手机号码',
    dataIndex: 'phone',
    width: 120,
  },
  {
    title: '用户姓名',
    dataIndex: 'userName',
    width: 100,
  },
  {
    title: '发放分值',
    dataIndex: 'integralScore',
    width: 100,
    customRender: ({ text, record }) => {
      const color = record.status === 'SUCCESS' ? 'green' : 'red';
      const prefix = record.operateType === 'INC' ? '+' : '-';
      return h(Tag, { color }, () => `${prefix}${text}`);
    },
  },
  {
    title: '发放状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        SUCCESS: { color: 'success', text: '成功' },
        FAILED: { color: 'error', text: '失败' },
      };
      const status = statusMap[text] || { color: 'default', text: text };
      return h(Tag, { color: status.color }, () => status.text);
    },
  },
  {
    title: '失败原因',
    dataIndex: 'failReason',
    width: 200,
    ellipsis: true,
    customRender: ({ text }) => {
      return text || '-';
    },
  },
  {
    title: '发放时间',
    dataIndex: 'createTime',
    width: 160,
  },
];

// 明细搜索表单配置
export const detailSearchFormSchema: FormSchema[] = [
  {
    field: 'phone',
    label: '手机号码',
    component: 'Input',
    componentProps: {
      placeholder: '请输入手机号码',
    },
    colProps: { span: 6 },
  },
  {
    field: 'userName',
    label: '用户姓名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户姓名',
    },
    colProps: { span: 6 },
  },
  {
    field: 'status',
    label: '发放状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择发放状态',
      options: [
        { label: '成功', value: 'SUCCESS' },
        { label: '失败', value: 'FAILED' },
      ],
    },
    colProps: { span: 6 },
  },
];
