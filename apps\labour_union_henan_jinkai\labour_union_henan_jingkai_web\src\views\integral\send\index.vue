<template>
  <PageWrapper
    dense
    contentFullHeight
    fixedHeight
    contentClass="flex"
  >
    <BasicTable
      @register="registerTable"
      class="w-full h-full"
    >
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleAdd"
        >
          积分发放
        </a-button>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'ant-design:eye-outlined',
                label: '查看明细',
                type: 'primary',
                onClick: handleViewDetail.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>

    <!-- 积分发放弹窗 -->
    <IntegralSendModal
      @register="registerSendModal"
      @success="handleSuccess"
      width="60%"
    />

    <!-- 明细弹窗 -->
    <IntegralDetailModal @register="registerDetailModal" />
  </PageWrapper>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { PageWrapper } from '@/components/Page';
import { useModal } from '@/components/Modal';
import { columns, searchFormSchema } from './data';
import { distributionIntegralList } from '@/api/integral/send';
import IntegralSendModal from './IntegralSendModal.vue';
import IntegralDetailModal from './IntegralDetailModal.vue';

defineOptions({ name: 'IntegralSend' });

const [registerTable, { reload }] = useTable({
  api: distributionIntegralList,
  columns,
  formConfig: {
    labelWidth: 80,
    schemas: searchFormSchema,
    autoSubmitOnEnter: true,
    showAdvancedButton: true,
    fieldMapToTime: [['dateRange', ['startTime', 'endTime'], 'YYYY-MM-DD HH:mm:ss']],
    actionColOptions: { span: 4 },
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
  rowKey: 'autoId',
  actionColumn: {
    width: 160,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
  beforeFetch: params => {
    // 处理搜索参数
    const { dateRange, ...otherParams } = params;
    return {
      ...otherParams,
    };
  },
});

const [registerSendModal, { openModal: openSendModal }] = useModal();
const [registerDetailModal, { openModal: openDetailModal }] = useModal();

// 新增积分发放
function handleAdd() {
  openSendModal(true, {});
}

// 查看明细
function handleViewDetail(record: Recordable) {
  openDetailModal(true, { record });
}

// 操作成功回调
function handleSuccess() {
  reload();
}
</script>

<style lang="less" scoped></style>
