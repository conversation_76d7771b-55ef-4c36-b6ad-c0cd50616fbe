<template>
  <ActivityTable
    :activity-type="ActivityType.INTEREST_GROUP"
    :columnAuth="columnAuth"
    :recordAuth="recordAuth"
    :titleAuth="titleAuth"
  />
</template>

<script lang="ts" setup>
import ActivityTable from '/@/views/activities/ActivityTable/index.vue'
import { ActivityType } from '/@/views/activities/activities.d'
import { ref } from 'vue'

const columnAuth = ref([
  '/interestGroupActivity/modify',
  '/interestGroupActivity/pushOrCut',
  '/interestGroupActivity/sum',
  '/interestGroupActivity/delete',
  '/interestGroupActivity/join',
  '/interestGroupActivity/link',
  '/interestGroupActivity/view',
  '/interestGroupActivity/audit',
  '/interestGroupActivity/comments',
  '/interestGroupActivity/archives',

])

const recordAuth = ref({
  modify: '/interestGroupActivity/modify',
  pushOrCut: '/interestGroupActivity/pushOrCut',
  sum: '/interestGroupActivity/sum',
  delete: '/interestGroupActivity/delete',
  link: '/interestGroupActivity/link',
  view: '/interestGroupActivity/view',
  join: '/interestGroupActivity/join',
  comments:'/interestGroupActivity/comments',
  archives:'/interestGroupActivity/archives',
  audit: '/interestGroupActivity/audit',
})

const titleAuth = ref('/interestGroupActivity/add')
</script>
