import {cloneDeep, filter, map} from 'lodash-es';
import { BasicColumn, FormSchema } from '/@/components/Table';
import { list } from '@/api/interestGroupManage/groupLabel';
import {Image, Rate, Tag} from 'ant-design-vue';
import { useDictionary } from '@/store/modules/dictionary';
import { useUserStore } from '@/store/modules/user';
import { userModalForm } from '@/views/interestGroup/groupUser/data';
import {searchNextUnionForm} from "@/utils/searchNextUnion";

const dictionary = useDictionary();
const userStore = useUserStore();
export const columns = (): BasicColumn[] => {
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '小组名称',
      dataIndex: 'groupName',
      width: 150,
    },
    {
      title: '用户姓名',
      dataIndex: 'nickName',
      width: 150,
    },
    {
      title: '审核类型',
      dataIndex: 'sourceType',
      width: 150,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`groupAuditType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '审核内容',
      dataIndex: 'content',
      width: 300,
      customRender: ({ text, record }) => {
        switch (record.sourceType) {
          case AuditSourceType.GROUP_COMMENT:
            return <div style={{ textAlign: 'left' }}>  <Rate
                disabled={true}
                value={record.score}
            /></div>
          case AuditSourceType.GROUP:
            return <div style={{ textAlign: 'left' }}>创建了【{text}】</div>;
          case AuditSourceType.JOIN:
            return (
              <div style={{ textAlign: 'left' }}>
                <div>
                  <div>申请加入：{record.groupName}</div>
                  <div class={`truncate`}>申请原因：{text}</div>
                </div>
              </div>
            );
          case AuditSourceType.SIGN_UP:
            return <div style={{ textAlign: 'left' }}>报名了【{text}】</div>;
          default:
            if (record.topicTitle) {
              return (
                <div style={{ textAlign: 'left' }}>
                  <span style={{ color: '#0284c7', marginRight: '10px' }}>
                    #{record.topicTitle}
                  </span>
                  {text}
                </div>
              );
            }
            return <div style={{ textAlign: 'left' }}>{text}</div>;
        }
      },
    },
    {
      dataIndex: 'checkCompanyName',
      title: '审核工会',
    },
    {
      dataIndex: 'state',
      title: '审核状态',
      width: 100,
      customRender: ({ text }) => {
        return (
          <span>{dictionary.getDictionaryMap.get(`activityVerifyStatus_${text}`)?.dictName}</span>
        );
      },
    },
    {
      title: '审核意见',
      dataIndex: 'auditOpinion',
      width: 150,
    },
    {
      title: '提交时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'state',
      label: '审核状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('activityVerifyStatus'),
        };
      },
    },
    {
      field: 'sourceType',
      label: '审核类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('groupAuditType'),
        };
      },
    },
    {
      field: 'companyId',
      label: '审核工会',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      ifShow: userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5',
      componentProps: function () {
        return {
          options:[{label:'南充市总工会',value:'6650f8e054af46e7a415be50597a99d5'},...dictionary.getDictionaryOpt.get(`unionsInfo`)],
        };
      },
    },
  ];
};

enum AuditSourceType {
  GROUP = 'group',
  JOIN = 'join',
  GROUP_COMMENT = 'groupComment',
  DYNAMIC = 'dynamic',
  ACTIVITY_COMMENT = 'activityComment',
  ACTIVITY_DYNAMIC = 'activityDynamic',
  SIGN_UP = 'signUp',
  REPLY = 'reply',
}

export const modalForm = (type): FormSchema[] => {
  let formSchema: any = [];
  switch (type) {
    case AuditSourceType.GROUP:
      formSchema = [
        {
          field: 'groupName',
          label: '小组名称',
          component: 'ShowSpan',
        },
        {
          field: 'userName',
          label: '申请人',
          colProps: { span: 12 },
          component: 'ShowSpan',
        },
        {
          field: 'phone',
          label: '联系电话',
          colProps: { span: 12 },
          component: 'ShowSpan',
        },
        {
          field: 'companyName',
          label: '工会名称',
          colProps: { span: 12 },
          component: 'ShowSpan',
        },
        {
          field: 'createTime',
          label: '申请时间',
          colProps: { span: 12 },
          component: 'ShowSpan',
        },
        {
          field: 'memberMax',
          label: '成员人数上限',
          colProps: { span: 12 },
          component: 'ShowSpan',
        },
        {
          field: 'labels',
          label: '小组标签',
          component: 'ShowSpan',
          colProps: { span: 12 },
          render({ values }) {
            return (
              <div>
                {map(values?.labels || [], t => (
                  <div className={`inline-block p-1`}>
                    <Tag color={'blue'}>{t.labelName}</Tag>
                  </div>
                ))}
              </div>
            );
          },
        },
        {
          field: 'logo',
          label: '封面图',
          component: 'ShowSpan',
          render({ values }) {
            const img = values.logo;
            return (
              <Image.PreviewGroup>
                <Image
                  src={userStore.getPrefix + img}
                  width={70}
                  height={70}
                ></Image>
              </Image.PreviewGroup>
            );
          },
        },
        {
          field: 'requirement',
          label: '加入要求',
          component: 'ShowSpan',
        },
        {
          field: 'groupDesc',
          label: '小组介绍',
          component: 'ShowSpan',
        },
      ];
      break;
    case AuditSourceType.JOIN:
      const user = userModalForm().filter(t => t.field !== 'interestGroups');
      formSchema = [
        ...user,
        {
          field: 'reason',
          label: '申请原因',
          component: 'ShowSpan',
          componentProps: {
            style: { whiteSpace: 'normal !important' },
          },
        },
      ];
      break;
    case AuditSourceType.SIGN_UP:
      const signUpUser = userModalForm().filter(t => t.field !== 'interestGroups');
      formSchema = [
        ...signUpUser,
        {
          field: 'answerRecords',
          slot: 'answerRecords',
          component: 'ShowSpan',
        },
      ];
      return formSchema;
    case AuditSourceType.GROUP_COMMENT:
    case AuditSourceType.DYNAMIC:
    case AuditSourceType.ACTIVITY_COMMENT:
    case AuditSourceType.ACTIVITY_DYNAMIC:
    case AuditSourceType.REPLY:
      formSchema = [
        {
          field: 'userName',
          label: '姓名',
          colProps: { span: 12 },
          component: 'ShowSpan',
        },
        {
          field: 'phone',
          label: '手机号',
          colProps: { span: 12 },
          component: 'ShowSpan',
        },
        {
          field: 'companyName',
          label: '工会名称',
          colProps: { span: 12 },
          component: 'ShowSpan',
        },
        {
          field: 'topicTitle',
          label: '话题名称',
          colProps: { span: 12 },
          component: 'ShowSpan',
          show: false,
        },
        {
          field: 'images',
          label: '图片',
          component: 'ShowSpan',
          show: false,
        },
        {
          field: 'score',
          label: '评分',
          component: 'ShowSpan',
          ifShow({values}){return values.score},
          render({ values }) {
            if(values.score){
              return <span> <Rate
                  disabled={true}
                  value={values.score}
              /></span>
            }
          }
        },
        {
          field: 'content',
          label: '评价内容',
          component: 'ShowSpan',
          render({ values }) {
            let topic;
            if (values.topicTitle) {
              topic = (
                <span style={{ color: '#0284c7', marginRight: '10px' }}>#{values.topicTitle}</span>
              );
            }
            let img =
              values?.images?.split(',').map(t => {
                return (
                  <Image
                    width={70}
                    height={70}
                    style={{ marginRight: '10px' }}
                    src={userStore.getPrefix + t}
                  />
                );
              }) ?? '';
            if (img) {
              img = <div>{img}</div>;
            }
            return (
              <div>
                {topic}
                {values.content}
                {img}
              </div>
            );
          },
        },
      ];
      break;
  }
  return formSchema;
};

export const groupAuditForm = (): FormSchema[] => {
  return [
    { field: '', label: '审核信息', colProps: { span: 24 }, component: 'Divider' },
    {
      field: 'state',
      label: '审核状态',
      component: 'RadioGroup',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: function () {
        const options = filter(
          dictionary.getDictionaryOpt.get('activityVerifyStatus'),
          t => t.value !== 'wait'
        ) as any;
        return {
          options: options,
        };
      },
    },
    {
      field: 'auditOpinion',
      label: '审核意见',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入审核意见',
        showCount: true,
        maxlength: 200,
      },
    },
  ];
};
