<template>
  <div>
    <BasicTable
      @register="registerTable"
      :clickToRowSelect="false"
    >
     <template #bodyCell="{ column, record }">
<template v-if="column.key === 'action'">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
            },
            {
              icon: 'ant-design:audit-outlined',
              label: '审核',
              type: 'primary',
              disabled: record.state !== 'wait',
              onClick: handleAudit.bind(null, record),
            },
          ]"
        />
      </template>
      </template>
    </BasicTable>
    <GroupAuditModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';

import { useModal } from '/@/components/Modal';

import { columns, formSchemas } from './data';

import { useMessage } from '@monorepo-yysz/hooks';
import GroupAuditModal from './GroupAuditModal.vue';
import { details, groupAudit, list } from '@/api/interestGroupManage/groupAudit';

const { createErrorModal, createSuccessModal } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  api: list,
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    actionColOptions: { span: 3 },
    autoSubmitOnEnter: true,
  },
  beforeFetch(p) {
    p.orderBy = 'auto_id';
    p.sortType = 'desc';
    return p;
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',
    fixed: undefined,
  },
});

async function handleView(record) {
  let data = await details({ autoId: record.autoId });
  if(record.sourceType == 'reply'){
    data =  data.replyInfo
  }
  openModal(true, {
    record: data,
    auditRecord: record,
    disabled: true,
  });
}

async function handleAudit(record) {
  const data = await details({ autoId: record.autoId });
  openModal(true, {
    record: data,
    isAudit: true,
    auditRecord: record,
    disabled: true,
  });
}

function handleSuccess({ values }) {
  groupAudit(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `操作成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `操作失败！${message}。`,
      });
    }
  });
}
</script>
