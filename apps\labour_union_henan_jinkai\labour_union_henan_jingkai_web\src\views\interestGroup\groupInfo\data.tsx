import {map} from 'lodash-es';
import { BasicColumn, FormSchema } from '/@/components/Table';
import {list} from "@/api/interestGroupManage/groupLabel";
import {Tag} from "ant-design-vue";
import {useDictionary} from "@/store/modules/dictionary";

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '小组名称',
      dataIndex: 'groupName',
      width: 150,
    },
    {
      title: '组织单位',
      dataIndex: 'companyName',
      width: 150,
    },
    {
      title: '小组标签',
      dataIndex: 'labelIds',
      width: 150,
      customRender({record}){
        return (
            <div>
              {map(record?.labels || [], t => (
                  <div className={`inline-block p-1`}>
                    <Tag color={'blue'}>{t.labelName}</Tag>
                  </div>
              ))}
            </div>
        );
      },
    },

    {
      title: '小组成员人数',
      dataIndex: 'memberMax',
      width: 150,
      customRender({text,record}){
        return (
            <div><span class={'text-blue'}>{record.memberCount} </span> / {text}</div>
        )
      },
    },
    {
      title: '待审核人数',
      dataIndex: 'reviewMemberCount',
      width: 80,
      ifShow:false,
      customRender({text,}){
        return (<div class={'text-blue'}>{text}</div>)
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 120,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'groupName',
      label: '小组名称',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入小组名称',
      },
    },
    {
      field: 'searchLabelId',
      label: '小组标签',
      component: 'ApiSelect',
      colProps: { span: 6 },
      componentProps: ( ) => {
        return {
          placeholder: '请选择小组标签',
          api: list,
          resultField: 'data',
          params: {
            pageSize:0,
          },
          immediate: true,
          onChange: () => {
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'labelName', value: 'autoId' },
        };
      },
    },
  ];
};


export const modalForm = (): FormSchema[] => {
  return [
    {
      field: 'groupName',
      label: '小组名称',
      required: true,
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 100,
        placeholder: '请输入小组名称',
      },
    },
    {
      field: 'memberMax',
      label: '成员人数上限',
      required: true,
      component: 'InputNumber',
      componentProps: {
        min: 1,
        max:10000,
        placeholder: '请输入成员人数上限',
      },
    },
    {
      field: 'labelIds',
      label: '小组标签',
      rules:[{required:true,trigger:['blur','change'],  validator: async (_, value) => {
          if (value?.length>5) {
            return Promise.reject('最多添加5个标签');
          }
          return Promise.resolve();
        },}],
      component: 'ApiSelect',
      componentProps: ({  }) => {
        return {
          mode: 'multiple',
          placeholder: '请选择小组标签（最多5个）',
          api: list,
          resultField: 'data',
          params: {
            pageSize:0,
          },
          immediate: true,
          onChange: () => {
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'labelName', value: 'autoId' },
        };
      },
    },
    {
      field: 'requirement',
      label: '加入要求',
      required: true,
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入加入要求',
        maxlength: 500,
        rows:5,
        showCount: true,
      },
    },
    {
      field: 'groupDesc',
      label: '小组介绍',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入小组介绍',
        rows:5,
        maxlength: 500,
        showCount: true,
      },
    },
    {
      field: 'logo',
      label: '封面图',
      required: true,
      component: 'CropperForm',
      renderComponentContent() {
        return {
          tip: () => (
              <div class="text-sm leading-7">
                注:大小比例为(<span class="text-red-500">1:1</span>)
              </div>
          ),
        };
      },
      componentProps({  }) {
        return {
          operateType: 161,
          imgSize: 1,
        };
      },
    },

  ];
};

export const userColumns: BasicColumn[] = [
  {
    title: '主键',
    dataIndex: 'autoId',
    width: 80,
    defaultHidden: true,
  },
  {
    title: '小组id',
    dataIndex: 'groupId',
    width: 80,
    defaultHidden: true,
  },
  {
    title: '用户id',
    dataIndex: 'userId',
    width: 80,
    defaultHidden: true,
  },
  {
    title: '用户姓名',
    dataIndex: 'userName',
    width: 80,
  },
  {
    title: '昵称',
    dataIndex: 'nickName',
    width: 80,
  },
  {
    title: '成员身份',
    dataIndex: 'identityType',
    editRow: true,
    width: 80,
    editComponent: 'Select',
    editComponentProps: ({}) => {
      const dictionary = useDictionary();
      return {
        fieldNames: { label: 'dictName', value: 'dictCode' },
        options: dictionary.getDictionaryOBJMap.get('circleLeader'),
      };
    },
    editRender: ({ record }) => {
      const dictionary = useDictionary();
      return (
          <span>
          {dictionary.getDictionaryMap.get(`circleLeader_${record.identityType}`)?.dictName}
        </span>
      );
    },
  },
  {
    title: '禁言状态',
    dataIndex: 'userState',
    editRow: true,
    width: 80,
    editComponent: 'Select',
    editComponentProps: ({}) => {
      const dictionary = useDictionary();
      return {
        fieldNames: { label: 'dictName', value: 'dictCode' },
        options: dictionary.getDictionaryOBJMap.get('memberState'),
      };
    },
    editRender: ({ record }) => {
      const dictionary = useDictionary();
      return (
          <span>{dictionary.getDictionaryMap.get(`memberState_${record.userState}`)?.dictName}</span>
      );
    },
  },
  {
    title: '加入小组时间',
    dataIndex: 'joinTime',
    width: 80,
  }
];

export const userFormSchemas: FormSchema[] = [
  {
    field: 'nickName',
    label: '昵称',
    component: 'Input',
    colProps: { span: 8 },
    rulesMessageJoinLabel: true,
  },
];
