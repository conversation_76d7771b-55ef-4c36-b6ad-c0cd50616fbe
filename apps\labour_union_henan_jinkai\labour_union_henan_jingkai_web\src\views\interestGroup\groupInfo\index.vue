<template>
  <div>
    <BasicTable @register="registerTable" :clickToRowSelect="false">
      <template #toolbar>
        <a-button type="primary" @click="handleClick"> 新增兴趣小组</a-button>
      </template>
   <template #bodyCell="{ column, record }">
     <template v-if="column.key === 'action'">
       <TableAction
           :actions="[
               {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'clarity:employee-group-solid',
              label: '成员管理',
              type: 'primary',
              onClick: handleMembers.bind(null, record),
            },
              {
              icon: 'lsicon:comments-outline',
              label: '小组动态',
              type: 'primary',
              onClick: handleComments.bind(null, record,),
            },
            {
              icon: 'ic:twotone-leave-bags-at-home',
              label: '解散',
              type: 'primary',
              danger: true,
              onClick: handleDelete.bind(null, record),
            },
          ]"
       />
     </template>
      </template>
    </BasicTable>
    <GroupInfoModal @register="registerModal"
               @success="handleSuccess"
               :canFullscreen="false"
               width="50%"
    />
    <GroupUserList @register="registerGroupUserListModal" :canFullscreen="false" width="70%" />
    <CommentsListModal @register="registerCommentsListModal" :canFullscreen="false" width="50%" />
  </div>
</template>

<script lang="ts" setup>
import { createVNode } from 'vue'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import { message, Modal } from 'ant-design-vue'

import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { useModal } from '/@/components/Modal'

import { columns, formSchemas } from './data'

import { useMessage } from '@monorepo-yysz/hooks';
import {deleteByGroupId, getByGroupId, list, saveOrUpdate} from "@/api/interestGroupManage/groupInfo";
import GroupInfoModal from './GroupInfoModal.vue'
import CommentsListModal from "@/views/interestGroup/groupInfo/CommentsListModal.vue";
import GroupUserList from "@/views/interestGroup/groupInfo/GroupUserList.vue";

const { createErrorModal, createSuccessModal } = useMessage()



const [registerModal, { openModal, closeModal }] = useModal()
const [registerGroupUserListModal, { openModal: openUserListModel }] = useModal()

const [registerCommentsListModal, { openModal: openCommentsListModel }] = useModal()


const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  api:list,
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    actionColOptions: { span: 3 },
    autoSubmitOnEnter: true,
  },
  beforeFetch(p){
    p.orderBy = 'auto_id'
    p.sortType = 'desc'
    return p
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 400,
    dataIndex: 'action',
    fixed: undefined,
  },
})

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  })
}


function handleDelete(record) {
  const name = record.groupName
  const groupId = record.groupId
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要解散【${name}】并清除小组成员信息?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise((resolve, reject) => {
          deleteByGroupId(groupId).then(({data}) => {
            if (data) {
              message.success('解散成功')
              reload()
              resolve(data)
            } else {
              message.error('解散失败')
              reject(data)
            }
          })
        })
      } catch {
        return console.log('Oops errors!')
      }
    },
  })
}

async function handleEdit({groupId}) {
  const data = await getByGroupId({groupId})
  openModal(true, {
    record: data,
    isUpdate: true,
    disabled: false,
  })
}

async function handleView({groupId}) {
  const data = await getByGroupId({groupId})
  openModal(true, {
    record: data,
    isUpdate: true,
    disabled: true,
  })
}
function handleSuccess({ isUpdate, values }){

  saveOrUpdate({
    ...values,dataSource:isUpdate?undefined:'union'
  }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败！${message}。`,
      });
    }
  });
}

function handleMembers(item) {
  openUserListModel(true, {
    record: item,
  })
}
function handleComments(item){
  openCommentsListModel(true,{
    record:item,
  })
}

</script>
