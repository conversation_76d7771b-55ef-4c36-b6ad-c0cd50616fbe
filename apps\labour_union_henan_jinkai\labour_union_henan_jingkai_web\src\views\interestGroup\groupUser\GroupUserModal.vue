<template>
  <BasicModal
      @register="registerModal"
      v-bind="$attrs"
      :title="title"
  >
    <BasicForm @register="registerForm"  :class="disabledClass"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { userModalForm} from "./data";

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const isUpdate = ref(false);
const disabled = ref(false)

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const formItem = computed(() => {
  return userModalForm(unref(isUpdate));
});

const title = computed(() => {
  return `${unref(record)?.userName} - 详情`
});

const [registerForm, { resetFields, validate,setProps, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  disabled.value = !!data.disabled
  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    await setFieldsValue({...data.record});
  }
  setProps({ disabled: unref(disabled) });
  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

</script>
