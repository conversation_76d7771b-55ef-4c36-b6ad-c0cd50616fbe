<template>
  <div>
    <BasicTable @register="registerTable" :clickToRowSelect="false">
      <template #bodyCell="{ column, record }">
<template v-if="column.key === 'action'">
        <TableAction
          :actions="[
               {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
            },
          ]"
        />
      </template>
      </template>
    </BasicTable>
    <GroupUserModal @register="registerModal"
               :canFullscreen="false"
               width="50%"
    />
  </div>
</template>

<script lang="ts" setup>

import { BasicTable, useTable, TableAction } from '/@/components/Table'
import { useModal } from '/@/components/Modal'
import { columns, formSchemas } from './data'
import { useMessage } from '@monorepo-yysz/hooks';
import { list} from "@/api/interestGroupManage/groupUser";
import GroupUserModal from './GroupUserModal.vue'
const { createErrorModal, createSuccessModal } = useMessage()



const [registerModal, { openModal, closeModal }] = useModal()

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  api:list,
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  beforeFetch(p){
    p.orderBy = 'auto_id'
    p.sortType = 'desc'
    return p
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',
    fixed: undefined,
  },
})



async function handleView(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: true,
  })

}
</script>
