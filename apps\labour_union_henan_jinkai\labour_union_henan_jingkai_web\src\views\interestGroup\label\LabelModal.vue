<template>
  <BasicModal
      @register="registerModal"
      v-bind="$attrs"
      :title="title"
      @ok="handleSubmit"
  >
    <BasicForm @register="registerForm"  :class="disabledClass"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const isUpdate = ref(false);
const disabled = ref(false)

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const title = computed(() => {
  if(unref(disabled)){
    return `${unref(record)?.labelName} - 详情`
  }
  return unref(isUpdate) ? `编辑 - ${unref(record)?.labelName || ''}` : '新增标签';
});

const [registerForm, { resetFields, validate,setProps, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas:  [
    {
      field: 'labelName',
      label: '标签名称',
      required: true,
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 100,
        placeholder: '请输入标签名称',
      },
    },
    {
      field: 'orderNum',
      label: '序号',
      required: true,
      component: 'InputNumber',
      componentProps: {
        min: 1,
        max:10000,
        placeholder: '请输入序号',
      },
    },
  ],
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  disabled.value = !!data.disabled
  isUpdate.value = !!data.isUpdate;

  await setFieldsValue({...data.record});
  setProps({ disabled: unref(disabled) });
  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});
async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
