<template>
  <BasicModal
      @register="registerModal"
      v-bind="$attrs"
      :title="title"
      @ok="handleSubmit"
  >
    <BasicForm @register="registerForm"  :class="disabledClass"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const isUpdate = ref(false);
const disabled = ref(false)

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const title = computed(() => {
  if(unref(disabled)){
    return `${unref(record)?.topicTitle} - 详情`
  }
  return unref(isUpdate) ? `编辑 - ${unref(record)?.topicTitle || ''}` : '新增话题';
});

const [registerForm, { resetFields, validate,setProps, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas:  [
    {
      field: 'topicTitle',
      label: '话题名称',
      required: true,
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 20,
        placeholder: '请输入话题名称',
      },
    },
    {
      field: 'state',
      label: '是否启用',
      component: 'RadioGroup',
      required:true,
      rulesMessageJoinLabel: true,
      defaultValue: 'y',
      componentProps: {
        options: [
          { label: '是', value: 'y' },
          { label: '否', value: 'n' },
        ],
      },
    },
    {
      field: 'remark',
      label: '描述',
      required: false,
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        maxlength: 200,
        placeholder: '请输入描述',
      },
    },

  ],
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  disabled.value = !!data.disabled
  isUpdate.value = !!data.isUpdate;

  await setFieldsValue({...data.record});
  setProps({ disabled: unref(disabled) });
  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});
async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
