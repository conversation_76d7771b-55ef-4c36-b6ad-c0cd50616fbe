<template>
  <div>
    <BasicTable @register="registerTable" :clickToRowSelect="false">
      <template #toolbar>
        <a-button type="primary" @click="handleClick"> 新增话题</a-button>
      </template>
     <template #bodyCell="{ column, record }">
<template v-if="column.key === 'action'">
        <TableAction
          :actions="[
               {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ic:twotone-leave-bags-at-home',
              label: '删除',
              type: 'primary',
              danger: true,
              onClick: handleDelete.bind(null, record),
            },
          ]"
        />
      </template>
      </template>
    </BasicTable>
    <TopicModal @register="registerModal"
               @success="handleSuccess"
               :canFullscreen="false"
               width="30%"
    />
  </div>
</template>

<script lang="ts" setup>
import {createVNode, unref} from 'vue'
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { useModal } from '/@/components/Modal'
import { useMessage } from '@monorepo-yysz/hooks';
import {list, saveOrUpdate,  deleteLine} from "@/api/interestGroupManage/topic";
import TopicModal from './TopicModal.vue'

const { createErrorModal, createSuccessModal } = useMessage()



const [registerModal, { openModal, closeModal }] = useModal()



const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '话题名称',
      dataIndex: 'topicTitle',
      width: 200,
    },
    {
      title: '话题热度',
      dataIndex: 'score',
      width: 150,
    },
    {
      title: '描述',
      dataIndex: 'remark',
    },
    {
      title: '是否启用',
      dataIndex: 'state',
      width: 150,
      customRender({text}){
        if(text === 'y'){
          return '是'
        }
        return '否'
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ],
  api:list,
  showIndexColumn: false,
  beforeFetch(p){
    p.orderBy = 'auto_id'
    p.sortType = 'desc'
    return p
  },
  formConfig: {
    labelWidth: 120,
    schemas: [
      {
        field: 'topicTitle',
        label: '话题名称',
        component: 'Input',
        colProps: { span: 6 },
        componentProps: {
          autocomplete: 'off',
          placeholder: '请输入话题名称',
        },
      },
    ],
    autoSubmitOnEnter: true,
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',
    fixed: undefined,
  },
})

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
    record: {},
  })
}


function handleDelete(record) {
  const name = record.topicTitle
  const autoId = record.autoId
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要删除【${name}】?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise((resolve, reject) => {
          deleteLine(autoId).then(({data}) => {
            if (data) {
              message.success('删除成功')
              reload()
              resolve(data)
            } else {
              message.error('删除失败')
              reject(data)
            }
          })
        })
      } catch {
        return console.log('Oops errors!')
      }
    },
  })
}

async function handleEdit(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: false,
  })
}

async function handleView(record) {

  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: true,
  })
}
function handleSuccess({ isUpdate, values }){

  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败！${message}。`,
      });
    }
  });
}

</script>
