import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import { searchNextUnionForm } from '@/utils/searchNextUnion';
import { h } from 'vue';
import { Tinymce } from '@/components/Tinymce';
import { Image } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/user';
export function liveColumns(): BasicColumn[] {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      dataIndex: 'liveCompanyName',
      title: '工会名称',
    },
    {
      dataIndex: 'liveTitle',
      title: '链接标题',
    },
    {
      dataIndex: 'liveCover',
      title: '链接封面',
      customRender: ({ record }) => {
        return record.liveCover ? (
          <Image
            src={userStore.getPrefix + record.liveCover}
            width={35}
            height={35}
          />
        ) : (
          ''
        );
      },
    },
    {
      dataIndex: 'isOpenLive',
      title: '链接状态',
      customRender({ text }) {
        return dictionary.getDictionaryMap.get(`liveStatus_${text}`)?.dictName || '';
      },
    },
    {
      dataIndex: 'publishStatus',
      title: '发布状态',
      customRender({ text }) {
        return text == 'n' ? '未发布' : '已发布';
      },
    },
  ];
}
//新增编辑详情
export const modalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'liveTitle',
      label: '链接标题',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 50,
      },
    },
    {
      field: 'livePlayBack',
      label: '链接地址',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 200,
      },
    },
    {
      field: 'liveStartTime',
      label: '链接开始时间',
      colProps: { span: 12 },
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'liveEndTime',
      label: '链接结束时间',
      colProps: { span: 12 },
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'liveIntroduction',
      label: '链接描述',
      component: 'InputTextArea',
      required: true,
      colProps: { span: 24 },
      componentProps: {
      autocomplete: 'off',
      showCount: true,
      maxlength: 300,
      autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    {
      field: 'liveCover',
      label: '链接封面',
      colProps: { span: 12 },
      component: 'CropperForm',
      componentProps: function () {
        return {
          operateType: 70,
        };
      },
      rulesMessageJoinLabel: true,
      required: true,
    },
    {
      field: 'introductionCover',
      label: '简介封面',
      colProps: { span: 12 },
      component: 'CropperForm',
      componentProps: function () {
        return {
          operateType: 70,
        };
      },
      rulesMessageJoinLabel: true,
      required: true,
    },
    
    // {
    //   field: 'liveIntroduction',
    //   component: 'Input',
    //   label: '链接简介',
    //   required: true,
    //   rulesMessageJoinLabel: true,
    //   colProps: {
    //     span: 24,
    //   },
    //   render: ({ model, field, disabled }) => {
    //     return h(Tinymce, {
    //       value: model[field],
    //       onChange: (value: string) => {
    //         model[field] = value;
    //       },
    //       showImageUpload: false,
    //       operateType: 68,
    //       options: {
    //         readonly: disabled,
    //       },
    //     });
    //   },
    // }, 
  ];
};
//关播回放地址
export const closeModalFormItem = (): FormSchema[] => {
  return [
    
    {
      field: 'livePlayReturn',
      label: '链接回放地址',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 20,
      },
    }
  ];
};
export function searchSchemas(): FormSchema[] {
  return [
    {
      field: 'liveTitle',
      label: '链接标题',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },

    ...searchNextUnionForm(),
  ];
}
