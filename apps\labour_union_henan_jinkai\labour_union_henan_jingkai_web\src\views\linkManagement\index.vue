<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/liveInfo/add"
        >
          新增链接
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/liveInfo/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/liveInfo/modify',
                disabled: record.publishStatus === 'y'
              },
              {
                icon:
                  record.publishStatus === 'n'
                    ? 'material-symbols:lock-open'
                    : 'material-symbols:lock',
                label: record.publishStatus === 'n' ? '发布' : '撤销',
                type: 'primary',
                onClick: handleConfirm.bind(null, record),
                auth: '/liveInfo/publish',
              },
              {
                icon:
                  record.isOpenLive === 'notStart'
                    ? 'material-symbols:auto-read-pause-rounded'
                    : 'material-symbols:auto-read-play',
                label: record.isOpenLive === 'notStart' ? '开播' : '关播',
                type: 'primary',
                onClick: handleOpen.bind(null, record),
                auth: '/liveInfo/openLive',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/liveInfo/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <VolunteerModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="40%"
    >
    </VolunteerModal>
    <OpenOrClsoeLiveModel
      @register="registerCloseModal"
      @success="handleCloseSuccess"
      :can-fullscreen="false"
      width="40%"
    >
    </OpenOrClsoeLiveModel>
  </div>
</template>

<script lang="ts" setup>
import VolunteerModal from './volunteerModal.vue';
import OpenOrClsoeLiveModel from './openOrClsoeLiveModel.vue';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { liveColumns, searchSchemas } from './data';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  liveInfoFindList,
  saveOrUpdateByDTO,
  deleteLiveInfo,
  publishStatus,
  getVoByDto,
  openStatus,
} from '@/api/linkManagement/index';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();
const [registerCloseModal, { openModal: openLiveModal, closeModal: closeLiveModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: liveColumns(),
  showIndexColumn: false,
  api: liveInfoFindList,
  authInfo: '/liveInfo/add',
  formConfig: {
    labelWidth: 120,
    schemas: searchSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  bordered: true,
  useSearchForm: true,
  actionColumn: {
    title: '操作',
    width: 430,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/liveInfo/view',
      '/liveInfo/modify',
      '/liveInfo/publish',
      '/liveInfo/openLive',
      '/liveInfo/delete',
    ],
  },
});

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record) {
  getVoByDto({ liveId: record.liveId }).then(res => {
    const { code, data, message: msg } = res;
    if (code === 200) {
      openModal(true, { isUpdate: true, record: data, disabled: false });
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}

//详情
function handleView(record) {
  getVoByDto({ liveId: record.liveId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除吗？`,
    onOk: function () {
      deleteLiveInfo(record.liveId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ isUpdate, values }) {
  if (!isUpdate) {
    //   const { groupCode, groupName } = unref(type);
    //   values.groupCode = groupCode;
    //   values.groupName = groupName;
  }
  saveOrUpdateByDTO(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
//回放地址回调
function handleCloseSuccess({ isUpdate, values }) {
  openStatus({
    liveId: values.liveId,
    isOpenLive: (values.isOpenLive = 'hasEnd'),
    livePlayReturn: values.livePlayReturn,
  }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `链接回放地址添加成功`,
      });
      reload();
      closeLiveModal();
    } else {
      createErrorModal({
        content: `链接回放地址添加失败! ${message}`,
      });
    }
  });
}

function handleConfirm(record) {
  const name = record.publishStatus === 'n' ? '发布' : '撤销';
  createConfirm({
    iconType: 'warning',
    content: `请确定${name}?`,
    onOk: function () {
      publishStatus({
        liveId: record.liveId,
        publishStatus: record.publishStatus === 'y' ? 'n' : 'y',
      }).then(({ code, message }) => {
        if (code === 200) {
          reload();
        } else {
          createErrorModal({
            content: `${record.publishStatus === 'n' ? '发布' : '撤销'}失败! ${message}`,
          });
        }
      });
    },
  });
}

function handleOpen(record) {
  const name = record.isOpenLive === 'notStart' ? '开播' : '关播';
  if (record.isOpenLive === 'notStart') {
    createConfirm({
      iconType: 'warning',
      content: `请确定${name}吗?`,
      onOk: function () {
        openStatus({
          liveId: record.liveId,
          isOpenLive: 'onGoing',
        }).then(({ code, message }) => {
          if (code === 200) {
            reload();
          } else {
            createErrorModal({
              content: `${record.isOpenLive === 'notStart' ? '开播' : '关播'}失败! ${message}`,
            });
          }
        });
      },
    });
  } else {
    openLiveModal(true, { isUpdate: true, disabled: true, record: record });
  }
}
</script>
