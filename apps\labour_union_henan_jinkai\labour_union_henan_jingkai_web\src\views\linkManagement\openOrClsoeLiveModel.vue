<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" :class="disabledClass">
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed,watch } from 'vue';
import { useModalInner,useModal, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { closeModalFormItem } from './data';


const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const isUpdate = ref(false);

const disabled = ref(false);

const formItem = computed(() => {
  return closeModalFormItem();
});
const title = computed(() => {
  return '添加回放地址';
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps}] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  isUpdate.value = !!data.isUpdate;
  
  if (unref(isUpdate)) {
    setFieldsValue({ ...data.record });
  }
  setModalProps({ confirmLoading: false ,showOkBtn: !unref(disabled)});

  setProps({ disabled: unref(disabled) });
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

</script>
