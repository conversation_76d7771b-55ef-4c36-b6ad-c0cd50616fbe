<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" :class="disabledClass">
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed,watch } from 'vue';
import { useModalInner,useModal, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from './data';


const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const isUpdate = ref(false);

const disabled = ref(false);

const companyName = ref('');
//工会id
const companyId = ref('');

const model = ref<Recordable>();

const field = ref('');
const formItem = computed(() => {
  return modalFormItem();
});
const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.userName || ''}-详情`
      : `编辑${unref(record)?.userName || ''}`
    : '新增志愿者'
});

//所有劳模信息
const [registermodelListModal, { openModal ,closeModal :closeModelModal }] = useModal();
//选择劳模人员列表
function choiceModel(m,f) {
  openModal(true);
  console.log(m,f);
  model.value = m;
  field.value = f;
}
const modelRecord=ref<Recordable>()
function handleModel({record}) {
  modelRecord.value=record
  closeModelModal()
}

//所有工会
const [registerCommentModal, { openModal: openUnionListModal, closeModal }] = useModal();

const [registerForm, { resetFields, validate, setFieldsValue, setProps}] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  disabled.value = !!data?.disabled;
  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({ ...data.record });
  }
  setModalProps({ confirmLoading: false ,showOkBtn: !unref(disabled)});

  setProps({ disabled: unref(disabled) });
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    emit('success', {
      values: {
        ...unref(record),
        ...values,
        ...model.value,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
//所属工会
function choiceUnion(m, f) {
  openUnionListModal(true);
  model.value = m;
  field.value = f;
}

function handleSuccess({ unionName, unionId, issueGrading }) {
  companyName.value = unionName;
  companyId.value = unionId;
  closeModal();
}

watch(companyName, () => {
  if (model.value) {
    model.value[unref(field)] = unref(companyName);
  }
});
watch(modelRecord, () => {
  if (modelRecord.value) {
    model.value['companyName'] =companyName.value?companyName.value:unref(modelRecord).companyName;
    model.value[unref(field)] = modelRecord.value.userName;
    model.value['companyId'] =companyId.value?companyId.value:unref(modelRecord).companyId;
    model.value['userIdCard'] =unref(modelRecord).identityCardNumber;
    model.value['userMobile'] =unref(modelRecord).phone;
    model.value['userBirth'] =unref(modelRecord).dateOfBirth;
    model.value['userSex'] =unref(modelRecord).gender;
    model.value['userId'] =unref(modelRecord).userId;
  }
});
</script>
