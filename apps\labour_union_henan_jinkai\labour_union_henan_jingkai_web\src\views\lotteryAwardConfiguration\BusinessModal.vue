<template>
  <BasicModal
    @register="registerModal"
    @ok="handleSuccess"
    :can-fullscreen="false"
    title="商品选择"
    :wrap-class-name="$style['business-modal']"
  >
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { useModalInner, BasicModal } from '/@/components/Modal';
import { Businesslist } from '/@/api/lotteryAwardConfiguration';
import { businessColumns } from './data';
import { useTable, BasicTable } from '/@/components/Table';

defineOptions({ name: 'BusinessModal' });

const emit = defineEmits(['success', 'register']);

const [registerModal, { closeModal }] = useModalInner();

const [registerTable, { getSelectRows }] = useTable({
  rowKey: 'autoId',
  columns: businessColumns(),
  rowSelection: {
    type: 'radio',
  },
  formConfig: {
    labelWidth: 120,
    schemas: [
      {
        field: 'productName',
        label: '商品名称',
        component: 'Input',
        colProps: {
          span: 18,
        },
        componentProps: {
          autocomplete: 'off',
        },
      },
    ],
    autoSubmitOnEnter: true,
    actionColOptions: {
      span: 6,
    },
  },
  clickToRowSelect: true,
  useSearchForm: true,
  showTableSetting: false,
  isCanResizeParent: true,
  bordered: true,
  api: Businesslist,
  showIndexColumn: true,
});

async function handleSuccess() {
  const dataSelect = await getSelectRows();

  emit('success', { dataSelect: dataSelect[0] });
  closeModal();
}
</script>

<style lang="less" module>
.business-modal {
  :global {
    .ant-modal-body {
      height: 72vh !important;

      .scrollbar__wrap {
        overflow: hidden;
      }

      .fix {
        height: 72vh !important;
      }
    }
  }
}
</style>
