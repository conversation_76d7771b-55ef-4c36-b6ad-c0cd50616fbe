import { Image, Input, InputNumber } from 'ant-design-vue';
import { BasicColumn, FormSchema } from '/@/components/Table';

export enum LotteryType {
  INTEGRAL = 'integral', // ("", "积分抽奖"),
  SIGN_EVERYDAY = 'sign', // ("", "每日签到"),
}

export const columns = (): BasicColumn[] => {
  return [
    {
      dataIndex: 'name',
      title: '奖品名称',
      customRender: ({ record }) => {
        return (
          <Input
            value={record.name}
            onChange={e => (record.name = e.target.value)}
            showCount
            maxlength={20}
          />
        );
      },
    },
    {
      dataIndex: 'type',
      title: '类型',
    },
    {
      dataIndex: 'productImg',
      title: '图片',
    },
    {
      dataIndex: 'prize',
      title: '奖品',
    },
    {
      dataIndex: 'probability',
      title: '中奖率(%)',
      customRender: ({ record }) => {
        return (
          <InputNumber
            value={record.probability}
            onChange={value => (record.probability = value)}
            max={100}
            min={0}
          ></InputNumber>
        );
      },
    },
  ];
};

export const businessColumns = (): BasicColumn[] => {
  return [
    {
      dataIndex: 'productName',
      title: '商品名称',
    },
    {
      dataIndex: 'productCoverPic',
      title: '展示图',
      customRender: ({ text }) => {
        return (
          <Image
            src={text}
            width={70}
            height={50}
          />
        );
      },
    },
  ];
};

export const modalFormItem = (activityType): FormSchema[] => {
  return [
    {
      field: '',
      label: '规则配置',
      component: 'Divider',
    },
    {
      field: 'integralFlag',
      label: '设置积分',
      component: 'Input',
      defaultValue: 'Y',
      show: false,
    },
    {
      field: 'integralOperateType',
      label: '积分操作类型',
      component: 'Input',
      defaultValue: 'DEC',
      show: false,
    },
    {
      field: 'numberPerDay',
      label: '个人每日抽奖次数',
      required: true,
      component: 'InputNumber',
      colProps: { span: 6 },
      defaultValue: 1,
      componentProps: { min: 0 },
    },
    {
      field: 'dailyAwardCount',
      label: '个人每日中奖次数',
      required: false,
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请输入个人每日中奖次数 (0:不限制次数)',
        min: 0,
      },
    },
    {
      field: 'integralScore',
      label: '每次消耗积分',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: { min: 1, step: 1 },
      required: true,
    },
    {
      field: 'integralThreshold',
      label: '参与积分门槛',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: { min: 0 },
    },
  ];
};
