<template>
  <div :class="$style.lottery">
    <div>
      <BasicForm
          @register="registerForm"
      />
    </div>
    <div class="p-5px">
      <BasicTable
          @register="registerTable"
          class="dy-prize-table"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'action'">
            <TableAction
                :actions="[
                    {
                      icon: 'ant-design:delete-twotone',
                      label: '删除',
                      type: 'text',
                      danger: true,
                      onClick: handleDeletePrize.bind(null, index),
                    },
                  ]"
            />
          </template>
          <template v-if="column.key === 'prizeImg'">
            <div class="w-70px h-70px inline-block">
              <CropperImg
                  :value="record.prizeImg"
                  :operate-type="ActivityDocAddr.integralTree"
                  @change="filePath => (record.prizeImg = filePath)"
              />
            </div>
          </template>
        </template>
      </BasicTable>
    </div>
    <div class="flex justify-center items-center my-5px">
      <a-button
          type="primary"
          shape="round"
          @click="handleAddQuestions()"
      >添加奖品</a-button>
      <a-button
        type="primary"
        shape="round"
        class="ml-1"
        @click="handleSubmit"
        >提交</a-button
      >
    </div>
    <TicketConfigModal
        @register="registerModal"
        width="80%"
        :canFullscreen="false"
    />
  </div>
</template>

<script lang="ts" setup>
import {computed,createVNode,onMounted, ref, unref} from 'vue';

import { useTable, BasicTable, TableAction } from '/@/components/Table';
import { useForm, BasicForm } from '/@/components/Form';
import {remove} from 'lodash-es';
import {saveOrUpdate,} from '@/api/activities';
import {ActivityDocAddr, ActivityType} from "@/views/activities/activities.d";
import CropperImg from "@/components/Cropper/src/CropperImg.vue";
import {modalFormItem} from "./data";
import {tableLotteryColum} from "@/views/activities/ActivityTable/BasicSetting/data";
import {getDetailByActivityMode} from "@/api/activities";
import {Modal} from "ant-design-vue";
import { CloseCircleFilled, CheckCircleOutlined } from '@ant-design/icons-vue';
import {useModal} from "@/components/Modal";
import TicketConfigModal from "@/views/coupon/TicketConfigModal.vue";

const props = defineProps({
  activityType: { type: String, default: ActivityType.SIGN_LOTTERY },
});


const record = ref<Recordable>();

onMounted(async ()=>{
  await init()

})

const init = async ()=>{
  const data =  await getDetailByActivityMode({activityMode:props.activityType},true) as any ?? {luckDrawInfo:{}}
  const {
    integralFlag,integralOperateType,integralScore,integralThreshold,
    luckDrawInfo:{prizeInfos,numberPerDay,dailyAwardCount,awardCountMax}} = data
  setFieldsValue({
    integralFlag,integralOperateType,integralScore,integralThreshold,
    numberPerDay,dailyAwardCount,awardCountMax
  });
  record.value = data
  if(prizeInfos?.length){
    setTableData(prizeInfos)
  }
}
const columns = computed(() => {
  return tableLotteryColum(false, ActivityType.INTEGRAL_LOTTERY, unref(record)?.activityId,handleClick);
});


const form = computed(() => {
  if(props.activityType === ActivityType.SIGN_LOTTERY){
    return []
  }
  return modalFormItem();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 180,
  schemas: form,
  showActionButtonGroup: false,
});




// 注册抽奖table
const [registerTable, { getDataSource, setTableData, insertTableDataRecord }] = useTable({
  rowKey: 'id',
  showTableSetting: false,
  columns: columns,
  bordered: true,
  showIndexColumn: true,
  pagination: false,
  actionColumn: {
    width: 50,
    title: '操作',
    dataIndex: 'action',
  },
});

function handleAddQuestions() {
  insertTableDataRecord({});
}

async function handleSubmit() {
  const dataSource = getDataSource();
  const values = await validate();
  const {integralFlag,integralOperateType,integralScore,integralThreshold,numberPerDay,dailyAwardCount} = values

  const {luckDrawInfo,...other} = unref(record) ?? {luckDrawInfo:{}}
  let params = {
    ...other,
    state:'publish',
    activityMode:props.activityType,
    activityCategory: props.activityType,
    luckDrawInfo:{
      ...luckDrawInfo,
      numberPerDay:props.activityType === ActivityType.SIGN_LOTTERY ? 1 : numberPerDay,
      dailyAwardCount,
      prizeInfos:dataSource,
    }
  }
  if(props.activityType === ActivityType.SIGN_LOTTERY){
    params = {
      ...params,
      activityName:'每日签到',
    }
  }else {
    params = {
      ...params,
      activityName:'积分抽奖',
      integralFlag:'Y',
      integralOperateType:'DEC',
      integralScore,
      integralThreshold,
    }
  }
  saveOrUpdate(params).then(async res => {
    const { code, message } = res;
    if (code === 200) {
      await init()
      Modal.success({
        title: '提示',
        icon: createVNode(CheckCircleOutlined),
        content: '提交成功!',
        okText: '确认',
        closable: true,
      });
    } else {
      Modal.error({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: `提交失败!${message}`,
        okText: '确认',
        closable: true,
      });
    }
  });
}

const [registerModal, { openModal:openCouponInfo }] = useModal();
function handleClick(record) {
  openCouponInfo(true, {
    state:'view',
    record,
  });
}
// 删除
function handleDeletePrize(index: number) {
  remove(getDataSource(), (_, k) => k === index);
}
</script>

<style lang="less" module>
.lottery {
  :global {
    .ant-select,
    .ant-input-number {
      width: 100% !important;
    }

    .ant-table-body {
      height: unset !important;
      max-height: unset !important;
    }
  }
}
</style>
