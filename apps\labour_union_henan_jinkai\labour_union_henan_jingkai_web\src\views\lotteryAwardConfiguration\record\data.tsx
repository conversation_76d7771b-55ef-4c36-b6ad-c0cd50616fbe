import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { filter } from 'lodash-es'
import { Image } from 'ant-design-vue'
import { useUserStore } from '/@/store/modules/user'

export function columns(): BasicColumn[] {
  const dictionary = useDictionary()
  return [
    {
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      dataIndex: 'activityName',
      title: '中奖来源',
    },
    {
      dataIndex: 'userName',
      title: '用户姓名',
    },
    {
      dataIndex: 'companyName',
      title: '所属工会',
    },
    {
      dataIndex: 'prizeName',
      title: '奖品名称',
    },
    {
      dataIndex: 'prizeType',
      title: '奖品类型',
      customRender: ({ text }) => {
        return (
            <span>{dictionary.getDictionaryMap.get(`prize_${text}`)?.dictName}</span>
        );
      },
    },
    {
      dataIndex: 'prizeContent',
      title: '奖品内容',
    },
    {
      dataIndex: 'createTime',
      title: '中奖日期',
    },
  ];
}

