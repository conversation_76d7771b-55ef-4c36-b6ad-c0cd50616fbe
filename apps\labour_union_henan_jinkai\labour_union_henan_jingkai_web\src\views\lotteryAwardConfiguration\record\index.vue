<template>
  <div :class="$style.operate">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
               {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'mdi:give-way',
                label: '确认发放',
                type: 'primary',
                disabled: record.assignState === 'y',
                ifShow: (record.prizeType === '6' || record.prizeType === '3')&&record.receiveType === '2',
                onClick: handleAudit.bind(null, record,'give'),
              },

            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PrizeView
      @register="registerModule"
      width="50%"
    />
    <PrizeAuditModal
      @register="registerAudit"
      width="40%"
      @success="handleAuditSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { columns } from './data';
import { useModal } from '@/components/Modal';
import { recordList } from '@/api/lotteryAwardConfiguration';
import PrizeAuditModal from '@/views/activities/ActivityTable/PrizeAuditModal.vue';
import { grantAwardsCode } from '@/api/activities';
import { useMessage } from '@monorepo-yysz/hooks';
import {prizeFormItem} from "@/views/activities/activity";
import PrizeView from "@/views/activities/ActivityTable/PrizeView.vue";

const { createErrorModal, createSuccessModal } = useMessage();

const [registerModule, { openModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  beforeFetch: params => {
    params.sortType = 'desc';
    params.orderBy = 'create_time';
    params.activityMode = 'signLottery,integralLottery'
    return { ...params, sourceType: 'other' };
  },
  api: recordList,
  formConfig: {
    labelWidth: 120,
    schemas: prizeFormItem(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 4 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    width: 250,
    fixed: undefined,
    auth: ['/drawRecord/give', '/drawRecord/view'],
  },
});

const [registerAudit, { closeModal, openModal: openAudit }] = useModal();

function handleView(record) {
  openModal(true, { record });
}
function handleAudit(record, type?: string) {
  let arr: string[] = [];
  arr.push(record.recordId);

  openAudit(true, { businessIds: arr, type, record });
}

function handleAuditSuccess({ values }) {
  grantAwardsCode(values).then(res => {
    const { code, message } = res;
    if (code === 200) {
      createSuccessModal({ content: '发放成功!' });
      reload();
      closeModal();
    } else {
      createErrorModal({ content: `发放失败!${message}` });
    }
  });
}
</script>
<style lang="less" module>
.operate {
  :global {
    .ant-picker {
      width: 100% !important;
    }
  }
}
</style>
