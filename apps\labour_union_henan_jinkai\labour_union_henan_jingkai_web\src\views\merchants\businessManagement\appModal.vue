<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <BasicForm @register="registerForm">
      <template #licensePic="{ model, field }">
        <div v-for="item in model[field]" class="flex">
          <img class="!w-128px !h-128px" :src="item" />
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, ref, computed, unref } from 'vue'
import { BasicModal, useModalInner } from '@/components/Modal'
import { appForm } from './businessManagment'
import { BasicForm, useForm } from '@/components/Form'

export default defineComponent({
  name: 'ColumnModal',
  components: { BasicModal, BasicForm },
  emits: ['success', 'register', 'cancel'],
  setup(_, { emit }) {
    const record = ref<Recordable>()

    const autoId = ref('')
    const companyName = ref('')
    const areaCode = ref('')

    const title = computed(() => {
      return `${unref(record)?.companyName || ''}--审核`
    })

    const myAppSchema = computed(() => {
      return appForm(unref(areaCode))
    })

    const [registerForm, { resetFields, validate }] = useForm({
      labelWidth: 120,
      schemas: myAppSchema,
      showActionButtonGroup: false,
    })

    const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
      await resetFields()
      setModalProps({
        confirmLoading: false,
      })
      record.value = data?.record
      autoId.value = data.record.autoId
      companyName.value = data.record.companyName
      areaCode.value = data.record.areaCode
      console.log(areaCode.value)
      console.log(companyName.value)
    })

    async function handleSubmit() {
      try {
        const values = await validate()
        setModalProps({ confirmLoading: true })
        // TODO custom api
        emit('success', {
          values: { ...values, autoId: autoId.value },
        })
        closeModal()
      } finally {
        setModalProps({ confirmLoading: false })
      }
    }

    function handleCancel() {
      emit('cancel')
    }

    return {
      registerModal,
      handleSubmit,
      handleCancel,
      title,
      registerForm,
    }
  },
})
</script>
