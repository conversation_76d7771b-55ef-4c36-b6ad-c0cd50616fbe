import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { cloneDeep, filter, values } from 'lodash-es'
import { Image } from 'ant-design-vue'
import { useUserStore } from '/@/store/modules/user'
import { searchNextUnionForm } from '/@/utils/searchNextUnion'
import { list } from '@/api/merchants/type';
import { nextTick } from 'vue'
import { uploadApi } from '@/api/sys/upload';

//申请列表
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary()
  const userStore = useUserStore()
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '商户名称',
      dataIndex: 'companyName',
      width: 150,
    },
    {
      title: '商户封面',
      dataIndex: 'companyIcon',
      width: 100,
      customRender: ({ text }) => {
        return <Image src={userStore.getPrefix + text} width={50} height={50}></Image>
      },
    },
    {
      title: '负责人姓名',
      dataIndex: 'contractName',
      width: 120,
    },
    {
      title: '所属区县',
      dataIndex: 'areaCode',
      width: 150,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`regionCode_${text}`)?.dictName}</span>
      },
    },

    {
      title: '审核状态',
      dataIndex: 'auditState',
      width: 120,
      customRender: ({ text, record }) => {
        const { auditState } = record
        return (
          <span
            class={
              auditState == 'pass' ? 'text-green-500' : auditState == 'refuse' ? 'text-red-500' : ''
            }
          >
            {dictionary.getDictionaryMap.get(`applyState_${text}`)?.dictName}
          </span>
        )
      },
    },

    {
      title: '申请时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
      width: 150,
    },
  ]
}

//顶部菜单栏搜索条件配置
export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'companyName',
      label: '商户名称',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请输入商户名称',
        autocomplete: 'off',
      },
    },
    {
      field: 'auditState',
      label: '审核状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('applyState'),
          placeholder: '请选择审核状态',
        }
      },
    },
    {
      field: 'areaCode',
      label: '所属区县',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('regionCode'),
          placeholder: '请选择所属区县',
        }
      },
    },
    ...searchNextUnionForm(),
  ]
}

//详情弹框配置
export const modalForm = (): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    { field: 'companyName', label: '商户名称', required: true, component: 'Input' },
    {
      field: 'address',
      label: '经营地点',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
    },
    {
      field: 'areaCode',
      label: '所属区县',
      colProps: { span: 12 },
      required: true,
      component: 'Select',
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('regionCode'),
        }
      },
    },
        {
      field: 'typeId',
      label: '所属分类',
      required: true,
      component: 'ApiSelect',
      colProps: { span: 12 },
      itemProps: {
        autoLink: true,
      },
      componentProps: ({ formActionType }) => {
        return {
          placeholder: '请选择类型',
          api: list,
          resultField: 'data',
          params: {
            pageSize: 0,
          },
          alwaysLoad: true,
          immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.typeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'typeName', value: 'autoId' },
        };
      },
    },
     {
      field: 'businessHours',
      required: true,
      label: '营业时间',
      component: 'TimeRangePicker',
      colProps: { span: 12 },
      // defaultValue: [
      // dayjs(dayjs().startOf('month').hour(8).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
      // dayjs(dayjs().endOf('month').hour(12).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
      // ],
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
        showNow: true,
      },
    },
     {
      field: 'labourUnionCode',
      label: '统一社会信用代码',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
    },
    {
      field: 'contractName',
      label: '联系人姓名',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
    },
    {
      field: 'contractPhone',
      label: '联系人电话',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
    },
    {
      field: 'identityType',
      label: '经营主体证件类型',
      colProps: { span: 12 },
      required: true,
      component: 'Select',
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('identityType'),
        }
      },
    },
    {
      field: 'identityNumber',
      label: '经营主体证件号码',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
    },
    {
      field: 'companyIcon',
      label: '商户封面图',
      colProps: { span: 12 },
      required: true,
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'licenseImg',
      label: '营业执照',
      required: true,
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
    },
      {
      field: 'openingImg',
      label: '开户许可证件',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyIcon',
      label: '法人身份证(正面)',
      required: true,
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'identityImgBack',
      label: '法人身份证(背面)',
      required: true,
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
    },
    // {
    //   field: 'qualificationImg',
    //   label: '资质证明',
    //   required: true,
    //   component: 'Input',
    //   slot: 'pic',
    //   rest: true,
    // },
     {
      field: 'publicityImg',
      label: '详情宣传图',
      component: 'Upload',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: {
        api: uploadApi,
        maxNumber: 3,
        uploadParams: {
          operateType: 65,
        },
      },
    },
    {
      field: 'qualificationImg',
      label: '资质证明',
      colProps: { span: 12 },
      component: 'Upload',
      rulesMessageJoinLabel: true,
      componentProps: {
        api: uploadApi,
        maxNumber: 3,
        uploadParams: {
          operateType: 65,
        },
      },
    },

    {
      field: 'marketName',
      ifShow: ({ values }) => {
        return values.marketName
      },
      label: '所属集市',
      required: false,
      component: 'Input',
    },
    {
      field: 'introduce',
      label: '商户介绍',
      required: false,
      component: 'InputTextArea',
    },

    {
      field: 'auditState',
      label: '审核状态',
      required: true,
      component: 'RadioGroup',
      defaultValue: 'review',
      // showActionButtonGroup: false,
      dynamicDisabled: true,
      componentProps: {
        options: [
          {
            label: '待审核',
            value: 'wait',
          },
          {
            label: '审核通过',
            value: 'pass',
          },
          {
            label: '审核拒绝',
            value: 'refuse',
          },
        ],
      },
    },
    {
      field: 'auditRemark',
      label: '审核备注',
      required: false,
      component: 'InputTextArea',
    },
  ]
}

//审核弹框配置
export const appForm = (areaCode): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'operateType',
      label: '审核意见',
      component: 'RadioGroup',
      // defaultValue: 'pass',
      required: true,
      componentProps: function ({ formModel }) {
        return {
          options: filter(
            cloneDeep(dictionary.getDictionaryOpt.get('applyState')),
            v => v.value !== 'wait'
          ),
          onChange: e => {
            //选择驳回时,清空值
            if ('refuse' === e.target.value) {
              formModel['accountNickname'] = undefined
              formModel['incomeAccountNumber'] = undefined
              formModel['transPlatformBusinessId'] = undefined
            }
          },
        }
      },
    },
    {
      field: 'accountNickname',
      label: '开户户名',
      component: 'Input',
      required: true,
      ifShow: ({ values }) => {
        return values.operateType === 'pass'
      },
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入开户户名',
        autocomplete: 'off',
      },
    },
    {
      field: 'incomeAccountNumber',
      label: '收款银行卡号',
      required: true,
      component: 'Input',
      ifShow: ({ values }) => {
        return values.operateType === 'pass'
      },
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入收款银行卡号',
        autocomplete: 'off',
      },
    },
    {
      field: 'transPlatformBusinessId',
      label: '交易平台业务ID',
      required: true,
      component: 'Input',
      ifShow: ({ values }) => {
        return values.operateType === 'pass'
      },
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入交易平台业务ID',
        autocomplete: 'off',
      },
    },
    {
      field: 'remark',
      label: '审核备注',
      required: function (Recordable) {
        if (Recordable.model.operateType == 'refuse') {
          return true
        } else {
          return false
        }
      },
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入审核备注,不能超过200个字符',
        showCount: true,
        maxlength: 200,
      },
    },

    // {
    //   field: 'isBelongMarket',
    //   label: '是否加入集市',
    //   colProps: { span: 12 },
    //   component: 'RadioGroup',
    //   defaultValue: 'n',
    //   required: true,
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get(`YesOrNo`),
    //   },
    // },

    {
      field: 'areaCode',
      label: '所属区域',
      ifShow: ({ values }) => {
        // return  values.isBelongMarket === 'y'
        return false
      },
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`quxian`),
      },
    },

   
  ]
}
