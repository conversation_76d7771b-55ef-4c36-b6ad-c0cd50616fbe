<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" @ok="handleSubmit" @cancel="handleCancel">
    <BasicForm @register="registerForm" :class="disabledClass">
      <!-- <template #pic="{ model, field }">
        <UploadSimple @change="info => handleImges(info, model, field)" :some-file="model[field]" :disabled="disabled"
          :operate-type="0" :fix="field === 'qualificationImg' ? 3 : 1" />
      </template> -->
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, ref, computed, unref } from 'vue'
import { BasicModal, useModalInner } from '@/components/Modal'
import { BasicForm, useForm } from '@/components/Form'
import { modalForm } from './businessManagment'
import { Image } from 'ant-design-vue'
import { list } from '@/api/merchantDic/index'
import UploadSimple from '@/views/components/upload-simple/index.vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

export default defineComponent({
  name: 'ColumnModal',
  components: {
    BasicModal,
    BasicForm,
    Image,
    ImagePreviewGroup: Image.PreviewGroup,
    UploadSimple,
    PlusOutlined,
    SearchOutlined,
  },
  emits: ['success', 'register', 'cancel'],
  setup(_, { emit }) {
    const isUpdate = ref(true)
    const autoId = ref('')

    const disabled = ref(false)
    const record = ref()

    const disabledClass = computed(() => {
      return unref(disabled) ? 'back-transparent' : ''
    })

    const visible = ref<boolean>(false)
    const setVisible = (value): void => {
      visible.value = value
    }

    const title = computed(() => {
      return unref(disabled)
        ? `${unref(record)?.companyName || ''}--详情`
        : unref(isUpdate)
          ? `修改${unref(record)?.companyName || ''}`
          : '新增'
    })

    const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
      labelWidth: 120,
      schemas: modalForm(),
      showActionButtonGroup: false,
    })

    const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
      await resetFields()
      isUpdate.value = !!data?.isUpdate
      disabled.value = !!data?.disabled
      if (unref(isUpdate)) {
        autoId.value = data.record.autoId
        record.value = data.record

        const { qualificationImg: m, openTime, closeTime } = data.record
        await setFieldsValue({
          ...data.record,
          qualificationImg: m ? m.split(',') : [],
          businessHours: [
            dayjs(openTime, 'HH:mm:ss'),
            dayjs(closeTime, 'HH:mm:ss')
          ]
        })
      }

      setModalProps({
        confirmLoading: false,
        showOkBtn: !unref(disabled),
      })

      setProps({ disabled: unref(disabled) })
    })

    function handleImges(info, model, field) {
      const { index, filePath } = info
      if (field === 'qualificationImg') {
        if (!model[field]) {
          model[field] = []
        }
        model[field][index] = filePath
      } else {
        model[field] = filePath
      }
    }

    async function handleSubmit() {
      try {
        const values = await validate()
        const { qualificationImg } = values
        setModalProps({ confirmLoading: true })
        emit('success', {
          isUpdate: unref(isUpdate),
          values: {
            ...record.value,
            ...values,
            autoId: isUpdate.value ? autoId.value : undefined,
            accountType: 'merchant',
            accountInfo: values.contactPhone,
            qualificationImg: qualificationImg ? qualificationImg.join(',') : '',
          },
        })
        closeModal()
      } finally {
        setModalProps({ confirmLoading: false })
      }
    }

    function handleCancel() {
      emit('cancel')
    }

    return {
      registerModal,
      handleSubmit,
      handleCancel,
      title,
      registerForm,
      list,
      disabledClass,
      handleImges,
      setVisible,
      disabled,
    }
  },
})
</script>
