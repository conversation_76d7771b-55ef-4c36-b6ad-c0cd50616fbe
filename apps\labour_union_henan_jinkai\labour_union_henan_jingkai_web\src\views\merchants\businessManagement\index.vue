<template>
  <div>
    <BasicTable @register="registerTable" :clickToRowSelect="false">
      <template #toolbar>
        <a-button type="primary" @click="batchAudit" auth='/merchants/manage/batchAudit'> 批量审核</a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              ifShow: false,
              onClick: handleEdit.bind(null, record),
              auth:'/merchants/manage/edit'
            },
            {
              icon: 'carbon:task-view',
              label: '商户详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              // auth: '/businessManagement/view',
              auth:'/merchants/manage/view'
            },
            {
              icon: 'ant-design:audit-outlined',
              label: '商户审核',
              type: 'primary',
              onClick: handleApp.bind(null, record),
              // auth: '/businessManagement/audit',
              auth:'/merchants/manage/audit',
              disabled:
                record.auditState !== 'wait',
            },
          ]"
        />
      </template>
      <!--      <template #form-businessBigType="{ model, field }">
        <ApiSelect :value="model[field]" :allowClear="true"
          @change="(info, node) => handleChangeType(info, node, model, field)" :api="merchantDicList"
          :params="{ parentCode: 'N' }" :resultField="'data'" :fieldNames="{ label: 'typeName', value: 'typeCode' }"
          placeholder="请选择经营类型">
        </ApiSelect>
      </template>
      <template #form-businessSmallType="{ model, field }">
        <Select v-model:value="model[field]" :allowClear="true" :options="options"
          :fieldNames="{ label: 'typeName', value: 'typeCode' }" />
      </template>-->
    </BasicTable>
    <BusinessModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="50%"
    />
    <AppModal
      @register="registerApp"
      @success="handleAppSuccess"
      :canFullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { createVNode, ref } from 'vue'
import { BasicTable, useTable, TableAction } from '@/components/Table'
// import { find, forEach, } from 'lodash-es';
import { Modal, Select } from 'ant-design-vue'
import BusinessModal from './businessModal.vue'
import {
  CloseCircleFilled,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons-vue'
import { useModal } from '@/components/Modal';
import { list, saveOrUpdate, app, getDetails } from '@/api/busniessManage'
import { list as merchantDicList } from '@/api/merchantDic/index'
import { columns, formSchemas } from './businessManagment'
import AppModal from './appModal.vue'
import { map } from 'lodash-es'
import { useUserStore } from '@/store/modules/user'

const user = useUserStore()
const options = ref([])

const [registerTable, { reload, getSelectRows, updateTableDataRecord }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  pagination: true,
  api: list,
  searchInfo: {
    // companyType: 'merchant',
  },
  //顶部搜索条件配置
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.auditState !== 'wait' || record.laborUnionId !== user.getUserInfo.companyId,
    }),
  },
  actionColumn: {
    title: '操作',
    width: 210,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
    // auth: ['/businessManagement/modify', '/businessManagement/audit', '/businessManagement/view'],
    auth: ['/merchants/manage/batchAudit','/merchants/manage/view', '/merchants/manage/audit', '/merchants/manage/edit'],
  },
})

const [registerModal, { openModal }] = useModal()

const [registerApp, { openModal: openModalApp }] = useModal()

function handleEdit(record) {
  if (record.applyState == 'wait') {
    openModal(true, {
      record: record,
      isUpdate: true,
      disabled: false,
    })
  } else {
    Modal.warning({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: '仅审核中商家信息可修改',
      okText: '确认',
      closable: true,
    })
  }
}
//单个审核
function handleApp(record) {
  if (record.auditState == 'wait') {
    openModalApp(true, {
      record: {
        autoId: [record.autoId],
        companyName: record.companyName,
        areaCode: record.areaCode,
      },
      isUpdate: true,
    })
  } else {
    Modal.warning({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: '仅审核中商家可审核',
      okText: '确认',
      closable: true,
    })
  }
}

//批量审核
function batchAudit() {
  const rows = getSelectRows()
  if (!rows || rows.length === 0) {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '请选择至少一条数据进行审核！',
      okText: '确认',
      closable: true,
    })
    return false
  }
  openModalApp(true, { record: { autoId: map(rows, v => v.autoId) } })
}

//查看详情
function handleView(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: true,
  })
}

function handleSuccess({ values }) {
  saveOrUpdate(values).then(res => {
    const { code, message } = res
    if (code === 200) {
      Modal.success({
        title: '提示',
        icon: createVNode(CheckCircleOutlined),
        content: '操作成功!' || message,
        okText: '确认',
        closable: true,
      })
      updateTableDataRecord(values.autoId, values)
      reload()
    } else {
      Modal.error({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: `操作失败!${message}`,
        okText: '确认',
        closable: true,
      })
    }
  })
}
function handleAppSuccess({ values }) {
  if (values.auditState === 'wait' || values.auditState === null) {
    Modal.error({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: `请选择一种审核意见`,
      okText: '确认',
      closable: true,
    })
  } else {
    const { autoId, ...params } = values
    app({ todoValueList: autoId, ...params }).then(res => {
      const { code, message } = res
      if (code === 200) {
        Modal.success({
          title: '提示',
          icon: createVNode(CheckCircleOutlined),
          content: '操作成功!' || message,
          okText: '确认',
          closable: true,
        })
        updateTableDataRecord(values.autoId, values)
        reload()
      } else {
        Modal.error({
          title: '提示',
          icon: createVNode(CloseCircleFilled),
          content: `操作失败!${message}`,
          okText: '确认',
          closable: true,
        })
      }
    })
  }
}

async function handleChangeType(info, node, model, field) {
  if (info == null) {
    options.value = []
  } else {
    model[field] = info
    //@ts-ignore
    options.value = await merchantDicList({ parentCode: info })
  }
}
</script>
