<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from './data';
import dayjs from 'dayjs';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.companyName || ''}--详情`
      : `编辑${unref(record)?.companyName || ''}`
    : '新增商户';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(unref(isUpdate));
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
  const {  publicityImg,qualificationImg,openTime,closeTime } = data.record;
    setFieldsValue({
      ...data.record,
      publicityImg: publicityImg ? publicityImg.split(',') : [],
      qualificationImg: qualificationImg ? qualificationImg.split(',') : [],
      businessHours: openTime&&closeTime?[dayjs(openTime, 'HH:mm:ss'),dayjs(closeTime, 'HH:mm:ss')]:[],
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const { publicityImg,qualificationImg,businessHours,...values} = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        openTime:dayjs(businessHours[0]).format('HH:mm:ss'),
        closeTime:dayjs(businessHours[1]).format('HH:mm:ss'),
        transConfig: {
          accountNickname: values.accountNickname,
          incomeAccountNumber: values.incomeAccountNumber,
          transPlatformBusinessId: values.transPlatformBusinessId,
        },
        openCompanyInfoDTO: {
          companyName: values.companyName,
          contractName: values.contractName,
          contractPhone: values.contractPhone,
          areaCode: values.areaCode,
          companyIcon: values.companyIcon,
          introduce: values.introduce,
          address: values.address,
          typeId: values.typeId,
          addressCoordinate: values.addressCoordinate,
          openTime:dayjs(businessHours[0]).format('HH:mm:ss'),
          closeTime:dayjs(businessHours[1]).format('HH:mm:ss')
        },
        publicityImg: publicityImg && publicityImg.length > 0 ? publicityImg.join(',') : '',
        qualificationImg: qualificationImg && qualificationImg.length > 0 ? qualificationImg.join(',') : '',
        companyMoreInfo: {
          // identityType: values.identityType,
          // identityNumber: values.identityNumber,
          // identityImgFront: values.identityImgFront,
          // identityImgBack: values.identityImgBack,
          // licenseImg: values.licenseImg,
          // openingImg: values.openingImg,
          labourUnionCode: values.labourUnionCode,
        },
        systemQueryType:"manage"
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
