<template>
  <div>
    <BasicTable @register="registerTable" @expand="expand">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth='/merchants/info/add'
        >
          新增商户
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/merchants/info/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/merchants/info/update',
              },
              {
                icon: 'ep:delete',
                label: '下架',
                danger: true,
                type: 'primary',
                onClick: handleDelete.bind(null, record),
                auth: '/merchants/info/down',
              },
              {
                icon: 'carbon:password',
                label: '重置密码',
                type: 'primary',
                onClick: handlePwd.bind(null, record),
                auth: '/merchants/pwd',
              },
            ]"
          />
        </template>
      </template>
      <template #expandedRowRender="{ expanded }">
        <BasicTable
          :columns="childrenColumns()"
          :api="findChildList"
          :pagination="false"
          :useSearchForm="true"
          :formConfig="{
            labelWidth: 120,
            schemas: childrenFormSchemas(),
            autoSubmitOnEnter: true,
            submitOnChange: true,
          }"
          ref="tableRef"
          rowKey="autoId"
          v-if="expanded"
          :showIndexColumn="false"
          :bordered="true"
          :actionColumn="{
            title: '操作',
            width: 330,
            dataIndex: 'action',
            slots: { customRender: 'action' },
            fixed: undefined,
          }"
          :searchInfo="{ pid: record?.autoId}"
          :maxHeight="400"
        >
          <template #action="{ record }">
            <TableAction
              :actions="[
                {
                  icon: 'carbon:task-view',
                  label: '详情',
                  type: 'default',
                  onClick: handleView.bind(null, record),
                  auth: '/merchants/info/view',
                },
                {
                icon: 'ep:delete',
                label: '下架',
                danger: true,
                type: 'primary',
                  onClick: handleDelete.bind(null, record),
                auth: '/merchants/info/down',
              },
              ]"
            />
          </template>
        </BasicTable>
      </template>
    </BasicTable>
    <MerchantsModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas, childrenColumns, childrenFormSchemas } from './data';
import MerchantsModal from './MerchantsModal.vue';
import {
  list, view, insertMerchant, updateMerchant,
  findChildList, deleteCompanyByCompanyId,queryExistBusinessByCompanyId,resetPassword
} from '@/api/merchants';
import { useMessage } from '@monorepo-yysz/hooks';
import { ref, unref } from 'vue'

const { createErrorModal, createSuccessModal,createConfirm } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
    actionColOptions: { span: 4 },
  },
  searchInfo: {pid: '0'},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 400,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/merchants/info/add','/merchants/info/view','/merchants/info/update' ,'/merchants/info/down','/merchants/pwd'],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

const tableRef = ref()

const record = ref({})

function expand(expanded, data) {
  record.value = data
}

// 新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

// 编辑
function handleEdit(record: Recordable<any>) {
  view({ companyId:record.companyId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

// 详情
function handleView(record: Recordable<any>) {
  view({ companyId:record.companyId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

// 重置密码
function handlePwd(record: any) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要重置${record.companyName}商户管理员的密码？`,
    onOk: function () {
      resetPassword({
        todoValueList:[record.accountAutoId]
      }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `重置成功！` });
          reload();
        } else {
          createErrorModal({ content: `重置失败！${message}。` });
        }
      });
    },
  });
}

// 删除操作
function handleDelete({ companyName, companyId }: Recordable) {
  queryExistBusinessByCompanyId(companyId).then(({ code, message,data }) => {
  if (code === 200) {
          createConfirm({
    iconType: 'warning',
    content: data ? `${data},请确认要下架商户:${companyName}？` : `请确认要删除商户:${companyName}？`,
    onOk: function () {
      deleteCompanyByCompanyId(companyId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
      } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
  
  });
  
}

// 新增修改
function handleSuccess({ values, isUpdate }: Recordable<any>) {
  const api = isUpdate ? updateMerchant : insertMerchant;
  api(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败！${message}。`,
      });
    }
  });
}
</script>
