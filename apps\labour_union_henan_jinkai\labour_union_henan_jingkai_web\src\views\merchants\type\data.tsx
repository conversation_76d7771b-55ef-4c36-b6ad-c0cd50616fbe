import { BasicColumn, FormSchema } from '/@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { Tooltip,Image } from 'ant-design-vue';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { useUserStore } from '@/store/modules/user';
import {findSimpleList} from "@/api/activities";
const userStore = useUserStore();
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '序号',
      dataIndex: 'sortNumber',
    },
    {
      title: '类型名称',
      dataIndex: 'typeName',
    },
    // {
    //   title:"封面图",
    //   dataIndex:'indexCover',
    //   customRender: ({ text }) => {
    //     return text? (
    //       <Image src={userStore.getPrefix + text }
    //         width={40}
    //         height={40}
    //        />
    //     ):('--')
    //   }
    // },
    // {
    //   title: '发布状态',
    //   dataIndex: 'publishStatus',
    //   customRender: ({ text }) => {
    //     const name = dictionary.getDictionaryMap.get(`categoryPublishStatus_${text}`)?.dictName;
    //     return <Tooltip title={name}>{name}</Tooltip>;
    //   },
    // },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      customRender: ({ text }) => {
        return text? <span>{text}</span> : '--';
      },
    },
  ];
};

//顶部搜索条件
export const checkFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'typeName',
      label: '类型名称',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
    // {
    //   field: 'publishStatus',
    //   label: '发布状态',
    //   component: 'RadioGroup',
    //   rulesMessageJoinLabel: true,
    //   colProps: { span: 6 },
    //   componentProps: function () {
    //     return {
    //       options: [
    //         ...dictionary.getDictionaryOpt.get('categoryPublishStatus'),
    //         {
    //           label: '全部',
    //           value: '',
    //         }
    //       ] as RadioGroupChildOption[],
    //     };
    //   },
    // },
    // {
    //   field: 'queryCompanyId',
    //   label: '下级工会',
    //   colProps: { span: 6 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   ifShow: userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5',
    //   componentProps: function () {
    //     return {
    //       showSearch: true,
    //       filterOption: (input: string, option: any) => {
    //         return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    //       },
    //       options: filter(
    //         cloneDeep(dictionary.getDictionaryOpt.get(`unionsInfo`)),
    //         v => v.value !== '6650f8e054af46e7a415be50597a99d5'
    //       ),
    //     };
    //   },
    // },
    // {
    //   field: 'nextLevelFlag',
    //   component: 'Checkbox',
    //   label: '包含下级',
    //   colProps: {
    //     span: 3,
    //   },
    //   defaultValue: true,
    // }
  ];
};

// 表单
export const modalFormItem = (disabled: boolean): FormSchema[] => {
  return [
  {
      field: 'typeName',
      label: '类型名称',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 24 },
      componentProps: {
        showCount: true,
        maxlength: 6,
      },
      required: true
    },
    {
      field: 'sortNumber',
      label: '序号',
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      colProps: { span: 24 },
      required: true
    },
    
    // {
    //   field: 'content',
    //   label: '内容',
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    //   colProps: { span: 24 },
    //   render: ({ model, field, disabled }) => {
    //     return h(Tinymce, {
    //       value: model[field],
    //       onChange: (value: string) => {
    //         model[field] = value;
    //       },
    //       showImageUpload: false,
    //       operateType: 2,
    //       options: {
    //         readonly: disabled,
    //       },
    //     });
    //   },
    //   required: true
    // },
    // {
    //   field: 'indexCover',
    //   label:"封面图",
    //   colProps: { span: 24 },
    //   component: 'CropperForm',
    //   componentProps: function () {
    //     return {
    //       operateType: 70,
    //        imgSize: 750 / 302,
    //     };
    //   },
    //   renderComponentContent() {
    //     return {
    //       tip: () => (
    //         <div class="text-sm leading-7">
    //           注:图标规格大小为(<span class="text-red-500">750 * 302</span>)以内
    //         </div>
    //       ),
    //     };
    //   },
    //   rulesMessageJoinLabel: true,
    //   required: true,
    // },
    // {
    //   field:"scenicImages",
    //   label:"详情图",
    //   colProps: { span: 24 },
    //   component: 'Upload',
    //   componentProps: {
    //     uploadParams: {
    //       operateType: 68,
    //     },
    //     maxSize: 10,
    //     maxNumber: 3,
    //     api: uploadApi,
    //     accept: ['image/*'],
    //   },
    //   rulesMessageJoinLabel: true,
    //   ifShow:!disabled
    // },
    // {
    //   field: 'scenicImages',
    //   label:"详情图",
    //   slot:'scenicImages',
    //   colProps: { span: 24 },
    //   ifShow:disabled
    // },
    // {
    //   field: 'pageLink',
    //   label: '关联活动',
    //   component: 'ApiSelect',
    //   required: true,
    //   colProps: { span: 12 },
    //   componentProps: () => {
    //     return {
    //       placeholder: '请选择活动',
    //       api: findSimpleList,
    //       resultField: 'data',
    //       params: {
    //         pageSize: 0,
    //         activityCategory:'friendship',
    //         state:'publish',
    //         queryCompanyId: useUser.getUserInfo.companyId,
    //         orderBy: 'auto_id',
    //         sortType: 'desc',
    //         nextLevelFlag: true,
    //       },
    //       immediate: true,
    //       onChange: () => {},
    //       showSearch: true,
    //       filterOption: (input: string, option: any) => {
    //         return option.activityName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    //       },
    //       fieldNames: { label: 'activityName', value: 'activityId' },
    //     };
    //   },
    // },
    // {
    //   field: 'video',
    //   label: '视频地址',
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    //   colProps: { span: 12 },
    // },
    // {
    //   field: 'audioUrl',
    //   label: '数字播报地址',
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    //   colProps: { span: 12 },
    // },
    // {
    //   field:'publishStatus',
    //   label: '发布状态',
    //   component: 'RadioGroup',
    //   componentProps: function () {
    //     return {
    //       options: dictionary.getDictionaryOpt.get('categoryPublishStatus') as RadioGroupChildOption[],
    //     };
    //   },
    //   ifShow:disabled,
    //   colProps: { span: 12 },
    // },
    // {
    //   field: 'publishTime',
    //   label: '发布时间',
    //   colProps: { span: 12 },
    //   component: 'DatePicker',
    //   componentProps: {
    //     valueFormat: `YYYY-MM-DD HH:mm:ss`,
    //     format: `YYYY-MM-DD HH:mm:ss`,
    //     showTime: true
    //   },
    //   ifShow:disabled
    // },
    // {
    //   field: 'createTime',
    //   label: '创建时间',
    //   colProps: { span: 12 },
    //   component: 'Input',
    //   ifShow:disabled
    // },
  ];
};
