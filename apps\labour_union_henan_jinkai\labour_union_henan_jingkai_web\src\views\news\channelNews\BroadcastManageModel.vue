<!-- 播报预览 -->
<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
    cancelText="关闭"
    :wrap-class-name="`${$style['video-modal']}`"
  >
    <a-button
      @click="handleRefresh"
      type="primary"
      >刷新</a-button
    >
    <a-button
      @click="handleBroadcast"
      type="primary"
      class="!ml-6px"
      v-if="handleBroadcastPreview()"
      >播报预览</a-button
    >
    <a-button
      @click="handleGenerate"
      type="primary"
      class="!ml-6px"
      v-if="handleRegeneration()"
      >重新生成</a-button
    >

    <div>
      <span>自动生成类型:</span>
      <span class="!ml-5px text-red-600">
        {{
          dictionary.getDictionaryMap.get(`automaticallyType_${unref(record)?.dataMode}`)?.dictName
        }}</span
      >
    </div>
    <div>
      <span>生成状态:</span>
      <span class="!ml-5px text-red-600">
        {{
          `${
            unref(record)?.dataMode === 'voice'
              ? dictionary.getDictionaryMap.get(`newsVoiceStatus_${unref(record)?.voiceStatus}`)?.dictName || '生成中'
              : dictionary.getDictionaryMap.get(`newsVideoStatus${unref(record)?.videoStatus}`)?.dictName  || '生成中'
          }`
        }}</span
      >
    </div>
  </BasicModal>
  <!-- 播报预览 -->
  <BroadcastModel
    @register="broadcastView"
    :can-fullscreen="false"
    width="40%"
  ></BroadcastModel>
</template>

<script lang="ts" setup>
import { ref, unref, computed, createVNode } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useDictionary } from '@/store/modules/dictionary';
import { newsGetOneNews, newsRegeneration } from '@/api/news';
import { useModal } from '@/components/Modal';
import BroadcastModel from './BroadcastModel.vue';
import { Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useMessage } from '@monorepo-yysz/hooks';

const { createErrorModal, createSuccessModal } = useMessage();

//播报预览
const [broadcastView, { openModal: openBroadcast }] = useModal();

const dictionary = useDictionary();

const record = ref<Recordable>();

const title = computed(() => {
  return `${unref(record)?.newsTitle || ''}--生成详情`;
});

const [registerModal, {}] = useModalInner(async data => {
  record.value = data.record;
  await refresh(false, '');
});

//刷新新闻生成状态
function handleRefresh() {
  refresh(true, '刷新成功!');
}

//公用刷新
function refresh(whetherPrompt, content) {
  newsGetOneNews({ autoId: unref(record)?.autoId }).then(res => {
    const { code, data, message: msg } = res;
    if (code === 200) {
      if (whetherPrompt) {
        createSuccessModal({ content: content });
      }
      record.value = data as Recordable;
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}
//处理重新生成状态
function handleRegeneration() {
  const dataMode1 = unref(record)?.dataMode;
  const aiVideoAddress1 = unref(record)?.aiVideoAddress;
  const aiVoiceAddress1 = unref(record)?.aiVoiceAddress;
  if ('voice' === dataMode1 && aiVoiceAddress1) {
    return false;
  } else if ('video' === dataMode1 && aiVideoAddress1) {
    return false;
  } else {
    return true;
  }
}

//处理播报预览状态
function handleBroadcastPreview() {
  const dataMode1 = unref(record)?.dataMode;
  const aiVideoAddress1 = unref(record)?.aiVideoAddress;
  const aiVoiceAddress1 = unref(record)?.aiVoiceAddress;
  if ('voice' === dataMode1 && aiVoiceAddress1) {
    return true;
  } else if ('video' === dataMode1 && aiVideoAddress1) {
    return true;
  } else {
    return false;
  }
}

//播报预览
function handleBroadcast() {
  openBroadcast(true, { record: unref(record) });
}
//重新生成播报
function handleGenerate() {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定重新生成数字播报?耗时任务请谨慎操作!`,
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          newsRegeneration({ autoId: unref(record)?.autoId }).then(res => {
            const { code, message: msg } = res;
            if (code === 200) {
              createSuccessModal({ content: '重新生成成功' });
              //生成完后刷新页面
              refresh(false, '');
              resolve();
            } else {
              createErrorModal({ content: `${msg}` });
            }
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}
</script>
<style lang="less" module>
.video-modal {
  :global {
    .footer-group {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
