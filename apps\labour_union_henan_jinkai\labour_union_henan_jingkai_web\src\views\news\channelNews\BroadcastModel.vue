<!-- 播报预览 -->
<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
    cancelText="关闭"
    @cancel="handleCancel"
    :wrap-class-name="`${$style['video-modal']}`"
  >
    <div class="video-container">
      <div v-if="!!videoAddress">
        <video
          :src="videoAddress"
          controls
          autoplay
          ref="videoRef"
          loop
        >
          <track
            v-if="subtitleURL"
            :src="subtitleURL"
            kind="subtitles"
            srclang="zh"
            label="中文"
            default
          />
        </video>
      </div>
      <div v-else>
        <Empty
          description="无播报信息"
          class="-enter-x my-empty-component"
        />
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed, nextTick } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useUserStore } from '@/store/modules/user';
import { Empty } from 'ant-design-vue';

const userStore = useUserStore();

const title = computed(() => {
  return `${unref(newsTitle) || ''}--播报预览`;
});

const subtitleURL = ref();
//video标签src路径
const videoAddress = ref();
//新闻标题
const newsTitle = ref();

const videoRef = ref();

const [registerModal, {}] = useModalInner(async data => {
  await nextTick();

  videoAddress.value = undefined;
  subtitleURL.value = undefined;
  const {
    newsTitle: nt,
    aiBroadcastType,
    aiVoiceAddress,
    aiVideoAddress,
    vttAddress,
    dataMode,
    aiLinkAddress,
  } = data.record;
  newsTitle.value = nt;
  switch (aiBroadcastType) {
    //自动生成
    case 'automatically':
      switch (dataMode) {
        case 'voice':
          videoAddress.value = userStore.getPrefix + aiVoiceAddress;
          break;
        case 'video':
          videoAddress.value = userStore.getPrefix + aiVideoAddress;
          const subtitleFile = await fetchSubtitleFile(userStore.getPrefix + vttAddress); // 获取字幕文件
          subtitleURL.value = URL.createObjectURL(subtitleFile); // 将字幕文件转换为BASE64编码
          break;
      }
      break;
    //手动录入
    case 'manualEntry':
      switch (dataMode) {
        case 'uploadAudio':
          videoAddress.value = userStore.getPrefix + aiVoiceAddress;
          break;
        case 'uploadVideo':
          videoAddress.value = userStore.getPrefix + aiVideoAddress;
          break;
        case 'inputLink':
          videoAddress.value = aiLinkAddress;
          break;
      }
      break;
  }
});

function fetchSubtitleFile(url) {
  return new Promise<File>((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'blob';
    xhr.onload = function () {
      if (xhr.status === 200) {
        const blob = xhr.response;
        const file = new File([blob], 'subtitle.vtt', { type: 'text/vtt' });
        resolve(file);
      } else {
        reject(new Error('Failed to fetch subtitle file'));
      }
    };
    xhr.onerror = function () {
      reject(new Error('Failed to fetch subtitle file'));
    };
    xhr.send();
  });
}
function handleCancel() {
  unref(videoRef) && (unref(videoRef).currentTime = 0);
}
</script>
<style scoped>
.my-empty-component {
  color: white;
}
.video-container {
  position: relative;
  padding-top: 56.25%; /* 宽高比例为16:9时，设置为56.25% */
  background-color: black; /* 将容器背景设置为黑色 */
}

.video-container video::cue {
  background-color: transparent; /* 设置背景颜色为透明 */
  border: none; /* 去掉边框 */
}

.video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
</style>

<style lang="less" module>
.video-modal {
  :global {
    .footer-group {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
