<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              }
            ]"
          >
          </TableAction>
        </template>
      </template>
    </BasicTable>
  </BasicModal>
  <CommentModal
    @register="registerComment"
    :can-fullscreen="false"
    width="40%"
  >
  </CommentModal>
</template>

<script lang="ts" setup>
import { useTable, TableAction, BasicTable } from '@/components/Table';
import { reviewColumns, reviewformSchemas } from './data';
import { userCommentFindList } from '@/api/commentsNews';

import { useModal,useModalInner,BasicModal } from '@/components/Modal';
import CommentModal from '../commentManagement/CommentModal.vue';
import { nextTick, ref, computed, unref } from 'vue';
const newsId = ref(null);
const newsTitle = ref(null);

const title = computed(() => {
      return `查看${unref(newsTitle)}评论`;
    });

const [registerModal, {}] = useModalInner(async data => {

  newsId.value = data.record.newsId;
  newsTitle.value = data.record.newsTitle
  
  await reload();
  await nextTick();
  await clearSelectedRowKeys();
});

const [registerComment, { openModal }] = useModal();

const [registerTable, { reload, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: reviewColumns(),
  api: userCommentFindList,
  maxHeight: 400,
  formConfig: {
    labelWidth: 120,
    schemas: reviewformSchemas(),
    autoSubmitOnEnter: true,
  },
  beforeFetch: params => {
    params.newsId = newsId.value;
    params.auditStatus = 'pass';
    //处理时间
    const { createTimeRange } = params;
    if (createTimeRange && createTimeRange.length === 2) {
      params.startTime = createTimeRange[0];
      params.endTime = createTimeRange[1];
    }
    params.createTimeRange = undefined;
    return params;
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 130,
    dataIndex: 'action',
    fixed: undefined,
  },
});
//详情
function handleView(record) {
  openModal(true, {
    record: { ...record, recordId: [record.autoId] },
    disabled: true,
    notShow: false,
  });
}
</script>
