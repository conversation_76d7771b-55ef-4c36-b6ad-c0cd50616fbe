<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    :show-ok-btn="false"
  >
    <BasicTable @register="registerTable"></BasicTable>
  </BasicModal>
</template>

<script lang="ts">
import { computed, defineComponent, ref, unref } from 'vue';
// import { shareAndLike, shareAndLikeForm } from './data'
import { shareAndLike } from './data';
import { useDictionary } from '@/store/modules/dictionary';
import { useModalInner, BasicModal } from '@/components/Modal';
import { RadioGroup, DatePicker } from 'ant-design-vue';
import { BasicTable, useTable } from '@/components/Table';
import { newsCollection, newsLike } from '@/api/news';

export default defineComponent({
  name: 'ShareAndLike',
  components: { BasicModal, RadioGroup, DatePicker, BasicTable },
  props: {
    ifcollect: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['register', 'success'],
  setup(props, {}) {
    const dictionary = useDictionary();

    const sourceId = ref('');

    const newsTitle = ref('');

    const ifcollect = computed(() => {
      return props.ifcollect;
    });

    const api = computed(() => {
      return unref(ifcollect) ? newsCollection : newsLike;
    });

    const columns = computed(() => {
      return shareAndLike(unref(ifcollect));
    });

    const title = computed(() => {
      return `查看${unref(newsTitle)}${unref(ifcollect) ? '收藏' : '点赞'}`;
    });

    const [registerModal, {}] = useModalInner(async data => {
      sourceId.value = data.record?.newsId;
      newsTitle.value = data.record?.newsTitle;

      reload({
        searchInfo: {
          sourceId: unref(sourceId),
          statefulFlowType: unref(ifcollect) ? 'collect' : 'like',
        },
      });
    });

    const [registerTable, { reload }] = useTable({
      rowKey: 'autoId',
      columns: columns,
      showIndexColumn: false,
      api: api,
      beforeFetch: info => {
        return { ...info, sourceId: unref(sourceId) };
      },
      immediate: false,
      searchInfo: {
        sourceId: unref(sourceId),
      },
      isCanResizeParent: false,
      maxHeight: 300,
      canResize: true,
      // formConfig: {
      //   labelWidth: 120,
      //   schemas: shareAndLikeForm(),
      //   autoSubmitOnEnter: true,
      //   labelCol: { span: 2 },
      //   wrapperCol: { span: 22 }
      // },
      pagination: true,
      useSearchForm: false,
      bordered: true,
    });

    return {
      dictionary,
      registerModal,
      title,
      registerTable,
    };
  },
});
</script>
