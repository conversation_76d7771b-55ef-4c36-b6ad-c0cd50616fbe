<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
    cancelText="关闭"
    :wrap-class-name="`${$style['video-modal']}`"
  >
    <Tabs
      :activeKey="statisticalKey"
      @change="changeTabs"
    >
      <TabPane
        key="newsClicks"
        tab="阅读量明细"
      ></TabPane>
      <TabPane
        key="likeVolume"
        tab="点赞量明细"
      ></TabPane>
      <TabPane
        key="collectVolume"
        tab="收藏量明细"
      ></TabPane>
      <TabPane
        key="shareVolume"
        tab="转发量明细"
      ></TabPane>
    </Tabs>
    <BasicTable @register="registerTable"></BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { statisticalDetails } from './data';
import { getStatisticalBySourceIdAndKey } from '@/api/news';
import { BasicTable, useTable } from '@/components/Table';
import { Tabs } from 'ant-design-vue';

const TabPane = Tabs.TabPane;

const statisticalKey = ref('newsClicks');

const record = ref<Recordable>();

const sourceId = ref('');

const title = computed(() => {
  return `${unref(record)?.newsTitle || ''}--统计指标明细`;
});

const form = computed(() => {
  let name = '操作时间';
  switch (unref(statisticalKey)) {
    //阅读量明细
    case 'newsClicks':
      name = '阅读时间';
      break;
    //点赞量明细
    case 'likeVolume':
      name = '点赞时间';
      break;
    //收藏量明细
    case 'collectVolume':
      name = '收藏时间';
      break;
    //转发量(分享)明细
    case 'shareVolume':
      name = '转发时间';
      break;
  }
  return statisticalDetails(name);
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: form,
  showIndexColumn: false,
  api: getStatisticalBySourceIdAndKey,
  beforeFetch: info => {
    return { ...info, sourceId: unref(sourceId), key: unref(statisticalKey) };
  },
  immediate: false,
  searchInfo: {
    sourceId: unref(sourceId),
    key: unref(statisticalKey),
  },
  isCanResizeParent: false,
  maxHeight: 300,
  canResize: true,
  // formConfig: {
  //   labelWidth: 120,
  //   schemas: shareAndLikeForm(),
  //   autoSubmitOnEnter: true,
  //   labelCol: { span: 2 },
  //   wrapperCol: { span: 22 }
  // },
  pagination: true,
  useSearchForm: false,
  bordered: true,
});

const [registerModal, {}] = useModalInner(async data => {
  sourceId.value = data.record?.newsId;
  record.value = data.record;
  statisticalKey.value = 'newsClicks';
  reload({
    searchInfo: {
      sourceId: unref(sourceId),
      key: unref(statisticalKey),
    },
  });
});

async function changeTabs(statistical) {
  statisticalKey.value = statistical;
  reload({ page: 1 });
}
</script>
<style lang="less" module>
.video-modal {
  :global {
    .footer-group {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
