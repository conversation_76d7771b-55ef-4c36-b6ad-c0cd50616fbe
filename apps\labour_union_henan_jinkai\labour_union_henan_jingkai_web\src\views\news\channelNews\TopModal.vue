<template>
<!-- :wrapClassName="$style['top-modal']" -->
  <BasicModal
    @register="registerModal"
    @ok="handleTopSuccess"
    title="设置置顶"
    
    v-bind="$attrs"
  >
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { useForm, BasicForm } from '@/components/Form';
import { modalTopFormItem } from './data';
import { useDictionary } from '@/store/modules/dictionary';
import { useModalInner, BasicModal } from '@/components/Modal';
import { RadioGroup, DatePicker } from 'ant-design-vue';

export default defineComponent({
  name: 'TopModal',
  components: { BasicForm, BasicModal, RadioGroup, DatePicker },
  emits: ['register', 'success'],
  setup(_, { emit }) {
    const dictionary = useDictionary();

    const autoId = ref('');

    const [registerForm, { validate, resetFields, setFieldsValue }] = useForm({
      labelWidth: 100,
      schemas: modalTopFormItem(),
      showActionButtonGroup: false,
    });

    const [registerModal, {}] = useModalInner(async data => {
      await resetFields();
      autoId.value = data.autoId || data.record?.autoId;
      if (data.record) {
        setFieldsValue({
          ...data.record,
        });
      }
    });

    async function handleTopSuccess() {
      const values = await validate();
      emit('success', { values, autoId: autoId.value });
    }

    return {
      dictionary,
      handleTopSuccess,
      registerForm,
      registerModal,
    };
  },
});
</script>

<style lang="less" module>
.top-modal {
  :global {
    .ant-form {
      height: 47vh;
    }
  }
}
</style>
