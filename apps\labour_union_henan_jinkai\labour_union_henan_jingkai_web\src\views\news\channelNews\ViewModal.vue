<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :wrap-class-name="` ${$style['view-modal']}`"
    :title="`即时预览${appTitle ? '--' + appTitle : ''}`"
    :show-ok-btn="false"
    cancel-text="关闭"
  >
    <div
      v-if="optionRecord"
      class="!my-5px"
    >
      <Tag color="orange">{{ optionRecord }}</Tag>
    </div>
    <div class="max-h-740px flex">
      <div>
        <div class="w-295px h-580px relative">
          <img :src="modelBg" class="w-full h-full" alt="" />
          <div class="absolute bottom-10px w-full h-full pt-70px pl-5px pr-15px pb-10px flex justify-center rounded-b-20px">
            <iframe :src="previewAddress" frameborder="0" scrolling="auto"
                    class="w-275px h-500px  rounded-b-20px"></iframe>
          </div>
        </div>
      </div>
      <div class=" ml-20px" >
        <a class="cursor-pointer text-blue whitespace-pre-wrap" @click="onClick(previewDescription)" >{{previewDescription}}</a>
        <div class="flex flex-col justify-center items-center">
          <QrCode
              :value="previewAddress"
              logo="/logo.png"
              class="enter-x flex justify-center xl:justify-start"
              :width="280"
          />
          <div class="font-600">（微信扫码预览）</div>
        </div>

      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import {ref, unref} from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { Tag } from 'ant-design-vue';
import modelBg from '@/assets/images/model_bg.png';
import { generatePreviewLink } from '@/api/news';
import { QrCode } from '@/components/Qrcode';
import {useCopyToClipboard, useMessage} from '@monorepo-yysz/hooks';


const { createErrorModal,createMessage } = useMessage();


const optionRecord = ref('');


const appTitle = ref<string>('');


const previewDescription = ref<string>('');

const previewAddress = ref<string>('');

const [registerModal] = useModalInner(async data => {
  //获取预览链接
  generatePreviewLink({ newsId: data?.record?.newsId }).then(res => {
    const { code, data, message: msg } = res;
    if (code === 200) {
      const { previewDescription: description, previewAddress: address,qrCode } = data;
      previewDescription.value = description;
      previewAddress.value = address;
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
});

const onClick = (description : string)=>{
  const { clipboardRef, isSuccessRef } = useCopyToClipboard();
  clipboardRef.value = description;
  if (unref(isSuccessRef)) {
    createMessage.success('复制成功');
  }
}
</script>

<style module lang="less">
.view-modal {
  :global {
  }
}
</style>
