<template>
  <div class="basic-form">
    <a-row class="m-10px">
      <!-- 资讯标题 -->
      <a-col
        :span="3"
        class="justify-center items-center !flex"
      >
        <label>
          <span class="text-red-600">*</span>
          <span>资讯标题：</span>
        </label>
      </a-col>
      <a-col :span="21">
        <a-input
          :value="title"
          @change="e => handleFieldChange('title', e.target.value)"
          :maxlength="80"
          showCount
          placeholder="请输入资讯标题(80个字符内)"
          autocomplete="off"
          class="w-full"
          allowClear
        />
      </a-col>

      <!-- 资讯摘要 -->
      <template v-if="!isUrl">
        <a-col
          :span="3"
          class="justify-center items-center !flex mt-1"
        >
          <label>
            <span
              v-if="abstractRequired"
              class="text-red-600"
              >*</span
            >
            <span class="!ml-5px">资讯摘要：</span>
          </label>
        </a-col>
        <a-col
          :span="21"
          class="mt-1"
        >
          <a-textarea
            :value="abstract"
            @change="e => handleFieldChange('abstract', e.target.value)"
            :maxlength="300"
            showCount
            class="w-full"
            placeholder="请输入资讯摘要(300个字符内)"
            autocomplete="off"
            allowClear
          />
        </a-col>
      </template>

      <!-- 外部链接地址（仅URL模式） -->
      <template v-if="isUrl">
        <a-col
          :span="2"
          class="justify-center items-center !flex"
        >
          <label>
            <span class="text-red-600">*</span>
            <span>链接地址：</span>
          </label>
        </a-col>
        <a-col :span="22">
          <a-textarea
            :value="externalLinkAddress"
            @change="e => handleFieldChange('externalLinkAddress', e.target.value)"
            placeholder="请输入链接地址"
            autocomplete="off"
            allowClear
            :maxlength="400"
            class="w-full"
            showCount
            :auto-size="{ minRows: 2, maxRows: 5 }"
          />
        </a-col>
      </template>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  title?: string;
  abstract?: string;
  externalLinkAddress?: string;
  abstractRequired?: boolean;
  isUrl?: boolean;
}

defineProps<Props>();

const emit = defineEmits<{
  'update:title': [value: string];
  'update:abstract': [value: string];
  'update:externalLinkAddress': [value: string];
  fieldChange: [field: string, value: string];
}>();

const handleFieldChange = (field: string, value: string) => {
  // 更新对应字段
  switch (field) {
    case 'title':
      emit('update:title', value);
      break;
    case 'abstract':
      emit('update:abstract', value);
      break;
    case 'externalLinkAddress':
      emit('update:externalLinkAddress', value);
      break;
  }

  // 通知字段变化
  emit('fieldChange', field, value);
};
</script>

<style scoped>
.basic-form .ant-col {
  display: flex;
  align-items: center;
}
</style>
