<template>
  <div class="content-editor">
    <!-- 编辑器提示 -->
    <a-row>
      <a-col
        :span="24"
        class="relative"
      >
        <a-tooltip placement="top">
          <template #title>
            <p>内容建议格式：字体12pt、两端对齐、首行缩进、行间距2px </p>
            <p>音视频支持文件后缀：mp4，mkv，avi，mp3</p>
            <p>图片支持文件后缀：webp，png，gif，jpg，jpeg</p>
            <p
              >其他支持文件后缀：xls，xlsx，doc，docx，pdf，rar，zip，csv，vtt，dwg，dxf，pptx，ppt</p
            >
            <p>单次上传文件，不能超过300M</p>
          </template>
          <a-tag
            color="#f50"
            class="absolute right-1 top-[-36px]"
          >
            文章格式建议
            <template #icon>
              <ExclamationCircleOutlined />
            </template>
          </a-tag>
        </a-tooltip>

        <!-- 富文本编辑器 -->
        <Tinymce
          v-if="!isUploadFiles"
          :showImageUpload="false"
          :operateType="19"
          :value="content"
          @change="handleContentChange"
        />

        <!-- 文件上传 -->
        <div
          v-else
          class="file-upload m-10px"
        >
          <a-upload
            :file-list="fileList"
            name="file"
            :headers="{ token: uploadConfig.token }"
            :data="{ operateType: 19, bucketName: uploadConfig.bucketName }"
            :action="uploadConfig.url"
            :maxCount="1"
            @change="handleFileChange"
            list-type="picture"
          >
            <a-button type="primary">
              <UploadOutlined />
              文件上传
            </a-button>
          </a-upload>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { UploadOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { Tinymce } from '@/components/Tinymce';

interface Props {
  content?: string;
  fileList?: any[];
  isUploadFiles?: boolean;
  uploadConfig?: {
    url: string;
    token: string;
    bucketName: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  fileList: () => [],
  isUploadFiles: false,
  uploadConfig: () => ({
    url: '',
    token: '',
    bucketName: '',
  }),
});

const emit = defineEmits<{
  'update:content': [value: string];
  'update:fileList': [value: any[]];
  fileChange: [changeInfo: any];
}>();

// 处理内容变化
const handleContentChange = (value: string) => {
  emit('update:content', value);
};

// 处理文件上传变化
const handleFileChange = (changeInfo: any) => {
  emit('update:fileList', changeInfo.fileList);
  emit('fileChange', changeInfo);
};
</script>

<style scoped>
.content-editor {
  position: relative;
}

.file-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  background: #fafafa;
  cursor: pointer;
  transition: border-color 0.3s;
}

.file-upload:hover {
  border-color: #1890ff;
}
</style>
