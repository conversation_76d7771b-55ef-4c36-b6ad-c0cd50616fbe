<template>
  <div class="resource-config">
    <!-- 是否关联资源 -->
    <a-col class="mt-1 flex">
      <a-col
        :span="3"
        class="justify-center items-center !flex"
      >
        <label>是否关联资源：</label>
      </a-col>
      <a-col :span="1">
        <a-switch
          :checked="whetherLinkResources"
          @change="handleLinkChange"
          checked-children="开"
          :checkedValue="true"
          :unCheckedValue="false"
        />
      </a-col>

      <!-- 资源类型选择 -->
      <template v-if="whetherLinkResources">
        <a-col
          :span="3"
          class="justify-center items-center !flex border-l border-[#f0f0f0]"
        >
          <label>
            <span class="text-red-600">*</span>
            <span class="!ml-5px">资源类型：</span>
          </label>
        </a-col>
        <a-col :span="7">
          <a-radio-group
            :value="resourceType"
            @change="handleResourceTypeChange"
            :options="resourceTypeOptions"
          />
        </a-col>

        <!-- 外部资源弹窗提示 -->
        <template v-if="resourceType === 'external'">
          <a-col
            :span="3"
            class="justify-center items-center !flex"
          >
            <label>是否弹窗提示：</label>
          </a-col>
          <a-col :span="1">
            <a-switch
              :checked="detailsWhetherPrompt"
              @change="value => $emit('update:detailsWhetherPrompt', value)"
              checked-children="开"
              :checkedValue="true"
              :unCheckedValue="false"
            />
          </a-col>
        </template>
      </template>
    </a-col>

    <!-- 资源地址配置 -->
    <a-col
      v-if="whetherLinkResources && resourceType"
      class="mt-1 flex"
    >
      <a-col
        :span="3"
        class="justify-center items-center !flex"
      >
        <label>
          <span class="text-red-600">*</span>
          <span class="!ml-5px">
            {{ resourceType === 'internal' ? '关联内部资源' : '外部资源链接' }}：
          </span>
        </label>
      </a-col>
      <a-col :span="21">
        <!-- 外部资源地址 -->
        <a-textarea
          v-if="resourceType === 'external'"
          :value="externalAddress"
          @change="e => $emit('update:externalAddress', e.target.value)"
          placeholder="请输入链接地址"
          autocomplete="off"
          allowClear
          :maxlength="400"
          class="w-full"
          showCount
          :auto-size="{ minRows: 2, maxRows: 5 }"
        />
        <!-- 内部资源选择 -->
        <ResourcesSelect
          class="w-full"
          v-else
          :value="activityName"
          @change="handleInternalResourceChange"
        />
      </a-col>
    </a-col>

    <!-- 外部资源封面图 -->
    <a-col
      v-if="whetherLinkResources && resourceType === 'external'"
      class="mt-1 flex"
    >
      <a-col
        :span="3"
        class="justify-center items-center !flex"
      >
        <label>
          <span class="text-red-600">*</span>
          <span class="!ml-5px">外部资源封面图：</span>
        </label>
      </a-col>
      <a-col :span="10">
        <CropperForm
          :value="externalCoverUrl"
          @change="value => $emit('update:externalCoverUrl', value)"
          :operateType="19"
          :imgSize="690 / 200"
        />
      </a-col>
    </a-col>
  </div>
</template>

<script lang="ts" setup>
import ResourcesSelect from '../ResourcesSelect.vue';
import { CropperForm } from '@/components/Cropper';

interface Props {
  whetherLinkResources?: boolean;
  resourceType?: string;
  externalAddress?: string;
  externalCoverUrl?: string;
  activityName?: string;
  detailsWhetherPrompt?: boolean;
  resourceTypeOptions?: Array<{ label: string; value: string }>;
}

defineProps<Props>();

const emit = defineEmits<{
  'update:whetherLinkResources': [value: boolean];
  'update:resourceType': [value: string];
  'update:externalAddress': [value: string];
  'update:externalCoverUrl': [value: string];
  'update:activityName': [value: string];
  'update:internalBusinessId': [value: string];
  'update:detailsWhetherPrompt': [value: boolean];
}>();

// 处理是否关联资源的变化
const handleLinkChange = (value: boolean) => {
  emit('update:whetherLinkResources', value);

  if (!value) {
    // 清除所有资源相关数据
    emit('update:resourceType', '');
    emit('update:externalAddress', '');
    emit('update:externalCoverUrl', '');
    emit('update:activityName', '');
    emit('update:internalBusinessId', '');
    emit('update:detailsWhetherPrompt', false);
  }
};

// 处理资源类型变化
const handleResourceTypeChange = (e: any) => {
  const newType = e.target.value;
  emit('update:resourceType', newType);

  // 清除之前类型的数据
  if (newType === 'external') {
    emit('update:activityName', '');
    emit('update:internalBusinessId', '');
  } else if (newType === 'internal') {
    emit('update:externalAddress', '');
    emit('update:externalCoverUrl', '');
    emit('update:detailsWhetherPrompt', false);
  }
};

// 处理内部资源选择
const handleInternalResourceChange = (resource: { activityName: string; activityId: string }) => {
  if (resource) {
    emit('update:activityName', resource.activityName);
    emit('update:internalBusinessId', resource.activityId);
  }
};
</script>

<style scoped>
.resource-config .ant-col {
  display: flex;
  align-items: center;
}

.resource-config .ant-radio-group {
  width: 100%;
}

.resource-config .ant-switch {
  margin: 0 8px;
}
</style>
