import { computed, reactive } from 'vue';
import { useGlobSetting } from '@/hooks/setting';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';

/**
 * 内容表单管理 Hook
 * 负责管理新闻内容的基础表单状态和操作
 */
export function useContentForm() {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  const { uploadUrl, bucket_name } = useGlobSetting();

  // 文件上传配置
  const uploadConfig = computed(() => ({
    url: uploadUrl + '/file/minio/uploadFile',
    token: userStore.getToken,
    bucketName: bucket_name,
  }));

  // 资源类型选项
  const newsResourceType = computed(() => dictionary.getDictionaryOpt.get('newsResourceType'));

  // 表单验证规则
  const validationRules = {
    title: {
      required: true,
      maxLength: 80,
      message: '请输入资讯标题(80个字符内)',
    },
    abstract: {
      maxLength: 300,
      message: '请输入资讯摘要(300个字符内)',
    },
    externalAddress: {
      maxLength: 400,
      message: '请输入链接地址(400个字符内)',
    },
  };

  // 表单状态
  const formState = reactive({
    isSubmitting: false,
    hasUnsavedChanges: false,
  });

  /**
   * 验证表单字段
   */
  const validateField = (field: string, value: string): boolean => {
    const rule = validationRules[field];
    if (!rule) return true;

    if (rule.required && !value?.trim()) {
      return false;
    }

    if (rule.maxLength && value?.length > rule.maxLength) {
      return false;
    }

    return true;
  };

  /**
   * 验证整个表单
   */
  const validateForm = (item: any): string[] => {
    const errors: string[] = [];

    if (!validateField('title', item.newsDetailsTitle)) {
      errors.push('资讯标题不能为空');
    }

    if (item.abstractWhether === 'y' && !item.newsDetailsAbstract?.trim()) {
      errors.push('资讯摘要不能为空');
    }

    if (item.whetherLinkResources) {
      if (!item.resourceType) {
        errors.push('请选择资源类型');
      }

      if (item.resourceType === 'external') {
        if (!item.externalAddress?.trim()) {
          errors.push('请输入外部资源链接');
        }
        if (!item.externalCoverUrl) {
          errors.push('请上传外部资源封面图');
        }
      } else if (item.resourceType === 'internal') {
        if (!item.internalBusinessId) {
          errors.push('请选择内部资源');
        }
      }
    }

    return errors;
  };

  /**
   * 标记表单已修改
   */
  const markAsModified = () => {
    formState.hasUnsavedChanges = true;
  };

  /**
   * 重置表单状态
   */
  const resetFormState = () => {
    formState.isSubmitting = false;
    formState.hasUnsavedChanges = false;
  };

  return {
    // 配置
    uploadConfig,
    newsResourceType,
    validationRules,

    // 状态
    formState,

    // 方法
    validateField,
    validateForm,
    markAsModified,
    resetFormState,
  };
}
