import { reactive, ref, unref, watch } from 'vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { newsExtractKeywords } from '@/api/news';

/**
 * 关键词管理 Hook
 * 负责管理新闻关键词的提取、选择、添加和删除
 */
export function useKeywordsManager(props: any, emit: any) {
  const { createMessage } = useMessage();

  // 候选关键字
  const candidateKeywords = ref<string[]>([]);

  // 手动添加的关键字
  const manualKeywords = ref<string[]>([]);

  // 选中的关键字
  const selectedKeywords = ref<string[]>([]);

  // 输入框引用
  const inputRef = ref();

  // 关键词状态
  const keywordState = reactive({
    tags: [] as string[],
    inputVisible: false,
    inputValue: '',
    isExtracting: false,
  });

  // 关键词限制配置
  const KEYWORD_LIMITS = {
    MAX_COUNT: 10,
    MAX_LENGTH: 6,
    EXTRACT_COUNT: 100,
  };

  /**
   * 提取关键词
   */
  const extractKeywords = async (item: any) => {
    if (!item?.newsDetailsContent?.trim()) {
      createMessage.error('提取关键字失败，请先填写内容！');
      return;
    }

    keywordState.isExtracting = true;

    try {
      const res = await newsExtractKeywords({
        content: item.newsDetailsContent,
        returnKeywordsNumber: KEYWORD_LIMITS.EXTRACT_COUNT,
      });

      candidateKeywords.value = res.data || [];

      if (candidateKeywords.value.length === 0) {
        createMessage.warning('未提取到关键词，请检查内容是否有效');
      } else {
        createMessage.success(`成功提取到 ${candidateKeywords.value.length} 个候选关键词`);
      }
    } catch (error) {
      createMessage.error('提取关键词失败，请稍后重试');
      console.error('Extract keywords error:', error);
    } finally {
      keywordState.isExtracting = false;
    }
  };

  /**
   * 处理候选关键词选择变化
   */
  const handleCandidateKeywordsChange = (selectedList: string[]) => {
    selectedKeywords.value = selectedList;

    const currentTags = unref(keywordState.tags);

    if (currentTags.length === 0) {
      keywordState.tags = [...selectedList];
      emitKeywordsChange();
      return;
    }

    // 找出来自候选的关键字
    const candidateTags = currentTags.filter(tag => !unref(manualKeywords).includes(tag));

    // 修改时初始化手动添加的关键字
    if (props.isUpdate && manualKeywords.value.length === 0) {
      manualKeywords.value = currentTags.filter(tag => !selectedList.includes(tag));
    }

    // 处理候选关键词的增减
    if (candidateTags.length > selectedList.length) {
      // 有候选关键词被移除
      const removedTags = candidateTags.filter(tag => !selectedList.includes(tag));
      keywordState.tags = currentTags.filter(tag => !removedTags.includes(tag));
    } else {
      // 有新的候选关键词被添加
      const newTags = selectedList.filter(tag => !candidateTags.includes(tag));
      keywordState.tags = [...currentTags, ...newTags];
    }

    emitKeywordsChange();
  };

  /**
   * 删除关键词
   */
  const removeKeyword = (tagToRemove: string) => {
    // 从手动添加列表中移除
    manualKeywords.value = manualKeywords.value.filter(tag => tag !== tagToRemove);

    // 从标签列表中移除
    keywordState.tags = keywordState.tags.filter(tag => tag !== tagToRemove);

    // 从选中列表中移除
    selectedKeywords.value = selectedKeywords.value.filter(tag => tag !== tagToRemove);

    emitKeywordsChange();
  };

  /**
   * 显示输入框
   */
  const showKeywordInput = () => {
    if (keywordState.tags.length >= KEYWORD_LIMITS.MAX_COUNT) {
      createMessage.warning(`最多只能添加 ${KEYWORD_LIMITS.MAX_COUNT} 个关键词`);
      return;
    }

    keywordState.inputVisible = true;

    // 下一帧聚焦输入框
    setTimeout(() => {
      inputRef.value?.focus();
    });
  };

  /**
   * 确认输入关键词
   */
  const confirmKeywordInput = () => {
    const inputValue = keywordState.inputValue.trim();

    if (!inputValue) {
      keywordState.inputVisible = false;
      keywordState.inputValue = '';
      return;
    }

    // 验证关键词长度
    if (inputValue.length > KEYWORD_LIMITS.MAX_LENGTH) {
      createMessage.error(
        `关键词不能超过 ${KEYWORD_LIMITS.MAX_LENGTH} 个字符，当前 ${inputValue.length} 个字符`
      );
      return;
    }

    // 验证关键词数量
    if (keywordState.tags.length >= KEYWORD_LIMITS.MAX_COUNT) {
      createMessage.error(`最多只能添加 ${KEYWORD_LIMITS.MAX_COUNT} 个关键词`);
      return;
    }

    // 检查是否重复
    if (keywordState.tags.includes(inputValue)) {
      createMessage.warning('关键词已存在');
      keywordState.inputValue = '';
      return;
    }

    // 添加关键词
    manualKeywords.value.push(inputValue);
    keywordState.tags = [...keywordState.tags, inputValue];

    // 重置输入状态
    keywordState.inputVisible = false;
    keywordState.inputValue = '';

    emitKeywordsChange();
  };

  /**
   * 取消输入
   */
  const cancelKeywordInput = () => {
    keywordState.inputVisible = false;
    keywordState.inputValue = '';
  };

  /**
   * 获取关键词提示信息
   */
  const getKeywordTip = () => {
    return `资讯关键词用于工会app推荐新闻，最多只能有${KEYWORD_LIMITS.MAX_COUNT}个关键词，每个关键词最多${KEYWORD_LIMITS.MAX_LENGTH}个字符，当前已有${keywordState.tags.length}个关键词！`;
  };

  /**
   * 发送关键词变化事件
   */
  const emitKeywordsChange = () => {
    emit('getTags', keywordState.tags);
  };

  /**
   * 初始化关键词
   */
  const initializeKeywords = (keywords: string) => {
    const keywordList = keywords ? keywords.split(',').filter(Boolean) : [];
    keywordState.tags = [...keywordList];
    selectedKeywords.value = [...keywordList];
  };

  /**
   * 重置关键词状态
   */
  const resetKeywords = () => {
    candidateKeywords.value = [];
    manualKeywords.value = [];
    selectedKeywords.value = [];
    keywordState.tags = [];
    keywordState.inputVisible = false;
    keywordState.inputValue = '';
    keywordState.isExtracting = false;
  };

  // 监听记录变化，初始化关键词
  watch(
    () => props.record?.keywords,
    keywords => {
      if (keywords) {
        initializeKeywords(keywords);
      } else {
        resetKeywords();
      }
    },
    { deep: true, immediate: true }
  );

  return {
    // 状态
    candidateKeywords,
    manualKeywords,
    selectedKeywords,
    keywordState,
    inputRef,

    // 配置
    KEYWORD_LIMITS,

    // 方法
    extractKeywords,
    handleCandidateKeywordsChange,
    removeKeyword,
    showKeywordInput,
    confirmKeywordInput,
    cancelKeywordInput,
    getKeywordTip,
    initializeKeywords,
    resetKeywords,
  };
}
