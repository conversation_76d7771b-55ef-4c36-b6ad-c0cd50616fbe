import { useMessage } from '@monorepo-yysz/hooks';

/**
 * 资源管理 Hook
 * 负责管理内部和外部资源的相关操作
 */
export function useResourceManager() {
  const { createMessage } = useMessage();

  /**
   * 处理文件上传变化
   */
  const handleFileChange = ({ file }, item: any) => {
    if (!file) return;

    // 更新文件名
    item.fileName = file.name;

    // 更新文件地址
    if (file.response?.data?.[0]) {
      item.fileAddress = file.response.data[0];
      createMessage.success('文件上传成功');
    } else if (file.status === 'error') {
      createMessage.error('文件上传失败，请重试');
    }
  };

  /**
   * 处理内部资源选择
   */
  const handleInternalResource = (
    resource: { activityName: string; activityId: string },
    newsItem: any
  ) => {
    if (!resource || !newsItem) {
      createMessage.error('资源信息不完整');
      return;
    }

    newsItem.activityName = resource.activityName;
    newsItem.internalBusinessId = resource.activityId;

    createMessage.success('内部资源关联成功');
  };

  /**
   * 验证外部资源链接
   */
  const validateExternalUrl = (url: string): boolean => {
    if (!url?.trim()) return false;

    // 简单的URL格式验证
    try {
      const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
      return urlPattern.test(url.trim());
    } catch {
      return false;
    }
  };

  /**
   * 处理资源类型切换
   */
  const handleResourceTypeChange = (resourceType: string, item: any) => {
    // 清除之前的资源数据
    if (resourceType === 'external') {
      // 切换到外部资源，清除内部资源数据
      item.activityName = '';
      item.internalBusinessId = '';
    } else if (resourceType === 'internal') {
      // 切换到内部资源，清除外部资源数据
      item.externalAddress = '';
      item.externalCoverUrl = '';
      item.detailsWhetherPrompt = false;
    }
  };

  /**
   * 处理是否关联资源的切换
   */
  const handleLinkResourcesChange = (whetherLink: boolean, item: any) => {
    if (!whetherLink) {
      // 不关联资源时，清除所有资源相关数据
      item.resourceType = '';
      item.externalAddress = '';
      item.externalCoverUrl = '';
      item.activityName = '';
      item.internalBusinessId = '';
      item.detailsWhetherPrompt = false;
    }
  };

  /**
   * 获取资源配置
   */
  const getResourceConfig = () => {
    return {
      external: {
        label: '外部资源',
        fields: ['externalAddress', 'externalCoverUrl', 'detailsWhetherPrompt'],
        validation: {
          externalAddress: { required: true, validator: validateExternalUrl },
          externalCoverUrl: { required: true },
        },
      },
      internal: {
        label: '内部资源',
        fields: ['activityName', 'internalBusinessId'],
        validation: {
          internalBusinessId: { required: true },
        },
      },
    };
  };

  // 类型定义
  interface ValidationRule {
    required?: boolean;
    validator?: (value: any) => boolean;
  }

  /**
   * 验证资源配置
   */
  const validateResourceConfig = (item: any): string[] => {
    const errors: string[] = [];

    if (!item.whetherLinkResources) {
      return errors; // 不关联资源时无需验证
    }

    if (!item.resourceType) {
      errors.push('请选择资源类型');
      return errors;
    }

    const config = getResourceConfig();
    const resourceConfig = config[item.resourceType];

    if (!resourceConfig) {
      errors.push('无效的资源类型');
      return errors;
    }

    // 验证必填字段
    for (const [field, validation] of Object.entries(resourceConfig.validation)) {
      const value = item[field];
      const rule = validation as ValidationRule;

      if (rule.required && !value) {
        errors.push(`${resourceConfig.label}配置不完整`);
        break;
      }

      if (rule.validator && value && !rule.validator(value)) {
        errors.push(`${resourceConfig.label}格式不正确`);
        break;
      }
    }

    return errors;
  };

  return {
    // 方法
    handleFileChange,
    handleInternalResource,
    validateExternalUrl,
    handleResourceTypeChange,
    handleLinkResourcesChange,
    getResourceConfig,
    validateResourceConfig,
  };
}
