import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { Tooltip } from 'ant-design-vue';
// import { map } from 'lodash-es';
// import { getUnionTree } from '@/api/category';
import dayjs from 'dayjs';
import { useInfos } from '@/store/modules/infos';
import { uploadApi } from '@/api/sys/upload';
import { getReferToNews } from '@/api/news';
// import { useMessage } from '@monorepo-yysz/hooks';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
// import { cloneDeep, filter } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';
// import { list as newsReleaseUnionlList } from '@/api/category/newsReleaseUnion';

const dictionary = useDictionary();

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const infos = useInfos();

  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '标题',
      dataIndex: 'newsTitle',
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },

    {
      title: '统计指标(次)',
      width: 120,
      dataIndex: 'newsClicks',
      customRender: ({ record, text }) => {
        return (
          <Tooltip
            title={
              <div>
                <div style={{ textAlign: 'left' }}>{'阅读量: ' + text}</div>
                <div style={{ textAlign: 'left' }}>{'点赞量: ' + record?.likeVolume}</div>
                <div style={{ textAlign: 'left' }}>{'收藏量: ' + record?.collectVolume}</div>
                <div style={{ textAlign: 'left' }}>{'转发量: ' + record?.shareVolume}</div>
              </div>
            }
          >
            {' '}
            <div>
              <div style={{ textAlign: 'left' }}>{'阅读量: ' + text}</div>
              <div style={{ textAlign: 'left' }}>{'点赞量: ' + record?.likeVolume}</div>
              {/* <div style={{ textAlign: 'left' }}>{'收藏量: ' + record?.collectVolume}</div> */}
              {/* <div style={{ textAlign: 'left' }}>{'转发量: ' + record?.shareVolume}</div> */}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '所属工会',
      width: 120,
      dataIndex: 'companyName',
    },
    {
      title: '所属栏目',
      width: 120,
      dataIndex: 'categoryName',
    },
    // {
    //   title: '所属平台',
    //   dataIndex: 'platformType',
    //   width: 140,
    //   customRender: ({ text }) => {
    //     const textArr = text.split(',');
    //     const all = map(
    //       textArr,
    //       v => dictionary.getDictionaryMap.get(`appType_${v}`)?.dictName
    //     )?.join(',');
    //     return <Tooltip title={all}>{all}</Tooltip>;
    //   },
    // },
    {
      title: '审核状态',
      dataIndex: 'newsAuditStatus',
      width: 80,
      customRender: ({ text, record }) => {
        const newsAuditStatus = dictionary.getDictionaryMap.get(
          `newsAuditStatus_${text}`
        )?.dictName;
        const { whetherAuditRecords } = record;
        if (whetherAuditRecords) {
          async function handleClick() {
            infos.setRecord(record);
            infos.setVisible(true);
          }
          return (
            <a
              title={newsAuditStatus}
              onClick={() => handleClick()}
            >
              <span>{newsAuditStatus}</span>
            </a>
          );
        } else {
          return <Tooltip title={newsAuditStatus}>{newsAuditStatus}</Tooltip>;
        }
      },
    },
    {
      title: '发布状态',
      dataIndex: 'newsPublishStatus',
      width: 80,
      customRender: ({ text }) => {
        const dictName = dictionary.getDictionaryMap.get(
          `latestNewsPublishStatus_${text}`
        )?.dictName;
        return <Tooltip title={dictName}>{dictName}</Tooltip>;
      },
    },
    // {
    //   title: '是否数字播报',
    //   dataIndex: 'whetherBroadcast',
    //   width: 120,
    //   customRender: ({ text }) => {
    //     const dictName = dictionary.getDictionaryMap.get(`YesOrNo_${text}`)?.dictName;
    //     return <Tooltip title={dictName}>{dictName}</Tooltip>;
    //   },
    // },
    // {
    //   title: '创建时间',
    //   dataIndex: 'createTime',
    //   width: 150,
    // },
    {
      title: '发布时间',
      dataIndex: 'publishTime',
      width: 150,
      customRender: ({ record, text }) => {
        if ('10' === record.newsPublishStatus) {
          return <span>{text}</span>;
        } else {
          return <span>{'--'}</span>;
        }
      },
    },
    // {
    //   title: '置顶状态',
    //   dataIndex: 'topOption',
    //   width: 140,
    //   customRender: ({ record, text }) => {
    //     if ('publishAndTop' === record.categorySortOrder) {
    //       let dictName = dictionary.getDictionaryMap.get(`topOption_${text}`)?.dictName;
    //       if (text === '10' && record.topEndTime) {
    //         let dictName1 = dictName + ':' + record.topEndTime;
    //         return (
    //           <Tooltip title={dictName1}>
    //             <div style={{ textAlign: 'left' }}>{dictName + ':'}</div>
    //             <div style={{ textAlign: 'left', whiteSpace: 'nowrap' }}>{record.topEndTime}</div>
    //           </Tooltip>
    //         );
    //       }
    //       return <Tooltip title={dictName}>{dictName}</Tooltip>;
    //     } else {
    //       return <span>{'--'}</span>;
    //     }
    //   },
    // },
    // {
    //   title: '排序号',
    //   dataIndex: 'sortNumber',
    //   width: 80,
    //   customRender: ({ record, text }) => {
    //     if ('sortNumber' === record.categorySortOrder) {
    //       return <span>{text}</span>;
    //     } else {
    //       return <span>{'--'}</span>;
    //     }
    //   },
    // },
  ];
};

export const formSchemas = (ifShow: boolean = true): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'newsTitle',
      label: '新闻标题',
      colProps: { span: 8 },
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入新闻标题',
      },
    },
    {
      field: 'categoryId',
      label: '新闻栏目',
      component: 'TreeSelect',
      colProps: { span: 8 },
      // componentProps: {
      //   api: getUnionTree,
      //   fieldNames: { label: 'categoryName', value: 'categoryId', children: 'children' },
      //   placeholder: '请选择新闻栏目',
      //   showSearch: true,
      //   treeNodeFilterProp: 'categoryName',
      // },
      slot: 'categoryId',
    },
    {
      field: 'pushDateRange',
      label: '发布时间',
      component: 'RangePicker',
      colProps: { span: 8 },
      componentProps: {
        showTime: {
          hideDisabledOptions: true,
          defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
        },
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DDTHH:mm:ss',
      },
    },
    // {
    //   field: 'platformType',
    //   label: '所属平台',
    //   component: 'Select',
    //   colProps: { span: 4 },
    //   componentProps: function () {
    //     return {
    //       options: dictionary.getDictionaryOpt.get('appType'),
    //       placeholder: '请选择所属平台',
    //     };
    //   },
    // },
    {
      label: '审核状态',
      field: 'newsAuditStatus',
      colProps: { span: 4 },
      component: 'Select',
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsAuditStatus'),
          placeholder: '请选择审核状态',
        };
      },
    },
    {
      label: '发布状态',
      field: 'newsPublishStatus',
      colProps: { span: 4 },
      component: 'Select',
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('latestNewsPublishStatus'),
          placeholder: '请选择发布状态',
        };
      },
    },
    {
      field: 'topOption',
      label: '置顶状态',
      component: 'Select',
      colProps: { span: 4 },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('topOption'),
      },
    },
    // {
    //   field: 'whetherBroadcast',
    //   label: '是否数字播报',
    //   component: 'Select',
    //   colProps: { span: 5 },
    //   rulesMessageJoinLabel: true,
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get('YesOrNo'),
    //   },
    // },
    // {
    //   field: 'queryCompanyId',
    //   label: '下级工会',
    //   colProps: { span: 4 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   ifShow: userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5',
    //   componentProps: function () {
    //     return {
    //       options: filter(
    //         cloneDeep(dictionary.getDictionaryOpt.get(`unionsInfo`)),
    //         v => v.value !== '6650f8e054af46e7a415be50597a99d5'
    //       ),
    //     };
    //   },
    // },
    {
      field: 'queryCompanyId',
      label: '所属工会',
      component: 'ApiSelect',
      colProps: { span: 4 },
      ifShow: ifShow,
      rulesMessageJoinLabel: true,
      // componentProps: ({}) => {
      //   return {
      //     api: newsReleaseUnionlList,
      //     params: {
      //       pageSize: 0,
      //     },
      //     resultField: 'data',
      //     immediate: true,
      //     showSearch: true,
      //     filterOption: (input: string, option: any) => {
      //       return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      //     },
      //     fieldNames: { label: 'companyName', value: 'companyId' },
      //   };
      // },
      slot: 'queryCompanyId',
    },
    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: { span: 3 },
      defaultValue: false,
      slot: 'nextLevelFlag',
      ifShow: ifShow,
    },
  ];
};

export const modalFormSchema = (
  categorySortOrder: string,
  categoryType: string,
  categoryPlatformType
): FormSchema[] => {
  return [
    {
      field: 'whetherExternalLink',
      label: '外部链接',
      component: 'Switch',
      defaultValue: false,
      colProps: {
        span: 12,
      },
      slot: 'outsideurl',
    },
    {
      field: 'whetherPrompt',
      label: '是否弹窗提示',
      component: 'Switch',
      defaultValue: false,
      show: false,
      ifShow: ({ values }) => values.whetherExternalLink,
      colProps: {
        span: 12,
      },
      componentProps: {
        checkedValue: true,
        checkedChildren: '开',
      },
    },
    {
      field: 'newsIsopenComment',
      label: '开启评论',
      component: 'Switch',
      colProps: {
        span: 12,
      },
      //目前外链都是在项目内打开，可以设置是否开启评论
      // ifShow({ values }) {
      //   return !values.whetherExternalLink;
      // },
      defaultValue: false,
      componentProps: {
        checkedValue: true,
        checkedChildren: '开',
      },
    },
    {
      field: 'newsTopOption',
      label: '加热',
      component: 'Switch',
      colProps: {
        span: 12,
      },
      ifShow: false,
      defaultValue: false,
      componentProps: {
        checkedValue: true,
        checkedChildren: '开',
      },
    },
    {
      field: 'topOption',
      label: '置顶',
      component: 'RadioGroup',
      defaultValue: '-1',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('topOption') as RadioGroupChildOption[],
      },
      ifShow: 'publishAndTop' === categorySortOrder,
    },
    {
      field: 'topEndTime',
      label: '截止时间',
      component: 'DatePicker',
      required: true,
      componentProps: {
        valueFormat: `YYYY-MM-DD HH:mm:ss`,
        format: `YYYY-MM-DD HH:mm:ss`,
        showTime: true,
        placeholder: '请选择时间',
      },
      ifShow({ model }) {
        return model.topOption === '10' && 'publishAndTop' === categorySortOrder;
      },
    },
    {
      field: 'sortNumber',
      label: '排序号',
      required: true,
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 1,
      },
      ifShow: 'sortNumber' === categorySortOrder,
    },
    {
      field: 'newsType',
      label: '新闻类型',
      component: 'RadioGroup',
      defaultValue: 'text',
      required: true,
      ifShow: 'universal' === categoryType && !!categoryPlatformType,
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('newsType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'publishTime',
      label: '发布时间',
      component: 'DatePicker',
      componentProps: {
        valueFormat: `YYYY-MM-DD HH:mm:ss`,
        format: `YYYY-MM-DD HH:mm`,
        showTime: true,
      },
      required: true,
    },
    {
      field: 'aiBroadcastType',
      label: '数字播报类型',
      component: 'RadioGroup',
      required: true,
      rulesMessageJoinLabel: true,
      defaultValue: 'notHave',
      dynamicDisabled: true,
      ifShow({ values }) {
        return (
          'universal' === categoryType &&
          !!categoryPlatformType?.includes('30') &&
          !values.whetherExternalLink
        );
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('aiBroadcastType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'dataMode',
      label: '自动生成类型',
      component: 'RadioGroup',
      required: true,
      labelWidth: 130,
      defaultValue: 'voice',
      ifShow({ values }) {
        return (
          'universal' === categoryType &&
          !!categoryPlatformType?.includes('30') &&
          'automatically' === values.aiBroadcastType &&
          !values.whetherExternalLink
        );
      },
      helpMessage: ({ values }) =>
        values.dataMode === 'voice'
          ? `请将新闻正文内容控制在10000个字符(文字+符号)以内,系统将自动生成语音播报文件,耗时任务,app端将延迟展示语音播报功能!`
          : `请将新闻正文内容控制在10000个字符(文字+符号)以内,系统将自动生成视频播报文件,耗时任务,app端将延迟展示视频播报功能`,
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('automaticallyType') as RadioGroupChildOption[],
        };
      },
    },
    {
      field: 'dataMode',
      label: '手动录入类型',
      component: 'RadioGroup',
      required: true,
      ifShow({ values }) {
        return (
          'universal' === categoryType &&
          !!categoryPlatformType?.includes('30') &&
          'manualEntry' === values.aiBroadcastType &&
          !values.whetherExternalLink
        );
      },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('manualEntryType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'aiVoiceAddress',
      label: '数字播报音频',
      required: true,
      rulesMessageJoinLabel: true,
      component: 'Upload',
      ifShow({ values }) {
        return (
          'universal' === categoryType &&
          !!categoryPlatformType?.includes('30') &&
          'manualEntry' === values.aiBroadcastType &&
          'uploadAudio' === values.dataMode &&
          !values.whetherExternalLink
        );
      },
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 19,
        },
        maxNumber: 1,
        maxSize: 100,
        accept: ['audio/*'],
      },
    },
    {
      field: 'aiVideoAddress',
      label: '数字播报视频',
      required: true,
      rulesMessageJoinLabel: true,
      component: 'Upload',
      ifShow({ values }) {
        return (
          'universal' === categoryType &&
          !!categoryPlatformType?.includes('30') &&
          'manualEntry' === values.aiBroadcastType &&
          'uploadVideo' === values.dataMode &&
          !values.whetherExternalLink
        );
      },
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 19,
        },
        maxNumber: 1,
        maxSize: 100,
        accept: ['video/*'],
      },
    },
    {
      field: 'aiLinkAddress',
      label: '数字播报链接',
      component: 'InputTextArea',
      required: true,
      ifShow({ values }) {
        return (
          'universal' === categoryType &&
          !!categoryPlatformType?.includes('30') &&
          'manualEntry' === values.aiBroadcastType &&
          'inputLink' === values.dataMode &&
          !values.whetherExternalLink
        );
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入数字播报链接',
        showCount: true,
        maxlength: 400,
      },
    },
    {
      field: 'newsSource',
      label: '来源',
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入来源',
        maxlength: 16,
        showCount: true,
      },
    },
  ];
};

//设置置顶弹窗
export const modalTopFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'topOption',
      label: '置顶',
      component: 'RadioGroup',
      defaultValue: 'NOT_TOP',
      componentProps: function ({ formModel }) {
        return {
          options: dictionary.getDictionaryOpt.get('topOption') as RadioGroupChildOption[],
          onChange: e => {
            if ('TOP_TIME' === e) {
              formModel['topEndTime'] = undefined;
            }
          },
        };
      },
    },
    {
      field: 'topEndTime',
      label: '截止日期',
      component: 'DatePicker',
      required: true,
      componentProps: {
        valueFormat: `YYYY-MM-DD HH:mm:ss`,
        format: `YYYY-MM-DD HH:mm:ss`,
        showTime: true,
        placeholder: '请选择截止日期',
      },
      ifShow({ model }) {
        return model.topOption === 'TOP_TIME';
      },
    },
  ];
};
//设置排序弹窗
export const modalSortFormItem = (autoId): FormSchema[] => {
  return [
    {
      field: 'newsTitle',
      label: '当前新闻标题',
      component: 'ShowSpan',
      colProps: { span: 24 },
    },
    {
      field: 'referToAutoId',
      label: '参照新闻标题',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      itemProps: {
        autoLink: false,
      },
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          api: getReferToNews,
          params: {
            autoId: autoId,
          },
          alwaysLoad: true,
          immediate: true,
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.newsTitle.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'newsTitle', value: 'autoId' },
        };
      },
    },
    {
      field: 'newsSequentialOptions',
      label: '顺序选项',
      component: 'RadioGroup',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: [
          { label: '之前', value: 'before' },
          { label: '之后', value: 'after' },
        ],
      },
    },
  ];
};

export const shareAndLike = (flg?: boolean): BasicColumn[] => {
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '昵称',
      dataIndex: 'nickName',
    },
    {
      title: flg ? '收藏时间' : '点赞时间',
      dataIndex: 'updateTime',
    },
  ];
};

//统计指标明细使用
export const statisticalDetails = (title?: string): BasicColumn[] => {
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '昵称',
      dataIndex: 'nickName',
    },
    {
      title: title,
      dataIndex: 'updateTime',
      customRender: ({ record, text }) => {
        return <span>{text ? text : record.createTime}</span>;
      },
    },
  ];
};

//现在查询接口不支持搜索名称
export const shareAndLikeForm = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '昵称',
      component: 'Input',
    },
  ];
};

// export const activitiesColumns = (): BasicColumn[] => {
//   return [
//     {
//       title: '活动名称',
//       dataIndex: 'activityName',
//       width: 160,
//       ellipsis: true,
//     },
//   ];
// };

export const activitiesColumns = (externalLink): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '活动名称',
      dataIndex: 'activityName',
      ellipsis: true,
    },
    {
      title: '活动类型',
      dataIndex: 'activityMode',
      width: 200,
      customRender: ({ text }) => {
        const dictName = dictionary.getDictionaryMap.get(`activityMode_${text}`)?.dictName;
        return <Tooltip title={dictName}>{dictName}</Tooltip>;
      },
    },
    {
      title: '发布状态',
      dataIndex: 'state',
      width: 100,
      customRender: ({ record }) => {
        if (record.auditState && record.auditState != 'pass') {
          return '-';
        }
        const dictionary = useDictionary();
        const name = dictionary.getDictionaryMap.get(`activityState_${record.state}`)?.dictName;
        return <span title={name}>{name}</span>;
      },
      ifShow: !externalLink,
    },
    {
      title: '是否外链',
      dataIndex: 'externalLink',
      width: 100,
      customRender: ({ record }) => {
        const dictionary = useDictionary();
        const name = dictionary.getDictionaryMap.get(`YesOrNo_${record.externalLink}`)?.dictName;
        return <span title={name}>{name}</span>;
      },
      ifShow: !externalLink,
    },
  ];
};

export const activitiesFormSchema = (externalLink): FormSchema[] => {
  return [
    {
      field: 'activityName',
      label: '活动名称',
      component: 'Input',
      colProps: { span: 8 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'activityMode',
      label: '活动类型',
      component: 'Select',
      colProps: { span: 8 },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('activityMode'),
      },
    },
    {
      field: 'state',
      label: '发布状态',
      component: 'Select',
      colProps: { span: 8 },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('activityState'),
      },
      ifShow: !externalLink,
    },
    {
      field: 'externalLink',
      label: '是否外链',
      component: 'Select',
      colProps: { span: 8 },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo'),
      },
      ifShow: !externalLink,
    },
    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: { span: 4 },
      defaultValue: true,
    },
  ];
};
export const reviewColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '评论内容',
      dataIndex: 'content',
      ellipsis: true,
      customRender: ({ text }) => {
        return <span title={text}>{text}</span>;
      },
    },
    {
      title: '评论人',
      dataIndex: 'createUser',
      width: 170,
    },
    {
      title: '评论时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      width: 100,
      customRender: ({ text }) => {
        return (
          <span
            class={`${text === 'pass' ? 'text-green-500' : text === 'refuse' ? 'text-red-500' : ''}`}
          >
            {dictionary.getDictionaryMap.get(`newsCommentAuditState_${text}`)?.dictName || ''}
          </span>
        );
      },
    },
    {
      title: '审核人',
      dataIndex: 'auditUser',
      width: 170,
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
      width: 150,
    },
  ];
};
export const reviewformSchemas = (): FormSchema[] => {
  return [
    {
      field: 'content',
      label: '评论内容关键字',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
      },
    },
    {
      field: 'createTimeRange',
      label: '评论时间',
      component: 'RangePicker',
      colProps: { span: 11 },
      rulesMessageJoinLabel: true,
      componentProps: {
        showTime: {
          hideDisabledOptions: true,
          defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
        },
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DDTHH:mm:ss',
      },
    },
  ];
};

export const authColumn = ['/channelNews/add'];
export const recordColumn = [
  '/channelNews/modify',
  '/channelNews/publish',
  '/channelNews/revoke',
  '/channelNews/delete',
  '/channelNews/hot',
  '/channelNews/top',
  '/channelNews/like',
  '/channelNews/share',
  '/channelNews/view',
  '/channelNews/statisticalDetails',
  '/channelNews/broadcastManage',
  '/channelNews/reviewsDetails',
];
