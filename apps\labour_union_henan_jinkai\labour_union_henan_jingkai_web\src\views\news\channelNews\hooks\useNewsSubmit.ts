import { unref } from 'vue';
import { sensitiveCheck } from '@/api/sensitive/check';
import { replaceHtml } from '@monorepo-yysz/utils';
import { isString, join, flatMap, map } from 'lodash-es';
import { useNewsSubmission } from '../components';

/**
 * 新闻提交管理 Hook
 */
export function useNewsSubmit() {
  const { processCoverImages } = useNewsSubmission();

  /**
   * 构建提交参数
   */
  const buildSubmitParams = async (
    validate: Function,
    tabsPane: any,
    categoryId: any,
    autoId: any,
    record: any
  ) => {
    const { publishTime, ...values } = await validate();

    if (!isString(publishTime)) {
      values.publishTime = publishTime?.format('YYYY-MM-DD HH:mm:ss') || '';
    }

    return {
      publishTime,
      ...values,
      newsDetailsList: unref(tabsPane),
      categoryId: unref(categoryId).join(','),
      autoId: unref(autoId),
      keywords: join(flatMap(map(unref(tabsPane) || [], v => v.tags)), ','),
      newsGoodId: unref(record)?.newsGoodId,
    };
  };

  /**
   * 执行敏感词检查
   */
  const performSensitiveCheck = async (params: Recordable, userStore: any) => {
    const { newsDetailsList } = params;
    const content = replaceHtml(newsDetailsList?.map(t => t.newsDetailsContent)?.join('') || '');
    const imageUrls =
      newsDetailsList
        ?.filter(t => t.newsCoverUrl)
        .flatMap(t => t.newsCoverUrl.split(','))
        .map(t => userStore.getPrefix + t) || [];

    const { code, data } = await sensitiveCheck({ code: 'checkNews', content, imageUrls });

    return {
      needsReview: code === 200 && data?.matchAny,
      sensitiveData: data,
    };
  };

  /**
   * 处理最终提交
   */
  const handleFinalSubmit = (params: Recordable, emit: Function, isUpdate: any) => {
    const { aiVoiceAddress, aiVideoAddress } = params;
    params.newsDetailsList = processCoverImages(params.newsDetailsList);

    emit('success', {
      params: {
        ...params,
        aiVoiceAddress: aiVoiceAddress && aiVoiceAddress.length > 0 ? aiVoiceAddress.join(',') : '',
        aiVideoAddress: aiVideoAddress && aiVideoAddress.length > 0 ? aiVideoAddress.join(',') : '',
      },
      isUpdate: unref(isUpdate),
    });
  };

  /**
   * 提交审核处理
   */
  const handleSubmitAudit = async (
    addType: any,
    validate: Function,
    tabsPane: any,
    categoryId: any,
    autoId: any,
    record: any,
    ifFilter: any,
    userStore: any,
    openSensitive: Function,
    emit: Function,
    isUpdate: any
  ) => {
    addType.value = 'submitAudit';
    const params = {
      ...(await buildSubmitParams(validate, tabsPane, categoryId, autoId, record)),
      addType: unref(addType),
    };

    if (unref(ifFilter)) {
      const { needsReview, sensitiveData } = await performSensitiveCheck(params, userStore);
      if (needsReview) {
        openSensitive(true, { data: sensitiveData });
      } else {
        handleFinalSubmit(params, emit, isUpdate);
      }
    } else {
      handleFinalSubmit(params, emit, isUpdate);
    }
  };

  /**
   * 保存草稿处理
   */
  const handleSaveDraft = async (
    addType: any,
    validate: Function,
    tabsPane: any,
    categoryId: any,
    autoId: any,
    record: any,
    ifFilter: any,
    userStore: any,
    openSensitive: Function,
    emit: Function,
    isUpdate: any
  ) => {
    addType.value = 'saveDraft';
    const params = {
      ...(await buildSubmitParams(validate, tabsPane, categoryId, autoId, record)),
      addType: unref(addType),
    };

    if (unref(ifFilter)) {
      const { needsReview, sensitiveData } = await performSensitiveCheck(params, userStore);
      if (needsReview) {
        openSensitive(true, { data: sensitiveData });
      } else {
        handleFinalSubmit(params, emit, isUpdate);
      }
    } else {
      handleFinalSubmit(params, emit, isUpdate);
    }
  };

  /**
   * 即时发布处理
   */
  const handleTimelyPublish = async (
    addType: any,
    validate: Function,
    tabsPane: any,
    categoryId: any,
    autoId: any,
    record: any,
    ifFilter: any,
    userStore: any,
    openSensitive: Function,
    emit: Function,
    isUpdate: any
  ) => {
    addType.value = 'timelyPublish';
    const params = {
      ...(await buildSubmitParams(validate, tabsPane, categoryId, autoId, record)),
      addType: unref(addType),
    };

    if (unref(ifFilter)) {
      const { needsReview, sensitiveData } = await performSensitiveCheck(params, userStore);
      if (needsReview) {
        openSensitive(true, { data: sensitiveData });
      } else {
        handleFinalSubmit(params, emit, isUpdate);
      }
    } else {
      handleFinalSubmit(params, emit, isUpdate);
    }
  };

  /**
   * 预览处理
   */
  const handlePreview = async (
    validate: Function,
    tabsPane: any,
    categoryId: any,
    autoId: any,
    record: any,
    emit: Function,
    isUpdate: any
  ) => {
    const params = await buildSubmitParams(validate, tabsPane, categoryId, autoId, record);
    if (params) {
      emit('view', {
        params: { ...params, newsClicks: unref(record)?.newsClicks },
        isUpdate: unref(isUpdate),
      });
    }
  };

  /**
   * 敏感词检查完成后的处理
   */
  const handleSensitiveCheckComplete = (
    filter: boolean,
    ifFilter: any,
    addType: any,
    handleTimelyPublish: Function,
    handleSubmitAudit: Function,
    handleSaveDraft: Function
  ) => {
    ifFilter.value = filter;

    const actionMap = {
      timelyPublish: handleTimelyPublish,
      submitAudit: handleSubmitAudit,
      saveDraft: handleSaveDraft,
    };

    const action = actionMap[unref(addType)];
    if (action) {
      action();
    }
  };

  return {
    buildSubmitParams,
    performSensitiveCheck,
    handleFinalSubmit,
    handleSubmitAudit,
    handleSaveDraft,
    handleTimelyPublish,
    handlePreview,
    handleSensitiveCheckComplete,
  };
}
