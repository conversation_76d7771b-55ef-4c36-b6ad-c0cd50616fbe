import { unref } from 'vue';
import { filter } from 'lodash-es';
import { countByCategoryId } from '@/api/news';
import { createVNode } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useMessage } from '@monorepo-yysz/hooks';

/**
 * 树形栏目操作管理 Hook
 */
export function useTreeOperations() {
  const { createConfirm } = useMessage();

  /**
   * 处理树形栏目选择
   */
  const handleTreeChange = async (
    selectedKeys: string[],
    event: any,
    categoryType: any,
    categoryPlatformType: any,
    autoId: any,
    handleCategoryChange: Function
  ) => {
    const { node } = event;
  
    categoryType.value = node.categoryType;
    categoryPlatformType.value = node.platformType;

    // 校验栏目配置的最多新增
    if (node.maxNewsNumber) {
      try {
        const res = await countByCategoryId({
          categoryId: node.categoryId,
          autoId: unref(autoId),
        });
        const { code, message } = res;

        if (200 !== code) {
          createConfirm({
            title: '信息',
            icon: createVNode(ExclamationCircleOutlined),
            content: message,
            okText: '确定',
            cancelText: '取消',
            async onOk() {
              try {
                return await new Promise<void>(resolve => {
                  resolve();
                });
              } catch {
                return console.log('Oops errors!');
              }
            },
          });
          return;
        }
      } catch (error) {
        console.error('校验栏目最大新闻数量失败:', error);
      }
    }

    await handleCategoryChange(node.platformType, node);
  };

  /**
   * 处理树形展开
   */
  const handleTreeExpand = (expandedKeys: string[], expand: any, columnList: any) => {
    expand.value = filter(expandedKeys, v => !unref(columnList).includes(v));
  };

  /**
   * 更新表单显示状态
   */
  const updateFormDisplay = (isUploadFiles: any, updateSchema: Function) => {
    updateSchema([
      {
        field: 'whetherExternalLink',
        ifShow: !unref(isUploadFiles),
      },
      {
        field: 'newsIsopenComment',
        ifShow: !unref(isUploadFiles),
      },
    ]);
  };

  return {
    handleTreeChange,
    handleTreeExpand,
    updateFormDisplay,
  };
}
