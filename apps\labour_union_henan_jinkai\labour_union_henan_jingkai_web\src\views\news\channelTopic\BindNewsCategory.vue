<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    title="管理专题栏目"
    @ok="handleSubmit"
    :wrap-class-name="$style['bind-news-category']"
  >
    <Row>
      <Col :span="6">
        <div>
          <Divider>栏目</Divider>
        </div>
        <div class="h-78vh overflow-y-auto">
          <ApiTree
            v-model:checkedKeys="categoryId"
            :api="getUnionTree"
            :params="{ nextLevelFlag: false }"
            :showLine="true"
            :showIcon="true"
            :autoExpandParent="true"
            :checkable="true"
            v-model:expandedKeys="categoryExpand"
            :fieldNames="{ title: 'categoryName', key: 'categoryId' }"
            @check="handelChangeTree"
          >
          </ApiTree>
        </div>
      </Col>
      <Col :span="18">
        <BasicTable @register="registerTable">
          <template #bodyCell="{ record, column }">
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    icon: 'file-icons:binder',
                    label: '移除关联',
                    type: 'primary',
                    danger: true,
                    onClick: handleRemoveBind.bind(null, record),
                  },
                ]"
              ></TableAction>
            </template>
          </template>
        </BasicTable>
      </Col>
    </Row>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { BindColumn } from './data';
import { Col, Row, Divider } from 'ant-design-vue';
import { getUnionTree } from '@/api/category';
import { ApiTree } from '@/components/Form';
import { filter, map } from 'lodash-es';

defineOptions({ name: 'BindNewsCategory' });

const emit = defineEmits(['register', 'success', 'cancel']);

const categoryId = ref<string[]>([]);

const categoryExpand = ref<string[]>([]);

const dataSource = ref<Recordable[]>([]);

const autoId = ref();

const platformType = ref<string>('');

const [registerModal, { closeModal }] = useModalInner(async data => {
  dataSource.value = data.record?.categoryInfoList || [];

  autoId.value = data.record?.autoId;

  platformType.value = data.record?.platformType;

  categoryId.value = map(unref(dataSource), v => v?.categoryId);

  categoryExpand.value = map(unref(dataSource), v => v?.categoryId);
});

const [registerTable, { setTableData, getDataSource }] = useTable({
  rowKey: 'categoryId',
  columns: BindColumn(),
  showIndexColumn: false,
  useSearchForm: false,
  dataSource: dataSource,
  bordered: true,
  pagination: false,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',

    fixed: undefined,
  },
});

function handleRemoveBind(record) {
  const dataSource = getDataSource();
  const filterData = filter(dataSource, v => v.categoryId != record.categoryId);

  categoryId.value = map(filterData, v => v.categoryId);
  setTableData(filterData);
}

async function handleSubmit() {
  const dataSource = getDataSource();

  emit('success', {
    values: {
      autoId: unref(autoId),
      categoryIdList: map(dataSource, v => v.categoryId),
      platformType: unref(platformType),
    },
    closeModal,
  });
}

function handelChangeTree(_, event) {
  const { checkedNodes } = event;
  const checkable = filter(checkedNodes, v => v.selectable);
  setTableData(checkable);
}
</script>

<style lang="less" module>
.bind-news-category {
  :global {
    .ant-input-number {
      width: 100% !important;
    }

    .ant-tree-switcher-noop {
      svg {
        display: none;
      }
    }
  }
}
</style>
