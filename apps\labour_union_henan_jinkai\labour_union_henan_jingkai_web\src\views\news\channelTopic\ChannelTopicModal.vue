<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :wrap-class-name="$style['channel-topic-modal']"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
      <template #pageType="{ model, field }">
        <RadioGroup
          v-model:value="model[field]"
          :options="dictionary.getDictionaryOpt.get('pageType')"
          @change="e => handleChangeFormItem(e, model)"
        >
        </RadioGroup>
      </template>
      <template #platformType="{ model, field }">
        <!--    :disabled="disabled" -->
        <CheckboxGroup
          v-model:value="model[field]"
          :options="dictionary.getDictionaryOpt.get('appType')"
          @change="e => handleChangeFormItem(e, model)"
          :disabled="true"
        >
        </CheckboxGroup>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { RadioGroup, CheckboxGroup } from 'ant-design-vue';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from './data';
import { useDictionary } from '@/store/modules/dictionary';
import { isEmpty, map } from 'lodash-es';
import { maxSortNumber } from '@/api/newsSpecial';

const emit = defineEmits(['register', 'success']);

const autoId = ref();

const isUpdate = ref(false);

const disabled = ref(false);

const record = ref<Recordable>();

const dictionary = useDictionary();

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.specialName || ''}--详情`
      : `编辑专题--${unref(record)?.specialName || ''}`
    : '新增自定义专题';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem();
});

const [
  registerForm,
  {
    getFieldsValue,
    resetFields,
    validate,
    setFieldsValue,
    appendSchemaByField,
    removeSchemaByField,
    setProps,
  },
] = useForm({
  labelWidth: 170,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  isUpdate.value = !!data.isUpdate;
  disabled.value = !!data.disabled;
  record.value = data.record;

  removeSchemaByField(['pcPageLink', 'appPageLink', 'zgfwwPageLink']);
  if (unref(isUpdate)) {
    const {
      enable,
      skipType,
      openType,
      userInfo,
      platformType,
      pageType,
      autoId: rId,
      startTime,
      endTime,
    } = data.record;
    autoId.value = rId;
    if (pageType === 'customSinglePage' && platformType?.length > 0) {
      map(platformType.split(','), v => {
        const obj = dictionary.getDictionaryMap.get(`appType_${v}`);
        let fieldAdd =
          obj?.dictCode === '20'
            ? 'pcPageLink'
            : obj?.dictCode === '10'
              ? 'zgfwwPageLink'
              : 'appPageLink';
        setForm(fieldAdd, `${obj?.dictName}页面链接`, 'Input', true, { autocomplete: 'off' });
      });
    }

    setFieldsValue({
      ...data.record,
      platformType: platformType.split(','),
      enable: `${enable}`,
      skipType: `${skipType}`,
      openType: `${openType}`,
      userInfo: `${userInfo}`,
      pageType: `${pageType}`,
      dateTime: startTime && endTime ? [startTime, endTime] : undefined,
    });
  } else {
    //新增重置autoId
    autoId.value = undefined;
    await maxSortNumber({}).then(res => {
      const { data } = res;
      setFieldsValue({
        sort: data,
      });
    });
  }
  setProps({ disabled: unref(disabled) });
  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

function handleChangeFormItem(_, model) {
  const pageType = model['pageType'];
  const platformType = model['platformType'];
  removeSchemaByField(['pcPageLink', 'appPageLink', 'zgfwwPageLink']);

  if (pageType === 'customSinglePage' && platformType?.length > 0) {
    const values = getFieldsValue();
    map(platformType, v => {
      const obj = dictionary.getDictionaryMap.get(`appType_${v}`);
      let fieldAdd =
        obj?.dictCode === '20'
          ? 'pcPageLink'
          : obj?.dictCode === '10'
            ? 'zgfwwPageLink'
            : 'appPageLink';
      setForm(fieldAdd, `${obj?.dictName}页面链接`, 'InputTextArea', true, {
        autocomplete: 'off',
        showCount: true,
        maxlength: 300,
      });
    });
    setFieldsValue(values);
  }
}

function setForm(field, label, component, required, opt) {
  appendSchemaByField(
    {
      field,
      label,
      component,
      required,
      rulesMessageJoinLabel: true,
      componentProps: {
        ...opt,
      },
    },
    'platformType'
  );
}

async function handleSubmit() {
  const values = await validate();

  const { dateTime, ...other } = values;

  emit('success', {
    values: {
      ...other,
      autoId: unref(autoId),
      platformType: values.platformType?.join(','),
      startTime: !isEmpty(dateTime) ? dateTime?.[0] : undefined,
      endTime: !isEmpty(dateTime) ? dateTime?.[1] : undefined,
    },
    isUpdate: unref(isUpdate),
  });
}
</script>

<style lang="less" module>
.channel-topic-modal {
  :global {
    .ant-input-number,
    .ant-picker {
      width: 100% !important;
    }
  }
}
</style>
