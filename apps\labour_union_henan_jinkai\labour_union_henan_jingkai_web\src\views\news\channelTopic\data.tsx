import { Tooltip } from 'ant-design-vue';
import { includes, map } from 'lodash-es';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { getReferToSpecial } from '@/api/newsSpecial';

//列表
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '排序号',
      dataIndex: 'sort',
      width: 80,
    },
    {
      title: '专题名称',
      dataIndex: 'specialName',
      customRender: ({ text }) => {
        return (
          <Tooltip title={text}>
            {text}
            {/* <span class="!truncate">{text}</span> */}
          </Tooltip>
        );
      },
    },
    // {
    //   title: '父级专题名称',
    //   dataIndex: 'parentName',
    //   width: 120,
    //   customRender: ({ text }) => {
    //     return (
    //       (text = text ? text : '--'),
    //       (
    //         <Tooltip title={text}>
    //           {text}
    //           {/* <span class="!truncate">{text}</span> */}
    //         </Tooltip>
    //       )
    //     );
    //   },
    // },
    // {
    //   title: '所属端口',
    //   dataIndex: 'platformType',
    //   width: 130,
    //   customRender: ({ text }) => {
    //     const textArr = text.split(',');
    //     const all = map(
    //       textArr,
    //       v => dictionary.getDictionaryMap.get(`appType_${v}`)?.dictName
    //     )?.join(',');
    //     return (
    //       <Tooltip title={all}>
    //         <span>{all}</span>
    //       </Tooltip>
    //     );
    //   },
    // },
    {
      title: '页面类型',
      dataIndex: 'pageType',
      customRender: ({ text }) => {
        const pageType = dictionary.getDictionaryMap.get(`pageType_${text}`)?.dictName;
        return (
          <Tooltip title={pageType}>
            <span>{pageType}</span>
          </Tooltip>
        );
      },
      width: 100,
    },
    // {
    //   title: '打开方式',
    //   dataIndex: 'openType',
    //   width: 150,
    //   ifShow: false,
    //   customRender: ({ text }) => {
    //     const openType = dictionary.getDictionaryMap.get(`specialOpenType_${text}`)?.dictName
    //     return (
    //       <Tooltip title={openType}>
    //         <span>{openType}</span>
    //       </Tooltip>
    //     )
    //   },
    // },
    {
      title: '是否启用',
      dataIndex: 'logicallyDelete',
      width: 80,
      customRender: ({ text }) => {
        const enable = dictionary.getDictionaryMap.get(`logicallyDelete_${text}`)?.dictName;
        return (
          <Tooltip title={enable}>
            <span>{enable}</span>
          </Tooltip>
        );
      },
    },
    // {
    //   title: '是否固定',
    //   dataIndex: 'regularState',
    //   width: 80,
    //   customRender: ({ text }) => {
    //     const enable = dictionary.getDictionaryMap.get(`logicallyDelete_${text}`)?.dictName;
    //     return (
    //       <Tooltip title={enable}>
    //         <span>{enable}</span>
    //       </Tooltip>
    //     );
    //   },
    // },
    // {
    //   title: '专题类型',
    //   dataIndex: 'specialType',
    //   width: 120,
    //   customRender: ({ text }) => {
    //     const enable = dictionary.getDictionaryMap.get(`specialType_${text}`)?.dictName;
    //     return (
    //       <Tooltip title={enable}>
    //         <span>{enable}</span>
    //       </Tooltip>
    //     );
    //   },
    // },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'specialName',
      label: '专题名称',
      colProps: { span: 6 },
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入专题名称',
      },
    },
    {
      field: 'pageType',
      label: '页面类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('pageType'),
      },
    },
    {
      field: 'logicallyDelete',
      label: '是否启用',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('logicallyDelete'),
      },
    },
    // {
    //   field: 'regularState',
    //   label: '是否固定',
    //   colProps: { span: 6 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get('logicallyDelete'),
    //   },
    // },
    // {
    //   field: 'specialType',
    //   label: '专题类型',
    //   colProps: { span: 6 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get('specialType'),
    //   },
    // },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();

  return [
    {
      field: 'specialName',
      label: '专题名称',
      component: 'Input',
      required: true,
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入专题名称',
        showCount: true,
        maxlength: 40,
      },
    },
    // {
    //   field: 'displayName',
    //   label: '展示名称',
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    //   required: true,
    //   componentProps: {
    //     autocomplete: 'off',
    //     showCount: true,
    //     maxlength: 40,
    //   },
    // },
    {
      field: 'pageType',
      label: '页面类型',
      required: true,
      colProps: {
        span: 12,
      },
      component: 'RadioGroup',
      defaultValue: 'defaultTemplate',
      slot: 'pageType',
    },
    {
      field: 'skipType',
      label: '跳转方式',
      component: 'RadioGroup',
      colProps: {
        span: 12,
      },
      helpMessage: '选择[内置]时,页面链接配置路由,选择[外部]时,页面链接配置完整可访问url',
      ifShow: ({ values }) => {
        console.log(1111, values);

        return values.pageType === 'customSinglePage';
      },
      defaultValue: 'builtIn',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('skipType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'whetherPrompt',
      label: '是否弹窗提示',
      component: 'RadioGroup',
      helpMessage: '选择[是]时,会提示外链访问弹窗,选择[否]时,不会提示外链访问弹窗,会直接访问',
      colProps: {
        span: 12,
      },
      ifShow: ({ values }) => {
        return values.pageType === 'customSinglePage' && values.skipType === 'external';
      },
      defaultValue: 'y',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
      },
    },
    {
      field: 'platformType',
      label: '平台类型',
      component: 'CheckboxGroup',
      required: true,
      defaultValue: ['30'],
      colProps: {
        span: 12,
      },
      slot: 'platformType',
    },
    {
      field: 'appBannerPath',
      label: 'APP Banner路径',
      required: true,
      rulesMessageJoinLabel: true,
      component: 'CropperForm',
      renderComponentContent() {
        return {
          tip: () => (
            <div class="text-sm leading-7">
              注:图标规格大小为(<span class="text-red-500">690*240</span>)以内
            </div>
          ),
        };
      },
      componentProps: function () {
        return {
          imgSize: 690 / 240,
          operateType: 18,
          cropendRatio: 1,
        };
      },
      ifShow: ({ values }) => {
        return includes(values.platformType, '30');
      },
    },
    {
      field: 'pcBannerPath',
      label: '官网 Banner路径',
      required: true,
      component: 'CropperForm',
      renderComponentContent() {
        return {
          tip: () => (
            <div class="text-sm leading-7">
              注:图标规格大小为(<span class="text-red-500">1920*360px</span>)以内
            </div>
          ),
        };
      },
      componentProps: function () {
        return {
          imgSize: 1920 / 360,
          operateType: 18,
          cropendRatio: 1,
        };
      },
      ifShow: ({ values }) => {
        return includes(values.platformType, '20');
      },
    },
    {
      field: 'sort',
      label: '排序号',
      required: true,
      component: 'InputNumber',
      componentProps: {
        min: 6,
        placeholder: '请输入序号',
      },
    },
    {
      field: 'logicallyDelete',
      label: '启用状态',
      component: 'RadioGroup',
      defaultValue: 'y',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('logicallyDelete') as RadioGroupChildOption[],
      },
    },
  ];
};

export const BindColumn = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '主键',
      dataIndex: 'categoryId',
      defaultHidden: true,
    },
    {
      title: '栏目名称',
      dataIndex: 'categoryName',
      width: 100,
    },
    {
      title: '栏目编码',
      dataIndex: 'categoryCode',
      width: 100,
    },
    {
      title: '栏目类型',
      dataIndex: 'platformType',
      width: 100,
      customRender: ({ text }) => {
        const textArr = text.split(',');
        const all = map(
          textArr,
          v => dictionary.getDictionaryMap.get(`appType_${v}`)?.dictName
        )?.join(',');
        return (
          <Tooltip title={all}>
            <span>{all}</span>
          </Tooltip>
        );
      },
    },
    {
      title: '排序号',
      dataIndex: 'sort',
      width: 30,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 100,
    },
  ];
};

//设置排序弹窗
export const modalSortFormItem = (autoId): FormSchema[] => {
  return [
    {
      field: 'specialName',
      label: '当前专题名称',
      component: 'ShowSpan',
      colProps: { span: 24 },
    },
    {
      field: 'referToAutoId',
      label: '参照专题名称',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      itemProps: {
        autoLink: false,
      },
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          api: getReferToSpecial,
          params: {
            autoId: autoId,
          },
          alwaysLoad: true,
          immediate: true,
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.specialName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'specialName', value: 'autoId' },
        };
      },
    },
    {
      field: 'sequentialOptions',
      label: '排序选项',
      component: 'RadioGroup',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: [
          { label: '之前', value: 'before' },
          { label: '之后', value: 'after' },
        ],
      },
    },
  ];
};
