<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/channelTopic/add"
        >
          新增专题
        </a-button>
        <span style="color: #ef3333">注：APP专题排序按照序号从大到小排序！</span>
        <!-- <a-tag color="error">注：仅限设置一个固定专题，请谨慎操作！</a-tag> -->
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/channelTopic/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/channelTopic/modify',
                ifShow: record.specialType === 'custom',
              },
              {
                icon: 'mdi:sort',
                label: '排序',
                type: 'primary',
                onClick: handleSort.bind(null, record),
                // auth: '/channelTopic/sort',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                ifShow: record.specialType === 'custom',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/channelTopic/delete',
              },
              // {
              //   icon: 'fluent:pin-12-regular',
              //   label: '固定专题',
              //   type: 'primary',
              //   onClick: handleRegular.bind(null, record),
              //   ifShow: record.parentId === null,
              //   disabled: record.regularState === 'y',
              //   auth: '/channelTopic/regular',
              // },
              {
                icon: 'carbon:cloud-service-management',
                label: '管理专题栏目',
                ifShow: !(
                  record.pageType !== 'defaultTemplate' ||
                  (record.specialType !== 'custom' && !!!record.parentId)
                ),
                type: 'primary',
                onClick: handleBind.bind(null, record),
                auth: '/channelTopic/columns',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ChannelTopicModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="50%"
    />
    <BindNewsCategory
      @register="registerBind"
      @success="handleSuccessBind"
      :canFullscreen="false"
      width="88%"
    />
    <SortModel
      @register="registerSort"
      :can-fullscreen="false"
      width="40%"
      @success="handleSortSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { createVNode } from 'vue';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { columns, formSchemas } from './data';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import ChannelTopicModal from './ChannelTopicModal.vue';
import { useModal } from '@/components/Modal';
import {
  getNewsSpecialList,
  getView,
  deleteNewsSpecial,
  addNewsSpecial,
  amend,
  newsSpecialCategory,
  getRegularSpecial,
  setRegularSpecial,
  setSpecialSort,
} from '@/api/newsSpecial';
import BindNewsCategory from './BindNewsCategory.vue';
import SortModel from './SortModel.vue';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createSuccessModal, createMessage } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: '/channelTopic/add',
  showIndexColumn: false,
  api: getNewsSpecialList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  searchInfo: {
    orderBy: 'sort',
    sortType: 'desc',
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 500,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/channelTopic/view',
      '/channelTopic/modify',
      '/channelTopic/columns',
      '/channelTopic/delete',
      '/channelTopic/regular',
    ],
    // align: 'left',
    // class: '!text-center',
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

const [registerBind, { openModal: openBind, closeModal: closeBind }] = useModal();
const [registerSort, { openModal: openSort, closeModal: closeSort }] = useModal();

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

//设置排序
function handleSort(record) {
  openSort(true, { record });
}

function handleDelete(record) {
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定删除?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          deleteNewsSpecial(record).then(res => {
            if (res.code === 200) {
              message.success('删除成功');
            } else {
              message.error('删除失败');
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}
function handleView(record) {
  getView({ autoId: record.autoId, whetherInquireColumn: false }).then(res => {
    openModal(true, {
      record: res.data,
      isUpdate: true,
      disabled: true,
    });
  });
}

function handleEdit(record) {
  getView({ autoId: record.autoId, whetherInquireColumn: false }).then(res => {
    openModal(true, {
      record: res.data,
      isUpdate: true,
      disabled: false,
    });
  });
}
//固定专题使用
async function handleRegular(record) {
  //查询是否有固定专题
  const { code, data, message: msg } = await getRegularSpecial();
  if (code !== 200) {
    message.error(`设置失败!${msg}`);
    return;
  }
  let content = `确定固定[${record?.specialName}]吗?`;
  if (data !== null) {
    content = `确定将[${record?.specialName}]设置为固定专题吗?当前固定专题[${data?.specialName}]将不在是固定专题!`;
  }

  Modal.confirm({
    title: '操作提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: content,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          setRegularSpecial({ autoId: record.autoId }).then(res => {
            if (res.code === 200) {
              message.success('设置成功!');
            } else {
              message.error(`设置失败!${res.message}`);
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}

function handleBind(record) {
  getView({ autoId: record.autoId, whetherInquireColumn: true }).then(res => {
    openBind(true, { record: res.data });
  });
}

function handleSuccess({ isUpdate, values }) {
  if (isUpdate) {
    amend(values).then(res => {
      const { code, message: msg } = res;
      if (code === 200) {
        message.success('编辑成功');
        // 刷新编辑数据的缓存值
        closeModal();
        reload();
      } else {
        message.error(`编辑失败!${msg}`);
      }
    });
  } else {
    addNewsSpecial(values).then(res => {
      const { code, message: msg } = res;
      if (code === 200) {
        message.success('新增成功');
        reload();
        closeModal();
      } else {
        message.error(`新增失败!${msg}`);
      }
    });
  }
}

//设置排序回调
function handleSortSuccess({ values, autoId }) {
  const { specialName, referToAutoId, sequentialOptions } = values;
  const newsSequentialOptionsName = 'before' === sequentialOptions ? '之前' : '之后';
  //根据新闻业务id查询新闻信息
  getView({ autoId: referToAutoId, whetherInquireColumn: false }).then(res => {
    const { code, data, message: mes } = res;
    if (code === 200) {
      createConfirm({
        title: '操作提示',
        icon: createVNode(ExclamationCircleOutlined),
        content: `确定将[${specialName}]设置在[${data?.specialName}]${newsSequentialOptionsName}嘛?确认后系统将自动修改排序号!`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
          try {
            return await new Promise<void>(resolve => {
              setSpecialSort({ autoId, sequentialOptions, referToAutoId }).then(res => {
                resolve();
                if (res.code === 200) {
                  createMessage.success('设置成功!');
                  reload();
                  closeSort();
                } else {
                  createMessage.error(`设置失败!${res.message}`);
                }
              });
            });
          } catch {
            return console.log('Oops errors!');
          }
        },
      });
    } else {
      createMessage.error(`设置失败!${mes}`);
    }
  });
}

function handleSuccessBind({ values }) {
  newsSpecialCategory({ ...values }).then(res => {
    const { code, message: msg } = res;

    if (code === 200) {
      message.success('操作成功');
      // 刷新编辑数据的缓存值
      closeBind();
      reload();
    } else {
      message.error(msg);
    }
  });
}
</script>
