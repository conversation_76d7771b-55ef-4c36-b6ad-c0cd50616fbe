<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
    :wrap-class-name="$style['comment-modal']"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts">
import { computed, defineComponent, ref, unref } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { Row, Col, Divider, RadioGroup } from 'ant-design-vue';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from './data';

export default defineComponent({
  name: 'ModerationModal',
  components: { BasicModal, Row, Col, Divider, BasicForm, RadioGroup },
  emits: ['register', 'success', 'cancel'],
  setup(_, { emit }) {
    const recordId = ref<string | string[]>('');

    const notShow = ref(false);

    const disabled = ref(false);

    const disabledClass = computed(() => {
      return unref(disabled) ? 'back-transparent' : '';
    });

    const title = computed(() => {
      return unref(disabled) ? '评论详情' : '评论审核';
    });

    const modalItem = computed(() => {
      return modalFormItem(notShow.value, disabled.value);
    });

    const [registerForm, { resetFields, validate, setFieldsValue, clearValidate }] = useForm({
      labelWidth: 100,
      schemas: modalItem,
      showActionButtonGroup: false,
    });

    const [registerModal, { setModalProps }] = useModalInner(async data => {
      await resetFields();

      recordId.value = data.record?.recordId;
      notShow.value = data.notShow;
      disabled.value = !!data?.disabled;
      if (!unref(notShow)) {
        setFieldsValue({
          ...data.record,
          //处理单个审核,审核状态赋值导致必填选项过
          auditStatus: unref(disabled) ? data.record.auditStatus : null,
        });
        //清除首次打开,未点击按钮的校验
        await clearValidate();
      }
      setModalProps({ confirmLoading: false ,showOkBtn: !unref(disabled) });
    });

    async function handleSubmit() {
      try {
        const values = await validate();
        console.log(values);
        emit('success', { values, recordId: unref(recordId) });
      } finally {
      }
    }

    return {
      registerModal,
      registerForm,
      handleSubmit,
      disabledClass,
      title,
    };
  },
});
</script>

<style lang="less" module>
.comment-modal {
  :global {
    .ant-divider {
      @apply border-gray-700 text-18px font-700 !important;
    }
  }
}
</style>
