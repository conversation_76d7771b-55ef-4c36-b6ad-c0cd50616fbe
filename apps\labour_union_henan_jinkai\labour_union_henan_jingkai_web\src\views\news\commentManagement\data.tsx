import { cloneDeep, filter } from 'lodash-es';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import dayjs from 'dayjs';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '新闻标题',
      dataIndex: 'newsTitle',
      ellipsis: true,
      customRender: ({ text }) => {
        return <span title={text}>{text}</span>;
      },
    },
    {
      title: '评论内容',
      dataIndex: 'content',
      ellipsis: true,
      customRender: ({ text }) => {
        return <span title={text}>{text}</span>;
      },
    },
    {
      title: '评论人',
      dataIndex: 'createUser',
      width: 170,
    },
    {
      title: '评论时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      width: 100,
      customRender: ({ text }) => {
        return (
          <span
            class={`${text === 'pass' ? 'text-green-500' : text === 'refuse' ? 'text-red-500' : ''}`}
          >
            {dictionary.getDictionaryMap.get(`newsCommentAuditState_${text}`)?.dictName || ''}
          </span>
        );
      },
    },
    {
      title: '审核人',
      dataIndex: 'auditUser',
      width: 170,
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'newsTitle',
      label: '新闻标题',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
      },
    },
    {
      field: 'content',
      label: '评论内容关键字',
      colProps: { span: 8 },
      rulesMessageJoinLabel: true,
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
      },
    },
    {
      field: 'createTimeRange',
      label: '评论时间',
      component: 'RangePicker',
      colProps: { span: 8 },
      rulesMessageJoinLabel: true,
      componentProps: {
        showTime: {
          hideDisabledOptions: true,
          defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
        },
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DDTHH:mm:ss',
      },
    },
    {
      field: 'auditStatus',
      label: '审核状态',
      colProps: { span: 5 },
      rulesMessageJoinLabel: true,
      component: 'Select',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('newsCommentAuditState'),
      },
    },
  ];
};

export const modalFormItem = (flg: boolean, disabled: boolean): FormSchema[] => {
  const dictionary = useDictionary();
  if (flg) {
    return [
      {
        field: '',
        label: '审核信息',
        component: 'Divider',
      },
      {
        field: 'auditStatus',
        label: '是否通过',
        component: 'RadioGroup',
        required: true,
        componentProps: {
          options: filter(
            cloneDeep(
              dictionary.getDictionaryOpt.get('newsCommentAuditState')
            ) as RadioGroupChildOption[],
            v => v.value !== 'wait'
          ),
        },
      },
      {
        field: 'auditOpinion',
        label: '审核意见',
        rulesMessageJoinLabel: true,
        // required: function (Recordable) {
        //   if (Recordable.model.auditStatus == 'refuse') {
        //     return true;
        //   } else {
        //     return false;
        //   }
        // },
        required: false,
        component: 'InputTextArea',
        componentProps: {
          autocomplete: 'off',
          showCount: true,
          maxlength: 100,
        },
      },
    ];
  } else {
    if (disabled) {
      return [
        {
          field: '',
          label: '评论信息',
          component: 'Divider',
        },
        {
          field: 'newsTitle',
          label: '新闻标题',
          component: 'Input',
          dynamicDisabled: true,
        },
        {
          field: 'createUser',
          label: '评论人',
          component: 'Input',
          dynamicDisabled: true,
        },
        {
          field: 'createTime',
          label: '评论时间',
          component: 'Input',
          dynamicDisabled: true,
        },
        {
          field: 'content',
          label: '内容',
          component: 'InputTextArea',
          dynamicDisabled: true,
        },
        {
          field: '',
          label: '审核信息',
          component: 'Divider',
        },
        {
          field: 'auditStatus',
          label: '审核状态',
          component: 'RadioGroup',
          dynamicDisabled: true,
          componentProps: {
            options: dictionary.getDictionaryOpt.get(
              'newsCommentAuditState'
            ) as RadioGroupChildOption[],
          },
        },
        {
          field: 'auditOpinion',
          label: '审核意见',
          component: 'InputTextArea',
          dynamicDisabled: true,
        },
      ];
    } else {
      return [
        {
          field: '',
          label: '评论信息',
          component: 'Divider',
        },
        {
          field: 'newsTitle',
          label: '新闻标题',
          component: 'ShowSpan',
        },
        {
          field: 'createUser',
          label: '评论人',
          component: 'ShowSpan',
        },
        {
          field: 'createTime',
          label: '评论时间',
          component: 'ShowSpan',
        },
        {
          field: 'content',
          label: '内容',
          component: 'ShowSpan',
        },
        {
          field: '',
          label: '审核信息',
          component: 'Divider',
        },
        {
          field: 'auditStatus',
          label: '是否通过',
          component: 'RadioGroup',
          required: true,
          componentProps: {
            options: filter(
              cloneDeep(
                dictionary.getDictionaryOpt.get('newsCommentAuditState') as RadioGroupChildOption[]
              ),
              v => v.value !== 'wait'
            ),
          },
        },
        {
          field: 'auditOpinion',
          label: '审核意见',
          rulesMessageJoinLabel: true,
          // required: function (Recordable) {
          //   if (Recordable.model.auditStatus == 'refuse') {
          //     return true;
          //   } else {
          //     return false;
          //   }
          // },
          required: false,
          component: 'InputTextArea',
          componentProps: {
            autocomplete: 'off',
            showCount: true,
            maxlength: 100,
          },
        },
      ];
    }
  }
};
