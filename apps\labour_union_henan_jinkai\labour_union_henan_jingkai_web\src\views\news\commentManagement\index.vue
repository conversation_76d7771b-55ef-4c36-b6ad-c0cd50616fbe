<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleAuditBatch"
          auth="/commentManagement/auditBatch"
        >
          批量审核
        </a-button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/commentManagement/view',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                disabled: record.auditStatus !== 'wait',
                onClick: handleAudit.bind(null, record),
                auth: '/commentManagement/audit',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                danger: true,
                type: 'primary',
                disabled: !(record.auditStatus === 'refuse'),
                onClick: handleDelete.bind(null, record),
                auth: '/commentManagement/delete',
              },
            ]"
          >
          </TableAction>
        </template>
      </template>
    </BasicTable>
    <CommentModal
      @register="registerComment"
      :can-fullscreen="false"
      width="40%"
      @success="handleSuccess"
    >
    </CommentModal>
  </div>
</template>

<script lang="ts" setup>
import { useTable, TableAction, BasicTable } from '@/components/Table';
import { columns, formSchemas } from './data';
import { userCommentFindList, deleteuserComment, auditOne } from '@/api/commentsNews';
import { Modal } from 'ant-design-vue';
import { useModal } from '@/components/Modal';
import CommentModal from './CommentModal.vue';
import { createVNode } from 'vue';
import { CloseCircleFilled, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { map } from 'lodash-es';
import { useMessage } from '@monorepo-yysz/hooks';

const { createErrorModal, createSuccessModal } = useMessage();

const [registerComment, { openModal, closeModal }] = useModal();

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: '/commentManagement/auditBatch',
  showIndexColumn: false,
  api: userCommentFindList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.auditStatus !== 'wait',
    }),
  },
  beforeFetch: params => {
    //处理时间
    const { createTimeRange } = params;
    if (createTimeRange && createTimeRange.length === 2) {
      params.startTime = createTimeRange[0];
      params.endTime = createTimeRange[1];
    }
    params.createTimeRange = undefined;
    return params;
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 250,
    dataIndex: 'action',

    fixed: undefined,
    auth: ['/commentManagement/audit', '/commentManagement/delete', '/commentManagement/view'],
  },
});

//审核
function handleAudit(record) {
  const recordId = [record.autoId];
  openModal(true, { record: { ...record, recordId: recordId }, notShow: false });
}

//详情
function handleView(record) {
  openModal(true, {
    record: { ...record, recordId: [record.autoId] },
    disabled: true,
    notShow: false,
  });
}
//single
function handleSuccess({ values, recordId }) {
  auditOne({ ...values, userCommentList: recordId }).then(res => {
    const { code, message: msg } = res;
    if (code === 200) {
      createSuccessModal({ content: '操作成功' });
      reload();
      closeModal();
      clearSelectedRowKeys();
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}

//batch
function handleAuditBatch() {
  const rows = getSelectRows();
  if (!rows || rows.length === 0) {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '请选择至少一条数据进行审核！',
      okText: '确认',
      closable: true,
    });
    return false;
  }

  openModal(true, { record: { recordId: map(rows, v => v.autoId) }, notShow: true });
}

function handleDelete(record) {
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定删除?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          deleteuserComment(record).then(({ code, message }) => {
            if (code === 200) {
              createSuccessModal({ content: '删除成功' });
            } else {
              createErrorModal({ content: `删除失败,${message}` });
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}
</script>
