<template>
  <div>
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              auth: '/goodNewsAudit/view',
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '审核',
              type: 'primary',
              onClick: handleEdit.bind(null, record),
              disabled: !(
                (upperType === '1' && record?.cityLevelAuditStatus === 'wait') ||
                (upperType === '2' && record?.countyLevelAuditStatus === 'wait')
              ),
              auth: '/goodNewsAudit/audit',
            },
            {
              icon: 'lsicon:submit-filled',
              label: '上报',
              type: 'primary',
              onClick: handleReport.bind(null, record),
              ifShow: '6650f8e054af46e7a415be50597a99d5' !== userStore.getUserInfo.companyId,
              disabled: !(
                record?.countyLevelAuditStatus === 'pass' && !record?.cityLevelAuditStatus
              ),
              auth: '/goodNewsAudit/report',
            },
            {
              icon: 'lsicon:submit-filled',
              label: '公布',
              type: 'primary',
              onClick: handlePublish.bind(null, record),
              disabled: !(
                (upperType === '1' && record?.cityLevelAuditStatus === 'pass') ||
                (upperType === '2' && record?.countyLevelAuditStatus === 'pass')
              ),
              auth: '/goodNewsAudit/publish',
            },
          ]"
        />
      </template>
    </BasicTable>
    <GoodNewsAuditModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
    <ChanelNewsModal
      @register="registerNewsModal"
      :can-fullscreen="false"
      width="88%"
      @success="handleNewsSuccess"
      @audit="handleNewsSuccess"
      @view="handleNewsView"
    />
    <ViewModal
      @register="registerView"
      :can-fullscreen="false"
      width="20%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import GoodNewsAuditModal from './goodNewsAuditModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  levelFindVoList,
  view,
  newsGoodInfoToNewsInfoVO,
  newsGoodAudit,
  countyLevelReportNews,
  getUpperType,
} from '@/api/news/goodNews';
import { computed, onMounted, ref, unref } from 'vue';
import { useUserStore } from '@/store/modules/user';
import ChanelNewsModal from '../channelNews/ChanelNewsModal.vue';
import ViewModal from '../channelNews/ViewModal.vue';
import { find } from 'lodash-es';
import { newsAdd, newsUpdate } from '@/api/news';

const userStore = useUserStore();

const upperType = ref('');

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const column = computed(() => {
  return columns();
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: column,
  showIndexColumn: false,
  api: levelFindVoList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 260,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();
const [registerNewsModal, { openModal: openNewsModal, closeModal: closeNnewsModal }] = useModal();
const [registerView, { openModal: openView }] = useModal();

function handleNewsSuccess({ params, isUpdate }) {
  if (isUpdate) {
    newsUpdate(params).then(res => {
      const { code, message: msg } = res;
      if (code === 200) {
        createSuccessModal({ content: '操作成功' });
        reload();
        closeNnewsModal();
      } else {
        createErrorModal({ content: `操作失败，${msg}` });
      }
    });
  } else {
    newsAdd(params).then(res => {
      const { code, message: msg } = res;
      if (code === 200) {
        createSuccessModal({ content: '操作成功' });
        reload();
        closeNnewsModal();
      } else {
        createErrorModal({ content: `操作失败，${msg}` });
      }
    });
  }
}

//新增编辑预览
function handleNewsView({ params }) {
  const { newsDetailsList, newsSource, newsClicks, keywords } = params as Recordable;

  const appDetail = find(newsDetailsList, v => v.platformType === '30');

  const zgDetail = find(newsDetailsList, v => v.platformType === '20');

  const pDetail = find(newsDetailsList, v => v.platformType === '10');

  openView(true, {
    record: {
      appContent: appDetail && appDetail.newsDetailsContent,
      appTitle: appDetail && appDetail.newsDetailsTitle,
      appUrl: appDetail && appDetail.externalLinkAddress,
      zgContent: zgDetail && zgDetail.newsDetailsContent,
      zgUrl: zgDetail && zgDetail.externalLinkAddress,
      pContent: pDetail && pDetail.newsDetailsContent,
      pUrl: pDetail && pDetail.externalLinkAddress,
      source: newsSource,
      reading: newsClicks,
      keywords: keywords,
    },
  });
}

// 基层工会上报
function handleReport(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要将${record.newsDetailsTitle}上报！`,
    onOk: function () {
      countyLevelReportNews({ autoId: record?.autoId }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `上报成功` });
          reload();
        } else {
          createErrorModal({ content: `上报失败，${message}` });
        }
      });
    },
  });
}

//审核
function handleEdit(record) {
  view({ autoId: record?.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}
//公布
function handlePublish(record) {
  newsGoodInfoToNewsInfoVO({ autoId: record?.autoId }).then(({ data }) => {
    openNewsModal(true, { isUpdate: true, record: { ...data, newsClicks: record?.newsClicks } });
  });
}

//详情
function handleView(record) {
  view({ autoId: record?.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

function handleSuccess({ values, isUpdate }) {
  newsGoodAudit(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `审核成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      });
    }
  });
}

onMounted(() => {
  getUpperType({}).then(res => {
    upperType.value = res.data;
  });
});
</script>
