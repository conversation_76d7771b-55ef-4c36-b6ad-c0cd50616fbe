import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { h } from 'vue';
import { Tinymce } from '@/components/Tinymce';
import { Tooltip } from 'ant-design-vue';

export const columns = (upperType: string): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '资讯标题',
      dataIndex: 'newsDetailsTitle',
    },
    {
      title: '资讯类型',
      dataIndex: 'newsType',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`newsType_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '来源',
      dataIndex: 'newsSource',
    },
    {
      title: '区县级审核工会',
      dataIndex: 'countyLevelAuditStatus',
      ifShow: '2' === upperType,
      customRender: ({ record, text }) => {
        const countyLevelAuditStatus = dictionary.getDictionaryMap.get(
          `newsCommentAuditState_${text}`
        )?.dictName;
        return (
          <Tooltip
            title={
              <div>
                <div
                  style={{ textAlign: 'left' }}
                >{`审核工会:${record?.countyLevelCompanyName || ''}`}</div>
                <div
                  style={{ textAlign: 'left' }}
                >{`审核状态:${countyLevelAuditStatus || ''}`}</div>
                <div
                  style={{ textAlign: 'left' }}
                >{`审核人:${record?.countyLevelAuditUser || ''}`}</div>
                <div
                  style={{ textAlign: 'left' }}
                >{`审核时间:${record?.countyLevelAuditTime || ''}`}</div>
              </div>
            }
          >
            {record?.countyLevelCompanyName || '--'}
          </Tooltip>
        );
      },
    },
    {
      title: '市级审核工会',
      dataIndex: 'cityLevelAuditStatus',
      customRender: ({ record, text }) => {
        const cityLevelAuditStatus = dictionary.getDictionaryMap.get(
          `newsCommentAuditState_${text}`
        )?.dictName;
        return (
          <Tooltip
            title={
              <div>
                <div
                  style={{ textAlign: 'left' }}
                >{`审核工会:${record?.cityLevelCompanyName || ''}`}</div>
                <div style={{ textAlign: 'left' }}>{`审核状态:${cityLevelAuditStatus || ''}`}</div>
                <div
                  style={{ textAlign: 'left' }}
                >{`审核人:${record?.cityLevelAuditUser || ''}`}</div>
                <div
                  style={{ textAlign: 'left' }}
                >{`审核时间:${record?.cityLevelAuditTime || ''}`}</div>
              </div>
            }
          >
            {record?.cityLevelCompanyName || '--'}
          </Tooltip>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const modalColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '',
      dataIndex: '',
    },
    {
      title: '',
      dataIndex: '',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (upperType: string): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: 'newsDetailsTitle',
      label: '资讯标题',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'newsType',
      label: '资讯类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsType'),
        };
      },
    },
    {
      field: 'countyLevelAuditStatus',
      label: '区县级审核状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      ifShow: '2' === upperType,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsCommentAuditState'),
        };
      },
    },
    {
      field: 'cityLevelAuditStatus',
      label: '市级审核状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsCommentAuditState'),
        };
      },
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: 'newsDetailsTitle',
      label: '资讯标题',
      colProps: { span: 24 },
      component: 'InputTextArea',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 80,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    {
      field: 'newsDetailsAbstract',
      label: '资讯摘要',
      colProps: { span: 24 },
      component: 'InputTextArea',
      required: false,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 300,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    {
      field: 'newsType',
      label: '资讯类型',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue: 'text',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('newsType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'newsSource',
      label: '来源',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入来源',
        maxlength: 16,
        showCount: true,
      },
    },
    {
      field: 'newsDetailsContent',
      label: '资讯正文',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      render: ({ model, field, disabled }) => {
        return h(Tinymce, {
          value: model[field],
          onChange: (value: string) => {
            model[field] = value;
          },
          showImageUpload: false,
          operateType: 19,
          options: {
            readonly: disabled,
          },
        });
      },
    },
  ];
};

export const columnSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: '',
      label: '',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get(''),
        };
      },
    },
  ];
};
