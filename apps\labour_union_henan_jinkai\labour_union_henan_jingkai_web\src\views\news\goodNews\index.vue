<template>
  <div>
    <BasicTable @register="registerTable">
      <template
        #toolbar
        v-if="'6650f8e054af46e7a415be50597a99d5' !== userStore.getUserInfo.companyId"
      >
        <a-button
          type="primary"
          @click="handleClick"
          auth="/goodNews/add"
        >
          新增优文
        </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              auth: '/goodNews/view',
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              onClick: handleEdit.bind(null, record),
              disabled: !(
                (upperType === '1' &&
                  (!record?.cityLevelAuditStatus || 'refuse' === record?.cityLevelAuditStatus)) ||
                (upperType === '2' &&
                  (!record?.countyLevelAuditStatus || 'refuse' === record?.countyLevelAuditStatus))
              ),
              auth: '/goodNews/update',
            },
            {
              icon: 'lsicon:submit-filled',
              label: '上报',
              type: 'primary',
              onClick: handleReport.bind(null, record),
              disabled: !(
                (upperType === '1' &&
                  (!record?.cityLevelAuditStatus || 'refuse' === record?.cityLevelAuditStatus)) ||
                (upperType === '2' &&
                  (!record?.countyLevelAuditStatus || 'refuse' === record?.countyLevelAuditStatus))
              ),
              auth: '/goodNews/report',
            },
            {
              icon: 'fluent:delete-16-filled',
              label: '删除',
              type: 'primary',
              danger: true,
              onClick: handleDelete.bind(null, record),
              disabled: !(
                (upperType === '1' && !record?.cityLevelAuditStatus) ||
                (upperType === '2' && !record?.countyLevelAuditStatus)
              ),
            },
          ]"
        />
      </template>
    </BasicTable>
    <GoodNewsModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import GoodNewsModal from './goodNewsModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  list,
  view,
  deleteLine,
  saveOrUpdate,
  substrateReportNews,
  getUpperType,
} from '@/api/news/goodNews';
import { computed, onMounted, ref, unref } from 'vue';
import { useUserStore } from '@/store/modules/user';

const upperType = ref('');
const userStore = useUserStore();

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const column = computed(() => {
  return columns(unref(upperType));
});

const form = computed(() => {
  return formSchemas(unref(upperType));
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: column,
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: form,
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record) {
  view({ autoId: record?.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

//详情
function handleView(record) {
  view({ autoId: record?.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.newsDetailsTitle}`,
    onOk: function () {
      deleteLine(record?.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

// 基层工会上报
function handleReport(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要将${record.newsDetailsTitle}上报！`,
    onOk: function () {
      substrateReportNews({ autoId: record?.autoId }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `上报成功` });
          reload();
        } else {
          createErrorModal({ content: `上报失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ values, isUpdate }) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}

onMounted(() => {
  getUpperType({}).then(res => {
    upperType.value = res.data;
  });
});
</script>
