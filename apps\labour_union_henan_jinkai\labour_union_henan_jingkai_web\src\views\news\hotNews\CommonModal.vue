<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :showOkBtn="false"
  >
    <TopTable v-if="ifTable" />
    <BasicForm
      @register="registerForm"
      v-else
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { nextTick, ref, unref } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import TopTable from '../settings/TopTable.vue';
import { useForm, BasicForm } from '@/components/Form';
import { hotForm, topForm } from './data';
import { newsSetUp } from '@/api/news';

const emit = defineEmits(['register', 'success']);

const ifTable = ref<boolean>(false);

const ifTop = ref<boolean>(false);

const title = ref<string>('');

const autoId = ref<number | undefined>();

const [registerModal, { setModalProps }] = useModalInner(async data => {
  ifTable.value = !!data.ifTable;

  ifTop.value = !!data.ifTop;

  title.value = data?.title || '';

  if (!unref(ifTable)) {
    await nextTick(async () => {
      await setProps({ schemas: unref(ifTop) ? topForm() : hotForm() });
      await clearValidate().then(async () => {
        const {
          hotNewsExcludeColumns,
          topSearchExcludeColumns,
          topMatchingIndex,
          topColumns,
          ...other
        } = await newsSetUp();

        autoId.value = other.autoId;

        setFieldsValue({
          ...other,
          hotNewsExcludeColumns: hotNewsExcludeColumns?.split(',') || [],
          topSearchExcludeColumns: topSearchExcludeColumns?.split(',') || [],
          topMatchingIndex: topMatchingIndex?.split(',') || [],
          topColumns: topColumns?.split(',') || [],
        });
      });
    });
  }

  setModalProps({ confirmLoading: false, showOkBtn: !unref(ifTable) });
});

const [registerForm, { setFieldsValue, validate, setProps, clearValidate }] = useForm({
  labelWidth: 200,
  showActionButtonGroup: false,
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const {
      hotNewsExcludeColumns,
      topSearchExcludeColumns,
      topMatchingIndex,
      topColumns,
      ...other
    } = await validate();

    emit('success', {
      values: {
        ...other,
        settingType: unref(ifTop) ? 'top' : 'hot',
        hotNewsExcludeColumns: hotNewsExcludeColumns?.join(',') || undefined,
        topSearchExcludeColumns: topSearchExcludeColumns?.join(',') || undefined,
        topMatchingIndex: topMatchingIndex?.join(',') || undefined,
        topColumns: topColumns?.join(',') || undefined,
        autoId: unref(autoId),
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
