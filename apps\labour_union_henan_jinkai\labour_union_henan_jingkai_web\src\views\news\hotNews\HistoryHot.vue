<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :showOkBtn="false"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'icomoon-free:cancel-circle',
                type: 'primary',
                label: '取消热门',
                onClick: handleUnhot.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { columns, formSchemas } from './data';
import { historyList, unhot } from '@/api/news';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { useMessage } from '@monorepo-yysz/hooks';

defineEmits(['register']);

const { createErrorModal, createConfirm, createSuccessModal } = useMessage();

const title = computed(() => {
  return '历史热门新闻';
});

const [registerModal, {}] = useModalInner(async () => {
  reload();
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: historyList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 4 },
  },
  useSearchForm: true,
  maxHeight: 430,
  bordered: true,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',

    fixed: undefined,
    width: 100,
  },
});

function handleUnhot(record) {
  const text = `设置`;

  createConfirm({
    iconType: 'warning',
    content: `请确定将${record.newsTitle}设置为非热门新闻吗？`,
    onOk: function () {
      unhot({
        ...record,
      }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `${text}成功` });
          reload();
        } else {
          createErrorModal({ content: `${text}失败，${message}` });
        }
      });
    },
  });
}
</script>
