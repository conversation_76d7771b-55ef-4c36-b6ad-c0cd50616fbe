<template>
  <div>
    <div class="text-xl text-[#2172f1] font-bold p-2 text-center">{{ title }}</div>
    <div class="hot-content">
      <BasicTable
        @register="registerTable"
        :dataSource="treeData"
      >
        <template #bodyCell="{ record, column }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  icon: 'bi:bookmark-star',
                  type: 'primary',
                  shape: 'circle',
                  tooltip: '设置热门',
                  onClick: handleHot.bind(null, record),
                },
                {
                  icon: 'carbon:task-view',
                  type: 'primary',
                  shape: 'circle',
                  tooltip: '预览',
                  onClick: handleView.bind(null, record),
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <ViewModal
      @register="registerView"
      :can-fullscreen="false"
      width="88%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, TableAction, useTable } from '@/components/Table';
import { columns } from './data';
import { newsGetOneNews, setPopular } from '@/api/news';
import { useModal } from '@/components/Modal';
import { find } from 'lodash-es';
import ViewModal from '@/views/news/channelNews/ViewModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';

const { createErrorModal, createConfirm, createSuccessModal } = useMessage();

const props = defineProps({
  title: String,
  treeData: Array as PropType<Recordable[]>,
  hotKey: String,
});

const emits = defineEmits(['reloadAll']);

const [registerTable, {}] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  useSearchForm: false,
  showTableSetting: false,
  bordered: true,
  pagination: false,
  showIndexColumn: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',

    fixed: undefined,
    width: 40,
  },
});

const [registerView, { openModal }] = useModal();

function handleView(record) {
  newsGetOneNews({ autoId: record.autoId }).then(res => {
    const { code, data, message: msg } = res;
    if (code === 200) {
      const { newsDetailsList, newsSource, newsClicks } = data as Recordable;

      const appDetail = find(newsDetailsList, v => v.platformType === '30');

      const zgDetail = find(newsDetailsList, v => v.platformType === '20');

      const pDetail = find(newsDetailsList, v => v.platformType === '10');

      openModal(true, {
        record: {
          appContent: appDetail && appDetail.newsDetailsContent,
          appTitle: appDetail && appDetail.newsDetailsTitle,
          appUrl: appDetail && appDetail.externalLinkAddress,
          zgContent: zgDetail && zgDetail.newsDetailsContent,
          zgUrl: zgDetail && zgDetail.externalLinkAddress,
          pContent: pDetail && pDetail.newsDetailsContent,
          pUrl: pDetail && pDetail.externalLinkAddress,
          source: newsSource,
          reading: newsClicks,
        },
      });
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}

function handleHot(record) {
  const text = `设置热门`;

  createConfirm({
    iconType: 'warning',
    content: `请确定将${record.newsTitle}设置为热门新闻吗？`,
    onOk: function () {
      setPopular({
        ...record,
        sourceListLogo: props.hotKey,
      }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `${text}成功` });

          emits('reloadAll');
        } else {
          createErrorModal({ content: `${text}失败，${message}` });
        }
      });
    },
  });
}
</script>

<style lang="less" module>
.tree-info {
  :global {
    padding: 15px;
  }
}
</style>
