import { BasicColumn } from '@/components/Table';

export function columns(): BasicColumn[] {
  return [
    {
      title: 'NO.',
      width: 30,
      dataIndex: 'serialNumber',
      customRender({ text }) {
        return (
          <span
            title={text}
            class={`text-red-500 font-semibold`}
          >
            {text}
          </span>
        );
      },
    },
    { title: '新闻名', width: 90, dataIndex: 'newsTitle' },
    { title: '区域', width: 60, dataIndex: 'companyName' },
    {
      title: '数量',
      width: 35,
      dataIndex: 'correlationQuantity',
      customRender({ text }) {
        return (
          <span
            title={text}
            class={`text-hex-[#0ea5e9] font-semibold`}
          >
            {text}
          </span>
        );
      },
    },
  ];
}
