import { FormSchema } from '@/components/Form';
import { useDictionary } from '@/store/modules/dictionary';
import { Tooltip } from 'ant-design-vue';
import { map } from 'lodash-es';
import { BasicColumn } from '@/components/Table';
import { listTree } from '@/api/category';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '标题',
      dataIndex: 'newsTitle',
      width: 200,
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '所属区域',
      dataIndex: 'companyId',
      width: 100,
      customRender: ({ text }) => {
        const dictName = dictionary.getDictionaryMap.get(`unionInformation_${text}`)?.dictName;
        return <Tooltip title={dictName}>{dictName}</Tooltip>;
      },
    },
    {
      title: '所属平台',
      dataIndex: 'platformType',
      width: 100,
      customRender: ({ text }) => {
        const textArr = text.split(',');
        const all = map(
          textArr,
          v => dictionary.getDictionaryMap.get(`appType_${v}`)?.dictName
        )?.join(',');
        return <Tooltip title={all}>{all}</Tooltip>;
      },
    },
    {
      title: '来源榜单',
      dataIndex: 'sourceListLogo',
      width: 100,
      customRender: ({ text }) => {
        const dictName = dictionary.getDictionaryMap.get(`sourceListLogo_${text}`)?.dictName;
        return <Tooltip title={dictName}>{dictName}</Tooltip>;
      },
    },
    {
      title: '设置时间',
      dataIndex: 'hotTime',
      width: 170,
    },
    {
      title: '发布时间',
      dataIndex: 'publishTime',
      width: 170,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'searchTitle',
      label: '新闻标题',
      colProps: { span: 7 },
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入新闻标题',
      },
    },
    {
      label: '所属区域',
      field: 'companyId',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('unionInformation'),
        };
      },
    },
    {
      label: '来源榜单',
      field: 'sourceListLogo',
      colProps: { span: 7 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('sourceListLogo'),
        };
      },
    },
  ];
};

export const topForm = (): FormSchema[] => {
  const dictionary = useDictionary();

  return [
    {
      field: 'whetherOpenTop',
      label: '是否开启自动置顶',
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      defaultValue: 'y',
      componentProps() {
        return {
          options: dictionary.getDictionaryOpt.get('YesOrNo'),
        };
      },
    },
    {
      field: 'topMatchingIndex',
      label: '自动置顶匹配指标',
      colProps: { span: 12 },
      required: true,
      ifShow: ({ values }) => values?.whetherOpenTop === 'y',
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        mode: 'multiple',
        options: dictionary.getDictionaryOpt.get('topMatchingIndex'),
      },
    },
    {
      field: 'collectThreshold',
      label: '收藏量阀值',
      colProps: { span: 12 },
      component: 'InputNumber',
      required: true,
      ifShow: ({ values }) =>
        values.topMatchingIndex?.includes('collectIndex') && values.whetherOpenTop === 'y',
      rulesMessageJoinLabel: true,
      className: '!w-full',
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'likeThreshold',
      label: '点赞量阀值',
      colProps: { span: 12 },
      component: 'InputNumber',
      required: true,
      ifShow: ({ values }) =>
        values.topMatchingIndex?.includes('likeIndex') && values.whetherOpenTop === 'y',
      rulesMessageJoinLabel: true,
      className: '!w-full',
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'shareThreshold',
      label: '分享量阀值',
      colProps: { span: 12 },
      component: 'InputNumber',
      required: true,
      ifShow: ({ values }) =>
        values.topMatchingIndex?.includes('shareIndex') && values.whetherOpenTop === 'y',
      rulesMessageJoinLabel: true,
      className: '!w-full',
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'readThreshold',
      label: '阅读量阀值',
      colProps: { span: 12 },
      component: 'InputNumber',
      required: true,
      ifShow: ({ values }) =>
        values.topMatchingIndex?.includes('readIndex') && values.whetherOpenTop === 'y',
      rulesMessageJoinLabel: true,
      className: '!w-full',
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'topColumns',
      label: '自动置顶栏目',
      colProps: { span: 12 },
      helpMessage: '可不选,不选就是全栏目',
      ifShow: ({ values }) => values?.whetherOpenTop === 'y',
      rulesMessageJoinLabel: true,
      component: 'ApiTreeSelect',
      componentProps: {
        api: listTree,
        params: {
          excludeParentCode: 'ya_an_sou_ye',
        },
        fieldNames: { label: 'categoryName', value: 'categoryId', children: 'children' },
        resultField: 'data',
        multiple: true,
        getPopupContainer: () => document.body,
        showSearch: true,
        treeNodeFilterProp: 'categoryName',
      },
      className: 'topColumns',
    },
  ];
};

export const hotForm = (): FormSchema[] => {
  return [
    { component: 'Divider', label: '热门新闻配置', field: '' },
    {
      field: 'likeSearchNumber',
      label: '点赞量榜单搜索条数',
      colProps: { span: 12 },
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      className: '!w-full',
      helpMessage: '限制1~999',
      componentProps: {
        min: 1,
        max: 999,
      },
    },
    {
      field: 'shareSearchNumber',
      label: '分享量榜单搜索条数',
      colProps: { span: 12 },
      component: 'InputNumber',
      helpMessage: '限制1~999',
      rulesMessageJoinLabel: true,
      className: '!w-full',
      componentProps: {
        min: 1,
        max: 999,
      },
    },
    {
      field: 'collectSearchNumber',
      label: '收藏量榜单搜索条数',
      colProps: { span: 12 },
      component: 'InputNumber',
      helpMessage: '限制1~999',
      rulesMessageJoinLabel: true,
      className: '!w-full',
      componentProps: {
        min: 1,
        max: 999,
      },
    },
    {
      field: 'readSearchNumber',
      label: '阅读量榜单搜索条数',
      colProps: { span: 12 },
      component: 'InputNumber',
      helpMessage: '限制1~999',
      rulesMessageJoinLabel: true,
      className: '!w-full',
      componentProps: {
        min: 1,
        max: 999,
      },
    },
    {
      field: 'hotNewsExcludeColumns',
      label: '热门新闻排除栏目',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      component: 'ApiTreeSelect',
      componentProps: {
        api: listTree,
        fieldNames: { label: 'categoryName', value: 'categoryId', children: 'children' },
        resultField: 'data',
        multiple: true,
        getPopupContainer: () => document.body,
      },
    },
  ];
};
