<template>
  <div class="pt-2 pl-15px">
    <div class="w-full flex items-center">
      <BasicForm
        @register="registerForm"
        class="w-full"
      />
      <a-button
        type="primary"
        auth="/hotNews/hotRuleSetting"
        @click="openCommon(true, { ifTable: false, ifTop: false, title: '热门规则设置' })"
      >
        热门规则设置
      </a-button>
      <a-button
        type="primary"
        auth="/hotNews/hotRecord"
        @click="openModal(true)"
        class="mx-2"
      >
        热门记录
      </a-button>
      <a-button
        type="primary"
        auth="/hotNews/setTheTopRule"
        @click="openCommon(true, { ifTable: false, ifTop: true, title: '置顶规则设置' })"
      >
        置顶规则设置
      </a-button>
      <a-button
        type="primary"
        auth="/hotNews/topRecord"
        class="mx-2"
        @click="openCommon(true, { ifTable: true, ifTop: false, title: '置顶记录' })"
      >
        置顶记录
      </a-button>
    </div>
    <Row>
      <Col
        :span="6"
        v-for="item in hot"
      >
        <HotTree
          :title="item.title"
          :hotKey="item.key"
          :treeData="item.treeData"
          @reloadAll="handleReload"
        />
      </Col>
    </Row>
    <HistoryHot
      @register="registerModal"
      :can-fullscreen="false"
      width="75%"
    />
    <CommonModal
      @register="registerCommon"
      :can-fullscreen="false"
      width="75%"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { Row, Col } from 'ant-design-vue';
import { ref, onMounted } from 'vue';
import HotTree from './components/HotTree.vue';
import { newsSet, rankingList } from '@/api/news';
import { useModal } from '@/components/Modal';
import HistoryHot from './HistoryHot.vue';
import CommonModal from './CommonModal.vue';
import { BasicForm, useForm } from '@/components/Form';
import { useDictionary } from '@/store/modules/dictionary';
import { useMessage } from '@monorepo-yysz/hooks';

const hot = ref<Recordable[]>([]);

const { createErrorModal, createSuccessModal } = useMessage();

const dictionary = useDictionary();

const [registerModal, { openModal }] = useModal();

const [registerCommon, { openModal: openCommon, closeModal: closeCommon }] = useModal();

const [registerForm, { getFieldsValue }] = useForm({
  labelWidth: 120,
  schemas: [
    {
      field: 'queryCompanyId',
      component: 'Select',
      label: '所属区域',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('unionInformation'),
        };
      },
    },
  ],
  actionColOptions: { span: 3 },
  autoSubmitOnEnter: true,
  submitFunc: () => init(true),
  resetFunc: () => init(false),
});

function handleSuccess({ values }) {
  newsSet(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `提交成功`,
      });
      init(false);
      closeCommon();
    } else {
      createErrorModal({
        content: `提交失败! ${message}`,
      });
    }
  });
}

async function initTree() {
  await init();
}

async function init(ifSub?: boolean) {
  const {
    likeList,
    readList,
    collectList,
    shareList,
    collectSearchNumber,
    likeSearchNumber,
    readSearchNumber,
    shareSearchNumber,
  } = await rankingList(ifSub ? getFieldsValue?.() : undefined);

  hot.value = [
    { title: '阅读量TOP:' + readSearchNumber, treeData: readList || [], key: 'readListLogo' },
    { title: '分享量TOP:' + shareSearchNumber, treeData: shareList || [], key: 'shareListLogo' },
    {
      title: '收藏量TOP:' + collectSearchNumber,
      treeData: collectList || [],
      key: 'collectListLogo',
    },
    { title: '点赞量TOP:' + likeSearchNumber, treeData: likeList || [], key: 'likeListLogo' },
  ];
}

function handleReload() {
  init();
}

onMounted(() => {
  initTree();
});
</script>
