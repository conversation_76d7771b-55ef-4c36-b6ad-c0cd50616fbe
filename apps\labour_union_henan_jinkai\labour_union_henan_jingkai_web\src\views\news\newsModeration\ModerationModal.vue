<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts">
import { defineComponent, ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { Row, Col, Divider, RadioGroup } from 'ant-design-vue';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from './data';
import { isArray } from 'lodash-es';

export default defineComponent({
  name: 'ModerationModal',
  components: { BasicModal, Row, Col, Divider, BasicForm, RadioGroup },
  emits: ['register', 'success', 'cancel', 'successBatch'],
  setup(_, { emit }) {
    const autoId = ref<string | string[]>('');

    const record = ref<Recordable>();

    const title = computed(() => {
      return `审核新闻${unref(record) ? unref(record)?.newsTitle : ''}`;
    });

    const [registerForm, { resetFields, validate }] = useForm({
      labelWidth: 100,
      schemas: modalFormItem(),
      showActionButtonGroup: false,
    });

    const [registerModal, { setModalProps }] = useModalInner(async data => {
      await resetFields();

      autoId.value = data.autoId;
      record.value = data.record;

      setModalProps({ confirmLoading: false });
    });

    async function handleSubmit() {
      const values = await validate();
      if (isArray(unref(autoId))) {
        emit('successBatch', { values, autoId: unref(autoId) });
      } else {
        emit('success', { values, autoId: unref(autoId) });
      }
    }

    return {
      registerModal,
      registerForm,
      handleSubmit,
      title,
    };
  },
});
</script>
