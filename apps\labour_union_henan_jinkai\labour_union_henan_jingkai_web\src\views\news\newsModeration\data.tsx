import { Tooltip } from 'ant-design-vue';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { useInfos } from '@/store/modules/infos';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const infos = useInfos();
  return [
    {
      title: '新闻标题',
      dataIndex: 'newsTitle',
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '提交人',
      dataIndex: 'submitter',
      width: 170,
      // sorter: true,
    },
    {
      title: '提交时间',
      dataIndex: 'submissionTime',
      width: 170,
      // sorter: true,
    },
    {
      title: '审核状态',
      dataIndex: 'newsAuditStatus',
      width: 80,
      customRender: ({ text, record }) => {
        const newsAuditStatus = dictionary.getDictionaryMap.get(
          `newsAuditStatus_${text}`
        )?.dictName;
        const { whetherAuditRecords } = record;
        if (whetherAuditRecords) {
          async function handleClick() {
            infos.setRecord(record);
            infos.setVisible(true);
          }

          return (
            <a
              title={newsAuditStatus}
              onClick={() => handleClick()}
            >
              <span>{newsAuditStatus}</span>
            </a>
          );
        } else {
          return <Tooltip title={newsAuditStatus}>{newsAuditStatus}</Tooltip>;
        }
      },
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
      width: 170,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'newsTitle',
      label: '新闻标题',
      colProps: { span: 6 },
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入新闻标题',
      },
    },
    {
      label: '审核状态',
      field: 'newsAuditStatus',
      colProps: { span: 6 },
      component: 'Select',
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsAuditStatus'),
          placeholder: '请选择审核状态',
        };
      },
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'newsAuditStatus',
      label: '是否通过',
      component: 'RadioGroup',
      required: true,
      componentProps: {
        options: [
          { label: '审核通过', value: 'pass' },
          { label: '审核拒绝', value: 'refuse' },
        ],
      },
    },
    {
      field: 'newsRejectCategory',
      label: '拒绝原因',
      component: 'Select',
      required: false,
      componentProps: {
        placeholder: '请选择拒绝原因',
        options: dictionary.getDictionaryOpt.get('newsRejectCategory'),
      },
      ifShow({ values }) {
        return values.newsAuditStatus === 'refuse';
      },
    },
    {
      field: 'newsAuditInstruction',
      label: '审核意见',
      // required: function (values) {
      //   if (values.model.newsAuditStatus == 'refuse') {
      //     return true;
      //   } else {
      //     return false;
      //   }
      // },
      required: false,
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入审核意见',
        showCount: true,
        maxlength: 100,
      },
    },
  ];
};
