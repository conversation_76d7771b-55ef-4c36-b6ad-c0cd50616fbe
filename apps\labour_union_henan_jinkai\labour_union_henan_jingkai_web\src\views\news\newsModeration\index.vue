<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleAuditBatch"
          auth="/newsModeration/auditBatch"
        >
          批量审核
        </a-button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '预览',
                icon: 'ant-design:eye-filled',
                type: 'primary',

                onClick: handleRecordView.bind(null, record),
                auth: '/newsModeration/view',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                disabled: record.newsAuditStatus !== '20',
                onClick: handleAudit.bind(null, record),
                auth: '/newsModeration/audit',
              },
            ]"
          >
          </TableAction>
        </template>
      </template>
    </BasicTable>
    <ViewModal
      @register="registerView"
      :can-fullscreen="false"
      width="40%"
    ></ViewModal>
    <ModerationModal
      @register="registerModerationModal"
      :can-fullscreen="false"
      width="40%"
      @success="handleSuccess"
      @success-batch="handleSuccessBatch"
    ></ModerationModal>
    <!--审核记录-->
    <BasicModal
      v-model:open="auditVisible"
      @cancel="handleCancel"
      width="40%"
      :title="`${line?.newsTitle || ''}审核记录`"
      @ok="handleCancel"
      :can-fullscreen="false"
    >
      <div class="p-5 h-full overflow-y-auto">
        <Timeline>
          <TimelineItem
            v-for="item in aduitLines"
            :color="item.newsAuditStatus === 'pass' ? 'green' : 'red'"
          >
            <div>审核时间：{{ item.createTime }}</div>
            <div>审核人：{{ item.createUser }}</div>
            <div>审核状态：{{ item.newsAuditStatus === 'pass' ? '通过' : '拒绝' }}</div>
            <div v-if="item.newsAuditStatus === 'refuse'"
              >拒绝原因：{{
                dictionary.getDictionaryMap.get(`newsRejectCategory_${item.newsRejectCategory}`)
                  ?.dictName || '无'
              }}
            </div>
            <div>审核意见：{{ item.newsAuditInstruction || '无' }}</div>
          </TimelineItem>
        </Timeline>
      </div>
      <template #footer> </template>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, TableAction, useTable } from '@/components/Table';
import { columns, formSchemas } from './data';
import { newsAudit, newsAuditList, NewsAuditRecord, newsBatchAudit } from '@/api/news';
import { Modal, Timeline, TimelineItem } from 'ant-design-vue';
import { useModal, BasicModal } from '@/components/Modal';
import ViewModal from '@/views/news/channelNews/ViewModal.vue';
import ModerationModal from './ModerationModal.vue';
import { computed, createVNode, ref, unref, watch } from 'vue';
import { CloseCircleFilled } from '@ant-design/icons-vue';
import { useInfos } from '@/store/modules/infos';
import { useDictionary } from '@/store/modules/dictionary';
import { useMessage } from '@monorepo-yysz/hooks';

const { createErrorModal, createSuccessModal } = useMessage();

const infos = useInfos();

const auditVisible = ref(false);

const line = computed(() => {
  return infos.getRecord as Recordable;
});

const aduitLines = ref<Recordable[]>([]);

const dictionary = useDictionary();

const [registerView, { openModal: openView }] = useModal();

const [registerModerationModal, { openModal, closeModal }] = useModal();

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: '/newsModeration/auditBatch',
  showIndexColumn: false,
  api: newsAuditList,
  searchInfo: {
    orderBy: 'submission_time',
    sortType: 'desc',
    nextLevelFlag: false,
  },
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.newsAuditStatus !== '20',
    }),
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',

    fixed: undefined,
    auth: ['/newsModeration/view', '/newsModeration/audit'],
  },
});

function handleCancel() {
  infos.setVisible(false);
  infos.setRecord(null);
}

//预览
function handleRecordView(record) {
  // newsGetOneNews({ autoId: record.autoId }).then(res => {
  //   const { code, data, message: msg } = res;
  //   if (code === 200) {
  //     const { newsDetailsList, newsSource, keywords } = data as Recordable;

  //     const appDetail = find(newsDetailsList, v => v.platformType === '30');

  //     const zgDetail = find(newsDetailsList, v => v.platformType === '20');

  //     const pDetail = find(newsDetailsList, v => v.platformType === '10');

  //     openView(true, {
  //       record: {
  //         appContent: appDetail && appDetail.newsDetailsContent,
  //         appUrl: appDetail && appDetail.externalLinkAddress,
  //         appTitle: appDetail && appDetail.newsDetailsTitle,
  //         zgContent: zgDetail && zgDetail.newsDetailsContent,
  //         zgUrl: zgDetail && zgDetail.externalLinkAddress,
  //         pContent: pDetail && pDetail.newsDetailsContent,
  //         pUrl: pDetail && pDetail.externalLinkAddress,
  //         source: newsSource,
  //         reading: record.newsClicks,
  //         keywords: keywords,
  //       },
  //     });
  //   } else {
  //     createErrorModal({ content: `${msg}` });
  //   }
  // });

  openView(true, {
    record: {
      newsId: record?.newsId,
    },
  });
}

//handleAudit
function handleAudit(record) {
  openModal(true, { autoId: record.autoId, record });
}

//batch
function handleAuditBatch() {
  const rows = getSelectRows();
  if (!rows || rows.length === 0) {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '请选择至少一条数据进行审核！',
      okText: '确认',
      closable: true,
    });
    return false;
  }
  openModal(true, { autoId: rows.map(v => v.autoId) });
}

//单个审批
function handleSuccess({ values, autoId }) {
  newsAudit({ newsAuditRecord: { ...values }, newsInfoList: [autoId] }).then(res => {
    const { code, message: msg } = res;
    if (code === 200) {
      createSuccessModal({ content: '操作成功' });
      reload();
      closeModal();
      clearSelectedRowKeys();
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}

//多个审批
function handleSuccessBatch({ values, autoId }) {
  newsBatchAudit({ newsAuditRecord: { ...values }, newsInfoList: [...autoId] }).then(res => {
    const { code, message: msg } = res;
    if (code === 200) {
      createSuccessModal({ content: '操作成功' });
      reload();
      closeModal();
      clearSelectedRowKeys();
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}

watch(
  () => infos.getVisible,
  () => {
    auditVisible.value = infos.getVisible;
  }
);

watch(line, async () => {
  if (unref(line)?.newsId) {
    aduitLines.value = await NewsAuditRecord({ newsId: unref(line)?.newsId });
  }
});
</script>
