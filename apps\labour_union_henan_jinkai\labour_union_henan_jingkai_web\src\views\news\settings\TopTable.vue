<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '明细',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <TopTableMdal
      @register="registerModal"
      :can-fullscreen="false"
      width="70%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns } from './data';
import { topList } from '@/api/news';
import TopTableMdal from './TopTableMdal.vue';

const [registerTable, {}] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  searchInfo: {
    orderBy: 'set_date',
    sortType: 'desc',
  },
  maxHeight: 420,
  api: topList,
  formConfig: {
    labelWidth: 120,
    schemas: [
      {
        field: 'dateTime',
        label: '设置时间',
        component: 'RangePicker',
        componentProps: {
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        colProps: {
          span: 10,
        },
      },
    ],
    autoSubmitOnEnter: true,
  },
  beforeFetch(params) {
    const { dateTime, ...other } = params;
    return {
      ...other,
      setDateStart: dateTime && dateTime.length === 2 ? dateTime[0] : undefined,
      setDateEnd: dateTime && dateTime.length === 2 ? dateTime[1] : undefined,
    };
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 100,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal }] = useModal();

//详情
function handleView(record) {
  openModal(true, { isUpdate: true, disabled: true, record });
}
</script>
