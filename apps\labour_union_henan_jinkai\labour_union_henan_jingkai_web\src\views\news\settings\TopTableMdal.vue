<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :showOkBtn="false"
  >
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { topNews } from '@/api/news';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useTable, BasicTable } from '@/components/Table';

defineEmits(['register']);

const record = ref<Recordable>();

const title = computed(() => {
  return `${unref(record)?.setDate || ''}自动置顶明细`;
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: [
    {
      dataIndex: 'newsTitle',
      title: '新闻标题',
    },
    {
      dataIndex: 'newsClicks',
      title: '阅读量',
    },
    {
      dataIndex: 'shareVolume',
      title: '分享量',
    },
    {
      dataIndex: 'likeVolume',
      title: '点赞量',
    },
    {
      dataIndex: 'collectVolume',
      title: '收藏量',
    },
  ],
  showIndexColumn: false,
  api: topNews,
  formConfig: {
    labelWidth: 120,
    schemas: [
      {
        field: 'newsTitle',
        label: '新闻标题',
        component: 'Input',
        rulesMessageJoinLabel: true,
        colProps: { span: 8 },
      },
    ],
    autoSubmitOnEnter: true,
  },
  beforeFetch(params) {
    params.autoTopRecordId = unref(record)?.autoTopRecordId;
    return params;
  },
  maxHeight: 420,
  immediate: false,
  useSearchForm: true,
  bordered: true,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  record.value = data.record;
  await reload({
    searchInfo: {
      autoTopRecordId: unref(record)?.autoTopRecordId,
    },
  });
  setModalProps({ confirmLoading: false });
});
</script>
