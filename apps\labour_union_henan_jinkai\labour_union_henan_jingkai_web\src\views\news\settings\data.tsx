import { FormSchema } from '@/components/Form';
import { useDictionary } from '@/store/modules/dictionary';
import { Tooltip } from 'ant-design-vue';
import { map } from 'lodash-es';
import { BasicColumn } from '@/components/Table';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '自动置顶',
      dataIndex: 'whetherOpenTop',
      width: 80,
      ellipsis: true,
      customRender: ({ text }) => {
        const dictName = dictionary.getDictionaryMap.get(`YesOrNo_${text}`)?.dictName;
        return <Tooltip title={dictName}>{dictName}</Tooltip>;
      },
    },
    {
      title: '设置时间',
      dataIndex: 'setDate',
      width: 150,
    },
    {
      title: '置顶指标',
      dataIndex: 'topMatchingIndex',
      ellipsis: true,
      customRender: ({ text }) => {
        const textArr = text?.split(',');
        const all = map(
          textArr || [],
          v => dictionary.getDictionaryMap.get(`topMatchingIndex_${v}`)?.dictName
        )?.join(',');
        return <Tooltip title={all}>{all}</Tooltip>;
      },
    },
    {
      title: '收藏量阀值',
      dataIndex: 'collectThreshold',
      width: 100,
    },
    {
      title: '点赞量阀值',
      dataIndex: 'likeThreshold',
      width: 100,
    },
    {
      title: '分享量阀值',
      dataIndex: 'shareThreshold',
      width: 100,
    },
    {
      title: '阅读量阀值',
      dataIndex: 'readThreshold',
      width: 100,
    },

    {
      title: '置顶新闻数',
      dataIndex: 'satisfyNumber',
      width: 150,
    },
    {
      title: '自动置顶栏目',
      dataIndex: 'topColumnsName',
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'executeResultExplain',
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'searchTitle',
      label: '新闻标题',
      colProps: { span: 7 },
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入新闻标题',
      },
    },
    {
      label: '所属区域',
      field: 'companyId',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('unionInformation'),
        };
      },
    },
    {
      label: '来源榜单',
      field: 'sourceListLogo',
      colProps: { span: 7 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('sourceListLogo'),
        };
      },
    },
  ];
};
