<template>
  <BasicModal
      :canFullscreen="false"
      :title="title"
      show-ok-btn
      v-bind="$attrs"
      @ok="handleOk"
      @register="registerModal"
      @visible-change="visibleChange"
  >
    <BasicTable
        @register="registerTable"
    ></BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicTable, useTable } from '@/components/Table';
import { computed, ref } from 'vue';
import { companyColumns, companyFormSchemas } from './data';
// 商户商品列表
import { findCompanyList } from '@/api/productManagement';

const emit = defineEmits(['success', 'register']);
const modalVisible = ref(null);
const title = computed(() => {
      return '选择商户';
});
const columns = computed(() => {
  return companyColumns();
});
const formSchemas = computed(() => {
  return companyFormSchemas();
});

const visibleChange = visible => {
  modalVisible.value = visible;
};
const [registerModal, { closeModal }] = useModalInner(async data => {
  await clearSelectedRowKeys();
  let api = findCompanyList;
  setProps({
    api,
    pagination: {
      current: 1,
    },
  });
  const params = {
    nextLevelFlag:'true',
    systemQueryType: 'manage',
  };
  // 清空表单搜索条件
  getForm().resetFields();
  reload({
    searchInfo: params,
  });
});
const [
  registerTable,
  { reload, getSelectRows, clearSelectedRowKeys, setProps, getForm },
] = useTable({
  columns: columns,
  beforeFetch: params => {
    params.isGift = 'n';
    return { ...params };
  },
  formConfig: {
    labelWidth: 120,
    actionColOptions: {
      span: 3,
    },
    autoSubmitOnEnter: true,
    schemas: formSchemas as any,
  },
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
  maxHeight: 420,
  rowSelection: {
    type: 'checkbox',
  },
});
// 提交
function handleOk() {
  const rows = getSelectRows();
  const autoIdList = rows?.map(t => t.autoId
  );
  emit('success', autoIdList);
  closeModal();
}
</script>

<style scoped></style>
