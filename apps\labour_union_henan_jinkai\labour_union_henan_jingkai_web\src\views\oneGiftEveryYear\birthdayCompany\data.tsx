import { findIndustryType } from '@/api/industrySet';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { useUserStore } from '@/store/modules/user';
import { searchNextUnionForm } from '@/utils/searchNextUnion';
import { validatePhone } from '@monorepo-yysz/utils';
import { Image } from 'ant-design-vue';
import { cloneDeep, filter } from 'lodash-es';
import { nextTick } from 'vue';

// 列表配置
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '普惠商户名称',
      dataIndex: 'companyName',
    },
    {
      title: '所属工会',
      dataIndex: 'labourUnionName',
    },
    {
      title: '商家封面',
      dataIndex: 'companyIcon',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          />
        );
      },
    },
    {
      title: '联系人',
      dataIndex: 'contractName',
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'desensitizationPhone',
      width: 120,
    },
    {
      title: '普惠商户地址',
      dataIndex: 'address',
      width: 200,
    },
    {
      title: '普惠商户状态',
      dataIndex: 'state',
      width: 120,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`state_${text}`)?.dictName}</span>;
      },
    },
  ];
};

//顶部搜索了配置
export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'companyName',
      label: '普惠商户名称',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { placeholder: '请输入普惠商户名称' },
    },

    {
      field: 'state',
      label: '普惠商户状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('state'),
          placeholder: '请选择普惠商户状态',
        };
      },
    },
    ...searchNextUnionForm(),
  ];
};

//新增弹框
export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: '',
      label: '账号信息',
      component: 'Divider',
    },
    {
      field: 'nickname',
      label: '用户名',
      required: true,
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入用户名',
        autocomplete: 'off',
      },
    },
    {
      field: 'account',
      label: '账号',
      component: 'Input',
      colProps: { span: 12 },
      rules: [{ required: true, validator: validatePhone, trigger: ['change', 'blur'] }],
      componentProps: {
        placeholder: '请输入(川工之家app注册/登录手机号)',
        autocomplete: 'off',
      },
    },
    {
      field: '',
      label: '交易配置信息',
      component: 'Divider',
    },
    {
      field: 'accountNickname',
      label: '开户户名',
      required: true,
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入开户户名',
        autocomplete: 'off',
      },
    },
    {
      field: 'incomeAccountNumber',
      label: '收款银行卡号',
      required: true,
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入收款银行卡号',
        autocomplete: 'off',
      },
    },
    {
      field: 'transPlatformBusinessId',
      label: '交易平台业务ID',
      required: true,
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入交易平台业务ID',
        autocomplete: 'off',
      },
    },

    // {
    //   field: '',
    //   label: '集市信息',
    //   ifShow:({values,disabled}) =>{
    //     return disabled===false
    //   },
    //   component: 'Divider',
    // },
    //
    // {
    //   field: 'isBelongMarket',
    //   label: '是否加入集市',
    //   colProps: { span: 12 },
    //   component: 'RadioGroup',
    //   defaultValue: 'n',
    //   ifShow: false,
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get(`YesOrNo`) as RadioGroupChildOption[],
    //   },
    // },
    {
      field: '',
      label: '商户信息',
      component: 'Divider',
    },

    {
      field: 'companyName',
      label: '商户名称',
      required: true,
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入普惠商户名称',
        autocomplete: 'off',
      },
    },

    {
      field: 'contractName',
      label: '联系人姓名',
      required: true,
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入联系人姓名',
        autocomplete: 'off',
      },
    },
    {
      field: 'contractPhone',
      label: '联系人电话',
      required: true,
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入联系人电话',
        autocomplete: 'off',
      },
    },
    /*    {
      field: 'identityNum',
      label: '联系人证件号码',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
      slot:'identityNumber',
      componentProps: {
        placeholder: '请选择联系人证件号码',
        autocomplete: 'off',
      },
    },*/
    {
      field: 'companyType',
      label: '主体类型',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: filter(
          cloneDeep(dictionary.getDictionaryOpt.get('companyType')),
          v => v.value === 'merchant'
        ),
      },
    },

    {
      field: 'areaCode',
      label: '所属区县',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          options: dictionary.getDictionaryOpt.get('regionCode'),
        };
      },
    },
    {
      field: 'industryId',
      label: '所属行业',
      required: true,
      component: 'ApiSelect',
      colProps: { span: 12 },
      ifShow:false,
      itemProps: {
        autoLink: true,
      },
      componentProps: ({ formActionType }) => {
        return {
          placeholder: '请选择行业类型',
          api: findIndustryType,
          resultField: 'data',
          params: {
            pageSize: 0,
            industryUseType: 'merchant',
            treeFlag: false,
            pid: 0,
          },
          alwaysLoad: true,
          immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.industryName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'industryName', value: 'autoId' },
        };
      },
    },
    {
      field: 'companyIcon',
      label: '普惠商户图标',
      required: true,
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
      componentProps: {
        operateType: 65,
      },
    },
    {
      field: 'introduce',
      label: '商户简介',
      required: true,
      component: 'InputTextArea',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'address',
      label: '经营地点',
      required: true,
      component: 'MapSelect',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      rest: true,
      componentProps({ formModel }) {
        return {
          onChangeLnglat: lnglat => (formModel['addressCoordinate'] = lnglat),
          lnglat: formModel['addressCoordinate'],
        };
      },
    },

    {
      field: 'addressCoordinate',
      label: '地址坐标',
      colProps: { span: 24 },
      component: 'ShowSpan',
      rulesMessageJoinLabel: true,
      show: false,
    },
  ];
};

//商户列表
export const companyColumns = (): BasicColumn[] => {
  const userStore = useUserStore();
  const dictionary = useDictionary();

  return [
    {
      title: '商户名称',
      dataIndex: 'companyName',
    },
    {
      title: '所属工会',
      dataIndex: 'labourUnionName',
    },
    {
      title: '商家封面',
      dataIndex: 'companyIcon',
      width: 150,
      customRender: ({ text }) => {
        return (
            <Image
                src={userStore.getPrefix + text}
                width={50}
                height={50}
            />
        );
      },
    },
    {
      title: '普惠商户状态',
      dataIndex: 'state',
      width: 150,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`state_${text}`)?.dictName}</span>;
      },
    },
  ];
};

//选择所属工会弹框筛选条件
export const companyFormSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'companyName',
      label: '商户名称',
      colProps: { span: 5 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'state',
      label: '普惠商户状态',
      component: 'Select',
      colProps: { span: 5 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('state'),
          placeholder: '请选择普惠商户状态',
        };
      },
    },
    ...searchNextUnionForm(),
  ];
};
