<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/birthdayCompany/add"
        >
          选择商户
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/birthdayCompany/view',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '移除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/birthdayCompany/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <MerchantsModal
      @register="registerModal"
      :can-fullscreen="false"
      width="50%"
    />
    <CompanyListModal
        @register="companyRegisterModal"
        :can-fullscreen="false"
        @success="handleSuccess"
        width="80%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import MerchantsModal from './MerchantsModal.vue';
import {chooseGiftCompany, list, removeGiftCompany, view,} from '@/api/merchants';
import { useMessage } from '@monorepo-yysz/hooks';
import CompanyListModal from "@/views/oneGiftEveryYear/birthdayCompany/CompanyListModal.vue";

const { createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  authInfo: ['/birthdayCompany/add'],
  beforeFetch: params => {
    params.isGift = 'y';
    return params;
  },
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
    actionColOptions: { span: 3 },
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 250,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/birthdayCompany/view',
      '/birthdayCompany/delete',
    ],
  },
});

const { createConfirm } = useMessage();
const [registerModal, { openModal, closeModal }] = useModal();
const [companyRegisterModal, { openModal: companyOpenModal, closeModal: companyCloseModal }] = useModal();

// 选择商户
function handleClick() {
  companyOpenModal(true, { isUpdate: false, disabled: false });
}

// 详情
function handleView(record: Recordable<any>) {
  view({ ...record }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

// 新增一岁一礼商家
function handleSuccess(autoIdList) {
  chooseGiftCompany(autoIdList).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `新增成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `新增失败！${message}。`,
      });
    }
  });
}

// 移除一岁一礼商家
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要从一岁一礼专区移除[${record.companyName}]商家吗?`,
    onOk: function () {
      removeGiftCompany({autoId:record.autoId}).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `移除成功` });
          reload();
        } else {
          createErrorModal({ content: `移除失败，${message}` });
        }
      });
    },
  });
}
</script>
