import { BasicColumn, FormSchema } from '/@/components/Table';
import {Image} from 'ant-design-vue';
import {useUserStore} from "@/store/modules/user";
import {uploadApi} from "@/api/sys/upload";

export const columns = (): BasicColumn[] => {
  const userStore = useUserStore();
  return [
    {
      title: '模板编号',
      dataIndex: 'templateCode',
    },
    {
      title: '弹窗图片',
      dataIndex: 'templatePicture',
      customRender: ({ text }) => {
        return (
            <Image
                src={userStore.getPrefix + text}
                style={{ maxWidth: '180px', maxHeight: '42px' }}
            ></Image>
        );
      },
    },
    {
      title: '背景图片',
      dataIndex: 'backgroundPicture',
      customRender: ({ text }) => {
        return (
            <Image
                src={userStore.getPrefix + text}
                style={{ maxWidth: '180px', maxHeight: '42px' }}
            ></Image>
        );
      },
    },
    {
      title: '列表图片',
      dataIndex: 'listPictures',
      customRender: ({ text }) => {
        return (
            <Image
                src={userStore.getPrefix + text}
                style={{ maxWidth: '180px', maxHeight: '42px' }}
            ></Image>
        );
      },
    },
    {
      title: '按钮内容',
      dataIndex: 'buttonContent',
    },
    {
      title: '启用状态',
      dataIndex: 'state',
      customRender({ text }) {
        const name = text === true? '启用' : '禁用';
        const color = text === true ? '#67C23A' : '#F56C6C';
        return <span title={name} style={{color}}>{name}</span>;
      },
    },
  ];
};

// 表单
export const modalFormItem = (disabled: boolean,isUpdate): FormSchema[] => {
  return [
    {
      field: 'templateCode',
      label: '模板编号',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 24 },
      componentProps: {
        disabled: true,
        showCount: true,
        maxlength: 20,
      },
    },
    {
      field: 'templatePicture',
      label: '弹窗图片',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      component: 'Upload',
      required: true,
      componentProps: {
        disabled: true,
        api: uploadApi,
        maxNumber: 1,
        maxSize: 1,
      },
    },
    {
      field: 'backgroundPicture',
      label: '背景图片',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      component: 'Upload',
      required: true,
      componentProps: {
        disabled: true,
        api: uploadApi,
        maxNumber: 1,
        maxSize: 1,
      },
    },
    {
      field: 'listPictures',
      label: '列表图片',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      component: 'Upload',
      required: true,
      componentProps: {
        disabled: true,
        api: uploadApi,
        maxNumber: 1,
        maxSize: 1,
      },
    },
    {
      field: 'buttonColor',
      label: '按钮颜色',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 24 },
      componentProps: {
        showCount: true,
        maxlength: 50,
      },
    },
    {
      field: 'buttonContent',
      label: '按钮内容',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 24 },
      componentProps: {
        showCount: true,
        maxlength: 10,
      },
    },
    {
      field: 'state',
      label: '是否启用',
      component: 'Switch',
      rulesMessageJoinLabel: true,
      colProps: { span: 24 },
      componentProps: function () {
        return {
          options: [
            { label: '启用', value: true },
            { label: '禁用', value: false },
          ],
        }
      },
    },
  ];
};
