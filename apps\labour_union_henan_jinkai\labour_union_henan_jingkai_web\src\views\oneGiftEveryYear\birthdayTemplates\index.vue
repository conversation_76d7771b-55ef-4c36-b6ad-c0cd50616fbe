<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record, true),
              auth: '/birthdayTemplates/view',
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              onClick: handleView.bind(null, record, false),
              auth: '/birthdayTemplates/edit',
            },
            {
                  icon: record.state ?'icon-park-outline:remind-disable':'icon-park-outline:remind',
                  label: record.state ?'禁用':'启用',
                  type: 'primary',
                  danger: record.state,
                  onClick: EnableDisable.bind(null,record),
                  auth: '/birthdayTemplates/disable',
            },
          ]" />
        </template>
      </template>
    </BasicTable>
    <handleModal @register="registerModal" @success="handleSuccess" :can-fullscreen="false" width="40%">
    </handleModal>
  </div>
</template>

<script lang="ts" setup>
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { columns } from './data';
import { useModal } from '/@/components/Modal';
import handleModal from './handleModal.vue';
import { findVoList,saveOrUpdate,changeState  } from '@/api/oneGiftEveryYear/birthdayTemplates';
import { useMessage } from '@monorepo-yysz/hooks';
const { createConfirm, createErrorModal, createSuccessModal } = useMessage();


const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  bordered: true,
  showIndexColumn: false,
  api: findVoList,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/birthdayTemplates/view',
      '/birthdayTemplates/edit',
      '/birthdayTemplates/disable',
    ],
  },
});

// 弹窗事件
const [registerModal, { openModal, closeModal }] = useModal();

function handleView(record, disabled) {
  openModal(true, {
    record,
    isUpdate: true,
    disabled,
  });
}

function handleSuccess({ isUpdate, values }) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '修改' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '修改' : '新增'}失败! ${message}`,
      });
    }
  });
}

//启用禁用
function EnableDisable(record) {
  const text = !record.state ? '启用' : '禁用';
  const state = !record.state;

  createConfirm({
    iconType: 'warning',
    content: `请确认要${text}模板${record.templateCode}吗`,
    onOk: function () {
      changeState({ autoId: record.autoId, state:state }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `${text}成功` });
          reload();
        } else {
          createErrorModal({ content: `${text}失败，${message}` });
        }
      });
    },
  });
}
</script>
