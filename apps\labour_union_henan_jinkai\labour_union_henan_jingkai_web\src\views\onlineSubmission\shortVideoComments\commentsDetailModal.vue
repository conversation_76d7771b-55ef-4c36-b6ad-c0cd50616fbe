<template>
  <BasicModal
      @register="registerModal"
      v-bind="$attrs"
      :title="title"
      :wrap-class-name="$style['recruit-modal']"
  >
    <BasicForm
        @register="registerForm"
        :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from './data';
import { maxSortNumber } from '@/api/system/userLabel';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const title = computed(() => {
  return `${unref(record)?.userName || ''}--详情`;
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(unref(isUpdate));
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
    });
  } else {
    await maxSortNumber({}).then(res => {
      const { data } = res;
      setFieldsValue({
        sortNumber: data,
      });
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

</script>
<style lang="less" module>
.recruit-modal {
  :global {
    .ant-input-number {
      width: 100% !important;
    }
  }
}
</style>
