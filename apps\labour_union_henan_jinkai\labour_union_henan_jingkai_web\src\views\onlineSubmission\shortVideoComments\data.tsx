import { cloneDeep, filter } from 'lodash-es';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import {useUserStore} from "@/store/modules/user";
const userStore = useUserStore();

export const columns = (): BasicColumn[] => {

  return [
    {
      title: '评论内容',
      dataIndex: 'content',
    },
    {
      title: '评论人员',
      dataIndex: 'userName',
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
    },
    {
      title: '短视频标题',
      dataIndex: 'title',
    },
    {
      title: '是否公开',
      dataIndex: 'state',
      customRender: ({ text }) => {
        const state = text?'是':'否';
        return <span title={state}>{state}</span>;
      },
    },
    {
      title: '评论时间',
      dataIndex: 'createTime',
    }
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'title',
      label: '短视频标题',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'userName',
      label: '评论人员',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'publicityState',
      label: '是否公开',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '是', value: true },
            { label: '否', value: false },
          ],
        }
      },
    },
  ];
};


export const modalFormItem = (isUpdate: boolean): FormSchema[] => {
  return [
    {
      field: 'content',
      label: '评论内容',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'userName',
      label: '评论人员',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'companyName',
      label: '所属工会',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'parentId',
      label: '父级id',
      ifShow:false,
    },
    {
      field: 'replyUserName',
      label: '回复人员',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      ifShow({ values }) {
        console.info(values.parentId)
        return values.parentId !== 0;
      },
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'state',
      label: '是否公开',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '是', value: true },
            { label: '否', value: false },
          ],
        }
      },
    },
    {
      field: 'createTime',
      label: '创建时间',
      colProps: { span: 12 },
      component: 'DatePicker',
      componentProps: {
        valueFormat: `YYYY-MM-DD HH:mm:ss`,
        format: `YYYY-MM-DD HH:mm:ss`,
        showTime: true,
      },
    },
  ];
};
