import { cloneDeep, filter } from 'lodash-es';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import {useUserStore} from "@/store/modules/user";
const userStore = useUserStore();
const dictionary = useDictionary();

export const columns = (): BasicColumn[] => {

    return [
        {
            title: '类型',
            dataIndex: 'type',
            customRender: ({ text }) => {
                const auditState = dictionary.getDictionaryMap.get(`shortVideoDescription_${text}`)?.dictName;
                return <span title={auditState}>{auditState}</span>;
            },
        },
        {
            title: '内容',
            dataIndex: 'content',
        },
        {
            title: '排序',
            dataIndex: 'sort',
        },
    ];
};

export const formSchemas = (): FormSchema[] => {
    const dictionary = useDictionary();
    return [
        {
            field: 'type',
            label: '类型',
            component: 'Select',
            colProps: { span: 6 },
            rulesMessageJoinLabel: true,
            componentProps: function () {
                return {
                    options: dictionary.getDictionaryOpt.get('shortVideoDescription'),
                };
            },
        },
    ];
};


export const modalFormItem = (isUpdate: boolean): FormSchema[] => {
    return [
        {
            field: 'type',
            label: '类型',
            component: 'RadioGroup',
            colProps: { span: 24 },
            required: true,
            rulesMessageJoinLabel: true,
            componentProps: function () {
                return {
                    options: dictionary.getDictionaryOpt.get('shortVideoDescription'),
                };
            },
        },
        {
            field: 'content',
            label: '内容',
            colProps: { span: 24 },
            component: 'InputTextArea',
            required: true,
            rulesMessageJoinLabel: true,
            componentProps: {
                showCount: true,
                maxlength: 255 ,
                autoSize: { minRows: 3, maxRows: 5 },
            },
        },
        {
            field: 'sort',
            label: '排序',
            colProps: { span: 24 },
            component: 'InputNumber',
            className: '!w-full',
            required: true,
            rulesMessageJoinLabel: true,
        },
    ];
};
