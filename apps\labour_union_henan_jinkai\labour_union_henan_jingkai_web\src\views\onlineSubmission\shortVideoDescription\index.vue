<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
            type="primary"
            @click="handleClick"
            auth="/shortVideoDescription/add"
        >新增短视频说明</a-button
        >
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
              :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/shortVideoDescription/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/shortVideoDescription/edit',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                ifShow: record.labelType !== 'builtIn',
                auth: '/shortVideoDescription/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <InfoModal
        @register="registerModal"
        :can-fullscreen="false"
        @success="handleSuccess"
        width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import { findVoList,getById,deleteById,saveOrUpdateByDTO } from '@/api/onlineSubmission/shortVideoDescription';
import { useMessage } from '@monorepo-yysz/hooks';
import InfoModal from "@/views/onlineSubmission/shortVideoDescription/infoModal.vue";

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();
const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  authInfo: ['/shortVideoDescription/add'],
  api: findVoList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 350,
    dataIndex: 'action',
    fixed: undefined,
    class: '!text-center',
    auth: [
      '/shortVideoDescription/view',
      '/shortVideoDescription/edit',
      '/shortVideoDescription/delete',
    ],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

//新增
function handleClick() {
  openModal(true, { isUpdate: false });
}

//详情
function handleView(record) {
  getById(record.autoId ).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}



//编辑
function handleEdit(record) {
  getById(record.autoId ).then(({ data }) => {
    openModal(true, {isUpdate: true,disabled: false, record:data});
  });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除吗?`,
    onOk: function () {
      deleteById(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

//新增或更新
function handleSuccess({ isUpdate, values }) {
  saveOrUpdateByDTO(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
