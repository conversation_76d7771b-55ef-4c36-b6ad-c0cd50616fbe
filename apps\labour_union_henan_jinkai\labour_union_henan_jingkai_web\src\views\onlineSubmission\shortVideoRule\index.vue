<template>
        <BasicTable
            @register="registerTable"
            @edit-end="edithandle"
        >
        </BasicTable>
</template>

<script setup lang="ts">
import { BasicTable, useTable } from '@/components/Table';
import { columns } from './data';
import {
  findVoList,changeValue
} from '@/api/onlineSubmission/shortVideoRule';


const [registerTable] = useTable({
  rowKey: 'autoId',
  api: findVoList,
  columns: columns(),
  useSearchForm: false,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: false,
  immediate:true
});

function edithandle(record: any) {
  changeValue({ autoId:record.record.autoId,ruleValue:record.record.ruleValue});
}
</script>
<style lang="less" module>
.user-info {
  :global {
    background-color: #fff;

    .ant-upload-list {
      display: none;
    }

    .ant-form {
      padding: 0 6px 0 6px;
    @apply px-6px;
    }

    .ant-page-header {
      padding: 0 0 0 36px;

      span {
      @apply !text-[#005090] font-bold;
      }
    }
  }
}
</style>
