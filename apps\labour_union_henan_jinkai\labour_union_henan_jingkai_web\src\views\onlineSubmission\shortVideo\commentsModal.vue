<template>
  <Spin :spinning="spinning">
    <BasicModal
        @register="registerModal"
        :show-ok-btn="false"
        :show-cancel-btn="false"
        v-bind="$attrs"
        :title="title"
        :destroyOnClose="true"
    >
      <BasicTable @register="registerTable">
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction
                :actions="[
                {
                  icon: 'carbon:task-view',
                  label: '详情',
                  type: 'default',
                  onClick: handleView.bind(null, record),
                },
                {
                  icon: record.state ? 'material-symbols:lock-open' : 'material-symbols:lock',
                  label: record.state ? '不公开' : '公开',
                  type: 'primary',
                  danger: record.state,
                  onClick: handleOpen.bind(null, record),
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </BasicModal>
    <CommentsDetailModal
        @register="registerCommentsDetailModal"
        :can-fullscreen="false"
        width="50%"
    />
  </Spin>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import {commentsColumns, commentsFormSchemas} from './data';
import { findVoList,getById,changeState } from '@/api/onlineSubmission/shortComments';
import { useMessage } from '@monorepo-yysz/hooks';
import {useModal, useModalInner} from "@/components/Modal";
import {computed, ref, unref} from "vue";
import BasicModal from "@/components/Modal/src/BasicModal.vue";
import CommentsDetailModal from "@/views/onlineSubmission/shortVideo/commentsDetailModal.vue";
import {Spin} from "ant-design-vue";

const shortVideoId = ref('');
const videoTitle = ref('');
//loading加载
const spinning = ref<boolean>(false);


//标题名
const title = computed(() => {
  return `${unref(videoTitle)}--评论列表`;
});

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();
const [registerCommentsDetailModal, { openModal: openCommentsDetail }] = useModal();

const [registerModal, {}] = useModalInner(async data => {
  shortVideoId.value = data?.shortVideoId;
  videoTitle.value=data?.videoTitle;
});


const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: commentsColumns(),
  showIndexColumn: false,
  api: findVoList,
  formConfig: {
    labelWidth: 120,
    schemas: commentsFormSchemas(),
    autoSubmitOnEnter: true,
  },
  beforeFetch: params => {
    params.shortVideoId = unref(shortVideoId);
    return params;
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 250,
    dataIndex: 'action',
    fixed: undefined,
  },
});


//公开
function handleOpen(record) {
  const text = !record.state ? '公开' : '不公开';
  const state = !record.state;

  createConfirm({
    iconType: 'warning',
    content: `请确认要${text}${record.userName}的评论吗?`,
    onOk: function () {
      changeState({ autoId: record.autoId, state }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `${text}成功` });
          reload();
        } else {
          createErrorModal({ content: `${text}失败，${message}` });
        }
      });
    },
  });
}

//详情
function handleView(record) {
  getById(record.autoId ).then(({ data }) => {
    openCommentsDetail(true, { isUpdate: true, disabled: true, record: data });
  });
}

</script>