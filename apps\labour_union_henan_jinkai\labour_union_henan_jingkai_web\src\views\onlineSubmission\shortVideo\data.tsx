import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useUserStore } from '/@/store/modules/user';
import { Image } from 'ant-design-vue';
import { useDictionary } from '/@/store/modules/dictionary';
import { cloneDeep, filter, includes, map, split, startsWith } from 'lodash-es';
import VideoView from '@/views/components/video-view.vue';
import { Tooltip } from 'ant-design-vue';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

const userStore = useUserStore();
const dictionary = useDictionary();
export const columns = (): BasicColumn[] => {
  const userStore = useUserStore();
  const dictionary = useDictionary();
  return [
    {
      title: '标题',
      dataIndex: 'title',
      width: 220,
    },
    {
      title: '统计指标(次)',
      dataIndex: 'viewVolume',
      width: 100,
      customRender: ({ record, text }) => {
        return (
          <Tooltip
            class={'cursor-pointer'}
            title={
              <>
                <div style={{ textAlign: 'left' }}>{'浏览量: ' + text}</div>
                <div style={{ textAlign: 'left' }}>{'点赞量: ' + record?.likeVolume}</div>
                <div style={{ textAlign: 'left' }}>{'收藏量: ' + record?.collectVolume}</div>
                <div style={{ textAlign: 'left' }}>{'分享量: ' + record?.sharVolume}</div>
                <div style={{ textAlign: 'left' }}>{'评论量: ' + record?.commentsVolume}</div>
              </>
            }
          >
            <div style={{ textAlign: 'left' }}>{'浏览量: ' + text}</div>
            <div style={{ textAlign: 'left' }}>{'点赞量: ' + record?.likeVolume}</div>
            <div style={{ textAlign: 'left' }}>{'评论量: ' + record?.commentsVolume}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '封面',
      dataIndex: 'pictures',
      width: 130,
      customRender: ({ text }) => {
        return (
          <Image
            src={startsWith(text, 'http') ? text : userStore.getPrefix + text}
            width={100}
            height={60}
          ></Image>
        );
      },
    },
    {
      title: '所属栏目',
      dataIndex: 'shortVideoType',
      width: 100,
      customRender: ({ text }) => {
        const shortVideoType = dictionary.getDictionaryMap.get(`shortVideoType_${text}`)?.dictName;
        return <span title={shortVideoType}>{shortVideoType}</span>;
      },
    },
    {
      title: '上传人员',
      dataIndex: 'userName',
      width: 100,
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
      width: 200,
    },
    {
      title: '所属区域',
      dataIndex: 'areaName',
      width: 100,
    },
    {
      title: '是否公开',
      dataIndex: 'publicityState',
      width: 80,
      customRender: ({ text }) => {
        const publicityState = text ? '是' : '否';
        return <span title={publicityState}>{publicityState}</span>;
      },
    },
    {
      title: '审核状态',
      dataIndex: 'auditState',
      width: 80,
      customRender: ({ text }) => {
        const auditState = dictionary.getDictionaryMap.get(`ShortVideoAudit_${text}`)?.dictName;
        return <span title={auditState}>{auditState}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const commentsColumns = (): BasicColumn[] => {
  return [
    {
      title: '评论内容',
      dataIndex: 'content',
    },
    {
      title: '评论人员',
      dataIndex: 'userName',
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
    },
    {
      title: '是否公开',
      dataIndex: 'state',
      customRender: ({ text }) => {
        const state = text ? '是' : '否';
        return <span title={state}>{state}</span>;
      },
    },
    {
      title: '评论时间',
      dataIndex: 'createTime',
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'title',
      label: '标题',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'shortVideoType',
      label: '所属栏目',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('shortVideoType')
        };
      },
    },
    {
      field: 'userName',
      label: '上传人员',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'areaId',
      label: '所属区域',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: map(dictionary.getDictionaryOBJMap.get('regionCode'), t => {
            return { value: t.remark, label: t.dictName };
          }) as RadioGroupChildOption[],
        };
      },
    },
    {
      field: 'auditState',
      label: '审核状态',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('ShortVideoAudit'),
        };
      },
    },
    {
      field: 'publicityState',
      label: '是否公开',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '是', value: true },
            { label: '否', value: false },
          ] as any,
        };
      },
    },
    {
      field: 'queryCompanyId',
      label: '下级工会',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      ifShow: userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5',
      componentProps: function () {
        return {
          options: filter(
            cloneDeep(dictionary.getDictionaryOpt.get(`unionsInfo`)),
            v => v.value !== '6650f8e054af46e7a415be50597a99d5'
          ),
        };
      },
    },
    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: {
        span: 3,
      },
      defaultValue: true,
    },
  ];
};

export const commentsFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '评论人员',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'state',
      label: '是否公开',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '是', value: true },
            { label: '否', value: false },
          ] as any,
        };
      },
    },
  ];
};

export const modalFormItem = (isUpdate: boolean): FormSchema[] => {
  return [
    {
      field: '',
      label: '短视频信息',
      component: 'Divider',
      componentProps: {
        style: {
          fontWeight: 600,
        },
      },
    },
    {
      field: 'title',
      label: '标题',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'describes',
      label: '描述',
      colProps: { span: 24 },
      component: 'InputTextArea',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 10,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    {
      field: 'pictures',
      label: '封面',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      component: 'CropperForm',
      required: true,
    },
    {
      field: 'linkUrl',
      label: '短视频',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      component: 'ShowSpan',
      pStyle: 'height: 250px !important;',
      render({ values }) {
        if (values.linkUrl) {
          const ids = split(values.linkUrl, '/') as unknown as string;

          const id = split(ids[ids.length - 1], '.')[0];
          const url = includes(values.linkUrl, 'http')
            ? values.linkUrl
            : userStore.getPrefix + values.linkUrl;

          return (
            <VideoView
              url={url}
              id={id}
              // style={{ width: '100%', height: '100%' }}
            />
          );
        } else {
          return <></>;
        }
      },
    },
    {
      field: 'shortVideoType',
      label: '所属栏目',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('shortVideoType'),
        };
      },
    },
    {
      field: 'userName',
      label: '发布人员',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'companyName',
      label: '所属工会',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'areaName',
      label: '所属区域',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'publicityState',
      label: '是否公开',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '是', value: true },
            { label: '否', value: false },
          ] as any,
        };
      },
    },
    {
      field: 'createTime',
      label: '创建时间',
      colProps: { span: 12 },
      component: 'DatePicker',
      componentProps: {
        valueFormat: `YYYY-MM-DD HH:mm:ss`,
        format: `YYYY-MM-DD HH:mm:ss`,
        showTime: true,
      },
    },
    // {
    //   field: '',
    //   label: '开户信息',
    //   component: 'Divider',
    //   componentProps: {
    //     style: {
    //       fontWeight: 600,
    //     },
    //   },
    // },
    // {
    //   field: 'cardUserName',
    //   label: '开户人姓名',
    //   colProps: { span: 12 },
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    // },
    // {
    //   field: 'cardNumber',
    //   label: '开户卡号',
    //   colProps: { span: 12 },
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    // },
    // {
    //   field: 'issuingBank',
    //   label: '开户行信息',
    //   colProps: { span: 12 },
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    // },
    {
      field: '',
      label: '审核信息',
      component: 'Divider',
      componentProps: {
        style: {
          fontWeight: 600,
        },
      },
    },
    {
      field: 'auditState',
      label: '审核状态',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('ShortVideoAudit'),
        };
      },
    },
    {
      field: 'auditUserName',
      label: '审核人员姓名',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'remark',
      label: '审核备注',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
  ];
};

export const commentsModalFormItem = (isUpdate: boolean): FormSchema[] => {
  return [
    {
      field: 'content',
      label: '评论内容',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'userName',
      label: '评论人员',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'companyName',
      label: '所属工会',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'parentId',
      label: '父级id',
      ifShow: false,
      component: 'Input',
    },
    {
      field: 'replyUserName',
      label: '回复人员',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      ifShow({ values }) {
        console.info(values.parentId);
        return values.parentId !== 0;
      },
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'state',
      label: '是否公开',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '是', value: true },
            { label: '否', value: false },
          ] as any,
        };
      },
    },
    {
      field: 'createTime',
      label: '创建时间',
      colProps: { span: 12 },
      component: 'DatePicker',
      componentProps: {
        valueFormat: `YYYY-MM-DD HH:mm:ss`,
        format: `YYYY-MM-DD HH:mm:ss`,
        showTime: true,
      },
    },
  ];
};

export const auditForm = (): FormSchema[] => {
  return [
    {
      field: 'auditState',
      label: '审核状态',
      component: 'RadioGroup',
      required: true,
      componentProps: {
        options: [
          { label: '通过', value: 'pass' },
          { label: '未通过', value: 'refuse' },
        ],
      },
    },
    {
      field: 'remark',
      label: '审核意见',
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入审核意见',
        showCount: true,
        maxlength: 255,
      },
    },
  ];
};

//统计指标明细使用
export const statisticalDetails = (title?: string): BasicColumn[] => {
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '昵称',
      dataIndex: 'nickName',
    },
    {
      title: title,
      dataIndex: 'updateTime',
      customRender: ({ record, text }) => {
        return <span>{text ? text : record.createTime}</span>;
      },
    },
  ];
};


export const exportColumn = [
  {
    label: '视频标题',
    value: 'title',
  },
  {
    label: '上传人员',
    value: 'userName',
  },
  {
    label: '认证电话',
    value: 'account',
  },
  {
    label: '工会名称',
    value: 'companyName',
  },
  {
    label: '开户人姓名',
    value: 'cardUserName',
  },
  {
    label: '开户卡号',
    value: 'cardNumber',
  },
  {
    label: '开户行信息',
    value: 'issuingBank',
  },
  {
    label: '上传时间',
    value: 'createTime',
  },
];