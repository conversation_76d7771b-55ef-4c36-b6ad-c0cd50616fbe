<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleAudit(null)"
          auth="/shortVideo/batchAudit"
          >批量审核</a-button
        >
        <a-button type="primary" @click="handleExport" :loading="spinning"> 导出</a-button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/shortVideo/view',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                // disabled: record.auditState !== 'wait',
                onClick: handleAudit.bind(null, record),
                auth: '/shortVideo/audit',
              },
              {
                icon: record.publicityState
                  ? 'material-symbols:lock-open'
                  : 'material-symbols:lock',
                label: record.publicityState ? '不公开' : '公开',
                type: 'primary',
                danger: record.publicityState,
                disabled: record.auditState !== 'pass',
                onClick: handleOpen.bind(null, record),
                auth: '/shortVideo/disable',
              },
              {
                icon: 'carbon:task-view',
                label: '评论',
                type: 'primary',
                onClick: handleComments.bind(null, record),
                auth: '/shortVideo/childComments',
              },
              {
                icon: 'material-symbols:summarize-outline-rounded',
                label: '统计',
                type: 'primary',
                // ghost: true,
                onClick: handleStatisticalDetails.bind(null, record),
                auth: '/shortVideo/statistics',
              },
              // {
              //   icon: 'fluent:delete-16-filled',
              //   label: '删除',
              //   type: 'primary',
              //   danger: true,
              //   onClick: handleDelete.bind(null, record),
              //   ifShow: record.labelType !== 'builtIn',
              // },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ShortVideoModel
      @register="registerModal"
      :can-fullscreen="false"
      width="50%"
    />
    <AuditModal
      @register="registerAudit"
      width="50%"
      @success="handleSubmit"
    />
    <CommentsModal
      @register="registerComments"
      :can-fullscreen="false"
      width="80%"
    />
    <!-- 统计指标明细 -->
    <StatisticalDetailsModel
      @register="registerStatisticalDetails"
      :can-fullscreen="false"
      width="60%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas, exportColumn } from './data';
import {
  findVoList,
  getVoByDto,
  changePublicityState,
  audit,
  exportRecord
} from '@/api/onlineSubmission/shortVideo';
import { useMessage } from '@monorepo-yysz/hooks';
import ShortVideoModel from './shortVideoModel.vue';
import { Modal } from 'ant-design-vue';
import { map } from 'lodash-es';
import AuditModal from './AuditModal.vue';
import { createVNode, ref } from 'vue';
import { CloseCircleFilled } from '@ant-design/icons-vue';
import CommentsModal from './commentsModal.vue';
import { useUserStore } from '@/store/modules/user';
import StatisticalDetailsModel from './StatisticalDetailsModel.vue';
import { downloadByUrl } from '@monorepo-yysz/utils';
import dayjs from "dayjs";

const userStore = useUserStore();

const formParams = ref({})

const ifcollect = ref(false);

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();
const [registerTable, { reload, getSelectRows, clearSelectedRowKeys,getForm }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: ['/shortVideo/batchAudit'],
  showIndexColumn: false,
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.auditState !== 'wait',
    }),
  },
  api: findVoList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  beforeFetch(params) {
    formParams.value = params
    return params
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 280,
    dataIndex: 'action',
    fixed: undefined,
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
    auth: [
      '/shortVideo/view',
      '/shortVideo/audit',
      '/shortVideo/disable',
      '/shortVideo/childComments',
      '/shortVideo/statistics',
    ],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();
const [registerAudit, { openModal: openAudit, closeModal: closeAuditModal }] = useModal();
const [registerComments, { openModal: openComments }] = useModal();
const [registerStatisticalDetails, { openModal: openStatisticalDetails }] = useModal();

//短视频统计明细页面
function handleStatisticalDetails(record) {
  openStatisticalDetails(true, { record });
  ifcollect.value = true;
}

//导出
function handleExport() {
  let params = getForm().getFieldsValue()
  exportRecord({
    ...params,
    ...formParams.value,
    exportColumn
  })
    .then(res => {
      const url = window.URL.createObjectURL(res)
      const day = dayjs().format('YYYY-MM-DD_HH-mm-ss')
      downloadByUrl({
        url,
        fileName: `短视频投稿记录_${day}` + '.xlsx',
      })
    })
    .catch(error => {
      createErrorModal({ content: `导出失败! ${error.message}` })
    })
}

//审核
function handleAudit(record: Nullable<Recordable>) {
  let arr: Recordable[] = [];
  if (record) {
    arr.push(record.autoId);
  } else {
    const rows = getSelectRows();
    if (!rows || rows.length === 0) {
      Modal.warning({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: '请选择至少一条数据进行审核！',
        okText: '确认',
        closable: true,
      });
      return false;
    }

    arr = map(rows, v => v.autoId);
  }
  openAudit(true, { record, autoId: arr });
}

//审核
function handleSubmit({ values, isUpdate }) {
  audit(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `审核成功`,
      });
      reload();
      closeModal();
      closeAuditModal();
      clearSelectedRowKeys();
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      });
    }
  });
}

//公开
function handleOpen(record) {
  const text = !record.publicityState ? '公开' : '不公开';
  const publicityState = !record.publicityState;

  createConfirm({
    iconType: 'warning',
    content: `请确认要${text}${record.title}吗?`,
    onOk: function () {
      changePublicityState({ autoId: record.autoId, publicityState }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `${text}成功` });
          reload();
        } else {
          createErrorModal({ content: `${text}失败，${message}` });
        }
      });
    },
  });
}

//详情
function handleView(record) {
  getVoByDto({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

//评论列表
function handleComments(record) {
  openComments(true, { shortVideoId: record.shortVideoId, videoTitle: record.title });
}

// 删除
// function handleDelete(record) {
//   createConfirm({
//     iconType: 'warning',
//     content: `请确认要删除[${record.title}]短视频吗?确认删除后将删除短视频关联的评论信息,请谨慎操作!`,
//     onOk: function () {
//       deleteById(record.autoId).then(({ code, message }) => {
//         if (code === 200) {
//           createSuccessModal({ content: `删除成功` });
//           reload();
//         } else {
//           createErrorModal({ content: `删除失败，${message}` });
//         }
//       });
//     },
//   });
// }
</script>
