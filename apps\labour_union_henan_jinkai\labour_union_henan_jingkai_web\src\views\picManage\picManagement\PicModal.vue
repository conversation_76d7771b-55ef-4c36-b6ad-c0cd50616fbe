<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
    :wrapClassName="$style['pic-modal']"
  >
    <BasicForm
      @register="registerForm"
      :class="clazz">
      <template #pic="{ model, field }">
        <UploadSimple
            @change="info => handleImges(info, model, field)"
            :some-file="model[field]"
            :operate-type="2"
            :fix="field === 'picSaveUrl' ? 1 : 1"
        />
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, unref } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { modalForm } from './picManagment';
import UploadSimple from '/@/views/components/upload-simple/index.vue'

const emit = defineEmits(['success', 'register']);

const isUpdate = ref(true);

const autoId = ref('');

const disabled = ref(false);

const clazz = computed(() => {
  return unref(disabled) ? 'back-transparent' : undefined;
});

const title = computed(() => {
  return unref(disabled) ? `banner图详情` : unref(isUpdate) ? `修改banner图` : '新增banner图';
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 120,
  schemas: modalForm(),
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  isUpdate.value = !!data?.isUpdate;
  disabled.value = !!data?.disabled;
  autoId.value = data.record?.autoId;

  if (unref(isUpdate)) {
    const { picSaveUrlOne, picSaveUrlTwo, picSaveUrlThree, audioUrl } = data.record;
    await setFieldsValue({
      ...data.record,
      picSaveUrlOne: picSaveUrlOne,
      picSaveUrlTwo: picSaveUrlTwo ? picSaveUrlTwo.split(',') : [],
      picSaveUrlThree: picSaveUrlThree ? picSaveUrlThree.split(',') : [],
      audioUrl: audioUrl ? audioUrl.split(',') : [],
    });
  }
  setProps({
    disabled: unref(disabled),
  });

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });
});

function handleImges(info, model, field) {
  const { filePath } = info
  if (field === 'picSaveUrl') {
    console.log(model[field])
    model[field] = filePath
  } else {
    model[field] = filePath
  }
}

async function handleSubmit() {
  try {
    const { uniqueName, ...values } = await validate();
    setModalProps({ confirmLoading: true });
    const { picSaveUrlOne, picSaveUrlTwo, picSaveUrlThree, audioUrl } = values;
    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...values,
        uniqueName,
        autoId: isUpdate.value ? autoId.value : undefined,
        picSaveUrlOne: picSaveUrlOne,
        picSaveUrlTwo: picSaveUrlTwo ? picSaveUrlTwo.join(',') : undefined,
        picSaveUrlThree: picSaveUrlThree ? picSaveUrlThree.join(',') : undefined,
        audioUrl: audioUrl ? audioUrl.join(',') : undefined,
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
<style lang="less" module>
.pic-modal {
  :global {
    .ant-input-number {
      width: 100% !important;
    }
  }
}
</style>
