<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/picManagement/add"
        >
          新增banner图</a-button
        >
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/picManagement/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/picManagement/modify',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/picManagement/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PicModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import PicModal from '@/views/picManage/picManagement/PicModal.vue';
import { useModal } from '/@/components/Modal';
import { list, saveOrUpdate, deleteLine, getDetails } from '@/api/picManage';
import { columns, formSchemas } from './picManagment';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  authInfo: ['/picManagement/add', '/picManagement/delete'],
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  searchInfo: {
    orderBy: 'showSort',
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/picManagement/modify', '/picManagement/view'],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除当前banner`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleEdit(record) {
  getDetails({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, {
      record: data,
      isUpdate: true,
      disabled: false,
    });
  });
}

function handleView(record) {
  getDetails({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, {
      record: data,
      isUpdate: true,
      disabled: true,
    });
  });
}

function handleSuccess({ values, isUpdate }) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '修改' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '修改' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
