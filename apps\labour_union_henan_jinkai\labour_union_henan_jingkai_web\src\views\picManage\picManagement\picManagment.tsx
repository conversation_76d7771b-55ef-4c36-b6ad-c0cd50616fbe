import { Image, Select } from 'ant-design-vue';
import { DefaultOptionType } from 'ant-design-vue/lib/select';
import { BasicColumn, FormSchema } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import { useUserStore } from '/@/store/modules/user';
import { h } from 'vue';
import { Tinymce } from '/@/components/Tinymce';
import { uploadApi } from '/@/api/sys/upload';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();

  return [
    {
      title: '显示顺序',
      dataIndex: 'showSort',
      width: 100,
      sorter: true,
    },
    {
      title: 'banner',
      dataIndex: 'picSaveUrl',
      width: 100,
      customRender: ({ text }) => {
        return text ? (
          <Image
            src={userStore.getPrefix + text}
            width={54}
            height={74}
          ></Image>
        ) : (
          ''
        );
      },
    },
    {
      title: '展示入口',
      dataIndex: 'showEntrance',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`entrance_${text}`)?.dictName}</span>;
      },
    },
    {
      title: 'banner类型',
      dataIndex: 'showPosition',
      width: 100,
      customRender: ({ text }) => {
        const value = dictionary.getDictionaryMap.get(`APP_${text}`)?.dictName;
        return <span title={value}>{value}</span>;
      },
    },
    {
      title: '所属区县',
      dataIndex: 'uniqueName',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      width: 100,
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      width: 130,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'showEntrance',
      label: '展示入口',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('entrance'),
        };
      },
      defaultValue: 'App',
      show: false,
      render: ({ model, field, disabled }) => {
        return (
          <Select
            value={model[field]}
            options={dictionary.getDictionaryOpt.get('entrance') as DefaultOptionType[]}
            disabled={!!disabled}
            onChange={value => {
              model[field] = value;
              model['showPosition'] = null;
            }}
            placeholder={'请选择展示入口'}
          />
        );
      },
    },
    {
      field: 'showPosition',
      label: 'banner类型',
      component: 'Select',
      colProps: { span: 6 },
      render: ({ model, field }) => {
        let options: DefaultOptionType[] = [];
        if (model['showEntrance'] === 'WX') {
          options = dictionary.getDictionaryOpt.get(`WX`) as DefaultOptionType[];
        } else if (model['showEntrance'] === 'GW') {
          options = dictionary.getDictionaryOpt.get('GW') as DefaultOptionType[];
        } else {
          options = dictionary.getDictionaryOpt.get('APP') as DefaultOptionType[];
        }

        return (
          <Select
            value={model[field]}
            options={options}
            onChange={value => {
              model[field] = value;
            }}
            placeholder={'请选择banner类型'}
          />
        );
      },
    },
  ];
};

export const modalForm = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'showSort',
      label: '显示顺序',
      required: true,
      component: 'InputNumber',
      rulesMessageJoinLabel: false,
      colProps: {
        span: 12,
      },
      className: '!w-full',
    },
    {
      field: 'showEntrance',
      label: '展示入口',
      required: true,
      component: 'Select',
      colProps: {
        span: 12,
      },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('entrance'),
        };
      },
      defaultValue: 'APP',
      render: ({ model, field, disabled }) => {
        return (
          <Select
            value={model[field]}
            options={dictionary.getDictionaryOpt.get('entrance') as DefaultOptionType[]}
            disabled={!!disabled}
            onChange={value => {
              model[field] = value;
              model['showPosition'] = null;
            }}
            placeholder={'请选择展示入口'}
          ></Select>
        );
      },
    },
    {
      field: 'showPosition',
      label: 'banner类型',
      component: 'RadioGroup',
      required: true,
      defaultValue: 'SY_TOP',
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          options: dictionary.getDictionaryOpt.get('APP') as RadioGroupChildOption[],
          onChange: () => {
            formModel.uniqueName = '';
            formModel.picSaveUrl = undefined;
          },
        };
      },
    },
    {
      field: 'uniqueCode',
      label: '所属区县',
      component: 'Select',
      required: true,
      ifShow({ values }) {
        return values?.showPosition === 'XQ_COVER';
      },
      colProps: { span: 12 },
      componentProps: function ({ formModel }) {
        return {
          options: dictionary.getDictionaryOpt.get('unionInformation'),
          showSearch: true,
          placeholder: '请选择所属区县',
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          onChange(label, value) {
            if (value) {
              formModel.uniqueName = value.label
            }else{
              formModel.uniqueName = ''
            }
          },
        }
      },
    },
    { field: 'uniqueName', label: '所属区县', component: 'ShowSpan', show: false },

    {
      field: 'hretFlag',
      label: '是否外链',
      component: 'RadioGroup',
      colProps: {
        span: 12,
      },
      defaultValue: 'N',
      ifShow({ values }) {
        return values?.showPosition !== 'XFSH_COVER';
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('showFlag') as RadioGroupChildOption[],
      },
    },
    {
      field: 'introductionFlag',
      label: '是否新增描述',
      component: 'RadioGroup',
      required: true,
      colProps: {
        span: 12,
      },
      defaultValue: 'N',
      ifShow({ values }) {
        return 'XFSH_COVER' === values?.showPosition;
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('showFlag') as RadioGroupChildOption[],
      },
    },
    {
      field: 'ac',
      label: '区县编码',
      component: 'Input',
      required: true,
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values?.showPosition === 'XQ_COVER';
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入区县编码',
        maxlength: 20,
        showCount: true,
      },
    },
    {
      field: 'jumpType',
      label: '跳转方式',
      component: 'RadioGroup',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values?.hretFlag === 'Y';
      },
      defaultValue: '1',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('specialSkipType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'carryUserInfo',
      label: '携带用户信息',
      component: 'RadioGroup',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values?.hretFlag === 'Y';
      },
      defaultValue: 'N',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('showFlag') as RadioGroupChildOption[],
      },
    },
    {
      field: 'hretUrl',
      label: '外链地址',
      required: true,
      component: 'Input',
      ifShow({ values }) {
        return values.hretFlag === 'Y';
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入外链地址',
        maxlength: 100,
        showCount: true,
      },
    },
    {
      field: 'encryptionKey',
      label: '加密key',
      component: 'Input',
      ifShow({ values }) {
        return values?.hretFlag === 'Y';
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入加密key',
        maxlength: 100,
        showCount: true,
      },
    },

    {
      field: 'picSaveUrl',
      label: '图片',
      required: true,
      component: 'CropperForm',
      renderComponentContent({ values }) {
        return {
          tip: () =>
            values.showPosition === 'SY_TOP' ? (
              <div class={`text-sm leading-7`}>
                app首页图片规格为(<span class="text-red-500">1080*540</span>)
              </div>
            ) : (
              <div class={`text-sm leading-7`}>
                背景图片规格为(<span class="text-red-500">342*456</span>)
              </div>
            ),
        };
      },
      componentProps({ formModel }) {
        return {
          operateType: 2,
          imgSize: formModel.showPosition === 'SY_TOP' ? 1080 / 540 : 452 / 542,
        };
      },
    },
    {
      field: 'picSaveUrl',
      label: '封面图',
      colProps: {
        span: 12,
      },
      required: true,
      component: 'CropperForm',
      ifShow({ values }) {
        return 'XFSH_COVER' === values?.showPosition;
      },
      renderComponentContent() {
        return {
          tip: (
            <div class={`text-sm leading-7`}>
              背景图片规格为(<span class="text-red-500">1000*360</span>)
            </div>
          ),
        };
      },
      componentProps() {
        return {
          operateType: 2,
          imgSize: 1000 / 360,
        };
      },
    },
    {
      field: 'picSaveUrlTwo',
      label: '背景图',
      colProps: { span: 12 },
      component: 'Upload',
      ifShow({ values }) {
        return 'XFSH_COVER' === values?.showPosition;
      },
      componentProps: {
        uploadParams: {
          operateType: 2,
        },
        maxSize: 10,
        maxNumber: 1,
        api: uploadApi,
        accept: ['image/*'],
      },
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'picSaveUrlThree',
      label: '文字图',
      colProps: { span: 12 },
      component: 'Upload',
      ifShow({ values }) {
        return 'XFSH_COVER' === values?.showPosition;
      },
      componentProps: {
        uploadParams: {
          operateType: 2,
        },
        maxSize: 10,
        maxNumber: 1,
        api: uploadApi,
        accept: ['image/*'],
      },
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'audioUrl',
      label: '播报音频',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      component: 'Upload',
      ifShow({ values }) {
        return 'XFSH_COVER' === values?.showPosition && 'Y' === values?.introductionFlag;
      },
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 152,
        },
        maxNumber: 1,
        maxSize: 100,
        accept: ['audio/*'],
      },
    },
    {
      field: 'articleTitle',
      label: '标题',
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return 'XFSH_COVER' === values?.showPosition && 'Y' === values?.introductionFlag;
      },
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 40,
      },
    },
    {
      field: 'articleContent',
      component: 'Input',
      required: true,
      label: '内容',
      ifShow({ values }) {
        return 'XFSH_COVER' === values?.showPosition && 'Y' === values?.introductionFlag;
      },
      render: ({ model, field, disabled }) => {
        return h(Tinymce, {
          value: model[field],
          onChange: (value: string) => {
            model[field] = value;
          },
          showImageUpload: false,
          operateType: 2,
          options: {
            readonly: disabled,
          },
        });
      },
    },
    {
      field: 'picSaveUrlOne',
      label: '区县名称图',
      colProps: {span: 24,},
      component: 'Input',
      ifShow({ values }) {
        return values?.showPosition === 'XQ_COVER';
      },
      componentProps: {
        operateType: 2,
      },
      slot: 'pic',
    },
    {
      field: 'bannerPicUrl',
      label: '区县大图',
      colProps: {
        span: 12,
      },
      component: 'CropperForm',
      ifShow({ values }) {
        return 'XQ_COVER' === values?.showPosition;
      },
      renderComponentContent() {
        return {
          tip: (
              <div class={`text-sm leading-7`}>
                区县大图规格为(<span class="text-red-500">690*260</span>)
              </div>
          ),
        };
      },
      componentProps() {
        return {
          operateType: 2,
          imgSize: 690 / 260,
        };
      },
    },
    {
      field: 'remarks',
      label: '备注',
      required: false,
      rulesMessageJoinLabel: true,
      component: 'InputTextArea',
      componentProps: {
        showCount: true,
        maxlength: 255 ,
      },
    },
  ];
};
