<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" :show-ok-btn="false">
    <BasicTable @register="registerTable">
    </BasicTable>
  </BasicModal>
</template>

<script setup lang="ts">

import {BasicModal, useModalInner} from "@/components/Modal";
import {BasicTable, useTable} from "@/components/Table";
import {integralTreeOperateList} from "@/api/activities";
import {ref, unref} from "vue";

const title = ref('')
const record = ref<Recordable>()
const [registerModal] = useModalInner(async data => {
  const {activityId,userId,userName} = data.record
  record.value = data.record
  title.value = `${userName}-操作记录`
  await reload({
    searchInfo: {
      activityId,
      userId,
    },
  })
})
const [registerTable,{reload}] = useTable({
  rowKey: 'autoId',
  columns: [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '来源：积分 养料',
      dataIndex: 'sourceType',
      defaultHidden: true,
    },
    {
      title: '操作类型',
      dataIndex: 'ruleCode',
      customRender({ record, text }) {
        const {sourceType} = record
        if(text==='activeSeed'){
          return '领取树苗'
        }
        if(text === 'water'){
          return sourceType === 'integral' ?'兑换水滴' :'浇水'
        }
        if(text === 'fertilizer'){
          return sourceType === 'integral' ?'兑换肥料' :'施肥'
        }
        return '-'
      }
    },
    {
      title: '操作日期',
      dataIndex: 'createTime',
    },
  ],
  api:integralTreeOperateList,
  showIndexColumn: false,
  immediate:false,
  maxHeight:430,
  beforeFetch(p){
    p.orderBy = 'auto_id'
    p.sortType = 'desc'
    p.activityId = unref(record)?.activityId
    p.userId = unref(record)?.userId
    return p
  },
  useSearchForm: false,
  bordered: true,
})
</script>
<style scoped lang="less">

</style>
