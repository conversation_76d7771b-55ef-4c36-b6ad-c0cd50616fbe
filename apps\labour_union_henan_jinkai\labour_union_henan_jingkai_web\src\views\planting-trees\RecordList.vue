<template>
  <div>
    <BasicTable @register="registerTable" :clickToRowSelect="false">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
              :actions="[
               {
              icon: 'carbon:task-view',
              label: '操作记录',
              type: 'default',
              onClick: handleView.bind(null, record),
            }, ]"
          />
        </template>
      </template>
    </BasicTable>
    <OperateListModal @register="registerModal"
                :canFullscreen="false"
                width="30%"
    />
  </div>
</template>

<script lang="ts" setup>

import { BasicTable, useTable, TableAction } from '/@/components/Table'

import { useModal } from '/@/components/Modal'

import OperateListModal from './OperateListModal.vue'
import {computed, inject, nextTick, onMounted, unref} from "vue";
import {integralTreeUserList} from "@/api/activities";
import {columns} from "@/views/planting-trees/data";


const props = defineProps({
  activityId: {
    type: String,
  },
  activityMode: {
    type: String,
  },
});
const activityInfo = inject('activityDetail');
const emit = defineEmits(['reload']);
const tableColumns = computed(()=>{
  return columns(unref(activityInfo)?.integralTreeConfig?.stage6)
})

const [registerModal, { openModal }] = useModal()


const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: tableColumns,
  api:integralTreeUserList,
  showIndexColumn: false,
  beforeFetch(p){
    p.orderBy = 'nutrient_num'
    p.sortType = 'desc'
    p.receiveFlag = p.receiveFlag ? true :undefined
    p.activityId = props.activityId
    return p
  },
  formConfig: {
    labelWidth: 120,
    schemas: [ {
      field: 'userName',
      label: '用户姓名',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入用户姓名',
      },
    },
      {
      field: 'companyName',
      label: '工会名称',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入工会名称',
      },
    },
      {
        field: 'receiveFlag',
        label: '是否兑换奖品',
        component: 'Select',
        colProps: { span: 6 },
      }
    ],
    autoSubmitOnEnter: true,
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',
    fixed: undefined,
  },
})

async function handleView(record) {

  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: true,
  })
}
function reloadAll() {
  reload({
    searchInfo: {
      activityId: props.activityId,
    },
  });
}

onMounted(() => {
  nextTick(() => {
    emit('reload', reloadAll);
  });
});

</script>
