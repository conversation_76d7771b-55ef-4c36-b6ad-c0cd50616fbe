<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :destroy-on-close="true"
    @ok="handleSubmit"
  >
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane
        key="1"
        tab="基础信息"
        :forceRender="true"
      >
        <BasicForm
          @register="registerForm"
          :class="disabledClass"
        />
      </a-tab-pane>
      <a-tab-pane
        key="2"
        tab="介绍信息"
        :forceRender="true"
      >
        <BasicForm
          :schemas="modalFormItem2"
          :labelWidth="180"
          :showActionButtonGroup="false"
          :disabled="disabled"
          ref="otherTabFormModel"
        />
      </a-tab-pane>
      <a-tab-pane
        key="3"
        tab="奖品配置"
        :forceRender="true"
      >
        <div class="p-5px">
          <!-- <Affix :target="container"> -->
          <div
            class="flex items-center my-5px"
            v-if="!disabled"
          >
            <a-button
              type="primary"
              shape="round"
              @click="handleAddQuestions()"
              >添加奖品</a-button
            >
          </div>
          <!-- </Affix> -->

          <BasicTable
            @register="registerTable"
            class="dy-prize-table"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      icon: 'ant-design:delete-twotone',
                      label: '删除',
                      type: 'text',
                      danger: true,
                      disabled: disabled,
                      onClick: handleDeletePrize.bind(null, index),
                    },
                  ]"
                />
              </template>
              <template v-if="column.key === 'prizeImg'">
                <div class="w-70px h-70px inline-block">
                  <CropperImg
                    :value="record.prizeImg"
                    :operate-type="ActivityDocAddr.integralTree"
                    :disabled="disabled"
                    @change="filePath => (record.prizeImg = filePath)"
                  />
                </div>
              </template>
            </template>
          </BasicTable>
        </div>
      </a-tab-pane>
    </a-tabs>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm, FormActionType } from '/@/components/Form';
import { modalFormItem, modalFormItem2 } from './data';
import dayjs from 'dayjs';
import { tableLotteryColum } from '../activities/ActivityTable/BasicSetting/data';
import { ActivityType, ActivityDocAddr } from '../activities/activities.d';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import CropperImg from '@/components/Cropper/src/CropperImg.vue';
import { isEmpty, remove } from 'lodash-es';
import {useUserStore} from "@/store/modules/user";

const emit = defineEmits(['register', 'success']);

const activeKey = ref<string>('1');

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

// 介绍信息ref
const otherTabFormModel = ref<FormActionType>();

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.activityName || ''}--详情`
      : `编辑${unref(record)?.activityName || ''}`
    : '新增积分种树';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const columns = computed(() => {
  return tableLotteryColum(unref(disabled), ActivityType.INTEGRAL_TREE, unref(record)?.activityId);
});

const form = computed(() => {
  return modalFormItem();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 180,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  activeKey.value = '1';

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    const {
      openingStartTime,
      integralTreeConfig,
      openingEndTime,
      activityStartTime,
      activityEndTime,
      areaCode,
    } = data.record;
    //反填时间
    let dailyTimeStart = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    let dailyTimeEnd = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    dailyTimeStart.splice(1, 1, openingStartTime);
    dailyTimeEnd.splice(1, 1, openingEndTime);
    const dailyTime = [dayjs(dailyTimeStart.join(' ')), dayjs(dailyTimeEnd.join(' '))];

    setFieldsValue({
      ...data.record,
      areaCode: areaCode ? areaCode?.split(',') : undefined,
      startEndDate:
        activityStartTime && activityEndTime ? [activityStartTime, activityEndTime] : [],
      dailyTime,
    });
    unref(otherTabFormModel)?.setFieldsValue({
      activityContent: data.record.activityContent, //活动介绍
      activityRules: data.record.activityRules, //活动规则
    });

    if (!isEmpty(integralTreeConfig?.prizeInfos)) setTableData(integralTreeConfig?.prizeInfos);
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

// 注册抽奖table
const [registerTable, { getDataSource, setTableData, insertTableDataRecord }] = useTable({
  rowKey: 'id',
  showTableSetting: false,
  columns: columns,
  bordered: true,
  showIndexColumn: true,
  pagination: false,
  actionColumn: {
    width: 50,
    title: '操作',
    dataIndex: 'action',
  },
});

function handleAddQuestions() {
  insertTableDataRecord({});
}

// 删除
function handleDeletePrize(index: number) {
  remove(getDataSource(), (_, k) => k === index);
}

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const { dailyTime, startEndDate, ...values } = await validate();
    const {areaType,areaCode} = values
    let ac = areaCode;
    if (areaType && areaType === '1') {
      ac = useUserStore().getUserInfo.areaName ?? '南充市';
    } else if (areaType && areaType === '0') {
      ac = '南充市';
    } else if (areaType && areaType === '3') {
      ac = '四川省';
    } else {
      ac = areaCode ? areaCode?.join(',') : '南充市';
    }
    const sTime = dailyTime ? dayjs(dailyTime[0]).format('HH:mm:ss') : '00:00:00';

    const eTime = dailyTime ? dayjs(dailyTime[1]).format('HH:mm:ss') : '23:59:59';

    const prize = getDataSource();

    const integralTreeConfig = {
      ...(unref(record)?.integralTreeConfig || {}),
      ...values.integralTreeConfig,
    };

    if (!isEmpty(prize)) {
      integralTreeConfig['prizeInfos'] = prize;
    }

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        areaCode: ac,
        integralTreeConfig,
        activityCategory: 'integralTree',
        activityMode: 'integralTree',
        openingStartTime: sTime,
        openingEndTime: eTime,
        activityStartTime: startEndDate
          ? dayjs(startEndDate[0]).format(`YYYY-MM-DD ${sTime}`)
          : undefined,
        activityEndTime: startEndDate
          ? dayjs(startEndDate[1]).format(`YYYY-MM-DD ${eTime}`)
          : undefined,
        ...(unref(otherTabFormModel)?.getFieldsValue() || {}),
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
