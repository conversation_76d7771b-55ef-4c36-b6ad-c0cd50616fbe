import { useUserStore } from '@/store/modules/user';
import { FormSchema } from '/@/components/Form';
import { useDictionary } from '/@/store/modules/dictionary';
import { isObject, isString, map } from 'lodash-es';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { InputNumber, Progress } from 'ant-design-vue';
import { Tinymce } from '@/components/Tinymce/index';
import { ActivityDocAddr } from '../activities/activities.d';
import { BasicColumn } from '@/components/Table';

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      field: '',
      label: '领取配置',
      component: 'Divider',
    },
    {
      field: 'activityName',
      required: true,
      label: '活动名称',
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入活动名称,不能超过100个字符',
        showCount: true,
        maxlength: 100,
      },
    },
    {
      field: 'customerType',
      required: true,
      label: '参与用户',
      component: 'Select',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('customerType'),
      },
    },
    {
      field: 'areaType',
      required: true,
      label: '参与用户区域',
      component: 'RadioGroup',
      colProps: { span: 12 },
      defaultValue:
        userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5' ? '0' : '1',
      componentProps: ({ formModel }) => {
        let options = dictionary.getDictionaryOpt.get('areaType') as RadioGroupChildOption[];
        if (userStore.getUserInfo.companyId !== '6650f8e054af46e7a415be50597a99d5') {
          options = options?.filter(t => t.value !== '2');
        }
        return {
          placeholder: '请选择参与用户区域',
          options,
          onChange: e => {
            if ((isObject(e) && e.target.value === '2') || (isString(e) && e === '2')) {
              formModel['areaCode'] = undefined;
            }
          },
        };
      },
    },
    {
      field: 'areaCode',
      label: '区域选择',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => {
        return (
          values.areaType === '2' &&
          userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5'
        );
      },
      componentProps: {
        options: map(dictionary.getDictionaryOpt.get('regionCode'), t => {
          return { value: t.label, label: t.label };
        }) as RadioGroupChildOption[],
        mode: 'multiple',
      },
    },
    {
      field: 'startEndDate',
      required: true,
      label: '活动起止日期',
      component: 'RangePicker',
      colProps: { span: 12 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'dailyTime',
      required: true,
      label: '每日开放时间',
      component: 'TimeRangePicker',
      colProps: { span: 12 },
      componentProps: {
        placeholder: ['起始时间', '结束时间'],
        showNow: true,
        // showTime: { showNow: true },
      },
    },
    {
      field: 'integralTreeConfig.assignCount',
      label: '已领取数量',
      component: 'InputNumber',
      show: false,
    },
    {
      field: 'integralTreeConfig.seedCount',
      label: '种子数量',
      component: 'InputNumber',
      required: true,
      colProps: { span: 12 },
      suffix({ values }) {
        return `已领取：${values?.['integralTreeConfig.assignCount'] ?? 0}`;
      },
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 0,
      },
    },
    {
      field: 'integralTreeConfig.firstActive',
      label: '首次领取种子奖励水滴（g）',
      component: 'InputNumber',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 0,
      },
    },
    {
      field: 'integralTreeConfig.receiveEndDate',
      label: '种子领取截止日期',
      component: 'DatePicker',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: {
        showTime: false,
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      className: '!w-full',
    },
    {
      field: 'integralTreeConfig.seedIntegral',
      label: '领取消耗积分',
      component: 'InputNumber',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 0,
      },
    },
    {
      field: '',
      label: '任务配置',
      component: 'Divider',
    },
    {
      field: 'integralTreeConfig.waterUseIntegral',
      label: '水滴兑换',
      component: 'InputNumber',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      rest: true,
      render({ model, field }) {
        return (
          <div class={`flex items-center pr-1`}>
            消耗南充频道
            <InputNumber
              style={{ width: '150px', height: '30px' }}
              value={model[field]}
              onChange={e => (model[field] = e)}
            />{' '}
            积分兑换
            <InputNumber
              style={{ width: '150px', height: '30px' }}
              value={model['integralTreeConfig.waterDropletNum']}
              onChange={e => (model['integralTreeConfig.waterDropletNum'] = e)}
            />{' '}
            g水滴， 每g水滴提供
            <InputNumber
              style={{ width: '150px', height: '30px' }}
              value={model['integralTreeConfig.waterOnceGrowth']}
              onChange={e => (model['integralTreeConfig.waterOnceGrowth'] = e)}
            />{' '}
            成长值， 每日限兑换
            <InputNumber
              style={{ width: '150px', height: '30px' }}
              value={model['integralTreeConfig.waterDailyCount']}
              onChange={e => (model['integralTreeConfig.waterDailyCount'] = e)}
            />{' '}
            次
          </div>
        );
      },
    },
    {
      field: 'integralTreeConfig.waterDropletNum',
      label: '水滴兑换',
      component: 'InputNumber',
      required: true,
      show: false,
    },
    {
      field: 'integralTreeConfig.waterDailyCount',
      label: '水滴兑换',
      component: 'InputNumber',
      required: true,
      show: false,
    },
    {
      field: 'integralTreeConfig.waterOnceGrowth',
      label: '水滴兑换',
      component: 'InputNumber',
      required: true,
      show: false,
    },

    {
      field: 'integralTreeConfig.fertilizerUseIntegral',
      label: '化肥兑换',
      component: 'InputNumber',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      rest: true,
      render({ model, field }) {
        return (
          <div class={`flex items-center pr-1`}>
            消耗南充频道
            <InputNumber
              style={{ width: '150px', height: '30px' }}
              value={model[field]}
              onChange={e => (model[field] = e)}
            />{' '}
            积分兑换
            <InputNumber
              style={{ width: '150px', height: '30px' }}
              value={model['integralTreeConfig.fertilizerNum']}
              readonly
              disabled
              onChange={e => (model['integralTreeConfig.fertilizerNum'] = e)}
            />{' '}
            袋化肥， 每袋化肥提供
            <InputNumber
              style={{ width: '150px', height: '30px' }}
              value={model['integralTreeConfig.fertilizerOnceGrowth']}
              onChange={e => (model['integralTreeConfig.fertilizerOnceGrowth'] = e)}
            />{' '}
            成长值， 每日限兑换
            <InputNumber
              style={{ width: '150px', height: '30px' }}
              value={model['integralTreeConfig.fertilizerDailyCount']}
              onChange={e => (model['integralTreeConfig.fertilizerDailyCount'] = e)}
            />{' '}
            次
          </div>
        );
      },
    },
    {
      field: 'integralTreeConfig.fertilizerNum',
      label: '化肥兑换',
      component: 'InputNumber',
      defaultValue: 1,
      required: true,
      show: false,
    },
    {
      field: 'integralTreeConfig.fertilizerOnceGrowth',
      label: '化肥兑换',
      component: 'InputNumber',
      required: true,
      show: false,
    },
    {
      field: 'integralTreeConfig.fertilizerDailyCount',
      label: '化肥兑换',
      component: 'InputNumber',
      required: true,
      show: false,
    },

    {
      field: 'integralTreeConfig.useWaterOnce',
      label: '每次浇水消耗水滴（g）',
      component: 'InputNumber',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 0,
      },
    },
    {
      field: 'integralTreeConfig.useFertilizerOnce',
      label: '每次施肥消耗（%）',
      component: 'InputNumber',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 0,
      },
    },

    {
      field: '',
      label: '果树配置',
      component: 'Divider',
    },

    {
      field: 'integralTreeConfig.nutrientDailyMax',
      label: '每日获取成长值上限',
      component: 'InputNumber',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 0,
      },
    },
    {
      field: 'integralTreeConfig.stage2',
      label: '成长阶段',
      component: 'InputNumber',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      rest: true,
      render({ model, field }) {
        return (
          <div class={'pr-1'}>
            <div class={`flex  items-center`}>
              小树：需要获取
              <InputNumber
                style={{ width: '150px', height: '30px' }}
                value={model[field]}
                onChange={e => (model[field] = e)}
              />
              点成长值；
            </div>
            <div class={`flex  items-center`}>
              大树：需要获取
              <InputNumber
                style={{ width: '150px', height: '30px' }}
                value={model['integralTreeConfig.stage3']}
                onChange={e => (model['integralTreeConfig.stage3'] = e)}
              />
              点成长值；
            </div>
            <div class={`flex  items-center`}>
              开花：需要获取
              <InputNumber
                style={{ width: '150px', height: '30px' }}
                value={model['integralTreeConfig.stage4']}
                onChange={e => (model['integralTreeConfig.stage4'] = e)}
              />
              点成长值。
            </div>
            <div class={`flex items-center`}>
              结果：需要获取
              <InputNumber
                style={{ width: '150px', height: '30px' }}
                value={model['integralTreeConfig.stage5']}
                onChange={e => (model['integralTreeConfig.stage5'] = e)}
              />
              点成长值；
            </div>
            <div class={`flex items-center`}>
              果实成熟：需要获取
              <InputNumber
                style={{ width: '150px', height: '30px' }}
                value={model['integralTreeConfig.stage6']}
                onChange={e => (model['integralTreeConfig.stage6'] = e)}
              />
              点成长值；
            </div>
          </div>
        );
      },
    },

    {
      field: 'integralTreeConfig.stage3',
      label: '成长阶段',
      component: 'InputNumber',
      required: true,
      show: false,
    },
    {
      field: 'integralTreeConfig.stage4',
      label: '成长阶段',
      component: 'InputNumber',
      required: true,
      show: false,
    },
    {
      field: 'integralTreeConfig.stage5',
      label: '成长阶段',
      component: 'InputNumber',
      required: true,
      show: false,
    },
    {
      field: 'integralTreeConfig.stage6',
      label: '成长阶段',
      component: 'InputNumber',
      required: true,
      show: false,
    },
  ];
};

export const modalFormItem2: FormSchema[] = [
  {
    field: 'activityContent',
    label: '活动介绍',
    component: 'Input',
    colProps: { span: 24 },
    render({ model, field, disabled }) {
      return (
        <Tinymce
          value={model[field]}
          options={{ readonly: !!disabled }}
          operateType={ActivityDocAddr.integralTree}
          showImageUpload={false}
          onChange={value => {
            model[field] = value;
          }}
        />
      );
    },
  },
  {
    field: 'activityRules',
    label: '活动规则',
    component: 'Input',
    colProps: { span: 24 },
    render({ model, field, disabled }) {
      return (
        <Tinymce
          value={model[field]}
          options={{ readonly: !!disabled }}
          operateType={ActivityDocAddr.integralTree}
          showImageUpload={false}
          onChange={value => {
            model[field] = value;
            console.log(model, value, model[field]);
          }}
        />
      );
    },
  },
];

const getPercentage = (score, total) => {
  if (!score) return 100;
  if (!total) return 0;
  const percentage = score / total;
  return Math.floor(percentage * 100);
};

export const columns = (total): BasicColumn[] => {
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '用户姓名',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '工会名称',
      dataIndex: 'companyName',
    },
    {
      title: '水滴数量',
      dataIndex: 'waterDropletNum',
      width: 100,
    },
    {
      title: '肥料数量',
      dataIndex: 'fertilizerNum',
      width: 100,
    },
    {
      title: '果树成长值',
      dataIndex: 'nutrientNum',
      width: 150,
      customRender({ text }) {
        return (
          <div>
            <div style={{ width: '100%', textAlign: 'left' }}>成长值：{text}</div>
            <Progress
              width={80}
              percent={getPercentage(text, total)}
              format={percent => `${percent}%`}
              strokeColor={'#40c340de'}
              size="small"
            />
          </div>
        );
      },
    },
    {
      title: '是否兑换奖品',
      dataIndex: 'receiveFlag',
      width: 150,
      customRender({ text }) {
        return <div>{text ? '是' : '否'}</div>;
      },
    },
    {
      title: '领取日期',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};
