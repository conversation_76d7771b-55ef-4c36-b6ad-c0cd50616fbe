<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增积分种树
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                ifShow: fixCutOut(record),
                onClick: handleModify.bind(null, record),
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                ifShow: record.auditState === 'wait',
                onClick: handleAudit.bind(null, record),
              },

              {
                icon: 'carbon:cut-out',
                label: handleChangeState(record),
                type: 'primary',
                ifShow: fixCutOut(record),
                onClick: pushOrCutdown.bind(null, record),
              },

              {
                icon: 'material-symbols:summarize-outline-rounded',
                label: '统计',
                type: 'primary',
                ifShow: fixCutOut(record),
                onClick: handleSum.bind(null, record),
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '评论',
                type: 'primary',
                ifShow: fixCutOut(record) && record.commentState === 'y',
                onClick: handleComments.bind(null, record),
              },
              {
                icon: 'fluent:delete-12-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                ifShow: fixCutOut(record),
                disabled: record.state !== 'draft',
                onClick: handleDelete.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <TreeModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="70%"
    />

    <AuditModal
      @register="registerAudit"
      :can-fullscreen="false"
      width="40%"
      :title="auditTitle"
      @success="handleAuditSuccess"
    />

    <CommentModal
      @register="registerComment"
      width="88%"
      :can-fullscreen="false"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import TreeModal from './TreeModal.vue';
import {
  activityAudit,
  deleteActivity,
  getDetails,
  list,
  pushDown,
  saveOrUpdate,
} from '@/api/activities';
import { useMessage } from '@monorepo-yysz/hooks';
import { fixCutOut, handleChangeState } from '@/views/activities/ActivityTable/utils';
import { computed, h, ref } from 'vue';
import AuditModal from '@/views/activities/ActivityTable/AuditModal.vue';
import { map } from 'lodash-es';
import { useDictionary } from '@/store/modules/dictionary';
import { ActivityType, ActivityView, BigActivityType } from '../activities/activities.d';
import { useRouter } from 'vue-router';
import CommentModal from '@/views/activities/comment/CommentModal.vue';
import { searchFormSchema, tableColumns } from '@/views/activities/activity';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const router = useRouter();

const dictionary = useDictionary();

const auditTitle = ref<string>();

const column = computed(() => {
  return tableColumns(ActivityType.INTEGRAL_TREE);
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: column,
  showIndexColumn: false,
  api: list,
  beforeFetch: params => {
    const { startEndDate, ...val } = params;
    if (startEndDate && startEndDate.length > 0) {
      val['firstTime'] = startEndDate[0] ? startEndDate[0] + ' 00:00:00' : undefined;
      val['lastTime'] = startEndDate[1] ? startEndDate[1] + ' 23:59:59' : undefined;
    }
    return val;
  },
  searchInfo: {
    activityMode: ActivityType.INTEGRAL_TREE,
    activityCategory: BigActivityType.integralTree,
  },
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema(),
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    actionColOptions: {
      span: 3,
    },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

// 审核
const [registerAudit, { openModal: openAudit, closeModal: closeAudit }] = useModal();

// 评论
const [registerComment, { openModal: openComment }] = useModal();

// 新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

// 编辑
function handleModify(record: Recordable<any>) {
  getDetails({ activityId: record.activityId }).then(res => {
    if (res.code === 200) {
      openModal(true, { isUpdate: true, record: res.data, disabled: false });
    }
  });
}

// 审核
function handleAudit(record) {
  auditTitle.value = `审核-${record.activityName}`;
  openAudit(true, { record, autoId: record.activityId });
}

// 详情
function handleView(record: Recordable<any>) {
  getDetails({ activityId: record.activityId }).then(res => {
    if (res.code === 200) {
      openModal(true, { isUpdate: true, record: res.data, disabled: true });
    }
  });
}

// 删除
function handleDelete(record: Recordable<any>) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.activityName || '当前数据'}？`,
    onOk: function () {
      deleteActivity(record).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功！` });
          reload();
        } else {
          createErrorModal({ content: `删除失败！${message}。` });
        }
      });
    },
  });
}

// 下架发布
function pushOrCutdown(record: Recordable) {
  const port = ['端口'];
  if (record.publishPort) {
    const text = map(
      record.publishPort?.split(','),
      v => dictionary.getDictionaryMap.get(`appType_${v}`)?.dictName || ''
    ).join(',');
    port.push(text);
  }
  createConfirm({
    content: h('div', [
      `确定${handleChangeState(record)}${record.activityName}`,
      h('div', `${port.length > 1 ? port.join(': ') : ''}`),
    ]),
    async onOk() {
      try {
        let state = 'draft';
        if (record.state === 'draft') {
          state = 'publish';
        } else if (record.state === 'publish') {
          state = 'unpublish';
        } else if (record.state === 'unpublish') {
          state = 'publish';
        }

        return await new Promise<void>(resolve => {
          pushDown({
            activityId: record.activityId,
            activityMode: record.activityMode,
            activityCategory: record.activityCategory,
            state: state,
          }).then(res => {
            const { code, message } = res;

            if (code === 200) {
              createSuccessModal({ content: `${handleChangeState(record)}成功！` });
              reload();
            } else {
              createErrorModal({ content: `${handleChangeState(record)}失败！${message} ` });
            }
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}

//统计
function handleSum(record: Recordable) {
  const { groupIds, ...params } = record;
  router.push({
    path: `/actStatisic/actStatisic/${ActivityView.integralTree}`,
    query: {
      record: JSON.stringify(params),
    },
  });
}

// 新增修改
function handleSuccess({ values, isUpdate }: Recordable<any>) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败！${message}。`,
      });
    }
  });
}

// 评价
function handleComments(record) {
  openComment(true, { record });
}

// 提交审核
function handleAuditSuccess({ values, autoId }) {
  activityAudit({ ...values, activityId: autoId }).then(res => {
    const { code, message: msg } = res;
    if (code === 200) {
      createSuccessModal({ content: '操作成功' });
      closeAudit();
      reload();
    } else {
      createErrorModal({ content: `操作失败，${msg}` });
    }
  });
}
</script>
