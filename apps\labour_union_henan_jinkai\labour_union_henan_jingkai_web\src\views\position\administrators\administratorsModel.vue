<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '选择',
                type: 'default',
                onClick: handleAdministrators.bind(null, record),
                ifShow: record.a0115 !== null,
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { administratorsColumns, administratorsSchemas } from './data';
import { useUserStore } from '@/store/modules/user';
import { userPagedAll } from '@/api';
const userStore = useUserStore();

const emit = defineEmits(['register', 'success']);

const title = computed(() => {
  return '新增管理员';
});

const [registerModal, {}] = useModalInner(async () => {
  await clearSelectedRowKeys();
  await reload();
});

const [registerTable, { clearSelectedRowKeys, reload }] = useTable({
  rowKey: 'autoId',
  api: userPagedAll,
  columns: administratorsColumns(),
  maxHeight: 430,
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
  indexColumnProps: { width: 90 },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: administratorsSchemas(),
  },
  fetchSetting: {
    totalField: 'recordCount',
  },
  beforeFetch: params => {
    params.pi = params.pageNum;
    params.ps = params.pageSize;
    params.uid = userStore.getUserInfo.companyId;
    return { ...params };
  },
  afterFetch: data => {
    return data?.data;
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
    // auth: ['/difficultEmployees/choice']
  },
});

//选择按钮操作
function handleAdministrators(record) {
  emit('success', { record: record });
}
</script>
