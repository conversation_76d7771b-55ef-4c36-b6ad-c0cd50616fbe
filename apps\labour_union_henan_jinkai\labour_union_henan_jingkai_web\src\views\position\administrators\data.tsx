import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'name',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
    },
    {
      title: '状态',
      dataIndex: 'state',
      customRender: ({ text }) => {
        return (
          <span
            title={text === 'y' ? '启用' : '禁用'}
            class={text == 'y' ? 'text-green-500' : text == 'n' ? 'text-red-500' : ''}
          >
            {text === 'y' ? '启用' : '禁用'}
          </span>
        )
      },
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'name',
      label: '姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'state',
      label: '状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '启用', value: 'y' },
            { label: '禁用', value: 'n' },
          ],
        }
      },
    },
  ]
}

//管理员列表
export const administratorsColumns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'a0100',
    },
    {
      title: '联系电话',
      dataIndex: 'a0115',
    },
    {
      title: '工会名称',
      dataIndex: 'c0100',
    },
  ]
}

//选择管理员弹框筛选条件
export const administratorsSchemas = (): FormSchema[] => {
  return [
    {
      field: 'key',
      label: '姓名',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'p',
      label: '联系电话',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}
