<template>
  <div>
    <PageWrapper
      :title="title"
      @back="goBack"
    >
      <BasicTable @register="registerTable">
        <template #toolbar>
          <a-button
            type="primary"
            @click="handleClick"
            auth="/administrators/add"
          >
            新增管理员
          </a-button>
        </template>
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :actions="[
                {
                  icon: 'icon-park-outline:remind-disable',
                  label: '禁用',
                  type: 'primary',
                  danger: true,
                  onClick: EnableDisable.bind(null, record, '禁用', 'n'),
                  auth: '/administrators/disable',
                  ifShow: record.state === 'y',
                },
                {
                  icon: 'icon-park-outline:remind',
                  label: '启用',
                  type: 'primary',
                  onClick: EnableDisable.bind(null, record, '启用', 'y'),
                  auth: '/administrators/ToEnableThe',
                  ifShow: record.state === 'n',
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </PageWrapper>

    <AdministratorsModel
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { columns, formSchemas } from './data';
import AdministratorsModel from './administratorsModel.vue';
import { list, save, enableOrDisable } from '/@/api/venueInfo/administrators';
import { useRouter, useRoute } from 'vue-router';
import { PageWrapper } from '/@/components/Page';
import { computed, createVNode } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useMessage } from '@monorepo-yysz/hooks';

const { createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: ['/administrators/add'],
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  beforeFetch(params) {
    params.venueInfoId = route.query.venueInfoId;
    return params;
  },
  searchInfo: { orderBy: 'create_time', sortType: 'desc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/administrators/ToEnableThe', '/administrators/disable'],
  },
});

const router = useRouter();

const route = useRoute();

const title = computed(() => {
  return `${route.query.venueName}--场所管理员`;
});
const [registerModal, { openModal, closeModal }] = useModal();

// 页面左侧点击返回链接时的操作
function goBack() {
  router.go(-1);
}

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

function EnableDisable(record, title, value) {
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定${title},管理员:${record.name}吗?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          enableOrDisable({ autoId: record.autoId, state: value }).then(res => {
            const { code } = res;
            console.log(res);
            if (code === 200) {
              message.success(`${title}成功`);
            } else {
              message.error(`${title}失败`);
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}

function handleSuccess({ record }) {
  const { a0115, a0100 } = record;
  const entity = {
    phone: a0115,
    name: a0100,
    venueInfoId: route.query.venueInfoId,
  };

  save(entity).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: '新增管理员成功',
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `新增管理员失败! ${message}`,
      });
    }
  });
}
</script>
