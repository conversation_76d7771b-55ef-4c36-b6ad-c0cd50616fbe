<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';

defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const title = computed(() => {
  return unref(disabled)
    ? unref(isUpdate)
      ? `${unref(record)?.venueName || ''}详情`
      : `编辑${unref(record)?.venueName || ''}`
    : '新增';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const [registerForm, { resetFields, setFieldsValue, setProps }] = useForm({
  labelWidth: 100,
  schemas: modalFormItem(),
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({ ...data.record });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});
</script>
