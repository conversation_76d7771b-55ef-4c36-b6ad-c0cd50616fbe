import { Image } from 'ant-design-vue'
import { map } from 'lodash-es'
import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useUserStore } from '/@/store/modules/user'

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '阵地名称',
      dataIndex: 'venueName',
    },
    {
      title: '故障描述',
      dataIndex: 'content',
    },
    {
      title: '上报人',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '上报时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ]
}

export const formSchemas = (venueInfoId): FormSchema[] => {
  return [
    {
      field: 'venueName',
      label: '阵地名称',
      colProps: { span: 6 },
      component: 'Input',
      ifShow: !venueInfoId,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'userName',
      label: '上报人',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'startEndDate',
      label: '上报日期',
      component: 'RangePicker',
      colProps: { span: 8 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ]
}

export const modalFormItem = (): FormSchema[] => {
  return [
    // {
    //   field: 'venueName',
    //   label: '阵地名称',
    //   colProps: { span: 24 },
    //   component: 'Input',
    // },
    {
      field: 'content',
      label: '故障描述',
      colProps: { span: 24 },
      component: 'InputTextArea',
    },
    {
      field: 'filePath',
      label: '现场照片',
      colProps: { span: 24 },
      component: 'Image',
      render({ model, field }) {
        const userStore = useUserStore()
        const img = model[field] ? model[field]?.split(',') : []
        return (
          <div class={`!flex`}>
            <Image.PreviewGroup>
              {map(img, v => {
                return (
                  <div class={`!mx-1`}>
                    <Image src={userStore.getPrefix + v} width={70} height={70}></Image>
                  </div>
                )
              })}
            </Image.PreviewGroup>
          </div>
        )
      },
    },
    {
      field: 'userName',
      label: '上报人',
      colProps: { span: 12 },
      component: 'Input',
    },
    {
      field: 'createTime',
      label: '上报时间',
      colProps: { span: 12 },
      component: 'Input',
    },
  ]
}
