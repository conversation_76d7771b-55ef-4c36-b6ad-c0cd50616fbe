<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/fault/view',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <FaultModel
      @register="registerModal"
      :can-fullscreen="false"
      width="70%"
    ></FaultModel>
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import FaultModel from './FaultModel.vue';
import { findFaultRecordList } from '@/api/venueInfo';
import { computed } from 'vue';

const props = defineProps({
  venueInfoId: { type: String, default: null },
});

const schemas = computed(() => {
  return formSchemas(props.venueInfoId);
});
const [registerTable, {}] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  // authInfo: '/channelTopic/add',
  showIndexColumn: false,
  api: findFaultRecordList,
  beforeFetch: params => {
    const { startEndDate } = params;
    if (startEndDate?.length === 2) {
      params.startTime = startEndDate[0] + 'T00:00:00';
      params.endTime = startEndDate[1] + 'T23:59:59';
      params.startEndDate = undefined;
    }

    return { ...params, venueInfoId: props.venueInfoId };
  },
  formConfig: {
    labelWidth: 120,
    schemas: schemas,
    autoSubmitOnEnter: true,
    actionColOptions: {
      span: 4,
    },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/fault/view'],
  },
});

const [registerModal, { openModal }] = useModal();

//详情
function handleView(record) {
  openModal(true, { isUpdate: true, disabled: true, record });
}
</script>
