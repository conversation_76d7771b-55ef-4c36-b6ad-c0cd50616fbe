<template>
  <BasicModal
    @register="registerModal"
    :showOkBtn="false"
    v-bind="$attrs"
    :title="title"
  >
    <FaultTable
      :venueInfoId="venueInfoId"
      v-if="getOpen"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { useModalInner, BasicModal } from '/@/components/Modal';

import { computed, ref, unref } from 'vue';
import FaultTable from '../fault/index.vue';

const venueName = ref('');

const venueInfoId = ref('');
const title = computed(() => {
  return `${unref(venueName)}--故障记录`;
});

const [registerModal, { getOpen }] = useModalInner(async data => {
  if (data.record) {
    venueName.value = data.record.venueName;
    venueInfoId.value = data.record.venueInfoId;
  }
});
</script>
