<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';
import dayjs from 'dayjs';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.venueName || ''}--详情`
      : `编辑${unref(record)?.venueName || ''}`
    : '新增场所';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    const { morningOpenTime,afterOpenTime, morningCloseTime,afterCloseTime, venueImages } = data.record;

    //反填时间
    let morningDailyTimeStart = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    let morningDailyTimeEnd = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    let afterDailyTimeStart = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    let afterDailyTimeEnd = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    morningDailyTimeStart.splice(1, 1, morningOpenTime);
    morningDailyTimeEnd.splice(1, 1, morningCloseTime);
    afterDailyTimeStart.splice(1, 1, afterOpenTime);
    afterDailyTimeEnd.splice(1, 1, afterCloseTime);
    const dailyTime = [dayjs(morningDailyTimeStart.join(' ')), dayjs(morningDailyTimeEnd.join(' '))];
    const afterDailyTime = [dayjs(afterDailyTimeStart.join(' ')), dayjs(afterDailyTimeEnd.join(' '))];

    setFieldsValue({
      ...data.record,
      morningTime: morningOpenTime ? dailyTime : [],
      afterTime: afterOpenTime ? afterDailyTime : [],
      venueImages: venueImages ? venueImages.split(',') : [],
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const { morningTime,afterTime, venueImages, ...val } = await validate();
    let morningOpenTime = '';
    let morningCloseTime = '';
    let afterOpenTime = '';
    let afterCloseTime = '';
    
    if (morningTime && morningTime.length === 2) {
      morningOpenTime =  dayjs(morningTime[0]).format('HH:mm:ss');
      morningCloseTime = dayjs(morningTime[1]).format('HH:mm:ss');
    }
    if (afterTime && afterTime.length === 2) {
      afterOpenTime =  dayjs(afterTime[0]).format('HH:mm:ss');
      afterCloseTime = dayjs(afterTime[1]).format('HH:mm:ss');
    }
    

    emit('success', {
      values: {
        ...unref(record),
        ...val,
        morningOpenTime,
        morningCloseTime,
        afterOpenTime,
        afterCloseTime,
        venueImages: venueImages && venueImages.length > 0 ? venueImages.join(',') : '',
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
