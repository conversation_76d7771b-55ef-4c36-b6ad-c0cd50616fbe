<template>
  <BasicModal
    @register="registerModal"
    :showOkBtn="false"
    v-bind="$attrs"
    :title="title"
  >
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useTable, BasicTable } from '/@/components/Table';
import { computed, ref, unref } from 'vue';
import { recordColumns, recordSchemas } from '/@/views/position/management/data';
import { findRecordList } from '/@/api/venueInfo';

const venueName = ref('');

const venueInfoId = ref('');
const title = computed(() => {
  return `${unref(venueName)}--使用记录`;
});

const [registerModal, {}] = useModalInner(async data => {
  if (data.record) {
    venueName.value = data.record.venueName;
    venueInfoId.value = data.record.venueInfoId;
    reload({
      searchInfo: {
        venueInfoId: unref(venueInfoId),
      },
    });
  }
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: recordColumns(),
  beforeFetch: params => {
    const { startEndDate } = params;
    if (startEndDate?.length === 2) {
      params.startTime = startEndDate[0] + 'T00:00:00';
      params.endTime = startEndDate[1] + 'T23:59:59';
      params.startEndDate = undefined;
    }
    return { ...params, venueInfoId: unref(venueInfoId) };
  },
  searchInfo: {
    venueInfoId: unref(venueInfoId),
  },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: recordSchemas(),
  },
  maxHeight: 450,
  useSearchForm: true,
  bordered: true,
  api: findRecordList,
});
</script>
