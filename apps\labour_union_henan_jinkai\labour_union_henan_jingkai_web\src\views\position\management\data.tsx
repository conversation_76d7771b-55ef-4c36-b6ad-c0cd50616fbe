import { CheckboxGroup, CheckboxOptionType } from 'ant-design-vue';
import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { h } from 'vue';
import { Tinymce } from '@/components/Tinymce';
import { useDictionary } from '@/store/modules/dictionary';
import { map } from 'lodash-es';
import { uploadApi } from '@/api/sys/upload';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { validatePhone } from '@monorepo-yysz/utils';
import dayjs from 'dayjs';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '场所名称',
      dataIndex: 'venueName',
      customRender({ record, text }) {
        const { panoramaUrl } = record;
        if (!!panoramaUrl) {
          return (
            <a
              title={text}
              href={panoramaUrl}
              target={'_blank'}
            >
              {text}
            </a>
          );
        } else {
          return <span title={text}>{text}</span>;
        }
      },
    },
    {
      title: '开放日',
      dataIndex: 'openWeekDay',
      customRender({ text }) {
        const arr = text.split(',');
        const week = map(arr, v => dictionary.getDictionaryMap.get(`week_${v}`)?.dictName)?.join(
          ','
        );
        return <span title={week}>{week}</span>;
      },
    },
    {
      title: '开放时间',
      customRender({ record }) {
        const time = `${record.morningOpenTime}~${record.morningCloseTime}`;
        const afterTime = `${record.afterOpenTime}~${record.afterCloseTime}`;
        return (
          <div>
            <span style={{ color: '#00BCD4' }}>上午: {time}</span> <br />
            <span style={{ color: '#FF5722' }}>下午: {afterTime}</span>
          </div>
        );
      },
      width: 200,
    },
    {
      title: '联系人',
      dataIndex: 'manager',
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      width: 120,
    },
    {
      title: '场所地址',
      dataIndex: 'address',
    },
    {
      title: '是否预约',
      dataIndex: 'reserveState',
      customRender({ text }) {
        return <span>{dictionary.getDictionaryMap.get(`YesOrNo_${text}`)?.dictName}</span>;
      },
      width: 80,
    },
    {
      title: '预约待审核数',
      dataIndex: 'reservationReviewTotal',
      width: 120,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'venueName',
      label: '场所名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'reserveState',
      label: '是否预约',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('YesOrNo'),
        };
      },
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'venueName',
      label: '场所名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'appCover',
      label: 'APP封面图',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
      required: true,
      renderComponentContent() {
        return {
          tip: () => (
            <div class="text-sm leading-7">
              注:图标规格大小为(<span class="text-red-500">200 * 224</span>)以内
            </div>
          ),
        };
      },
      componentProps: function () {
        return {
          imgSize: 200 / 224,
          operateType: 42,
        };
      },
    },
    {
      field: 'venueImages',
      label: '详情轮播图',
      colProps: { span: 12 },
      component: 'Upload',
      componentProps: {
        maxSize: 10,
        maxNumber: 9,
        api: uploadApi,
        accept: ['image/*'],
        uploadParams: {
          operateType: 43,
        },
      },
      rulesMessageJoinLabel: true,
      required: true,
    },
    
    
    {
      field: 'reserveState',
      label: '是否需要预约',
      required: true,
      colProps: { span: 12 },
      component: 'RadioGroup',
      defaultValue: 'n',
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
        };
      },
    },
    {
      field: 'punchRange',
      label: '打卡范围(m)',
      required: true,
      colProps: { span: 12 },
      component: 'InputNumber',
      ifShow: ({ values }) => values.reserveState === 'y',
      defaultValue: 200,
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'capacity',
      label: '容纳人数',
      colProps: { span: 12 },
      component: 'InputNumber',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 1,
      },
      className: '!w-full',
    },
    {
      field: 'venueAcreage',
      label: '场所面积(㎡)',
      colProps: { span: 12 },
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 1,
      },
      className: '!w-full',
    },
    {
      field: 'manager',
      label: '联系人',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      rules: [{ validator: validatePhone, required: true }],
    },
    {
      field: 'openWeekDay',
      label: '每周开放日期',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: ({ values }) => values.reserveState === 'y',
      render({ model, field, disabled }) {
        const value = model[field]?.split(',') || [];
        return (
          <CheckboxGroup
            value={value}
            onChange={e => {
              model[field] = e.join(',');
            }}
            disabled={!!disabled}
            options={dictionary.getDictionaryOpt.get('week') as CheckboxOptionType[]}
          />
        );
      },
    },
    {
      field: 'morningTime',
      required:  ({ values }) => values.reserveState === 'y',
      label: '上午开放时间',
      component: 'TimeRangePicker',
      defaultValue: [
      dayjs(dayjs().startOf('month').hour(8).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
      dayjs(dayjs().endOf('month').hour(12).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
      ],
      colProps: { span: 12 },
      componentProps: {
        placeholder: ['起始时间', '结束时间'],
        disabledTime: () => {
          return {
            disabledHours: () => [13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
          };
        },
        format: 'HH:mm:00',
      },
    },
    {
      field: 'afterTime',
      required: ({ values }) => values.reserveState === 'y',
      label: '下午开放时间',
      component: 'TimeRangePicker',
      colProps: { span: 12 },
      defaultValue: [
      dayjs(dayjs().startOf('month').hour(12).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
      dayjs(dayjs().endOf('month').hour(17).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
      ],
      componentProps: {
        placeholder: ['起始时间', '结束时间'],
        showNow: true,
        disabledTime: () => {
          return {
            disabledHours: () => [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
          };
        },
        format: 'HH:mm:00',
      },
    },
    {
      field: 'address',
      label: '场所位置',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'areaName',
      label: '区县',
      colProps: { span: 24 },
      component: 'ShowSpan',
      rulesMessageJoinLabel: true,
      show: false,
    },
    // {
    //   field: 'panoramaUrl',
    //   label: '全景图地址',
    //   colProps: { span: 24 },
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    // },
    {
      field: 'venueContent',
      label: '场所介绍',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      render: ({ model, field, disabled }) => {
        return h(Tinymce, {
          value: model[field],
          onChange: (value: string) => {
            model[field] = value;
          },
          showImageUpload: false,
          options: {
            readonly: disabled,
          },
        });
      },
    },
    //暂时不要  场所须知 2023-05-08
    // {
    //   field: 'venueInstructions',
    //   label: '场所须知',
    //   colProps: { span: 24 },
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    //   render({ model, field, disabled }) {
    //     return (
    //       <Tinymce
    //         value={model[field]}
    //         options={{ readonly: !!disabled }}
    //         onChange={value => {
    //           model[field] = value
    //         }}
    //       ></Tinymce>
    //     )
    //   },
    // },
  ];
};

export const recordColumns = (): BasicColumn[] => {
  return [
    {
      title: '职工姓名',
      dataIndex: 'userName',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
    },
    {
      title: '使用时间',
      dataIndex: 'updateTime',
    },
  ];
};

export const recordSchemas = (): FormSchema[] => {
  // const dictionary = useDictionary()
  return [
    {
      field: 'userName',
      label: '姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'startEndDate',
      label: '使用日期',
      component: 'RangePicker',
      colProps: { span: 6 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    // {
    //   field: 'recordType',
    //   label: '记录方式',
    //   colProps: { span: 6 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   componentProps: function () {
    //     return {
    //       options: dictionary.getDictionaryOpt.get('venueRecordType'),
    //     }
    //   },
    // },
  ];
};

export const faultRecordFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '上报人',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'startEndDate',
      label: '上报日期',
      component: 'RangePicker',
      colProps: { span: 8 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
};
