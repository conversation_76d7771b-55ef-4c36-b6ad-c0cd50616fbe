<template>
  <div>
    <PageWrapper
      :title="title"
      @back="goBack"
    >
      <BasicTable @register="registerTable">
        <template #toolbar>
          <a-button
            type="primary"
            @click="handleClick"
            auth="/management/add"
          >
            新增场所
          </a-button>
        </template>
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :actions="[
                {
                  icon: 'carbon:task-view',
                  label: '详情',
                  type: 'default',
                  onClick: handleView.bind(null, record),
                  auth: '/management/view',
                },
                {
                  icon: 'fa6-solid:pen-to-square',
                  label: '编辑',
                  type: 'primary',
                  onClick: handleEdit.bind(null, record),
                  auth: '/management/modify',
                },
                {
                  icon: 'ic:baseline-people-outline',
                  label: '管理员',
                  type: 'primary',
                  onClick: handleAdministrators.bind(null, record),
                  auth: '/management/administrators',
                },
                {
                  icon: 'ant-design:audit-outlined',
                  label: '预约管理',
                  type: 'primary',
                  onClick: handleAudit.bind(null, record),
                  auth: '/management/use',
                },
                {
                  icon: 'fluent:delete-16-filled',
                  label: '删除',
                  type: 'primary',
                  danger: true,
                  onClick: handleDelete.bind(null, record),
                  auth: '/management/delete',
                },
                // {
                //   icon: 'healthicons:default-outline',
                //   label: '故障记录',
                //   type: 'default',
                //   onClick: handleFaultRecord.bind(null, record),
                //   auth: '/management/fault',
                // },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </PageWrapper>
    <ManagementModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="70%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import ManagementModal from './ManagementModal.vue';
import { deleteLine, list, saveOrUpdate } from '@/api/venueInfo';
import { PageWrapper } from '@/components/Page';
import { useRouter, useRoute } from 'vue-router';
import { computed } from 'vue';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const router = useRouter();

const route = useRoute();

const title = computed(() => {
  return `${route.query.positionName}--场所管理`;
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: ['/management/add'],
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  beforeFetch(params) {
    params.positionInfoId = route.query.positionInfoId;
    params.systemQueryType = 'manage';
    return params;
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 320,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/management/modify', '/management/use', '/management/delete', '/management/view'],
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

// const [registerRecordModal, { openModal: openRecordModal }] = useModal()

// const [registerFaultRecordModal, { openModal: openFaultRecordModal }] = useModal()

// 页面左侧点击返回链接时的操作
function goBack() {
  router.go(-1);
}

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record) {
  openModal(true, { isUpdate: true, disabled: false, record: record });
}

//详情
function handleView(record) {
  openModal(true, { isUpdate: true, disabled: true, record: record });
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.venueName}`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

//预约管理
function handleAudit({ venueName, venueInfoId }) {
  router.push({ path: '/reservation', query: { venueName, venueInfoId } });
}
//管理员
function handleAdministrators({ venueName, venueInfoId }) {
  router.push({ path: '/administrators', query: { venueName, venueInfoId } });
}

//故障记录
// function handleFaultRecord(record) {
//   openFaultRecordModal(true, { record })
// }

function handleSuccess({ isUpdate, values }) {
  saveOrUpdate({ ...values, positionInfoId: route.query.positionInfoId }).then(
    ({ code, message }) => {
      if (code === 200) {
        createSuccessModal({
          content: `${isUpdate ? '编辑' : '新增'}成功`,
        });
        reload();
        closeModal();
      } else {
        createErrorModal({
          content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
        });
      }
    }
  );
}
</script>
