import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { searchNextUnionForm } from '@/utils/searchNextUnion';

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '拉黑阵地',
      dataIndex: 'positionName',
    },
    {
      title: '阵地所属工会',
      dataIndex: 'companyName',
    },
    {
      title: '拉黑场所',
      dataIndex: 'venueName',
    },
    {
      title: '职工姓名',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      width: 150,
    },
    {
      title: '拉黑时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '职工姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    ...searchNextUnionForm(),
  ];
};

export const modalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'positionName',
      label: '拉黑阵地',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyName',
      label: '阵地所属工会',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'venueName',
      label: '拉黑场所',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'userName',
      label: '职工姓名',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },

    {
      field: 'createTime',
      label: '拉黑时间',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
  ];
};
