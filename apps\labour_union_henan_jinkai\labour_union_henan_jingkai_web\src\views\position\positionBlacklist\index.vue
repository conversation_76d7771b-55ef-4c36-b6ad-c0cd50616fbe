<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/positionBlacklist/view',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                ifShow: userStore.getUserInfo.companyId === record.companyId,
                auth: '/positionBlacklist/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PositionBlacklistModal
      @register="registerModal"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import PositionBlacklistModal from './positionBlacklistModal.vue';
import { list, deleteLine } from '@/api/venueInfo/positionBlacklist';
import { useUserStore } from '@/store/modules/user';
import { useMessage } from '@monorepo-yysz/hooks';

const userStore = useUserStore();

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 180,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/positionBlacklist/view', '/positionBlacklist/delete'],
  },
});

const [registerModal, { openModal }] = useModal();

//详情
function handleView(record) {
  openModal(true, { isUpdate: true, disabled: true, record: record });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除[${record.userName}]黑名单记录?`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}
</script>
