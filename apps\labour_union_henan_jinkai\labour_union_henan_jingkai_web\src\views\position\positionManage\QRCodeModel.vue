<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    okText="下载二维码"
  >
    <div class="flex justify-center items-center">
      <Image
        :src="record?.data"
        :width="450"
        :height="450"
      />
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { Image } from 'ant-design-vue';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const title = computed(() => {
  return `${unref(record)?.positionName}--二维码预览`;
});

const [registerModal, {}] = useModalInner(async data => {
  record.value = data.record;
});

async function handleSubmit() {
  try {
    emit('success', {
      values: {
        ...unref(record),
      },
    });
  } finally {
  }
}
</script>
