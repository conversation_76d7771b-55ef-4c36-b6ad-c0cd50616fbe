import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { list as venueTypeManageList } from '@/api/venueInfo/venueTypeManage';
import { list as venueServiceTypeList } from '@/api/venueInfo/venueServiceType';
import { cloneDeep, filter } from 'lodash-es';
import { uploadApi } from '@/api/sys/upload';
import { searchNextUnionForm } from '/@/utils/searchNextUnion';
import { optionalArea } from '@/api/venueInfo/positionManage';
import { CheckboxGroup, CheckboxOptionType } from 'ant-design-vue';
import dayjs from 'dayjs';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '阵地名称',
      dataIndex: 'positionName',
    },
    {
      title: '所属分类',
      dataIndex: 'typeName',
      width: 120,
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
    },
    {
      title: '所属区域',
      dataIndex: 'areaCode',
      width: 120,
      customRender: ({ text }) => {
        const education = dictionary.getDictionaryMap.get(`regionCode_${text}`)?.dictName;
        return <span title={education}>{education}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '发布状态',
      dataIndex: 'publishStatus',
      width: 80,
      customRender: ({ text }) => {
        const education = dictionary.getDictionaryMap.get(`newsPublishStatus_${text}`)?.dictName;
        return <span title={education}>{education}</span>;
      },
    },
    // {
    //   title: '访问量',
    //   dataIndex: 'clickNumber',
    //   width: 90,
    // },
    {
      title: '签到人数',
      dataIndex: 'accumulateTotal',
      width: 90,
    },
    // {
    //   title: '开门次数',
    //   dataIndex: 'openTotal',
    //   width: 90,
    // },
    {
      title: '待审核/预约总数',
      dataIndex: 'reservationReviewTotal',
      width: 130,
      customRender: ({ text, record }) => {
        return (
          <span title={text + '/' + record.reservationTotal}>
            {text + '/' + record.reservationTotal}
          </span>
        );
      },
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'positionName',
      label: '阵地名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'typeManageId',
      label: '所属分类',
      colProps: { span: 6 },
      component: 'ApiSelect',
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          api: venueTypeManageList,
          params: {
            pageSize: 0,
            orderBy: 'sort_number',
            sortType: 'asc',
          },
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.typeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'typeName', value: 'typeManageId' },
        };
      },
    },
    {
      field: 'publishStatus',
      label: '发布状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: filter(
            cloneDeep(dictionary.getDictionaryOpt.get(`newsPublishStatus`)),
            v => v.value !== '-1'
          ),
        };
      },
    },
    {
      field: 'areaCode',
      label: '所属区域',
      colProps: { span: 6 },
      component: 'ApiSelect',
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          api: optionalArea,
          params: {
            groupCode: 'regionCode',
          },
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.dictName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'dictName', value: 'dictCode' },
        };
      },
    },
    ...searchNextUnionForm(),
  ];
};

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'positionName',
      label: '阵地名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 50,
      },
    },
    {
      field: 'positionCoverImage',
      label: '封面图',
      required: true,
      component: 'CropperForm',
      colProps: { span: 24 },
      renderComponentContent() {
        return {
          tip: () => (
            <div class="text-sm leading-7">
              注:图标规格大小为(<span class="text-red-500">200 * 224</span>)以内
            </div>
          ),
        };
      },
      componentProps({}) {
        return {
          imgSize: 200 / 224,
          operateType: 42,
        };
      },
    },
    {
      field: 'positionImages',
      label: '详情轮播图',
      colProps: { span: 12 },
      component: 'Upload',
      componentProps: {
        maxSize: 10,
        maxNumber: 9,
        api: uploadApi,
        accept: ['image/*'],
        uploadParams: {
          operateType: 40,
        },
      },
      rulesMessageJoinLabel: true,
      required: true,
    },
    // {
    //   field: 'entranceState',
    //   label: '是否门禁',
    //   required: true,
    //   colProps: { span: 12 },
    //   component: 'RadioGroup',
    //   defaultValue: 'n',
    //   componentProps: function () {
    //     return {
    //       options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
    //     };
    //   },
    // },
    // {
    //   field: 'deviceCode',
    //   label: '门禁设备标识',
    //   colProps: { span: 12 },
    //   ifShow: ({ values }) => values.entranceState === 'y',
    //   component: 'Input',
    //   required: true,
    //   rulesMessageJoinLabel: true,
    // },
    {
      field: 'typeManageId',
      label: '所属分类',
      colProps: { span: 12 },
      component: 'ApiSelect',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          api: venueTypeManageList,
          params: {
            pageSize: 0,
            orderBy: 'sort_number',
            sortType: 'asc',
          },
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.typeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'typeName', value: 'typeManageId' },
        };
      },
    },
    {
      field: 'areaCode',
      label: '所属区域',
      required: true,
      component: 'ApiSelect',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          api: optionalArea,
          params: {
            groupCode: 'regionCode',
          },
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.dictName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'dictName', value: 'dictCode' },
        };
      },
    },
    {
      field: 'manager',
      label: '负责人',
      colProps: { span: 12 },
      component: 'Input',
      // required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 20,
      },
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 12 },
      component: 'Input',
      // required: true,
      rulesMessageJoinLabel: true,
      // componentProps: {
      //   autocomplete: 'off',
      //   showCount: true,
      //   maxlength: 11,
      // },
    },
    {
      field: 'capacity',
      label: '容纳人数',
      colProps: { span: 12 },
      component: 'InputNumber',
      // required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 1,
      },
      className: '!w-full',
    },
    // {
    //   field: 'serviceTime',
    //   label: '服务时间',
    //   colProps: { span: 24 },
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    //   componentProps: {
    //     showCount: true,
    //     maxlength: 80,
    //   },
    // },
    {
      field: 'openWeekDay',
      label: '每周开放日期',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: true,
      render({ model, field, disabled }) {
        const value = model[field]?.split(',') || [];
        return (
          <CheckboxGroup
            value={value}
            onChange={e => {
              model[field] = e.join(',');
            }}
            disabled={!!disabled}
            options={dictionary.getDictionaryOpt.get('week') as CheckboxOptionType[]}
          />
        );
      },
    },
    {
      field: 'morningTime',
      required: true,
      label: '上午开放时间',
      component: 'TimeRangePicker',
      colProps: { span: 12 },
      defaultValue: [
      dayjs(dayjs().startOf('month').hour(8).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
      dayjs(dayjs().endOf('month').hour(12).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
      ],
      componentProps: {
        placeholder: ['起始时间', '结束时间'],
        showNow: true,
        disabledTime: () => {
          return {
            disabledHours: () => [13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23],
          };
        },
      },
    },
    {
      field: 'afterTime',
      required: true,
      label: '下午开放时间',
      component: 'TimeRangePicker',
      colProps: { span: 12 },
      defaultValue: [
      dayjs(dayjs().startOf('month').hour(12).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
      dayjs(dayjs().endOf('month').hour(17).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss')),
      ],
      componentProps: {
        placeholder: ['起始时间', '结束时间'],
        showNow: true,
        disabledTime: () => {
          return {
            disabledHours: () => [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
          };
        },
      },
    },
    {
      field: 'serviceContent',
      label: '服务内容',
      colProps: { span: 24 },
      component: 'ApiSelect',
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          api: venueServiceTypeList,
          params: {
            pageSize: 0,
            orderBy: 'sort_number',
            sortType: 'asc',
          },
          mode: 'multiple',
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.serviceTypeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'serviceTypeName', value: 'serviceTypeId' },
        };
      },
    },
    {
      field: 'panoramaUrl',
      label: 'VR地址',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 200,
      },
    },
    {
      field: 'address',
      required: true,
      label: '阵地地址',
      component: 'MapSelect',
      rest: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          onChangeLnglat: lnglat => (formModel['coordinate'] = lnglat),
          lnglat: formModel['coordinate'],
        };
      },
    },
    {
      field: 'coordinate',
      label: '地址坐标',
      colProps: { span: 24 },
      component: 'ShowSpan',
      rulesMessageJoinLabel: true,
      show: false,
    },
    //暂时不要  介绍内容 2023-05-08
    // {
    //   field: 'presentation',
    //   component: 'Input',
    //   label: '介绍内容',
    //   render: ({ model, field, disabled }) => {
    //     return h(Tinymce, {
    //       value: model[field],
    //       onChange: (value: string) => {
    //         model[field] = value
    //       },
    //       showImageUpload: false,
    //       options: {
    //         readonly: disabled,
    //       },
    //     })
    //   },
    // },
  ];
};

//发布弹窗使用
export const releaseModalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'publishTime',
      label: '发布时间',
      component: 'DatePicker',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      required: true,
      componentProps: {
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        format: 'YYYY-MM-DD HH:mm',
        showTime: true,
      },
    },
  ];
};

export const signInColumnsLine = (): BasicColumn[] => {
  return [
    {
      title: '职工姓名',
      dataIndex: 'userName',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
    },
    {
      title: '签到时间',
      dataIndex: 'updateTime',
    },
  ];
};

export const accessControlColumnsLine = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '评价人姓名',
      dataIndex: 'evaluationName',
    },
    {
      title: '评分',
      dataIndex: 'positionScore',
      customRender: ({ text }: { text: number }) => {
        // 构造 HTML 内容
        let starsHTML = '';
        for (let i = 0; i < 5; i++) {
          starsHTML += `<span style="cursor: pointer;" class="${i < text ? 'text-yellow-500' : 'text-gray-400'}">
                        ${i < text ? '★' : '☆'}
                      </span>`;
        }
        return <span innerHTML={starsHTML}></span>;
      },
    },
    {
      title: '评价内容',
      dataIndex: 'positionContent',
    },
    {
      title: '评价时间',
      dataIndex: 'evaluationTime',
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      customRender: ({ text }) => {
        return (
          <span
            class={`${text === 'pass' ? 'text-green-500' : text === 'refuse' ? 'text-red-500' : ''}`}
          >
            {dictionary.getDictionaryMap.get(`newsCommentAuditState_${text}`)?.dictName || ''}
          </span>
        );
      },
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
    },
    {
      title: '审核人',
      dataIndex: 'auditName',
    },
  ];
};

export const faultColumnsLine = (): BasicColumn[] => {
  // const dictionary = useDictionary()
  return [
    {
      title: '故障描述',
      dataIndex: 'content',
    },
    {
      title: '上报人',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '上报时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

//搜索条件
export const signInFormSchemasLine = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '职工姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'startEndDate',
      label: '签到时间',
      component: 'RangePicker',
      colProps: { span: 6 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
};

//搜索条件
export const accessControlFormSchemasLine = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'evaluationName',
      label: '评价人姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'positionContent',
      label: '评价内容',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'auditStatus',
      label: '审核状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: filter(
            cloneDeep(dictionary.getDictionaryOpt.get(`newsCommentAuditState`)),
            v => v.value !== '-1'
          ),
        };
      },
    },
  ];
};
//搜索条件
export const faultFormSchemasLine = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '上报人',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'startEndDate',
      label: '上报日期',
      component: 'RangePicker',
      colProps: { span: 6 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
  ];
};

export const auditModalForm = (): FormSchema[] => {
  return [
    {
      field: 'operateType',
      label: '审核结果',
      component: 'RadioGroup',
      required: true,
      componentProps: {
        options: [
          { label: '通过', value: 'pass' },
          { label: '驳回', value: 'refuse' },
        ],
      },
    },
    {
      field: 'remark',
      label: '审核意见',
      required: true,
      component: 'InputTextArea',
      componentProps: {
        maxlength: 255,
        showCount: true,
      },
    },
  ];
};
