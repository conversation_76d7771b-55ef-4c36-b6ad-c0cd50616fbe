<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/positionManage/add" 
        >
          新增阵地
        </a-button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/positionManage/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                ifShow:
                  userStore.getUserInfo.companyId === record.companyId &&
                  record.publishStatus !== '10',
                auth: '/positionManage/modify',
              },
              {
                icon: 'carbon:cut-out',
                label: '发布',
                type: 'primary',
                onClick: pushOrCutdown.bind(null, record),
                auth: '/positionManage/release',
                ifShow:
                  (!('0' !== record.publishStatus ) || !('20' !== record.publishStatus)) &&
                  userStore.getUserInfo.companyId === record.companyId,
              },
              {
                icon: 'bx:log-out-circle',
                label: '撤销',
                type: 'primary',
                onClick: pushOrCutdown.bind(null, record),
                auth: '/positionManage/revocation',
                ifShow:
                  !('10' !== record.publishStatus) &&
                  userStore.getUserInfo.companyId === record.companyId,
              },
              {
                icon: 'mdi:calendar-text-outline',
                label: '场所',
                type: 'primary',
                onClick: handleManagement.bind(null, record),
                auth: '/positionManage/management',
                ifShow: userStore.getUserInfo.companyId === record.companyId,
              },
              {
                icon: 'ic:baseline-qrcode',
                label: '二维码',
                type: 'primary',
                onClick: handleDownQr.bind(null, record),
                auth: '/positionManage/qrCode',
              },
              {
                icon: 'mdi:user-outline',
                label: '使用管理',
                type: 'primary',
                onClick: handleUsage.bind(null, record),
                auth: '/positionManage/usage',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                ifShow:
                  userStore.getUserInfo.companyId === record.companyId &&
                  record.publishStatus !== '10',
                auth: '/positionManage/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <PositionManageModel
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
    <QRCodeModel
      @register="QRCodeRegisterModal"
      @success="QRCodeHandleSuccess"
      :can-fullscreen="false"
      width="40%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import PositionManageModel from './positionManageModel.vue';
import QRCodeModel from './QRCodeModel.vue';
import {
  list,
  view,
  deleteLine,
  saveOrUpdate,
  releaseOrRevokePosition,
} from '@/api/venueInfo/positionManage';
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';
import { getQrCode } from '@/api/venueInfo';
import { ref, unref } from 'vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { downloadByBase64 } from '@monorepo-yysz/utils';

const router = useRouter();
//搜索条件
const searchParams = ref();

const userStore = useUserStore();

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: ['/positionManage/add'],
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
    // submitOnChange: true,
  },
  beforeFetch: params => {
    searchParams.value = params;
    return params;
  },
  searchInfo: {
    // orderBy: 'create_time',
    // sortType: 'desc',
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 350,
    dataIndex: 'action',
    fixed: undefined,
    align: 'left',
    // class: '!text-center',
    className: 'deal-action',
    auth: [
      '/positionManage/view',
      '/positionManage/modify',
      '/positionManage/release',
      '/positionManage/revocation',
      '/positionManage/management',
      '/positionManage/qrCode',
      '/positionManage/usage',
      '/positionManage/delete',
    ],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();
const [QRCodeRegisterModal, { openModal: QRCodeOpenModal, closeModal: QRCodeCloseModal }] =
  useModal();

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record) {
  view({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

function pushOrCutdown(record) {
  if (record.publishStatus === '10') {
    releaseLine(record);
  } else if (record.publishStatus === '0' || record.publishStatus === '20') {
    openModal(true, { record, isPublish: true, isUpdate: true });
  }
}

//详情
function handleView(record) {
  view({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.positionName}`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload({ ...unref(searchParams) });
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

//场所管理
function handleManagement({ positionName, positionInfoId }: Recordable) {
  router.push({
    path: '/positionManagement',
    query: { positionName, positionInfoId },
  });
}

//使用管理
function handleUsage({ positionName, positionInfoId, deviceCode }: Recordable) {
  router.push({
    path: '/usage',
    query: {
      positionName,
      positionInfoId,
      deviceCode,
    },
  });
}

//下载二维码
function handleDownQr(record) {
  getQrCode({ ...record, recordType: 'scanQrCode' }).then(({ code, data }) => {
    if (code === 200) {
      QRCodeOpenModal(true, { isUpdate: false, disabled: false, record: { ...record, data } });
    }
  });
}

function releaseLine(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要撤销${record.positionName || ''}`,
    onOk: function () {
      releaseOrRevokePosition({ autoId: record.autoId, publishStatus: '20' }).then(
        ({ code, message }) => {
          if (code === 200) {
            createSuccessModal({ content: `撤销成功` });
            reload({ ...unref(searchParams) });
          } else {
            createErrorModal({ content: `撤销失败，${message}` });
          }
        }
      );
    },
  });
}

function handleSuccess({ values, isUpdate, isPublish }) {
  if (!isPublish) {
    saveOrUpdate(values).then(({ code, message }) => {
      if (code === 200) {
        createSuccessModal({
          content: `${isUpdate ? '编辑' : '新增'}成功`,
        });
        reload({ ...unref(searchParams) });
        closeModal();
      } else {
        createErrorModal({
          content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
        });
      }
    });
  } else {
    releaseOrRevokePosition({
      autoId: values.autoId,
      publishTime: values.publishTime,
      publishStatus: '10',
    }).then(({ code, message }) => {
      if (code === 200) {
        createSuccessModal({ content: `发布成功` });
        reload({ ...unref(searchParams) });
        closeModal();
      } else {
        createErrorModal({ content: `发布失败，${message}` });
      }
    });
  }
}
function QRCodeHandleSuccess({ values }) {
  const { positionName, data } = values;
  downloadByBase64(data as string, `${positionName}阵地二维码.png`);
  QRCodeCloseModal();
}
</script>
