<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem, releaseModalFormItem } from './data';
import dayjs from 'dayjs';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const isPublish = ref(false);

const title = computed(() => {
  return unref(isPublish)
    ? `发布--${unref(record)?.positionName || ''}`
    : unref(isUpdate)
      ? unref(disabled)
        ? `${unref(record)?.positionName || ''}--详情`
        : `编辑--${unref(record)?.positionName || ''}`
      : '新增阵地';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return !unref(isPublish) ? modalFormItem() : releaseModalFormItem();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  isPublish.value = !!data.isPublish;

  if (unref(isUpdate)) {
    const { publishTime, morningOpenTime,afterOpenTime, morningCloseTime,afterCloseTime, positionImages } = data.record;
    let morningDailyTimeStart = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    let morningDailyTimeEnd = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    let afterDailyTimeStart = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    let afterDailyTimeEnd = dayjs().format('YYYY-MM-DD HH:mm:ss').split(' ');
    morningDailyTimeStart.splice(1, 1, morningOpenTime);
    morningDailyTimeEnd.splice(1, 1, morningCloseTime);
    afterDailyTimeStart.splice(1, 1, afterOpenTime);
    afterDailyTimeEnd.splice(1, 1, afterCloseTime);
    const dailyTime = [dayjs(morningDailyTimeStart.join(' ')), dayjs(morningDailyTimeEnd.join(' '))];
    const afterDailyTime = [dayjs(afterDailyTimeStart.join(' ')), dayjs(afterDailyTimeEnd.join(' '))];
    
    setFieldsValue({
      ...data.record,
      morningTime: morningOpenTime ? dailyTime : [],
      afterTime: afterOpenTime ? afterDailyTime : [],
      publishTime: publishTime ? dayjs(publishTime) : dayjs(),
      positionImages: positionImages ? positionImages.split(',') : [],
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate(); 
    const { morningTime,afterTime, positionImages } = values;
    let morningOpenTime = '';
    let morningCloseTime = '';
    let afterOpenTime = '';
    let afterCloseTime = '';
    if (morningTime && morningTime.length === 2) {
      morningOpenTime =  dayjs(morningTime[0]).format('HH:mm:ss');
      morningCloseTime = dayjs(morningTime[1]).format('HH:mm:ss');
    }
    if (afterTime && afterTime.length === 2) {
      afterOpenTime =  dayjs(afterTime[0]).format('HH:mm:ss');
      afterCloseTime = dayjs(afterTime[1]).format('HH:mm:ss');
    }
    emit('success', {
      values: {
        ...unref(record),
        ...values,
        morningOpenTime,
        morningCloseTime,
        afterOpenTime,
        afterCloseTime,
        positionImages: positionImages && positionImages.length > 0 ? positionImages.join(',') : '',
      },
      isUpdate: unref(isUpdate),
      isPublish: unref(isPublish),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
