<template>
  <PageWrapper
    :title="title"
    @back="goBack"
  >
    <Tabs
      type="card"
      v-model:active-key="recordKey"
    >
      <Tabs.TabPane
        key="signIn"
        tab="签到记录"
      >
      </Tabs.TabPane>
      <Tabs.TabPane
        key="accessControl"
        tab="评价记录"
      >
        <!-- </Tabs.TabPane>
      <Tabs.TabPane
        key="fault"
        tab="故障上报记录"
      > -->
      </Tabs.TabPane>
    </Tabs>
    <BasicTable
      @register="registerS"
      v-if="recordKey === 'signIn'"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/usageManagement/signInRecord',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <BasicTable
      @register="registerA"
      v-else-if="recordKey === 'accessControl'"
    >
      <template #toolbar>
        <!-- <a-button type="primary" @click="handleAuditBatch" auth="/usageManagement/accessAudit">
        批量审核
      </a-button> -->
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              // {
              //   icon: 'carbon:task-view',
              //   label: '详情',
              //   type: 'default',
              //   onClick: handleView.bind(null, record),
              //   auth: '/usageManagement/accessControlRecord',
              // },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                onClick: handleAudit.bind(null, record),
                disabled: record.auditStatus !== 'wait',
                auth: '/usageManagement/accessAudit',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/usageManagement/accessDelete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <BasicTable
      @register="registerF"
      v-else
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/usageManagement/failureRecord',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <FaultModel
      @register="registerModal"
      :can-fullscreen="false"
      width="70%"
    />
    <RecordModel
      :can-fullscreen="false"
      width="70%"
      @register="registerRecordModal"
    />
    <AuditModal
      @register="registerAuditModal"
      @success="handleAuditSuccess"
      :canFullscreen="false"
      width="50%"
    />
  </PageWrapper>
</template>

<script lang="ts" setup>
import { Tabs } from 'ant-design-vue';
import { ref, unref, computed } from 'vue';
import { useTable, BasicTable, TableAction, BasicTableProps } from '@/components/Table';
import {
  signInColumnsLine,
  faultColumnsLine,
  signInFormSchemasLine,
  faultFormSchemasLine,
  accessControlColumnsLine,
  accessControlFormSchemasLine,
} from './data';
import { useRoute, useRouter } from 'vue-router';
import {
  findFaultRecordList,
  findRecordList,
  recordView,
  findFaultRecordView,
  deleteCommentLine,
  findCommentList,
  batchAudit,
} from '@/api/venueInfo';
import FaultModel from '../fault/FaultModel.vue';
import { useModal } from '@/components/Modal';
import { PageWrapper } from '@/components/Page';
import RecordModel from '../record/RecordModel.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import AuditModal from './auditModal.vue';
import { map } from 'lodash-es';
import { Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { CloseCircleFilled } from '@ant-design/icons-vue';

const route = useRoute();

const router = useRouter();

const recordKey = ref('signIn');

const title = computed(() => {
  return `${route.query.positionName}--使用管理`;
});

//搜索条件
const searchParams = ref();

const signInSchemas = computed(() => {
  return signInFormSchemasLine();
});
const faultSchemas = computed(() => {
  return faultFormSchemasLine();
});

const accessControlSchemas = computed(() => {
  return accessControlFormSchemasLine();
});

const [registerModal, { openModal }] = useModal();
const [registerRecordModal, { openModal: openRecordModal }] = useModal();

const [registerAuditModal, { openModal: openAuditModal, closeModal: closeAuditModal }] = useModal();

const signInBasicProps: BasicTableProps = {
  rowKey: 'autoId',
  columns: signInColumnsLine(),
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: signInSchemas,
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  beforeFetch: params => {
    const { startEndDate } = params;
    if (startEndDate?.length === 2) {
      params.startTime = startEndDate[0] + 'T00:00:00';
      params.endTime = startEndDate[1] + 'T23:59:59';
      params.startEndDate = undefined;
    }
    params.recordType = 'scanQrCode';
    return { ...params };
  },
  searchInfo: {
    positionInfoId: route.query.positionInfoId,
    orderBy: 'update_time',
    sortType: 'desc',
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 100,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/usageManagement/signInRecord'],
  },
};
const accessControlBasicProps: BasicTableProps = {
  rowKey: 'autoId',
  columns: accessControlColumnsLine(),
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: accessControlSchemas.value,
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.auditStatus !== 'wait',
    }),
  },
  beforeFetch: params => {
    searchParams.value = params;
    return { ...params };
  },

  searchInfo: {
    positionInfoId: route.query.positionInfoId,
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/usageManagement/accessControlRecord',
      '/usageManagement/accessAudit',
      '/usageManagement/accessDelete',
    ],
  },
};
const faultBasicProps: BasicTableProps = {
  rowKey: 'autoId',
  columns: faultColumnsLine(),
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: faultSchemas.value,
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  beforeFetch: params => {
    const { startEndDate } = params;
    if (startEndDate?.length === 2) {
      params.startTime = startEndDate[0] + 'T00:00:00';
      params.endTime = startEndDate[1] + 'T23:59:59';
      params.startEndDate = undefined;
    }
    return { ...params };
  },
  searchInfo: {
    positionInfoId: route.query.positionInfoId,
    orderBy: 'create_time',
    sortType: 'desc',
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 100,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/usageManagement/failureRecord'],
  },
};

const [registerS] = useTable({ ...signInBasicProps, api: findRecordList });

const [registerA, { reload, getSelectRows }] = useTable({
  ...accessControlBasicProps,
  api: findCommentList,
});

const [registerF] = useTable({ ...faultBasicProps, api: findFaultRecordList });

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

// 页面左侧点击返回链接时的操作
function goBack() {
  router.go(-1);
}

async function handleView(record) {
  if (unref(recordKey) === 'signIn') {
    const { data } = await recordView({ autoId: record.autoId });
    openRecordModal(true, { isUpdate: true, disabled: true, record: data, recordKey });
  } else if (unref(recordKey) === 'accessControl') {
    const { data } = await recordView({ autoId: record.autoId });
    openRecordModal(true, { isUpdate: true, disabled: true, record: data, recordKey });
  } else {
    findFaultRecordView({ autoId: record.autoId }).then(res => {
      //故障记录详情
      openModal(true, { isUpdate: true, disabled: true, record: res.data });
    });
  }
}

function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除该评价?`,
    onOk: function () {
      deleteCommentLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

//批量审核
function handleAuditBatch() {
  const rows = getSelectRows();
  if (!rows || rows.length === 0) {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '请选择至少一条数据进行审核！',
      okText: '确认',
      closable: true,
    });
    return false;
  }
  openAuditModal(true, {
    record: { autoId: map(rows, v => v.autoId), isBatch: 'y' },
    notShow: true,
    isBatch: true,
  });
}

function handleAudit(record) {
  openAuditModal(true, {
    isUpload: true,
    record,
  });
}

function handleAuditSuccess(item) {
  const { autoId, ...params } = item;
  batchAudit({
    todoValueList: typeof item.autoId === 'number' ? [item.autoId] : item.autoId,
    ...params,
  }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({ content: '处理成功' });
      reload();
      closeAuditModal();
    } else {
      createErrorModal({ content: `${message}` });
    }
  });
}
</script>
