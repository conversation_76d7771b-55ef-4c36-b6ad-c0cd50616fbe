<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :canFullscreen="false"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { auditFormItem } from './data';

const emit = defineEmits(['register', 'success']);

const record = ref();
const userName = ref<any>();
const recordIds = ref<any>();
const recordId = ref('');
const title = computed(() => {
  return `${unref(userName) ? `${unref(userName)}--` : ''}预约审核`;
});

const [registerForm, { resetFields, validate }] = useForm({
  labelWidth: 100,
  schemas: auditFormItem(),
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  record.value = data.record;
  userName.value = data.record?.userName;
  recordIds.value = data.recordIds;
  recordId.value = data.recordId;
  setModalProps({ confirmLoading: false });
});

const handleSubmit = async () => {
  const values = await validate();
  console.log(unref(recordId),111);
  
  emit('success', {
    values: {
      ...values,
      // recordIds: unref(record) ? [unref(record).recordId] : unref(recordIds)
      recordId: unref(recordId)
      
    },
  });
};
</script>
