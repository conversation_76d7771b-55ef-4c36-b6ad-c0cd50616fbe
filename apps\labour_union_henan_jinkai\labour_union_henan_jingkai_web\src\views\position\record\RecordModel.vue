<template>
  <BasicModal
    :title="title"
    v-bind="$attrs"
    @register="registerModal"
  >
    <BasicForm
      :class="disabledClass"
      @register="registerForm"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from './data';

defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const recordKey = ref();

const title = computed(() => {
  return unref(disabled)
    ? unref(isUpdate)
      ? `${unref(record)?.userName || ''}--详情`
      : `编辑${unref(record)?.userName || ''}`
    : '新增';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(unref(recordKey));
});

const [registerForm, { resetFields, setFieldsValue, setProps }] = useForm({
  labelWidth: 100,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  recordKey.value = data.recordKey;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({ ...data.record });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});
</script>
