import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '职工姓名',
      dataIndex: 'userName',
    },
    {
      title: '预约日期',
      dataIndex: 'reservationDate',
    },
    // {
    //   title: '场所名称',
    //   dataIndex: 'venueName',
    // },
    {
      title: '状态',
      dataIndex: 'state',
      customRender: ({ text }) => {
        const dictName = dictionary.getDictionaryMap.get(`venueRecordState_${text}`)?.dictName;
        return <span title={dictName}>{dictName}</span>;
      },
    },
    {
      title: '审核意见',
      dataIndex: 'auditOpinion',
    },
    {
      title: '是否已加入黑名单',
      dataIndex: 'whetherBlackList',
      customRender: ({ text }) => {
        const dictName = dictionary.getDictionaryMap.get(`YesOrNo_${text}`)?.dictName;
        return <span title={dictName}>{dictName}</span>;
      },
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'userName',
      label: '职工姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'state',
      label: '状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get(`venueRecordState`),
        };
      },
    },
    {
      field: 'startEndDate',
      label: '预约日期',
      component: 'RangePicker',
      colProps: { span: 6 },
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'whetherBlackList',
      label: '是否已加入黑名单',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get(`YesOrNo`),
        };
      },
    },
  ];
};

export const modalFormItem = (recordKey): FormSchema[] => {
  const dictionary = useDictionary();
  if ('audit' === recordKey) {
    return [
      // {
      //   field: 'venueName',
      //   label: '场所名称',
      //   colProps: { span: 24 },
      //   component: 'Input',
      //   required: true,
      //   rulesMessageJoinLabel: true,
      // },
      {
        field: 'userName',
        label: '职工姓名',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
      },
      {
        field: 'phone',
        label: '联系电话',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
      },
      {
        field: 'reservationDate',
        label: '预约时间',
        component: 'Input',
        colProps: { span: 12 },
        render: ({ values }) => {
          const { reservationDate, reservationStartTime, reservationEndTime } = values;
          return <span>{`${reservationDate} ${reservationStartTime}~${reservationEndTime} `}</span>;
        },
      },
      {
        field: 'companyName',
        label: '所属工会',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
      },
      {
        field: 'state',
        label: '状态',
        colProps: { span: 24 },
        component: 'Select',
        required: true,
        rulesMessageJoinLabel: true,
        componentProps: {
          options: dictionary.getDictionaryOpt.get('venueRecordState'),
        },
      },
      {
        field: 'auditOpinion',
        label: '审核意见',
        component: 'InputTextArea',
        componentProps: {
          autocomplete: 'off',
        },
      },

      { field: 'reservationStartTime', label: '所属工会', component: 'Input', ifShow: false },
      { field: 'reservationEndTime', label: '所属工会', component: 'Input', ifShow: false },
    ];
  } else if ('signIn' === recordKey) {
    return [
      {
        field: 'userName',
        label: '职工姓名',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
      },
      {
        field: 'phone',
        label: '联系电话',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
      },
      {
        field: 'companyName',
        label: '所属工会',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
      },
      {
        field: 'updateTime',
        label: '签到时间',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
      },
    ];
  } else {
    return [
      {
        field: 'userName',
        label: '职工姓名',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
      },
      {
        field: 'phone',
        label: '联系电话',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
      },
      {
        field: 'companyName',
        label: '所属工会',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
      },
      {
        field: 'visitTime',
        label: '使用时间',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
      },
    ];
  }
};

export const auditFormItem = (): FormSchema[] => {
  return [
    {
      field: 'state',
      label: '是否通过',
      component: 'RadioGroup',
      required: true,
      defaultValue: 'pass',
      componentProps: {
        options: [
          { label: '通过', value: 'pass' },
          { label: '驳回', value: 'refuse' },
        ],
      },
    },
    {
      field: 'auditOpinion',
      label: '审核意见',
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入审核意见',
      },
    },
  ];
};
