<template>
  <div>
    <PageWrapper
      :title="title"
      @back="goBack"
    >
      <BasicTable @register="registerTable">
        <!-- <template #toolbar>
          <a-button
            type="primary"
            @click="handleAudit(null)"
            auth="/position/record/batchAudit"
            >批量审核</a-button
          >
        </template> -->
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :actions="[
                {
                  icon: 'carbon:task-view',
                  label: '详情',
                  type: 'default',
                  onClick: handleView.bind(null, record),
                  auth: '/position/record/view',
                },
                {
                  icon: 'ant-design:audit-outlined',
                  label: '审核',
                  type: 'primary',
                  disabled: record.state !== 'review',
                  onClick: handleAudit.bind(null, record),
                  auth: '/position/record/audit',
                },
                {
                  icon: 'clarity:list-solid',
                  label: '加入黑名单',
                  type: 'primary',
                  disabled: record.state !== 'useExpire' || 'n' !== record.whetherBlackList,
                  onClick: handleBlacklist.bind(null, record),
                  auth: '/position/record/blacklist',
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </PageWrapper>
    <AuditModel
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    ></AuditModel>
    <RecordModel
      :can-fullscreen="false"
      width="50%"
      @register="registerRecordModal"
    ></RecordModel>
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import { auditRecord, findRecordList, recordView } from '@/api/venueInfo';
import { save } from '@/api/venueInfo/positionBlacklist';
import AuditModel from '@/views/position/record/AuditModel.vue';
import { Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { CheckCircleOutlined, CloseCircleFilled } from '@ant-design/icons-vue';
import { map } from 'lodash-es';
import RecordModel from './RecordModel.vue';
import { useRouter, useRoute } from 'vue-router';
import { PageWrapper } from '@/components/Page';
import { computed } from 'vue';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { getSelectRows, clearSelectedRowKeys, reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: '/position/record/batchAudit',
  showIndexColumn: false,
  api: findRecordList,
  beforeFetch: params => {
    const { startEndDate } = params;
    if (startEndDate?.length === 2) {
      params.startTime = startEndDate[0] + 'T00:00:00';
      params.endTime = startEndDate[1] + 'T23:59:59';
      params.startEndDate = undefined;
    }
    params.sortType = 'desc';
    params.orderBy = 'create_time';
    params.recordType = 'reservation';
    params.venueInfoId = route.query.venueInfoId;
    params.systemQueryType = 'manage';
    return params;
  },
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    // actionColOptions: { span: 3 },
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.state !== 'review',
    }),
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/position/record/audit', '/position/record/view'],
  },
});

const router = useRouter();

const route = useRoute();

const title = computed(() => {
  return `${route.query.venueName}--预约记录管理`;
});

const [registerModal, { openModal, closeModal }] = useModal();
const [registerRecordModal, { openModal: openRecordModal }] = useModal();
//审核
function handleAudit(record) {
  let arr = [];
  if (record) {
    //@ts-ignore
    arr.push(record.recordId);
  } else {
    const rows = getSelectRows();
    if (!rows || rows.length === 0) {
      Modal.warning({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: '请选择至少一条数据进行审核！',
        okText: '确认',
        closable: true,
      });
      return false;
    }
    //@ts-ignore
    arr = map(rows, v => v.recordId);
  }
  openModal(true,
    // { recordIds: arr, record }
    { recordId: record.recordId,record }
  );
}

// 添加黑名单
function handleBlacklist(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要将[${record.userName}]加入黑名单?`,
    onOk: function () {
      save({ recordId: record.recordId }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `加入黑名单成功!` });
          reload();
        } else {
          createErrorModal({ content: `加入黑名单失败，${message}` });
        }
      });
    },
  });
}

// 页面左侧点击返回链接时的操作
function goBack() {
  router.go(-1);
}

//详情
function handleView(record) {
  recordView({ autoId: record.autoId }).then(res => {
    openRecordModal(true, { isUpdate: true, disabled: true, record: res.data, recordKey: 'audit' });
  });
}

//提交审核
function handleSuccess({ values }) {
  auditRecord(values).then(res => {
    const { code, message } = res;
    if (code === 200) {
      Modal.success({
        title: '提示',
        icon: createVNode(CheckCircleOutlined),
        content: '审核成功!' || message,
        okText: '确认',
        closable: true,
      });
      reload();
      closeModal();
      clearSelectedRowKeys();
    } else {
      Modal.error({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: `审核失败!${message}`,
        okText: '确认',
        closable: true,
      });
    }
  });
}
</script>
