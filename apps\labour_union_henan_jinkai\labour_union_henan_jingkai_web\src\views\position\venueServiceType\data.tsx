import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { Image } from 'ant-design-vue'
import { uploadApi } from '/@/api/sys/upload'
import { useUserStore } from '/@/store/modules/user'

export const columns = (): BasicColumn[] => {
  const userStore = useUserStore()
  return [
  {
      title: '排序号',
      dataIndex: 'sortNumber',
      width: 100,
    },
    {
      title: '服务类型名称',
      dataIndex: 'serviceTypeName',
      width: 200,
    },
    {
      title: '服务类型描述',
      dataIndex: 'serviceTypeDescribe',
      width: 250,
    },
    // {
    //   title: '展示默认图标',
    //   dataIndex: 'showIcon',
    //   customRender: ({ text }) => {
    //     if (text) {
    //       return (
    //         <Image
    //           src={userStore.getPrefix + text}
    //           style={{ maxWidth: '180px', maxHeight: '42px' }}
    //         ></Image>
    //       )
    //     }
    //   },
    //   width: 160,
    // },
    // {
    //   title: '展示点击图标',
    //   dataIndex: 'clickIcon',
    //   customRender: ({ text }) => {
    //     if (text) {
    //       return (
    //         <Image
    //           src={userStore.getPrefix + text}
    //           style={{ maxWidth: '180px', maxHeight: '42px' }}
    //         ></Image>
    //       )
    //     }
    //   },
    //   width: 160,
    // },
     {
      title: '展示图标',
      dataIndex: 'iconCode',
      customRender: ({ text }) => {
        if (text) {
          return (
            <Image
              src={userStore.getPrefix + text}
              style={{ maxWidth: '180px', maxHeight: '42px' }}
            ></Image>
          )
        }
      },
      width: 160,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'serviceTypeName',
      label: '服务类型名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  return [
  {
      field: 'sortNumber',
      label: '排序号',
      colProps: { span: 24 },
      component: 'InputNumber',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'serviceTypeName',
      label: '服务类型名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 20,
      },
    },
    {
      field: 'serviceTypeDescribe',
      label: '服务类型描述',
      colProps: { span: 24 },
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 260,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    // {
    //   field: 'showIcon',
    //   label: '展示默认图标',
    //   rulesMessageJoinLabel: true,
    //   component: 'Upload',
    //   required: true,
    //   componentProps: {
    //     api: uploadApi,
    //     uploadParams: {
    //       operateType: 44,
    //     },
    //     maxNumber: 1,
    //     maxSize: 1,
    //     accept: ['image/*'],
    //   },
    // },
    // {
    //   field: 'clickIcon',
    //   label: '展示点击图标',
    //   rulesMessageJoinLabel: true,
    //   component: 'Upload',
    //   required: true,
    //   componentProps: {
    //     api: uploadApi,
    //     uploadParams: {
    //       operateType: 44,
    //     },
    //     maxNumber: 1,
    //     maxSize: 1,
    //     accept: ['image/*'],
    //   },
    // },
    {
      field: 'iconCode',
      label: '展示图标',
      rulesMessageJoinLabel: true,
      component: 'Upload',
      required: true,
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 44,
        },
        maxNumber: 1,
        maxSize: 1,
        accept: ['image/*'],
      },
    },
    
  ];
};
