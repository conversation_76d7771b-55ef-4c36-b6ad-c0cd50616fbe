import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { uploadApi } from '/@/api/sys/upload'
import { Image } from 'ant-design-vue'
import { useUserStore } from '/@/store/modules/user'

export const columns = (): BasicColumn[] => {
  const userStore = useUserStore()
  return [
    {
      title: '排序号',
      dataIndex: 'sortNumber',
      width: 120,
    },
    {
      title: '分类名称',
      dataIndex: 'typeName',
    },
    // {
    //   title: '展示默认图标',
    //   dataIndex: 'showIcon',
    //   customRender: ({ text }) => {
    //     if (text) {
    //       return (
    //         <Image
    //           src={userStore.getPrefix + text}
    //           style={{ maxWidth: '180px', maxHeight: '42px' }}
    //         ></Image>
    //       )
    //     }
    //   },
    //   width: 120,
    // },
    // {
    //   title: '展示点击图标',
    //   dataIndex: 'clickIcon',
    //   customRender: ({ text }) => {
    //     if (text) {
    //       return (
    //         <Image
    //           src={userStore.getPrefix + text}
    //           style={{ maxWidth: '180px', maxHeight: '42px' }}
    //         ></Image>
    //       )
    //     }
    //   },
    //   width: 120,
    // },
    // {
    //   title: '定位默认图标',
    //   dataIndex: 'positioningIcon',
    //   customRender: ({ text }) => {
    //     if (text) {
    //       return (
    //         <Image
    //           src={userStore.getPrefix + text}
    //           style={{ maxWidth: '180px', maxHeight: '42px' }}
    //         ></Image>
    //       )
    //     }
    //   },
    //   width: 120,
    // },
    // {
    //   title: '定位点击图标',
    //   dataIndex: 'clickPositioningIcon',
    //   customRender: ({ text }) => {
    //     if (text) {
    //       return (
    //         <Image
    //           src={userStore.getPrefix + text}
    //           style={{ maxWidth: '180px', maxHeight: '42px' }}
    //         ></Image>
    //       )
    //     }
    //   },
    //   width: 120,
    // },
    {
      title: '展示默认图标',
      dataIndex: 'showIcon',
      customRender: ({ text }) => {
        if (text) {
          return (
            <Image
              src={userStore.getPrefix + text}
              style={{ maxWidth: '180px', maxHeight: '42px' }}
            ></Image>
          )
        }
      },
      width: 160,
    },
    {
      title: '展示点击图标',
      dataIndex: 'clickIcon',
      customRender: ({ text }) => {
        if (text) {
          return (
            <Image
              src={userStore.getPrefix + text}
              style={{ maxWidth: '180px', maxHeight: '42px' }}
            ></Image>
          )
        }
      },
      width: 160,
    },
     {
      title: '定位展示图标',
      dataIndex: 'iconCode',
      customRender: ({ text }) => {
        if (text) {
          return (
            <Image
              src={userStore.getPrefix + text}
              style={{ maxWidth: '180px', maxHeight: '42px' }}
            ></Image>
          )
        }
      },
      width: 160,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'typeName',
      label: '分类名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}

export const modalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'typeName',
      label: '分类名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 20,
      },
    },
    {
      field: 'showIcon',
      label: '展示默认图标',
      rulesMessageJoinLabel: true,
      component: 'Upload',
      required: true,
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 44,
        },
        maxNumber: 1,
        maxSize: 10,
        accept: ['image/*'],
      },
    },
    {
      field: 'clickIcon',
      label: '展示点击图标',
      rulesMessageJoinLabel: true,
      component: 'Upload',
      required: true,
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 44,
        },
        maxNumber: 1,
        maxSize: 10,
        accept: ['image/*'],
      },
    },
    {
      field: 'iconCode',
      label: '定位默认图标',
      rulesMessageJoinLabel: true,
      component: 'Upload',
      required: true,
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 44,
        },
        maxNumber: 1,
        maxSize: 10,
        accept: ['image/*'],
      },
    },
    // {
    //   field: 'clickPositioningIcon',
    //   label: '定位点击图标',
    //   rulesMessageJoinLabel: true,
    //   component: 'Upload',
    //   required: true,
    //   componentProps: {
    //     api: uploadApi,
    //     uploadParams: {
    //       operateType: 44,
    //     },
    //     maxNumber: 1,
    //     maxSize: 10,
    //     accept: ['image/*'],
    //   },
    // },
    {
      field: 'sortNumber',
      label: '排序号',
      colProps: { span: 24 },
      component: 'InputNumber',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 1,
      },
    },
  ]
}
