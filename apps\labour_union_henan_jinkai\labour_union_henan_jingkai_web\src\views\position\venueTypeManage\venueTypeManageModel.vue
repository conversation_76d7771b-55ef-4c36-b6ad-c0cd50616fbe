<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :wrap-class-name="$style['recruit-modal']"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from './data';
import { maxSortNumber } from '@/api/venueInfo/venueTypeManage';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.typeName || ''}--详情`
      : `编辑--${unref(record)?.typeName || ''}`
    : '新增分类';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    const { showIcon,clickIcon,iconCode } = data.record
    setFieldsValue({
      ...data.record,
      showIcon: showIcon ? showIcon.split(',') : [],
      clickIcon: clickIcon ? clickIcon.split(',') : [],
      iconCode:iconCode?iconCode.split(',') : [],
    });
  } else if (!unref(isUpdate)) {
    await maxSortNumber({ type: 'venueTypeManage' }).then(res => {
      const { data } = res;
      setFieldsValue({
        sortNumber: data,
      });
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    const { showIcon,clickIcon,iconCode } = values

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        showIcon: showIcon?.join(','),
        clickIcon: clickIcon?.join(','),
        iconCode: iconCode?.join(',')
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" module>
.recruit-modal {
  :global {
    .ant-input-number {
      width: 100% !important;
    }
  }
}
</style>
