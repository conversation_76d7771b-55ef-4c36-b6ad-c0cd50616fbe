<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    ok-text="确认"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';
const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>({});

const disabled = ref(false);

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.userName || ''}--申请详情`
    : `审核${unref(record)?.userName || ''}申请`;
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(unref(disabled));
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 140,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  record.value = data.record;

  disabled.value = !!data.disabled;

  if (unref(record)) {
    const approvalDocuments = data.record.approvalDocuments
      ? data.record.approvalDocuments.split(',')
      : [];
    const applyMaterial = data.record.applyMaterial ? data.record.applyMaterial.split(',') : [];

    setFieldsValue({
      ...data.record,
      approvalDocuments,
      applyMaterial,
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    console.log(values);

    const { status, aduitInstruction, stateLevelOpinion, approvalDocuments } = values;
    const { autoId } = unref(record);
    emit('success', {
      values: {
        autoId,
        status,
        aduitInstruction,
        stateLevelOpinion,
        approvalDocuments:
          approvalDocuments && approvalDocuments.length > 0
            ? approvalDocuments.join(',')
            : undefined,
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
