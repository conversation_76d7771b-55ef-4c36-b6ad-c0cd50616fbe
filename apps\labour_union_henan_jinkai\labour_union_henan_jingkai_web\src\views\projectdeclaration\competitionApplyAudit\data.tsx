import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { cloneDeep, filter } from 'lodash-es'
import { uploadApi } from '/@/api/sys/upload'
import {
  applyAuditGetCompetitionInfoList,
  getPassCompetitionApplyAuditList,
} from '/@/api/projectdeclaration/competition'

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary()

  return [
    {
      title: '项目名称',
      dataIndex: 'entryName',
    },
    {
      title: '竞赛主题名称',
      dataIndex: 'subjectName',
    },
    {
      title: '申请人',
      dataIndex: 'userName',
      customRender({ text }) {
        return <span title={text}>{text}</span>
      },
    },
    {
      title: '申报单位',
      dataIndex: 'applyUnionName',
      customRender({ text }) {
        return <span title={text}>{text}</span>
      },
    },
    {
      title: '审核状态',
      dataIndex: 'aduitStatus',
      width: 80,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`competitionAduitStatus_${text}`)?.dictName

        return <span title={name}>{name}</span>
      },
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      width: 150,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'competitionId',
      label: '竞赛主题名称',
      component: 'ApiSelect',
      colProps: { span: 12 },
      slot: 'competitionId',
      rulesMessageJoinLabel: true,
      labelWidth: 150,
    },
    {
      field: 'aduitStatus',
      label: '审核状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      slot: 'aduitStatus',
    },
  ]
}

export const oneClickApprovalModalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'competitionId',
      label: '竞赛主题名称',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      itemProps: {
        autoLink: false,
      },
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          api: applyAuditGetCompetitionInfoList,
          resultField: 'data',
          showSearch: true,
          getPopupContainer: () => document.body,
          listHeight: 130,
          onChange: () => {
            formModel['applyAuditIdList'] = []
          },
          filterOption: (input: string, option: any) => {
            return option.subjectName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          fieldNames: { label: 'subjectName', value: 'competitionId' },
        }
      },
    },
    {
      field: 'applyAuditIdList',
      label: '申请人(申报单位)',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      itemProps: {
        autoLink: false,
      },
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          mode: 'multiple',
          api: getPassCompetitionApplyAuditList,
          resultField: 'data',
          getPopupContainer: () => document.body,
          listHeight: 130,
          params: {
            pageSize: 0,
            competitionId: formModel['competitionId'],
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          fieldNames: { label: 'label', value: 'value' },
        }
      },
    },
    {
      field: 'approvalDocuments',
      label: '批复文件',
      component: 'Upload',
      colProps: { span: 12 },
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        maxNumber: 3,
        maxSize: 50,
        api: uploadApi,
        uploadParams: {
          operateType: 111,
        },
      },
    },
  ]
}

export const modalFormItem = (disabled: boolean): FormSchema[] => {
  const dictionary = useDictionary()

  return [
    {
      field: '',
      label: '项目信息',
      component: 'Divider',
      ifShow({ values }) {
        return values.entryName
      },
    },
    {
      field: 'entryName',
      label: '项目名称',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      ifShow({ values }) {
        return values.entryName
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'subjectName',
      label: '竞赛主题名称',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      ifShow({ values }) {
        return values.entryName
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'projectClassification',
      label: '立项分类',
      component: 'Select',
      class: 'back-transparent',
      dynamicDisabled: true,
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.projectClassification
      },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('projectClassification'),
        }
      },
    },
    {
      field: 'userName',
      label: '申请人',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.userName
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'applyUnionName',
      label: '申报单位',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.applyUnionName
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'applyTime',
      label: '申请时间',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.applyTime
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'executeUnit',
      label: '实施单位',
      component: 'InputTextArea',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 24,
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'craftNumber',
      label: '开展竞赛工种数(个)',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.craftNumber
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyNumber',
      label: '覆盖企业数(个)',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.companyNumber
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'teamNumber',
      label: '覆盖班组数(个)',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.teamNumber
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'workersNumber',
      label: '覆盖职工数(个)',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.workersNumber
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'stateCapital',
      label: '申请市级补助资金(万)',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.projectClassification === 'continent'
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'countyCapital',
      label: '县本级补助资金(万)',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.projectClassification === 'continent'
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'enterpriseCapital',
      label: '本级自筹资金(万)',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.projectClassification === 'continent'
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'applyCapital',
      label: '申请补助资金数(万)',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.projectClassification === 'county'
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'approvalDocuments',
      label: '批复文件',
      component: 'Upload',
      colProps: { span: 12 },
      dynamicDisabled: true,
      ifShow() {
        return disabled
      },
      componentProps: {
        maxNumber: 3,
        maxSize: 50,
        api: uploadApi,
        uploadParams: {
          operateType: 111,
        },
      },
    },
    {
      field: 'applyMaterial',
      label: '申请材料',
      component: 'Upload',
      colProps: { span: 12 },
      dynamicDisabled: true,
      ifShow({ values }) {
        return !(values.projectClassification === 'continent')
      },
      componentProps: {
        maxNumber: 3,
        maxSize: 50,
        api: uploadApi,
      },
    },
    {
      field: 'stateLevelOpinion',
      label: '市级补助建议',
      component: 'InputTextArea',
      dynamicDisabled: true,
      class: 'back-transparent',
      ifShow({ values }) {
        return disabled && values.projectClassification === 'continent'
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'remarks',
      label: '备注',
      component: 'InputTextArea',
      dynamicDisabled: true,
      class: 'back-transparent',
      ifShow({ values }) {
        return values.remarks
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '审核信息',
      component: 'Divider',
    },
    {
      field: 'status',
      label: '审核状态',
      component: 'RadioGroup',
      required: true,
      ifShow() {
        return !disabled
      },
      componentProps: function () {
        return {
          options: filter(
            cloneDeep(dictionary.getDictionaryOpt.get(`competitionAduitStatus`)),
            v => v.value !== 'toBeReviewed'
          ),
        }
      },
    },
    {
      field: 'approvalDocuments',
      label: '批复文件',
      component: 'Upload',
      colProps: { span: 12 },
      ifShow({ values }) {
        return !disabled && values.status === 'pass'
      },
      componentProps: {
        maxNumber: 3,
        maxSize: 50,
        api: uploadApi,
        uploadParams: {
          operateType: 111,
        },
      },
    },
    {
      field: 'stateLevelOpinion',
      label: '市级补助建议',
      required: true,
      ifShow({ values }) {
        return !disabled && values.projectClassification === 'continent' && values.status === 'pass'
      },
      component: 'InputTextArea',
      componentProps: {
        showCount: true,
        maxlength: 240,
        autocomplete: 'off',
        placeholder: '请输入市级补助建议',
      },
    },
    {
      field: 'aduitInstruction',
      label: '审核意见',
      required: ({ values }) => {
        return values.status === 'reject' || values.status === 'refuse'
      },
      ifShow() {
        return !disabled
      },
      component: 'InputTextArea',
      componentProps: {
        showCount: true,
        maxlength: 240,
        autocomplete: 'off',
        placeholder: '请输入审核意见',
      },
    },
    {
      field: 'aduitStatus',
      label: '审核状态',
      component: 'RadioGroup',
      ifShow() {
        return disabled
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`competitionAduitStatus`),
      },
    },
    {
      field: 'aduitInstruction',
      label: '审核意见',
      ifShow() {
        return disabled
      },
      component: 'InputTextArea',
    },
  ]
}
