<template>
  <Spin :spinning="spinning">
    <div>
      <BasicTable @register="registerTable">
        <template #form-competitionId="{ model, field }">
          <Select
            v-model:value="model[field]"
            placeholder="请选择所属项目名称"
            :showSearch="true"
            :filterOption="filterOption"
            @change="changeCompetitionId"
            :fieldNames="{ label: 'subjectName', value: 'competitionId' }"
            :options="CompetitionIdOptions"
          />
        </template>
        <template #form-aduitStatus="{ model, field }">
          <Select
            v-model:value="model[field]"
            placeholder="请选择审核状态"
            @change="changeAduitStatus"
            :options="dictionary.getDictionaryOpt.get('competitionAduitStatus')"
          />
        </template>
        <template #toolbar>
          <a-button type="primary" @click="handleDownInfo" auth="/competitionApplyAudit/export"
            >导出申请审核数据
          </a-button>
          <!-- auth="/competitionApplyAudit/export" -->
          <a-button
            type="primary"
            @click="handleOneClickApproval"
            v-if="'6650f8e054af46e7a415be50597a99d5' === userStore.getUserInfo.companyId"
            >一键批复</a-button
          >
          <span
            style="color: #ef3333"
            v-if="'6650f8e054af46e7a415be50597a99d5' === userStore.getUserInfo.companyId"
            >注：一键批复功能针对于同一个竞赛主题下审核通过且没有上传批复文件的进行批量上传批复文件!</span
          >
        </template>
        <template #action="{ record }">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/competitionApplyAudit/view',
              },

              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                disabled: record.aduitStatus !== 'toBeReviewed',
                onClick: handleAudit.bind(null, record),
                auth: '/competitionApplyAudit/audit',
                ifShow: '6650f8e054af46e7a415be50597a99d5' === userStore.getUserInfo.companyId,
              },
            ]"
          />
        </template>
      </BasicTable>
      <CompetitionApplyAuditModal
        @register="registerModal"
        @success="handleSuccess"
        :can-fullscreen="false"
        width="50%"
      />
      <OneClickApprovalModel
        @register="registerOneClickApprovalModal"
        @success="handleOneClickApprovalSuccess"
        :can-fullscreen="false"
        width="50%"
      />
    </div>
  </Spin>
</template>

<script lang="ts" setup>
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { ref, unref, onMounted } from 'vue'
import { useModal } from '@/components/Modal'
import { columns, formSchemas } from './data'
import CompetitionApplyAuditModal from './CompetitionApplyAuditModal.vue'
import OneClickApprovalModel from './oneClickApprovalModel.vue'
import { useMessage } from '@monorepo-yysz/hooks'
import {
  ApplyAudit,
  applyAuditList,
  exportCompetitionApplyAudit,
  applyAuditGetCompetitionInfoList,
  oneClickApproval,
} from '@/api/projectdeclaration/competition'
import { downloadByUrl } from '@monorepo-yysz/utils'
import { Spin } from 'ant-design-vue'
import dayjs from 'dayjs'
import { Select } from 'ant-design-vue'
import { useDictionary } from '/@/store/modules/dictionary'
import { useUserStore } from '/@/store/modules/user'

const userStore = useUserStore()

const { createErrorModal, createSuccessModal } = useMessage()

const search = ref()

//loading加载
const spinning = ref<boolean>(false)

const CompetitionIdOptions = ref()

const dictionary = useDictionary()

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: ['/competitionApplyAudit/export'],
  showIndexColumn: false,
  api: applyAuditList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  beforeFetch: params => {
    search.value = params
    return params
  },
  searchInfo: { orderBy: 'apply_time', sortType: 'desc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 180,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
    auth: ['/competitionApplyAudit/view', '/competitionApplyAudit/audit'],
  },
})

const filterOption = (input: string, option: any) => {
  return option.subjectName.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 自动请求并暴露内部方法
onMounted(() => {
  applyAuditGetCompetitionInfoList({}).then(res => {
    const { data } = res
    CompetitionIdOptions.value = data ? data : []
  })
})

function changeCompetitionId(val) {
  search.value.competitionId = val
}

function changeAduitStatus(val) {
  search.value.aduitStatus = val
}

const [registerModal, { openModal, closeModal }] = useModal()
const [
  registerOneClickApprovalModal,
  { openModal: openOneClickApprovalModal, closeModal: closeOneClickApprovalModal },
] = useModal()

//详情
function handleView(record) {
  openModal(true, { disabled: true, record: record })
}
//一键批复
function handleOneClickApproval() {
  openOneClickApprovalModal(true, { isUpdate: false, disabled: false })
}

//导出
function handleDownInfo() {
  spinning.value = true
  const searchParameter = unref(search)
  const competitionId = searchParameter?.competitionId
  if (!competitionId) {
    spinning.value = false
    createErrorModal({
      content: '请选择[所属竞赛主题名称]搜索条件后在导出数据!',
    })
  } else {
    exportCompetitionApplyAudit(searchParameter).then(res => {
      spinning.value = false
      const url = window.URL.createObjectURL(res)
      const day = dayjs().format('YYYY-MM-DD HH:mm')
      downloadByUrl({
        url,
        fileName: '劳动竞赛汇总表' + day + '.xlsx',
      })
    })
  }
}

function handleAudit(record) {
  //重置上次审核非必填的值
  record.stateLevelOpinion = null
  record.approvalDocuments = null
  record.aduitInstruction = null
  openModal(true, { disabled: false, record: record })
}

function handleSuccess({ values }) {
  ApplyAudit(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `审核成功`,
      })
      reload()
      closeModal()
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      })
    }
  })
}

function handleOneClickApprovalSuccess({ values }) {
  oneClickApproval(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `一键批复成功`,
      })
      reload()
      closeOneClickApprovalModal()
    } else {
      createErrorModal({
        content: `一键批复失败! ${message}`,
      })
    }
  })
}
</script>
