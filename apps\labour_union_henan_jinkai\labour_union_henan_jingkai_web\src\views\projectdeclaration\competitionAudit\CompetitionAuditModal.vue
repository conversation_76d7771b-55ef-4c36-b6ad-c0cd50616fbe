<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    ok-text="确认"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>({});

const recordIds = ref<number[]>([]);

const disabled = ref(false);

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.subjectName || ''}--详情`
    : `审核${unref(record)?.subjectName || ''}`;
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(unref(disabled));
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  record.value = data.record;

  disabled.value = !!data.disabled;

  recordIds.value = data.recordIds;

  if (unref(record)) {
    const { startTime, endTime, auditStatus } = unref(record);

    setFieldsValue({
      ...data.record,
      auditStatus: `${auditStatus}`,
      startEndDate: startTime && endTime ? [startTime, endTime] : [],
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();

    emit('success', {
      values: {
        ...values,
        autoIdList: unref(recordIds),
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
