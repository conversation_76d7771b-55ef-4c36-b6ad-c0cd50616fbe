import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { h } from 'vue'
import { Tinymce } from '/@/components/Tinymce'
// import { searchNextUnionForm } from '/@/utils/searchNextUnion'

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary()

  return [
    // {
    //   title: '所属工会',
    //   dataIndex: 'unionName',
    // },
    {
      title: '竞赛主题名称',
      dataIndex: 'subjectName',
    },
    {
      title: '项目周期',
      dataIndex: 'startTime',
      width: 300,
      customRender({ record }) {
        const time = `${record.startTime}~${record.endTime}`
        return <span title={time}>{time}</span>
      },
    },
    {
      title: '立项分类',
      width: 80,
      dataIndex: 'projectClassification',
      customRender({ text }) {
        const name =
          dictionary.getDictionaryMap.get(`projectClassification_${text}`)?.dictName || ''
        return <span title={name}>{name}</span>
      },
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      width: 80,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`newsAduitStatus_${text}`)?.dictName

        return <span title={name}>{name}</span>
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary()

  return [
    {
      field: 'subjectName',
      label: '竞赛主题名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'projectClassification',
      label: '立项分类',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('projectClassification'),
        }
      },
    },
    {
      field: 'auditStatus',
      label: '审核状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsAduitStatus'),
        }
      },
    },
    // ...searchNextUnionForm(),
  ]
}

export const modalFormItem = (disabled: boolean): FormSchema[] => {
  const dictionary = useDictionary()

  return [
    {
      field: '',
      label: '项目信息',
      component: 'Divider',
      ifShow({ values }) {
        return values.subjectName
      },
    },
    {
      field: 'subjectName',
      label: '竞赛主题名称',
      component: 'Input',
      dynamicDisabled: true,
      class: 'back-transparent',
      ifShow({ values }) {
        return values.subjectName
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'projectClassification',
      label: '立项分类',
      component: 'Select',
      class: 'back-transparent',
      dynamicDisabled: true,
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.projectClassification
      },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('projectClassification'),
        }
      },
    },
    {
      field: 'startEndDate',
      label: '项目周期',
      component: 'RangePicker',
      class: 'back-transparent',
      dynamicDisabled: true,
      colProps: {
        span: 12,
      },
      ifShow({ values }) {
        return values.startEndDate
      },
      componentProps: {
        valueFormat: `YYYY-MM-DD HH:mm:ss`,
        format: `YYYY-MM-DD HH:mm:ss`,
        showTime: true,
      },
    },
    {
      field: 'entryContent',
      label: '介绍内容',
      component: 'Input',
      class: 'back-transparent',
      dynamicDisabled: true,
      colProps: { span: 24 },
      ifShow({ values }) {
        return values.entryContent
      },
      render: ({ model, field, disabled }) => {
        return h(Tinymce, {
          value: model[field],
          onChange: (value: string) => {
            model[field] = value
          },
          showImageUpload: false,
          options: {
            readonly: disabled,
            height: 250,
          },
        })
      },
    },
    {
      field: '',
      label: '审核信息',
      component: 'Divider',
    },
    {
      field: 'status',
      label: '审核状态',
      component: 'RadioGroup',
      required: true,
      ifShow() {
        return !disabled
      },
      componentProps: {
        options: [
          { label: '批准', value: true },
          { label: '拒发', value: false },
        ],
      },
    },
    {
      field: 'auditStatus',
      label: '审核状态',
      component: 'RadioGroup',
      ifShow() {
        return disabled
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`newsAduitStatus`),
      },
    },
    {
      field: 'aduitInstruction',
      label: '审核意见',
      required: ({ values }) => {
        return values.status === false
      },
      component: 'InputTextArea',
      componentProps: {
        showCount: true,
        maxlength: 240,
        autocomplete: 'off',
        placeholder: '请输入审核意见',
      },
    },
  ]
}
