<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleAudit(null)" auth="/competitionAudit/batchAudit"
          >批量审核
        </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              auth: '/competitionAudit/view',
            },

            {
              icon: 'ant-design:audit-outlined',
              label: '审核',
              type: 'primary',
              disabled:
                record.auditStatus !== '20' || userStore.getUserInfo.companyId !== record.companyId,
              onClick: handleAudit.bind(null, record),
              auth: '/competitionAudit/audit',
            },
          ]"
        />
      </template>
    </BasicTable>
    <CompetitionAuditModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table'
import { useModal } from '@/components/Modal'
import { columns, formSchemas } from './data'
import CompetitionAuditModal from './CompetitionAuditModal.vue'
import { useMessage } from '@monorepo-yysz/hooks'
import { auditList, view, batchAudit } from '@/api/projectdeclaration/competition'
import { map } from 'lodash-es'
import { useUserStore } from '@/store/modules/user'

const userStore = useUserStore()

const { createErrorModal, createSuccessModal, createWarningModal } = useMessage()

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: '/competitionAudit/batchAudit',
  showIndexColumn: false,
  api: auditList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: {
      span: 3,
    },
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.auditStatus !== '20' || userStore.getUserInfo.companyId !== record.companyId,
    }),
  },
  searchInfo: { orderBy: 'create_time', sortType: 'desc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 180,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
    auth: ['/competitionAudit/view', '/competitionAudit/audit'],
  },
})

const [registerModal, { openModal, closeModal }] = useModal()

//详情
function handleView(record) {
  view({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { disabled: true, record: data })
  })
}

function handleAudit(record) {
  if (record) {
    view({ autoId: record.autoId }).then(({ data }) => {
      openModal(true, { disabled: false, record: data, recordIds: [record.autoId] })
    })
  } else {
    const rows = getSelectRows()
    if (!rows || rows.length === 0) {
      createWarningModal({ content: '请选择至少一条数据进行审核!' })
      return false
    }
    openModal(true, { recordIds: map(rows, v => v.autoId), disabled: false })
  }
}

function handleSuccess({ values }) {
  batchAudit(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `审核成功`,
      })
      reload()
      clearSelectedRowKeys()
      closeModal()
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      })
    }
  })
}
</script>
