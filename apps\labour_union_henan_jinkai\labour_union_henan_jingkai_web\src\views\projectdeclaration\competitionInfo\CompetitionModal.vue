<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="() => handleSubmit()"
    ok-text="即时发布"
    :okButtonProps="{ auth: '/competitionInfo/timelyPublish' }"
    :showOtherBtn="true"
    otherText="提交审核"
    :otherButtonProps="{ auth: '/competitionInfo/submitAudit' }"
    @other="handleOther"
    :wrapClassName="$style.competition"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>({});

const disabled = ref(false);

const isUpdate = ref(false);

const isPublish = ref(false);

const title = computed(() => {
  return unref(isPublish)
    ? `发布${unref(record)?.subjectName || ''}`
    : unref(isUpdate)
      ? unref(disabled)
        ? `${unref(record)?.subjectName || ''}--详情`
        : `编辑${unref(record)?.subjectName || ''}`
      : '新增主题';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(unref(isPublish));
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  isPublish.value = !!data.isPublish;

  if (unref(isUpdate)) {
    const { startTime, endTime } = unref(record);
    setFieldsValue({
      ...data.record,
      startEndDate: startTime && endTime ? [startTime, endTime] : [],
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
    okText: unref(isPublish) ? '确认' : '及时发布',
    showOtherBtn: !unref(isPublish) && !unref(disabled),
  });
});

async function handleSubmit(type?: string) {
  try {
    setModalProps({ confirmLoading: true });

    const { startEndDate, ...values } = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        startTime: startEndDate && startEndDate.length === 2 ? startEndDate[0] : null,
        endTime: startEndDate && startEndDate.length === 2 ? startEndDate[1] : null,
        addType: type ? type : 'timelyPublish',
      },
      isUpdate: unref(isUpdate),
      isPublish: unref(isPublish),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

function handleOther() {
  handleSubmit('submitAudit');
}
</script>

<style lang="less" module>
.competition {
  :global {
    .footer-group {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
