import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { h } from 'vue'
import { Tinymce } from '/@/components/Tinymce'
// import { searchNextUnionForm } from '/@/utils/searchNextUnion'

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary()

  return [
    // {
    //   title: '所属工会',
    //   dataIndex: 'unionName',
    // },
    {
      title: '竞赛主题名称',
      dataIndex: 'subjectName',
    },
    {
      title: '立项分类',
      dataIndex: 'projectClassification',
      width: 120,
      customRender({ text }) {
        const name =
          dictionary.getDictionaryMap.get(`projectClassification_${text}`)?.dictName || ''
        return <span title={name}>{name}</span>
      },
    },
    {
      title: '项目周期',
      dataIndex: 'startTime',
      customRender({ record }) {
        const time = `${record.startTime}~${record.endTime}`
        return <span title={time}>{time}</span>
      },
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      width: 120,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`newsAduitStatus_${text}`)?.dictName

        return <span title={name}>{name}</span>
      },
    },
    {
      title: '发布状态',
      dataIndex: 'publishStatus',
      width: 120,
      customRender: ({ text }) => {
        const dictName = dictionary.getDictionaryMap.get(`newsPublishStatus_${text}`)?.dictName
        return <span title={dictName}>{dictName}</span>
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
    },
    {
      title: '发布时间',
      dataIndex: 'publishTime',
      width: 160,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary()

  return [
    {
      field: 'subjectName',
      label: '竞赛主题名称',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'projectClassification',
      label: '立项分类',
      colProps: { span: 4 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('projectClassification'),
        }
      },
    },
    {
      field: 'auditStatus',
      label: '审核状态',
      colProps: { span: 4 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsAduitStatus'),
        }
      },
    },
    {
      field: 'publishStatus',
      label: '发布状态',
      colProps: { span: 4 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('newsPublishStatus'),
        }
      },
    },
    // ...searchNextUnionForm(),
  ]
}

export const modalFormItem = (isPublish: boolean): FormSchema[] => {
  const dictionary = useDictionary()

  if (!isPublish) {
    return [
      {
        field: 'subjectName',
        label: '竞赛主题名称',
        colProps: { span: 24 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
        componentProps: {
          showCount: true,
          maxlength: 100,
        },
      },
      {
        field: 'projectClassification',
        label: '立项分类',
        required: true,
        colProps: { span: 12 },
        component: 'Select',
        rulesMessageJoinLabel: true,
        componentProps: function () {
          return {
            options: dictionary.getDictionaryOpt.get('projectClassification'),
          }
        },
      },
      {
        field: 'startEndDate',
        label: '项目周期',
        component: 'RangePicker',
        colProps: {
          span: 12,
        },
        required: true,
        componentProps: {
          valueFormat: `YYYY-MM-DD HH:mm:ss`,
          format: `YYYY-MM-DD HH:mm:ss`,
          showTime: true,
        },
      },
      {
        field: 'publishTime',
        label: '发布时间',
        component: 'DatePicker',
        componentProps: {
          valueFormat: `YYYY-MM-DD HH:mm:ss`,
          format: `YYYY-MM-DD HH:mm`,
          showTime: true,
        },
        rulesMessageJoinLabel: true,
        required: true,
      },
      {
        field: 'entryContent',
        label: '介绍内容',
        component: 'Input',
        required: true,
        colProps: { span: 24 },
        render: ({ model, field, disabled }) => {
          return h(Tinymce, {
            value: model[field],
            onChange: (value: string) => {
              model[field] = value
            },
            showImageUpload: false,
            operateType: 110,
            options: {
              readonly: disabled,
            },
          })
        },
      },
    ]
  } else {
    return [
      {
        field: 'publishTime',
        label: '发布时间',
        component: 'DatePicker',
        colProps: { span: 24 },
        rulesMessageJoinLabel: true,
        required: true,
        componentProps: {
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          format: 'YYYY-MM-DD HH:mm',
          showTime: true,
        },
      },
    ]
  }
}
