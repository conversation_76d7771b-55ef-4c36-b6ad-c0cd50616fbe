<template>
  <div>
    <BasicTable @register="registerTable">
      <template
        #toolbar
        v-if="'6650f8e054af46e7a415be50597a99d5' === userStore.getUserInfo.companyId"
      >
        <a-button type="primary" @click="handleClick" auth="/competitionInfo/add">
          新增主题
        </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              auth: '/competition/view',
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              disabled:
                !(
                  (record.publishStatus === '20' && record.auditStatus === '10') ||
                  (record.publishStatus === '-1' && record.auditStatus === '40')
                ) || userStore.getUserInfo.companyId !== record.companyId,
              onClick: handleEdit.bind(null, record),
              auth: '/competition/update',
            },
            {
              icon: 'ic:baseline-published-with-changes',
              label: '发布',
              type: 'primary',
              disabled:
                !(record.auditStatus === '30' && record.publishStatus === '0') ||
                userStore.getUserInfo.companyId !== record.companyId,
              onClick: handlePublish.bind(null, record),
              auth: '/competition/publish',
            },
            {
              icon: 'bx:log-out-circle',
              label: '撤销',
              type: 'primary',
              disabled:
                record.publishStatus !== '10' ||
                userStore.getUserInfo.companyId !== record.companyId,
              onClick: handlePublish.bind(null, record),
              auth: '/competition/revoke',
            },
            {
              icon: 'fluent:delete-16-filled',
              label: '删除',
              type: 'primary',
              disabled:
                !(
                  (record.publishStatus === '20' && record.auditStatus === '10') ||
                  (record.publishStatus === '-1' && record.auditStatus === '40')
                ) || userStore.getUserInfo.companyId !== record.companyId,
              danger: true,
              auth: '/competition/delete',
              onClick: handleDelete.bind(null, record),
            },
          ]"
        />
      </template>
    </BasicTable>
    <CompetitionModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table'
import { useModal } from '@/components/Modal'
import { columns, formSchemas } from './data'
import CompetitionModal from './CompetitionModal.vue'
import { useMessage } from '@monorepo-yysz/hooks'
import {
  list,
  saveOrUpdate,
  deleteLine,
  release,
  view,
} from '/@/api/projectdeclaration/competition'

import { useUserStore } from '@/store/modules/user'

const userStore = useUserStore()

const { createConfirm, createErrorModal, createSuccessModal } = useMessage()

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: '/competitionInfo/add',
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: {
      span: 3,
    },
  },
  searchInfo: { orderBy: 'create_time', sortType: 'desc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 380,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
    auth: [
      '/competition/view',
      '/competition/update',
      '/competition/publish',
      '/competition/revoke',
      '/competition/delete',
    ],
  },
})

const [registerModal, { openModal, closeModal }] = useModal()

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false })
}

//编辑
function handleEdit(record) {
  view({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data })
  })
}

//详情
function handleView(record) {
  view({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data })
  })
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.subjectName}`,
    onOk: function () {
      deleteLine({ ...record }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` })
          reload()
        } else {
          createErrorModal({ content: `删除失败，${message}` })
        }
      })
    },
  })
}

function handlePublish(record) {
  if (record.publishStatus === '10') {
    releaseLine(record)
  } else if (record.publishStatus === '0' || record.publishStatus === '20') {
    openModal(true, { record, isPublish: true, isUpdate: true })
  }
}

function releaseLine(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要撤销${record.subjectName}`,
    onOk: function () {
      release({ autoId: record.autoId, releaseType: 'revoke' }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `撤销成功` })
          reload()
        } else {
          createErrorModal({ content: `撤销失败，${message}` })
        }
      })
    },
  })
}

function handleSuccess({ values, isUpdate, isPublish }) {
  if (!isPublish) {
    saveOrUpdate(values).then(({ code, message }) => {
      if (code === 200) {
        createSuccessModal({
          content: `${isUpdate ? '编辑' : '新增'}成功`,
        })
        reload()
        closeModal()
      } else {
        createErrorModal({
          content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
        })
      }
    })
  } else {
    release({ ...values, releaseType: 'release' }).then(({ code, message }) => {
      if (code === 200) {
        createSuccessModal({ content: `发布成功` })
        reload()
        closeModal()
      } else {
        createErrorModal({ content: `发布失败，${message}` })
      }
    })
  }
}
</script>
