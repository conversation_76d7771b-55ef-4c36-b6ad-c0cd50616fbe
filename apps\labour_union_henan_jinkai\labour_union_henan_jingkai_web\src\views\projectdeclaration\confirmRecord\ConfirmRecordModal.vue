<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :showOkBtn="false"
  >
    <BasicForm
      @register="registerForm"
      class="back-transparent"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem, projectDetailsSchemas } from './data';

defineEmits(['register']);

const record = ref<Recordable>();

const title = computed(() => {
  return `归档-${unref(record)?.projectName || ''}--详情`;
});

const form = computed(() => {
  return modalFormItem();
});

const [registerForm, { resetFields, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: projectDetailsSchemas(),
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  if (unref(record)) {
    setFieldsValue({
      ...data.record,
    });
  }

  setProps({ disabled: true });

  setModalProps({ confirmLoading: false });
});
</script>
