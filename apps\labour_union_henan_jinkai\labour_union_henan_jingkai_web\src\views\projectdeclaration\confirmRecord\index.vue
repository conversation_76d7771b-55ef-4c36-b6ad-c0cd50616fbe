<template>
  <div>
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              auth: '/projectArchive/view',
            },
          ]"
        />
      </template>
    </BasicTable>
    <ConfirmRecordModal @register="registerModal" :can-fullscreen="false" width="60%" />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import { useModal } from '/@/components/Modal'
import { ArchiveProjectColumns, formSchemas } from './data'
import ConfirmRecordModal from './ConfirmRecordModal.vue'
import { projectArchiveInfoList } from '/@/api/projectdeclaration/archiveProject'

const [registerTable, {}] = useTable({
  rowKey: 'autoId',
  columns: ArchiveProjectColumns(),
  showIndexColumn: false,
  api: projectArchiveInfoList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  pagination: true,
  maxHeight: 700,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
  },
})

const [registerModal, { openModal }] = useModal()

//详情
function handleView(record) {
  // view({ ...record }).then(({ data }) => {
  //   openModal(true, { isUpdate: true, disabled: true, record: data })
  // })
  openModal(true, { isUpdate: true, disabled: true, record })
}
</script>
