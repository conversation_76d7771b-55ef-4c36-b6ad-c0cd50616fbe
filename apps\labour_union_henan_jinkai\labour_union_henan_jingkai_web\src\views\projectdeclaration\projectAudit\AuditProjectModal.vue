<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    title="审核"
    @ok="handleSubmit"
    :wrap-class-name="$style['comment-modal']"
  >
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts">
import { computed, defineComponent, ref, unref } from 'vue'
import { useModalInner, BasicModal } from '/@/components/Modal'
import { Row, Col, Divider, RadioGroup } from 'ant-design-vue'
import { useForm, BasicForm } from '/@/components/Form'
import { AuditFormItem } from './data'

export default defineComponent({
  name: 'ModerationModal',
  components: { BasicModal, Row, Col, Divider, BasicForm, RadioGroup },
  emits: ['register', 'success', 'cancel'],
  setup(_, { emit }) {
    const autoId = ref<string | string[]>('')

    const notShow = ref(false)

    const modalItem = computed(() => {
      return AuditFormItem(notShow.value)
    })

    const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
      labelWidth: 100,
      schemas: modalItem,
      showActionButtonGroup: false,
    })

    const [registerModal, { setModalProps }] = useModalInner(async data => {
      await resetFields()
      autoId.value = data.record?.autoId
      notShow.value = data.notShow

      if (!unref(notShow)) {
        setFieldsValue({
          ...data.record,
          verifyStatus: '1',
        })
      }

      setModalProps({ confirmLoading: false })
    })

    async function handleSubmit() {
      const values = await validate()
      emit('success', { values, autoId: unref(autoId) })
    }

    return {
      registerModal,
      registerForm,
      handleSubmit,
    }
  },
})
</script>

<style lang="less" module>
.comment-modal {
  :global {
    .ant-divider {
      @apply border-gray-700 text-18px font-700 !important;
    }
  }
}
</style>
