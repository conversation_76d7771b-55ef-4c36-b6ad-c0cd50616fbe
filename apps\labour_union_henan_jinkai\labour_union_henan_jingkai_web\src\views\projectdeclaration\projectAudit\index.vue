<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleAuditBatch" auth="/project/batchAudit"
          >批量审核</a-button
        >
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              auth: '/project/view',
            },
            {
              icon: 'ant-design:audit-outlined',
              label: '审核',
              type: 'primary',
              disabled: record.auditState !== 'wait',
              onClick: handleAudit.bind(null, record),
              auth: '/project/audit',
            },
          ]"
        />
      </template>
    </BasicTable>
    <ProjectAuditModal @register="registerModal" :can-fullscreen="false" width="60%" />
    <AuditModal
      @register="registerAudit"
      :can-fullscreen="false"
      width="50%"
      @success="handleSuccess"
    ></AuditModal>
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import { useModal } from '/@/components/Modal'
import { columns, formSchemas } from './data'
import ProjectAuditModal from './ProjectAuditModal.vue'
import AuditModal from './AuditProjectModal.vue'
import { projectList } from '/@/api/projectdeclaration/projectAudit'
import { useMessage } from '@monorepo-yysz/hooks'
import { map } from 'lodash-es'
import { projectAudit } from '/@/api/projectdeclaration/projectAudit'
import { Modal } from 'ant-design-vue'
import { createVNode } from 'vue'
import { CloseCircleFilled } from '@ant-design/icons-vue'

const { createWarningModal } = useMessage()
const { createErrorModal, createSuccessModal } = useMessage()

//注册审核表单
const [registerAudit, { openModal: openAuditModal, closeModal }] = useModal()

const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: projectList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  rowSelection: {
    type: 'checkbox',
    getCheckboxProps: record => ({
      disabled: record.auditState !== 'wait',
    }),
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  pagination: true,
  maxHeight: 650,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
  },
})

const [registerModal, { openModal }] = useModal()

//详情
function handleView(record) {
  // view({ ...record }).then(({ data }) => {
  //   openModal(true, { isUpdate: true, disabled: true, record: data })
  // })
  openModal(true, { isUpdate: true, disabled: true, record })
}

//单个审核
function handleAudit(record) {
  openAuditModal(true, { record: { autoId: [record.autoId] }, notShow: false })
}

//批量审核
function handleAuditBatch() {
  const rows = getSelectRows()
  if (!rows || rows.length === 0) {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '请选择至少一条数据进行审核！',
      okText: '确认',
      closable: true,
    })
    return false
  }

  openAuditModal(true, { record: { autoId: map(rows, v => v.autoId) }, notShow: true })
}

//审核提交操作
function handleSuccess({ values, autoId }) {
  projectAudit({ ...values, todoValueList: autoId }).then(res => {
    const { code, message: msg } = res
    if (code === 200) {
      createSuccessModal({ content: '操作成功' })
      reload()
      closeModal()
      clearSelectedRowKeys()
    } else {
      createErrorModal({ content: `${msg}` })
    }
  })
}
</script>
