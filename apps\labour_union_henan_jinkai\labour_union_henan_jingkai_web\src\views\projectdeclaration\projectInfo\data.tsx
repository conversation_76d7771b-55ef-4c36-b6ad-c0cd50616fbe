import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { SelectItem } from '/@/settings/designSetting'

//列表字段
export const projectInfoColumns = (): BasicColumn[] => {
  return [
    {
      title: '申报文件名',
      dataIndex: 'fileName',
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
    },
    {
      title: '申报文号',
      dataIndex: 'projectNumber',
      ifShow: false,
    },
    {
      title: '申报批次',
      dataIndex: 'declareBatch',
    },
    {
      title: '投资方向',
      dataIndex: 'investment',
    },
    /*    {
      title: '项目类型',
      dataIndex: 'projectType',
      customRender({ text }) {
        return dictionary.getDictionaryMap.get(`gender_${text}`)?.dictName || ''
      },
    },*/
    /*    {
      title: '品种类型',
      dataIndex: 'varietyType',
      customRender({ text }) {
        return dictionary.getDictionaryMap.get(`gender_${text}`)?.dictName || ''
      },
    },*/
    {
      title: '建设单位',
      dataIndex: 'buildCompany',
    },
    /*    {
      title: '申报时间',
      dataIndex: 'createTime',
      width: 150,
    },*/
  ]
}

//顶部搜索栏
export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'projectName',
      label: '项目名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}

//编辑字段
export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: '',
      label: '',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '',
      required: true,
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get(''),
        }
      },
    },
  ]
}

//详情展示字段
export const projectDetailsSchemas = (): FormSchema[] => {
  return [
    {
      field: 'autoId',
      label: '主键',
      component: 'Input',
      ifShow: false,
    },
    {
      field: 'projectName',
      label: '项目名称',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入项目名称',
      },
      component: 'Input',
    },
    {
      field: 'projectNumber',
      label: '申报文号',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入申报文号',
      },
      component: 'Input',
    },
    {
      field: 'fileName',
      label: '申报文文件名',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入申报文文件名',
      },
      component: 'Input',
    },
    {
      field: 'year',
      label: '申报年份',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入申报年份',
      },
      component: 'Input',
    },
    {
      field: 'declareBatch',
      label: '申报批次',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入申报批次',
      },
      component: 'Input',
    },

    {
      field: 'declareForcom',
      label: '申报主管单位',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入申报主管单位',
      },
      component: 'Input',
    },
    {
      field: 'investment',
      label: '投资方向',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入申报主管单位',
      },
      component: 'Input',
    },

    {
      field: 'projectType',
      label: '项目类型',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      componentProps: function () {
        const map = useDictionary().getDictionaryOpt
        const obj = map.get('gender')
        const options: SelectItem[] = obj as SelectItem[]
        return { options, placeholder: '请选择项目类型' }
      },
    },
    {
      field: 'varietyType',
      label: '品种类型',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      componentProps: function () {
        const map = useDictionary().getDictionaryOpt
        const obj = map.get('gender')
        const options: SelectItem[] = obj as SelectItem[]
        return { options, placeholder: '请选择品种类型' }
      },
    },
    {
      field: 'projectAddress',
      label: '项目所属地区',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入项目所属地区',
      },
      component: 'Input',
    },

    {
      field: 'buildAddress',
      label: '建设地点',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入建设地点',
      },
      component: 'Input',
    },

    {
      field: 'detailsAddress',
      label: '建设详细地址',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入建设详细地址',
      },
      component: 'Input',
    },

    {
      field: 'buildCompany',
      label: '建设单位',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入建设单位',
      },
      component: 'Input',
    },
    {
      field: 'buildYear',
      label: '建设年限',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入建设年限',
      },
      component: 'Input',
    },

    {
      field: 'buildNature',
      label: '建设性质',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      componentProps: function () {
        const map = useDictionary().getDictionaryOpt
        const obj = map.get('buildType')
        const options: SelectItem[] = obj as SelectItem[]
        return { options, placeholder: '请选择品种类型' }
      },
    },
    {
      field: 'farming',
      label: '建设单位是否属农垦系统',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      componentProps: function () {
        const map = useDictionary().getDictionaryOpt
        const obj = map.get('farmingState')
        const options: SelectItem[] = obj as SelectItem[]
        return { options, placeholder: '请选择是否' }
      },
    },
    {
      field: 'buildContent',
      label: '建设内容',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入建设内容',
      },
      component: 'Input',
    },
  ]
}
