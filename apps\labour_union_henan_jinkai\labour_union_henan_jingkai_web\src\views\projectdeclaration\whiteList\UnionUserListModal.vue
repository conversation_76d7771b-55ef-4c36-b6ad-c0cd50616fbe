<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '选择',
              type: 'default',
              onClick: handleCommentView.bind(null, record),
              // auth: '/difficultEmployees/choice',
            },
          ]"
        />
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '/@/components/Modal'
import { useTable, BasicTable, TableAction } from '/@/components/Table'
import { computed, ref, unref } from 'vue'
import { paged } from '/@/api/cadre'
import { UnionFormSchemas, Unioncolumns } from './data'

const emit = defineEmits(['success', 'register'])

const title = computed(() => {
  return '干部列表'
})

//工会id
const companyId = ref('')

const [registerModal, {}] = useModalInner(async data => {
  await clearSelectedRowKeys()
  companyId.value = data.companyId
  reload()
})

const [registerTable, { clearSelectedRowKeys, reload }] = useTable({
  rowKey: 'id',
  api: paged,
  columns: Unioncolumns(),
  maxHeight: 400,
  beforeFetch: params => {
    params.pi = params.pageNum
    params.ps = params.pageSize
    params.uid = unref(companyId)
    //干部姓名
    params.un = params.un ? params.un : undefined
    //干部电话号码
    params.tel = params.tel ? params.tel : undefined
    return { ...params }
  },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: UnionFormSchemas(),
  },
  afterFetch: data => {
    const userData = data.data
    return userData && userData.length > 0 ? userData : []
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
    // auth: ['/difficultEmployees/choice']
  },
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
  indexColumnProps: { width: 90 },
})

//选择按钮操作
function handleCommentView(record) {
  emit('success', { record: record })
}
</script>
