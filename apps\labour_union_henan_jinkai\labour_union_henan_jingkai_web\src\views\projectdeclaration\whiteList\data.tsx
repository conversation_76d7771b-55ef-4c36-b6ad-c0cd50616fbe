import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary()
  return [
    {
      title: '人员姓名',
      dataIndex: 'name',
    },
    {
      title: '工会名称',
      dataIndex: 'companyId',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`whiteList_${text}`)?.dictName || ''
        return <span title={name}>{name}</span>
      },
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      width: 150,
    },
    {
      title: '证件号码',
      dataIndex: 'identityCardNumber',
      width: 180,
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'companyId',
      label: '工会名称',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('whiteList'),
        }
      },
    },
    {
      field: 'name',
      label: '人员姓名',
      colProps: { span: 5 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 5 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'identityCardNumber',
      label: '证件号码',
      colProps: { span: 5 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}

//选择所属会员弹框筛选条件
export const UnionFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'un',
      label: '干部姓名',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'tel',
      label: '联系电话',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}

//筛选干部列表
export const Unioncolumns = (): BasicColumn[] => {
  return [
    {
      title: '干部姓名',
      dataIndex: 'cadreName',
    },
    {
      title: '联系电话',
      dataIndex: 'contractPhone',
    },
    {
      title: '职务名称',
      dataIndex: 'postName',
    },
  ]
}

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'companyId',
      label: '工会名称',
      required: true,
      colProps: { span: 24 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function ({ formModel }) {
        return {
          options: dictionary.getDictionaryOpt.get('whiteList'),
          getPopupContainer: () => document.body,
          listHeight: 200,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          onChange: () => {
            formModel['name'] = undefined
            formModel['phone'] = undefined
            formModel['identityCardNumber'] = undefined
          },
        }
      },
    },
    {
      field: 'name',
      label: '干部名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      slot: 'name',
      ifShow: ({ values }) => {
        return values.companyId
      },
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => {
        return values.companyId
      },
    },
    {
      field: 'identityCardNumber',
      label: '证件号码',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => {
        return values.companyId
      },
    },
  ]
}
