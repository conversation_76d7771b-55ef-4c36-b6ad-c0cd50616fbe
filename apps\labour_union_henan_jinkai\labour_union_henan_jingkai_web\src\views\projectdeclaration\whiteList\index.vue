<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleClick" auth="/whiteList/add"> 新增人员 </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'fluent:delete-16-filled',
              label: '删除',
              type: 'primary',
              danger: true,
              auth: '/whiteList/delete',
              onClick: handleDelete.bind(null, record),
            },
          ]"
        />
      </template>
    </BasicTable>
    <WhiteListModel
      @register="registerWhiteListModel"
      :canFullscreen="false"
      width="50%"
      @success="handleWhiteListSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table'
import { useModal } from '/@/components/Modal'
import { columns, formSchemas } from './data'
import { useMessage } from '@monorepo-yysz/hooks'
import { saveOrUpdate, deleteWhiteList, list } from '/@/api/projectdeclaration/whiteList'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { createVNode } from 'vue'
import WhiteListModel from './whiteListModel.vue'

const { createErrorModal, createSuccessModal } = useMessage()

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: ['/whiteList/add'],
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
    auth: ['/whiteList/delete'],
  },
})

//选择工会干部
const [registerWhiteListModel, { openModal: openWhiteListModel, closeModal: closeWhiteListModel }] =
  useModal()

//新增
function handleClick() {
  //选择工会干部
  openWhiteListModel(true, { isUpdate: false, disabled: false })
}

//删除
function handleDelete(record) {
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定删除,${record.name}吗?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          deleteWhiteList({ autoId: record.autoId }).then(res => {
            const { code } = res
            console.log(res)
            if (code === 200) {
              message.success('删除成功')
            } else {
              message.error('删除失败')
            }
            reload()
            resolve()
          })
        })
      } catch {
        return console.log('Oops errors!')
      }
    },
  })
}

function handleWhiteListSuccess({ values }) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: '新增人员成功',
      })
      reload()
      closeWhiteListModel()
    } else {
      createErrorModal({
        content: `新增人员失败! ${message}`,
      })
    }
  })
}
</script>
