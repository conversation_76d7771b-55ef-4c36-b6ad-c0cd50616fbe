<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
      <template #name="{ model, field }">
        <a-input
          type="primary"
          @click="choiceUnion(model, field)"
          :disabled="disabled"
          v-model:value="model[field]"
          placeholder="请选择干部名称"
          autocomplete="off"
          readonly
        ></a-input>
      </template>
    </BasicForm>
  </BasicModal>
  <UnionUserListModal
    @register="registerCommentModal"
    :canFullscreen="false"
    width="70%"
    @success="handleSuccess"
  />
</template>

<script lang="ts" setup>
import { ref, unref, computed, watch } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';
import UnionUserListModal from './UnionUserListModal.vue';
import { useModal } from '/@/components/Modal';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const model = ref<Recordable>();

const field = ref('');
const name = ref('');
const phone = ref('');
const identityCardNumber = ref('');

//所有的干部
const [registerCommentModal, { openModal: openUnionListModal, closeModal }] = useModal();

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.xx || ''}--详情`
      : `编辑${unref(record)?.xx || ''}`
    : '新增人员';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

//干部
function choiceUnion(m, f) {
  openUnionListModal(true, { companyId: m.companyId });
  model.value = m;
  field.value = f;
}

function handleSuccess({ record }) {
  const { cadreName, contractPhone, cadreIdentity } = record;
  name.value = cadreName;
  phone.value = contractPhone;
  identityCardNumber.value = cadreIdentity;
  closeModal();
}

watch(name, () => {
  if (model.value) {
    model.value[unref(field)] = unref(name);
    model.value['phone'] = unref(phone);
    model.value['identityCardNumber'] = unref(identityCardNumber);
  }
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
