<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :wrap-class-name="$style['reply']"
  >
    <div id="chat-box " >
      <a-list class="comment-list h-650px"  item-layout="horizontal" :pagination="pagination" :data-source="recordData">
        <template #renderItem="{ item }">
          <a-list-item :class="item.type=='consult' ? '' : 'comment'">
            <a-comment :author="item.type=='consult' ? `（用户）${item.createUser}` : `（专家）${item.createUser}`">
              <template #content>
                <div class="max-w-4/5 pr-5px">
                  <p>{{item.content}}</p>
                </div>
              </template>
              <template #datetime>
                <a-tooltip :title="item.createTime">
                  <span>{{ item.createTime }}</span>
                </a-tooltip>
              </template>
            </a-comment>
            
          </a-list-item>
        </template>
      </a-list>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, unref, onUpdated,nextTick } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal'
import { List, Comment, Tooltip } from 'ant-design-vue';
import { useUserStore } from '/@/store/modules/user'
import {psychologicaDialogue } from '@/api/stationAgent/index'; 
const userStore = useUserStore()
const isUpdate = ref(true)
const userName = ref('')
const createrName = ref('')
const recordData = ref([])
const record=ref(null)
const title = computed(() => {
  return `用户${createrName.value}-咨询-${userName.value}的记录`
})

const pagination = ref({

  pageSize: 5,

  current: 1,

  total: 0,

  showTotal:(total, range)=>{
  return " 共" + total + "条数据"
  },
  showSizeChanger: true,//是否显示更改每页条数
  showQuickJumper: true,//是否显示跳至第几页
  pageSizeOptions: ['5', '10', '20', '40', '80', '100'],
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  position:'bottom', //指定分页显示的位置 'top' | 'bottom' | 'both'
  // 设置页面变化时的回调，调用methods中的onChange方法
  onChange: ((e) => { 
      pagination.value.current = e
      getList(pagination.value.current, pagination.value.pageSize);  

  }),

  // 设置每页显示条数变化时的回调

  onShowSizeChange: (page,pageSize) => {

      pagination.value.current = page

      pagination.value.pageSize = pageSize

      getList(pagination.value.current, pagination.value.pageSize);  

  }

})

const [registerModal, { setModalProps }] = useModalInner(async data => {
  userName.value=data.userName;
  createrName.value=data.record.createUser;
  record.value=data.record;
  setModalProps({
    confirmLoading: false,
  })
  isUpdate.value = !!data?.isUpdate;
  await nextTick()
  getList()
})
function getList() {
  psychologicaDialogue({ 
    psychologicalExpertId:record.value.psychologicalExpertId,
    psychologicalUserId:record.value.psychologicalUserId,
    pageNum:pagination.value.current,
    pageSize:pagination.value.pageSize
   }).then(res => {
        if (res.code === 200) {
          pagination.value.total=res.total
          recordData.value=res.data;
        }
    });
}
</script>

<style lang="less" module>
.reply {
  :global {
    .ant-modal-body {
      .ant-list-items{
        height: 500px;
      }
      .ant-spin-container{
        height: 580px;
      }
      .comment {
          justify-content: end;
          .ant-comment-content-author {
            justify-content: end;
          }
          .ant-comment-content-detail {
            justify-content: end;
          }
        }
      #chat-box {
        overflow: scroll;
        overflow-x: hidden;
        .ant-list{
          height: 50vh;
        }
        .ant-list-item{
          padding:0 !important
        }
        .ant-list-items{
          height: 400px;
        }
        .ant-comment-inner{
          padding: 15px 0 !important;
        }
        .ant-comment-content-detail {
          display: flex;
        }
       
      }
    }
  }
}
</style>
