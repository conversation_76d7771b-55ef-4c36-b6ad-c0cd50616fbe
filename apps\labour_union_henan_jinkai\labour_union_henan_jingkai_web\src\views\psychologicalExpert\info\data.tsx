
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import {Image} from "ant-design-vue";
import {useUserStore} from "@/store/modules/user";
import {startsWith} from "lodash-es";
export function typeColumns(): BasicColumn[] {
  const userStore = useUserStore();
    return [
      {
        title: '头像',
        dataIndex: 'avatar',
        width: 150,
        customRender: ({ text }) => {
          return text ? (
              <Image src={startsWith(text, 'http') ? text : userStore.getPrefix + text} width={60} height={75}></Image>
          ) : (
              ''
          );
        },
      },
      {
        dataIndex: 'userName',
        title: '专家姓名',
        width: 150,
      },
      {
        title: '咨询时间',
        customRender({ record }) {
          const time = `${record.startTime}~${record.endTime}`;
          return <span title={time}>{time}</span>;
        },
        width: 140,
      },
      {
        dataIndex: 'affiliatedHospital',
        title: '所属医院',
        width: 250,
      },
      {
        dataIndex:'introduction',
        title:'个人介绍',
      },
      {
        dataIndex: 'state',
        title: '是否展示',
        width: 140,
        customRender({ text }) {
          const title = text  ? '是' : '否'
          const color = text === true ? '#67C23A' : '#F56C6C';
          return <span title={title} style={{color}}>{title}</span>;
        },
      },
    ];
  }
export function searchSchemas (): FormSchema[] {
    return [
        {
            field: 'userName',
            label: '专家姓名',
            colProps: { span: 6},
            component: 'Input',
            rulesMessageJoinLabel: true,
        },
        {
            field: 'state',
            label: '是否展示',
            colProps: { span: 6},
            component: 'Select',
            rulesMessageJoinLabel: true,
            componentProps: function () {
              return {
                options: [
                  { label: '是', value: true },
                  { label: '否', value: false },
                ],
              }
            },
          },

    ]
}
export function typeFormItem(isUpdate: boolean,disable): FormSchema[] {
  const dictionary = useDictionary();
    return [
      {
        field: 'avatar',
        label: '头像',
        required: true,
        component: 'CropperForm',
        renderComponentContent({ values }) {
          return {
            tip: () =>
                (
                    <div class={`text-sm leading-7`}>
                      图片规格为(<span class="text-red-500">452*542</span>)
                    </div>
                ),
          }
        },
        componentProps({ formModel }) {
          return {
            operateType: 164,
            imgSize: 452 / 542,
          }
        },
      },
      {
        field: 'userName',
        label: '专家姓名',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
        ifShow:!disable,
        slot:'nameButton'
      },
      {
        field: 'userName',
        label: '专家姓名',
        colProps: { span: 12 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
        ifShow:disable
      },
      {
        field: 'phone',
        label: '电话',
        colProps: { span: 8 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
        ifShow:false
      },
      {
        field: 'state',
        label: '是否展示',
        component: 'Switch',
        required: true,
        rulesMessageJoinLabel: true,
        colProps: {
          span: 12,
        },
        defaultValue: false,
        componentProps: {
          checkedValue: true,
          checkedChildren: '开启',
        },
      },
      {
        field: 'startTime',
        label: '咨询开始时间',
        required: true,
        component: 'TimePicker',
        colProps: {
            span: 12,
          },
        className: '!w-full',
        componentProps: {
            valueFormat: 'HH:mm:ss',
            format: 'HH:mm:ss',
            showTime: true,
        },
      },
      {
        field: 'endTime',
        label: '咨询结束时间',
        required: true,
        component: 'TimePicker',
        rulesMessageJoinLabel: true,
        colProps: {
            span: 12,
          },
        className: '!w-full',
          componentProps: {
            valueFormat: 'HH:mm:ss',
            format: 'HH:mm:ss',
            showTime: true,
        },
      },
      {
        field: 'affiliatedHospital',
        component: 'Input',
        label: '所属医院',
        required: true,
        rulesMessageJoinLabel: true,
        colProps: {
          span: 24,
        },
        componentProps: {
          showCount: true,
          maxlength: 20 ,
        },
      },
      {
        field: 'qualification',
        component: 'Input',
        label: '专家资质',
        required: true,
        rulesMessageJoinLabel: true,
        colProps: {
            span: 24,
          },
        componentProps: {
          showCount: true,
          maxlength: 20 ,
        },
      },
      {
        field: 'introduction',
        component: 'InputTextArea',
        label: '个人介绍',
        required: true,
        colProps: {
            span: 24,
          },
        rulesMessageJoinLabel: true,
        componentProps: {
          showCount: true,
          maxlength: 400 ,
          autoSize: { minRows: 3, maxRows: 5 },
        },
      },
      {
        field: 'expertiseArea',
        component: 'InputTextArea',
        label: '擅长领域',
        required: true,
        colProps: {
            span: 24,
          },
        rulesMessageJoinLabel: true,
        componentProps: {
          showCount: true,
          maxlength: 100 ,
          autoSize: { minRows: 2, maxRows: 4 },
        },
      },
    ];
  }
//人员列表
export const modelColumns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'userName',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
    },
    {
      title: '性别',
      customRender({ text }) {
        const name = text === 'female' ? '女' : '男';
        return <span title={name}>{name}</span>;
    },
    },
  ]
}

//选择弹框人员列表筛选条件
export const modelSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '姓名',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}