<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" :class="disabledClass"> 
      <template #nameButton="{ model, field }">
        <a-input
          type="primary"
          @click="choiceModel(model, field)"
           :disabled="disabled || isUpdate"
          v-model:value="model[field]"
          placeholder="请选择专家姓名"
          autocomplete="off"
          readonly
        ></a-input>
      </template>
    </BasicForm>
  </BasicModal>
  <modelListModals
    @register="registermodelListModal"
    :canFullscreen="false"
    width="60%"
    @success="handleModel" 
  />

</template>

<script lang="ts" setup>
import { ref, unref, computed ,watch} from 'vue';
import { useModalInner, BasicModal,useModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { typeFormItem } from './data';

import modelListModals from '../../workStar/info/modelList.vue';
const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();
const model = ref<Recordable>();

const field = ref('');
const isUpdate = ref(false);
const disabled = ref(false);
const formItem = computed(() => {
  return typeFormItem(unref(isUpdate),unref(disabled));
});
const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});
const title = computed(() => {
  return unref(disabled)
    ? `${unref(record).userName || ''}--专家详情`
    : unref(isUpdate)
      ? `编辑${unref(record).userName || ''}专家`
      : '新增专家';
});


const [registerForm, { resetFields, validate, setFieldsValue ,setProps}] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;
  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({ ...data.record });
  }
  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
  setProps({ disabled: unref(disabled) })
});
async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
//所有劳模信息
const [registermodelListModal, { openModal ,closeModal :closeModelModal }] = useModal();
//选择人员列表
function choiceModel(m,f) {
  openModal(true);
  model.value = m;
  field.value = f;
}
const modelRecord=ref<Recordable>()
function handleModel({record}) {
  modelRecord.value=record
  closeModelModal()
}
watch(modelRecord, () => {
  if (modelRecord.value) {
    model.value[unref(field)] = unref(modelRecord).userName;
    model.value['phone'] = unref(modelRecord).phone;
    model.value['genderCn'] =unref(modelRecord).gender;
  }
});


</script>
