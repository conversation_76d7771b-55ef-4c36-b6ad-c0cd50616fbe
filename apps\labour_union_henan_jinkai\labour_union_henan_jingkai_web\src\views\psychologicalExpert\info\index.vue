<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleAdd"
          auth="/psychologicalExpert/add"
        >
          新增专家
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'tabler:list-details',
                label: '详情',
                type: 'default',
                onClick: handleDetails.bind(null, record),
                auth: '/psychologicalExpert/view',
              },

              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/psychologicalExpert/edit',
              },
              {
                icon: 'fluent:delete-20-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/psychologicalExpert/delete',
              },
              {
                icon: 'mdi:user',
                label: '咨询人员',
                type: 'default',
                onClick: handleUserList.bind(null, record),
                auth: '/psychologicalExpert/user',
              },
              {
                  icon: record.state ?'icon-park-outline:remind-disable':'icon-park-outline:remind',
                  label: record.state ?'不展示':'展示',
                  type: 'primary',
                  danger: record.state,
                  onClick: EnableDisable.bind(null,record),
                  auth: '/psychologicalExpert/disable',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <expertModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    >
    </expertModal>
    <userListModals
      @register="registerUserListModal"
      :canFullscreen="false"
      width="50%"
      @success="handleUserModel"
    />
  </div>
</template>
<script lang="ts" setup>
import expertModal from './expertModal.vue';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { typeColumns, searchSchemas } from './data';
import {  unref } from 'vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  expertFindList,
  saveOrUpdateByDTO,
  expertDelete,
  getDetails,
  changeState
} from '@/api/stationAgent/index';
import userListModals from './userList.vue';


const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: typeColumns(),
  showIndexColumn: false,
  api: expertFindList,
  authInfo: ['/psychologicalExpert/add'],
  beforeFetch: params => {
    return params;
  },
  formConfig: {
    labelWidth: 120,
    schemas: searchSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  bordered: true,
  useSearchForm: true,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',
    fixed: undefined,
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
    auth: [
      '/psychologicalExpert/view',
      '/psychologicalExpert/edit',
      '/psychologicalExpert/disable',
      '/psychologicalExpert/user',
      '/psychologicalExpert/delete',
    ],
  },
});
const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();
//新增专家信息
function handleAdd() {
  openModal(true, { isUpdate: false, disabled: false, record: {} });
}
//编辑
function handleEdit(record) {
  getDetails({ autoId: record.autoId }).then(res => {
    if (res.code === 200) {
      openModal(true, { isUpdate: true, record, disabled: false });
    }
  });
}

//启用禁用
function EnableDisable(record) {
  const text = !record.state ? '展示' : '不展示';
  const state = !record.state;

  createConfirm({
    iconType: 'warning',
    content: `请确认要${text}${record.userName}吗`,
    onOk: function () {
      changeState({ autoId: record.autoId, state:state }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `${text}成功` });
          reload();
        } else {
          createErrorModal({ content: `${text}失败，${message}` });
        }
      });
    },
  });
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.userName}`,
    onOk: function () {
      expertDelete(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}
//详情
function handleDetails(record) {
  getDetails({ autoId: record.autoId }).then(res => {
    if (res.code === 200) {
      openModal(true, { record: res?.data, isUpdate: true, disabled: true });
    }
  });
}
//新增-编辑
function handleSuccess({ isUpdate, values }) {
  console.log(values, 345);

  if (!isUpdate) {
    //   const { groupCode, groupName } = unref(type);
    //   values.groupCode = groupCode;
    //   values.groupName = groupName;
  }
  saveOrUpdateByDTO(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
//所有人员列表信息
const [registerUserListModal, { openModal: openUserModal, closeModal: closeModelModal }] =
  useModal();

//人员
function handleUserList(record) {
  openUserModal(true, {
    psychologicalExpertId: record.psychologicalExpertId,
    userName: unref(record)?.userName,
  });
}
function handleUserModel(record) {
  console.log(record);
}
</script>
