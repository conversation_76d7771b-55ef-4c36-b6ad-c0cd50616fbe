<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction 
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '咨询详情',
                type: 'default',
                onClick: handleModel.bind(null, record),
              },
            ]"
          />
        </template>
       
      </template>
    </BasicTable>
  </BasicModal>
  <ReplyModal @register="registerReplyModal" :canFullscreen="false" width="50%" />
</template>

<script lang="ts" setup>
import { computed,ref } from 'vue'
import { useModalInner, BasicModal,useModal } from '/@/components/Modal'
import { useTable, BasicTable, TableAction } from '/@/components/Table'
import { modelColumns, modelSchemas } from './data'
import { psychologicalUser } from '@/api/stationAgent/index'; 
import ReplyModal from './consultationModal.vue'
import { nextTick } from 'vue';
const emit = defineEmits(['register', 'success'])

const title = computed(() => {
  return '咨询人员列表'
})
const psychologicalExpertId = ref<Recordable>();
const userName = ref<Recordable>();
const [registerModal, {}] = useModalInner(async (data) => {
  await nextTick()
  await reload({
    searchInfo:{
      psychologicalExpertId: data.psychologicalExpertId
    }
  })

  psychologicalExpertId.value=data.psychologicalExpertId;
  userName.value=data.userName
  await clearSelectedRowKeys()
})


const [registerTable, { clearSelectedRowKeys, reload }] = useTable({
  rowKey: 'autoId',
  api: psychologicalUser,
  columns: modelColumns(),
  maxHeight: 430,
  // beforeFetch: params => {
  //   params.psychologicalExpertId=psychologicalExpertId.value
  //   return { ...params }
  // },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: modelSchemas(),
  },
  afterFetch: data => {
    const userData = data
    return userData && userData.length > 0 ? userData : []
  },
  
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
  },
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
  indexColumnProps: { width: 90 },
})
const [registerReplyModal, { openModal }] = useModal()

//详情操作
function handleModel(record) {
  openModal(true,{record: record,userName:userName.value})
}
</script>
