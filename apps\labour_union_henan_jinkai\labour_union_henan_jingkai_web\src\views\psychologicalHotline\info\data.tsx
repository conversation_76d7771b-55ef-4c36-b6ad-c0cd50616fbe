import dayjs from 'dayjs';
import { useDictionary } from '@/store/modules/dictionary';
import { BasicColumn, FormSchema } from '@/components/Table';
import {validatePhone} from "../../../../../../../packages/utils/src";
export function typeColumns(): BasicColumn[] {
    return [
    //   {
    //     dataIndex: 'sort',
    //     title: '序号',
    //     width: 150,
    //   },
      {
        dataIndex: 'name',
        title: '名称',
      },
      {
        dataIndex: 'phone',
        title: '联系电话',
      },
      // {
      //   dataIndex: 'createTime',
      //   title: '创建时间',
      // },
    ];
  }
  export function typeFormItem(isUpdate: boolean,disabled:boolean): FormSchema[] {
    return [
    //   {
    //     field: 'sort',
    //     label: '排序',
    //     colProps: { span: 24 },
    //     component: 'InputNumber',
    //     className: '!w-full',
    //     required: true,
    //     rulesMessageJoinLabel: true,
    //   },
      {
        field: 'name',
        label: '名称',
        colProps: { span: 24 },
        component: 'Input',
        required: true,
        rulesMessageJoinLabel: true,
        componentProps: {
          showCount: true,
          maxlength: 20 ,
        },
      },
      {
        field: 'phone',
        label: '联系电话',
        required: true,
        colProps: { span: 24},
        component: 'Input',
        rulesMessageJoinLabel: true,
        rules: [{ required: true, validator: validatePhone, trigger: ['change', 'blur'] }],
      },
     
    ];
  }
 
  export function searchSchemas (): FormSchema[] {
    return [
        {
            field: 'name',
            label: '名称',
            colProps: { span: 8 },
            component: 'Input',    rulesMessageJoinLabel: true,
          },
          {
            field: 'phone',
            label: '联系电话',
            colProps: { span: 8},
            component: 'Input',
            rulesMessageJoinLabel: true,
          },
     
    
    ]
  }

