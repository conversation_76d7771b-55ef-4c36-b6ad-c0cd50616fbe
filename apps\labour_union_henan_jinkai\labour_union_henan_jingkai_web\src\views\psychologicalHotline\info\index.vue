<template>
  <div>
    <BasicTable @register="registerTable">
<!--      <template #toolbar>-->
<!--        <a-button-->
<!--          type="primary"-->
<!--          @click="handleClick"-->
<!--        >-->
<!--          新增咨询热线-->
<!--        </a-button>-->
<!--      </template>-->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'tabler:list-details',
                label: '详情',
                type: 'default',
                onClick: handleDetail.bind(null, record),
                auth: '/psychologicalHotline/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/psychologicalHotline/edit',
              },
              // {
              //   icon: 'fluent:delete-16-filled',
              //   label: '删除',
              //   type: 'primary',
              //   danger: true,
              //   onClick: handleDelete.bind(null, record),
              //     auth: '/employeeMessageType/delete',
              // },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <TypeModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
    >
    </TypeModal>
  </div>
</template>

<script lang="ts" setup>
import TypeModal from './typeModal.vue';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { typeColumns } from './data';
import { computed, unref, useAttrs } from 'vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  hotlineFindList,
  saveOrUpdateByDTO,
  deleteModelType,
  getDetails,
} from '@/api/stationAgent/hotline';
const attrs = useAttrs();

const type = computed<any>(() => {
  return attrs.type;
});

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: typeColumns(),
  showIndexColumn: false,
  api: hotlineFindList,
  beforeFetch: params => {
    params.groupCode = unref(type)?.groupCode;
    return params;
  },
  // formConfig: {
  //   labelWidth: 120,
  //   schemas: searchSchemas(),
  //   autoSubmitOnEnter: true,
  //   actionColOptions: { span: 3 },
  // },
  bordered: true,
  // useSearchForm: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/psychologicalHotline/view',
      '/psychologicalHotline/edit',
    ],
  },
});

//详情
function handleDetail(record) {
  getDetails(record.autoId).then(res => {
    if (res.code === 200) {
      openModal(true, { isUpdate: true, disabled: true, record: res.data });
    }
  });
}
function handleClick() {
  openModal(true, { isUpdate: false });
}
//编辑
function handleEdit(record) {
  getDetails(record.autoId).then(res => {
    if (res.code === 200) {
      openModal(true, { isUpdate: true, record: res.data });
    }
  });
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.name}`,
    onOk: function () {
      deleteModelType(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ isUpdate, values }) {
  if (!isUpdate) {
    //   const { groupCode, groupName } = unref(type);
    //   values.groupCode = groupCode;
    //   values.groupName = groupName;
  }
  saveOrUpdateByDTO(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
