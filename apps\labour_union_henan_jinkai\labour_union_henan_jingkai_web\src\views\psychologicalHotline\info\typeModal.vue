<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" :class="disabledClass"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { typeFormItem } from './data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const isUpdate = ref(false);
const disabled=ref(false)
const formItem = computed(() => {
  return typeFormItem(unref(isUpdate),unref(disabled));
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});
const title = computed(() => {
  return unref(disabled)
    ? `${unref(record).name || ''}--详情`
    : unref(isUpdate)
      ? `编辑${unref(record).name || ''}`
      : '新增咨询热线';
});


const [registerForm, { resetFields, validate, setFieldsValue,setProps}] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  record.value = data.record;

  isUpdate.value = !!data.isUpdate;
  disabled.value=!!data.disabled;
  if (unref(isUpdate)) {
    setFieldsValue({ ...data.record });
  }
  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });  
  setProps({ disabled: unref(disabled) })

});
async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
