import { BasicColumn, FormSchema } from '/@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import {findRedPacketActivityList} from "@/api/activities";
import {upload} from "@/api/rechargeOrder";
import {nextTick} from "vue";
import {Image} from "ant-design-vue";
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '活动名称',
      dataIndex: 'title',
    },
    {
      title: '充值金额（元）',
      dataIndex: 'rechargeAmount',
      customRender: ({ text }) => {
        return <span>{text || '-'}</span>
      }
    },
    {
      title: '税费（元）',
      dataIndex: 'taxesAmount',
      customRender: ({ text }) => {
        return <span>{text || '-'}</span>
      }
    },
    {
      title: '到账金额（元）',
      dataIndex: 'receivedAmount',
      customRender: ({ text }) => {
        return <span>{text || '-'}</span>
      }
    },
    {
      title: '活动余额（元）',
      dataIndex: 'balance',
      customRender: ({ text,record }) => {
        if(record.state === 'pass'){
          return <span>{text || '-'}</span>
        }
        return <span>-</span>
      }
    },
    {
      title: '确认状态',
      dataIndex: 'state',
      width: 100,
      customRender: ({ text }) => {
        const state = dictionary.getDictionaryMap.get(`auditState_${text}`);
        const name = state?.dictName || '';
        const color = state?.remark || '';
        return <span style={{ color }}>{name}</span>;
      },
    },
    {
      title: '确认意见',
      dataIndex: 'opinion',
      customRender({ text }) {
        if(!text){
          return <div>-</div>
        }
        return <div>{ text }</div>
      }
    },
    {
      title: '提交日期',
      dataIndex: 'createTime',
    },
  ];
};

//顶部搜索条件
export const checkFormSchemas = (): FormSchema[] => {
  const dictionary = useDictionary()
  return [
    {
      field: 'activityId',
      component: 'ApiSelect',
      label: '活动主体',
      colProps: { span: 6 },
      componentProps: ({ formActionType, formModel }) => {
        return {
          placeholder: '请选择活动',
          api: findRedPacketActivityList,
          resultField: 'data',
          params: {
            orderBy: 'auto_id',
            sortType: 'desc',
          },
          onChange: (e, node) => {
          },
          immediate: true,
          alwaysLoad: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.activityName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'activityName', value: 'activityId' },
        };
      },
    },
    {
      field: 'state',
      label: '确认状态',
      colProps: { span: 6},
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('auditState'),
        };
      },
    },
  ];
};

// 表单
export const modalFormItem = (disabled: boolean): FormSchema[] => {
  const dictionary = useDictionary()
  if(disabled){
    return [
      {
        field: 'title',
        label: '活动名称',
        component: 'ShowSpan',
      },
      {
        field: 'remark',
        label: '备注',
        component: 'InputTextArea',
        colProps: { span: 24 },
      },
      {
        field: 'rechargeAmount',
        label: '充值金额（元）',
        component: 'ShowSpan',
        colProps: { span: 12 },
      },
      {
        field: 'taxesAmount',
        label: '税费（元）',
        component: 'ShowSpan',
        ifShow({values}){return !['review','cancel'].includes(values.state) },
        colProps: { span: 12 },
      },
      {
        field: 'receivedAmount',
        label: '到账金额（元）',
        component: 'ShowSpan',
        ifShow({values}){return !['review','cancel'].includes(values.state) },
        colProps: { span: 12 },
      },
      {
        field: 'balance',
        label: '活动余额（元）',
        component: 'ShowSpan',
        ifShow({values}){return values.state === 'pass' },
        colProps: { span: 12 },
      },
      {
        field: 'createTime',
        label: '提交日期',
        component: 'ShowSpan',
        colProps: { span: 12 },
      },
      {
        field: 'file',
        label: '上传附件',
        component: 'ShowSpan',
        ifShow: disabled,
        render({ values }) {
          let img =
              values?.file?.map(t => {
                return (
                    <span style={{ marginRight: '10px' }}>
                  <Image
                      width={70}
                      height={70}
                      src={ t}
                  />
                </span>
                );
              }) ?? '';
          return (<div>{img}</div>)
        },
      },
      {
        field: 'state',
        label: '确认状态',
        component: 'ShowSpan',
        colProps: { span: 12 },
        render({ values }) {
          const state = dictionary.getDictionaryMap.get(`auditState_${values.state}`);
          const name = state?.dictName || '';
          const color = state?.remark || '';
          return <span style={{ color }}>{name}</span>;
        },
      },
      {
        field: 'opinion',
        label: '确认意见',
        component: 'ShowSpan',
        ifShow({values}){return !['review','cancel'].includes(values.state) },
        colProps: { span: 12 },
      },
      {
        field: 'updateTime',
        label: '确认日期',
        ifShow({values}){return !['review','cancel'].includes(values.state) },
        component: 'ShowSpan',
        colProps: { span: 12 },

      },
    ]
  }

  return [
    {
      field: 'activityId',
      component: 'ApiSelect',
      label: '活动名称',
      required: true,
      componentProps: ({ formActionType, formModel }) => {
        return {
          placeholder: '请选择活动',
          api: findRedPacketActivityList,
          resultField: 'data',
          params: {
            orderBy: 'auto_id',
            sortType: 'desc',
          },
          onChange: (e, node) => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
            formModel['title'] = node?.activityName;
          },
          immediate: true,
          alwaysLoad: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.activityName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'activityName', value: 'activityId' },
        };
      },
    },
    {
      field: 'title',
      label: '标题',
      component: 'Input',
      show:false,
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      colProps: { span: 24 },
      required: true
    },
    {
      field: 'rechargeAmount',
      label: '充值金额（元）',
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      colProps: { span: 12 },
      required: false
    },
    {
      field: 'file',
      label: '上传附件',
      component: 'Upload',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      required: true,
      ifShow: !disabled,
      componentProps: {
        api: upload,
        maxNumber: 3,
        accept: ['image/*'],
      },
    },


  ];
};
