<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
            type="primary"
            @click="handleClick"
        >
          新增充值订单
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
              :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
                {
                icon: 'bx:log-out-circle',
                label: '撤销',
                type: 'primary',
                onClick: handleCancel.bind(null, record, 'revoke'),
                disabled: record.state !== 'review'
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <RechargeOrderModal
        width="50%"
        @register="registerModal"
        @success="handleSuccess"
        :can-fullscreen="false"
    >
    </RechargeOrderModal>
  </div>
</template>

<script lang="ts" setup>
import RechargeOrderModal from './rechargeOrderModal.vue';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useMessage } from '@monorepo-yysz/hooks';
import {columns,checkFormSchemas} from "@/views/redPacketManage/rechargeOrder/data";
import {cancel, list, save} from "@/api/rechargeOrder";
import {message, Modal} from "ant-design-vue";
import {createVNode} from "vue";
import {ExclamationCircleOutlined} from "@ant-design/icons-vue";



const { createErrorModal, createSuccessModal } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  beforeFetch: params => {
    params.orderBy = 'order_num';
    params.sortType = 'asc';
    return params;
  },
  useSearchForm: true,
  formConfig: {
    labelWidth: 120,
    schemas: checkFormSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  bordered: true,

  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
  },
});


//编辑
function handleView(record) {
  openModal(true, { isUpdate: true,disabled:true, record });
}

function handleSuccess({ isUpdate, values }) {
  save(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}

const handleCancel = (record)=>{
  const name = record.title
  const autoId = record.autoId
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要撤销【${name}】?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise((resolve, reject) => {
          cancel({autoId}).then(({data,message:msg}) => {
            if (data) {
              message.success('撤销成功')
              reload()
              resolve(data)
            } else {
              message.error(msg || '撤销失败')
              reject(data)
            }
          })
        })
      } catch {
        return console.log('Oops errors!')
      }
    },
  })
}

const handleClick = ()=>{
  openModal(true, { isUpdate: false,disabled:false, });
}
</script>
