<template>
  <BasicModal
      @register="registerModal"
      v-bind="$attrs"
      :title="title"
      @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" :class="disabledClass"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from "@/views/redPacketManage/rechargeOrder/data";
import {isArray} from "lodash-es";

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const isUpdate = ref(false);
const disabled = ref(false);

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});
const formItem = computed(() => {
  return modalFormItem(unref(disabled));
});

const title = computed(() => {
  return unref(isUpdate) ? `编辑${unref(record)?.dictName || ''}` : '新增充值订单';
});

const [registerForm, { resetFields, validate, setFieldsValue,setProps }] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  if(data?.record?.file && !isArray(data?.record?.file)){
    data.record.file = data.record.file?.split(',')
  }
  record.value = data.record;
  isUpdate.value = !!data.isUpdate;
  disabled.value = !!data.disabled;

  if (unref(isUpdate)) {
    setFieldsValue({ ...data.record });
  }
  setProps({ disabled: unref(disabled) });
  setModalProps({ confirmLoading: false,showOkBtn: !unref(disabled) });
});
async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const {file,...values} = await validate();
    if(file?.length){
      values.file = file.join(',')
    }
    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
