<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />
  </BasicModal>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { modalForm } from './data';

defineOptions({ name: 'ApplyViewInfoModal' });

const emit = defineEmits(['success', 'register', 'cancel']);

const isUpdate = ref(true);

const autoId = ref('');

const disabled = ref(false);

const record = ref();

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const title = computed(() => {
  return `${unref(record)?.userName || ''}--详情`;
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 120,
  schemas: modalForm(),
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
  await resetFields();
  isUpdate.value = !!data?.isUpdate;
  disabled.value = !!data?.disabled;

  if (unref(isUpdate)) {
    autoId.value = data.record.autoId;
    record.value = data.record;

    const { naturalFileUrl: m } = data.record;
    await setFieldsValue({
      ...data.record,
      naturalFileUrl: m ? m.split(',') : [],
    });
  }

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });

  setProps({
    disabled: unref(disabled),
  });
});

async function handleSubmit() {
  try {
    const values = await validate();
    const { naturalFileUrl } = values;
    setModalProps({ confirmLoading: true });
    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...record.value,
        ...values,
        autoId: isUpdate.value ? autoId.value : undefined,
        accountType: 'merchant',
        accountInfo: values.contactPhone,
        naturalFileUrl: naturalFileUrl ? naturalFileUrl.join(',') : '',
      },
    });
    closeModal();
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
