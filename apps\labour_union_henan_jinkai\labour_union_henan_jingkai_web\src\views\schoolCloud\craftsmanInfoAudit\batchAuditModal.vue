<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    title="审核"
    @ok="handleSubmit"
    :wrap-class-name="$style['comment-modal']"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormItem } from './data';

defineOptions({ name: 'CraftsBatchAuditModal' });

const emit = defineEmits(['register', 'success', 'cancel']);

const autoId = ref<string | string[]>('');

const notShow = ref(false);

const modalItem = computed(() => {
  return modalFormItem(notShow.value);
});

const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: modalItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  autoId.value = data.record?.autoId;
  notShow.value = data.notShow;

  if (!unref(notShow)) {
    setFieldsValue({
      ...data.record,
      verifyStatus: '1',
    });
  }

  setModalProps({ confirmLoading: false });
});

async function handleSubmit() {
  const values = await validate();
  emit('success', { values, autoId: unref(autoId) });
}
</script>

<style lang="less" module>
.comment-modal {
  :global {
    .ant-divider {
      @apply border-gray-700 text-18px font-700 !important;
    }
  }
}
</style>
