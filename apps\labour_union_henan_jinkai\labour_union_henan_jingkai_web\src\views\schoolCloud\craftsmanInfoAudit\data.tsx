import { BasicColumn, FormSchema } from '/@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { cloneDeep, filter } from 'lodash-es';
import { uploadApi } from '/@/api/sys/upload';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
// import { useUserStore } from '@/store/modules/user';
// const useUserStore = useUserStore()
const dictionary = useDictionary();

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
    },
    {
      title: '姓名',
      dataIndex: 'userName',
    },
    
    {
      title: '性别',
      dataIndex: 'gender',
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`gender_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
    },
    // {
    //   title: '所在区域',
    //   dataIndex: 'auditCompanyId',
    //   width: 100,
    //   customRender: ({ text }) => {
    //     return <span>{dictionary.getDictionaryMap.get(`unionInformation_${text}`)?.dictName}</span>
    //   },
    // },
    // {
    //   title: '数据来源',
    //   dataIndex: 'source',
    //   width: 150,
    //   customRender: ({ text }) => {
    //     return <span>{dictionary.getDictionaryMap.get(`craftsmanSource_${text}`)?.dictName}</span>;
    //   },
    // },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      customRender: ({ text, record }) => {
        const { auditStatus } = record;
        return (
          <span
            class={
              auditStatus == 'pass'
                ? 'text-green-500'
                : auditStatus == 'refuse'
                  ? 'text-red-500'
                  : ''
            }
          >
            {dictionary.getDictionaryMap.get(`commonAudit_${text}`)?.dictName}
          </span>
        );
      },
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
      defaultHidden: false,
      customRender: ({ text }) => {
        return <span>{text ? text : '--'}</span>;
      },
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
      defaultHidden: false,
    },
  ];
};

//顶部菜单栏搜索条件配置
export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'userName',
      label: '姓名',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请输入姓名',
        autocomplete: 'off',
      },
    },
    {
      field: 'auditStatus',
      label: '审核状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('commonAudit'),
          placeholder: '请选择审核状态',
        };
      },
    },
    // {
    //   field: 'queryCompanyId',
    //   label: '下级工会',
    //   colProps: { span: 6 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   ifShow: useUserStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5',
    //   componentProps: function () {
    //     return {
    //       options: filter(
    //         cloneDeep(dictionary.getDictionaryOpt.get(`unionsInfo`)),
    //         v => v.value !== '6650f8e054af46e7a415be50597a99d5'
    //       ),
    //     };
    //   },
    // },
    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: { span: 3 },
      defaultValue: true,
    },
  ];
};

//审核弹框
export const modalFormItem = (flg: boolean): FormSchema[] => {
 if (flg) {
    return [
      {
        field: 'auditStatus',
        label: '审核状态',
        component: 'RadioGroup',
        required: true,
        componentProps: {
          options: filter(
            cloneDeep(dictionary.getDictionaryOpt.get('commonAudit')) as RadioGroupChildOption[],
            v => v.value !== 'wait'&&v.value !=="cancel"
          ),
        },
      },
      {
        field: 'auditInstruction',
        label: '审核备注',
        required: function ({ values }) {
          if (values.auditStatus == 'refuse' ||  values.auditStatus == 'return') {
            return true;
          } else {
            return false;
          }
        },
        component: 'InputTextArea',
        componentProps: {
          placeholder: '请输入审核备注',
          showCount: true,
          maxlength: 100,
        },
      },
    ];
  }
  return [
    {
      field: 'auditStatus',
      label: '审核状态',
      component: 'RadioGroup',
      required: true,
      componentProps: {
        options: filter(
          cloneDeep(dictionary.getDictionaryOpt.get('commonAudit')) as RadioGroupChildOption[],
          v => v.value !== 'wait'&&v.value !=="cancel"
        ),
      },
    },
    {
      field: 'auditInstruction',
      label: '审核备注',
      // required: function ({ values }) {
      //   if (values.auditStatus == 'refuse' ||  values.auditStatus == 'return') {
      //     return true;
      //   } else {
      //     return false;
      //   }
      // },
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入审核备注',
        showCount: true,
        maxlength: 100,
      },
    },
  ];
};

//详情弹框配置
export const modalForm = (): FormSchema[] => {
  return [
    {
      field: '',
      label: '工匠信息',
      component: 'Divider',
    },
    {
      field: 'avatar',
      label: '工匠照片',
      required: true,
      component: 'CropperForm',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      componentProps: {
        operateType: 70,
      },
    },
    {
      field: 'userName',
      label: '姓名',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
    },
    {
      field: 'gender',
      label: '性别',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      componentProps: function () {
        return {
          fieldNames: { label: 'dictName', value: 'dictCode' },
          options: dictionary.getDictionaryOBJMap.get('gender'),
        };
      },
    },
    {
      field: 'phone',
      label: '联系电话',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
    },
    {
      field: 'identityCardNumber',
      label: '身份证号码',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
    },
    {
      field: 'companyName',
      label: '所属工会',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
    },
    //
    // {
    //   field: 'auditCompanyId',
    //   label: '所在区域',
    //   required: true,
    //   colProps: { span: 12 },
    //   component: 'Select',
    //   componentProps: function () {
    //     return {
    //       fieldNames: { label: 'dictName', value: 'dictCode' },
    //       options: dictionary.getDictionaryOBJMap.get('unionInformation'),
    //     }
    //   },
    // },
  
    {
      field: 'evidentiaryMaterial',
      component: 'Upload',
      label: '证书照片',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 70,
        },
        maxNumber: 1,
        maxSize: 50,
      },
    },
    // {
    //   field: 'source',
    //   label: '来源',
    //   required: true,
    //   component: 'Select',
    //   colProps: { span: 12 },
    //   componentProps: function () {
    //     return {
    //       fieldNames: { label: 'dictName', value: 'dictCode' },
    //       options: dictionary.getDictionaryOBJMap.get('craftsmanSource'),
    //     };
    //   },
    // },
    {
      field: 'createTime',
      label: '申请时间',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
    },
    {
      field: 'companyName',
      label: '所属工会',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
    },
    {
      field: '',
      label: '审核信息',
      component: 'Divider',
    },
    
    {
      field: 'auditStatus',
      label: '审核状态',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('commonAudit'),
      },
      
    },
    {
      field: 'auditTime',
      label: '审核时间',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
    },
    {
      field: 'auditInstruction',
      label: '审核备注',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
    },
  ];
};
