<template>
  <div>
    <BasicTable @register="registerTable">
      <!-- <template #toolbar>
        <a-button
          type="primary"
          @click="handleAuditBatch"
          auth="/craftsmanInfoAudit/batchAudit"
        >
          批量审核
        </a-button>
      </template> -->
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/craftsmanInfoAudit/view',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                onClick: handleAudit.bind(null, record),
                auth: '/craftsmanInfoAudit/audit',
                disabled: record.auditStatus !== 'wait',
                ifShow: record.showFlag,
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ApplyViewInfoModal
      @register="registerViewInfo"
      :can-fullscreen="false"
      width="60%"
      @success="handleView"
    />
    <BatchModal
      @register="registerBatchApp"
      :can-fullscreen="false"
      width="50%"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { useTable, TableAction, BasicTable } from '@/components/Table';
import { columns, formSchemas } from './data';
import { Modal } from 'ant-design-vue';
import { useModal } from '@/components/Modal';
import ApplyViewInfoModal from './applyViewInfoModal.vue';
import BatchModal from './batchAuditModal.vue';
import { createVNode } from 'vue';
import { CloseCircleFilled } from '@ant-design/icons-vue';
import { map } from 'lodash-es';
import { list, getDetails, app } from '@/api/craftsmanInfoAudit';
import { useMessage } from '@monorepo-yysz/hooks';

const { createErrorModal, createSuccessModal } = useMessage();
//审核
const [registerBatchApp, { openModal, closeModal }] = useModal();
//详情
const [registerViewInfo, { openModal: viewModal }] = useModal();
//报名列表
const [registerTable, { reload, getSelectRows, clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: '/craftsmanInfoAudit/batchAudit',
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  searchInfo: {
    modelType:1
  },
  // rowSelection: {
  //   type: 'checkbox',
  //   getCheckboxProps: record => ({
  //     disabled: record.auditStatus !== 'wait' || record.showFlag !== true,
  //   }),
  // },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/craftsmanInfoAudit/view', '/craftsmanInfoAudit/audit'],
    align: 'left',
    class: '!text-center',
  },
});

//查看详情
function handleView(record) {
  getDetails({ autoId: record.autoId }).then(res => {
    if (res.code === 200) {
      viewModal(true, { record: res?.data, isUpdate: true, disabled: true });
    }
  });
}

//handleAudit
function handleAudit(record) {
  openModal(true, { record: { autoId: record.autoId }, notShow: false });
}

function handleSuccess({ values, autoId }) {
  app({ ...values, autoId: autoId }).then(res => {
    const { code, message: msg } = res;
    if (code === 200) {
      createSuccessModal({ content: '操作成功' });
      reload();
      closeModal();
      clearSelectedRowKeys();
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}

//batch
function handleAuditBatch() {
  const rows = getSelectRows();
  if (!rows || rows.length === 0) {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '请选择至少一条数据进行审核！',
      okText: '确认',
      closable: true,
    });
    return false;
  }
  openModal(true, { record: { workerId: map(rows, v => v.autoId) }, notShow: true });
}
</script>
