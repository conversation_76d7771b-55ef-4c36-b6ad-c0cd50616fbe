<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '选择',
                type: 'default',
                onClick: handleCommentView.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useTable, BasicTable, TableAction } from '/@/components/Table';
import { computed } from 'vue';
import { useUserStore } from '/@/store/modules/user';
import { findAccountUnionUser } from '/@/api/authorInfoManage';
import { appAccountFormSchemas, appAccountColums } from './data';

const userStore = useUserStore();

const emit = defineEmits(['success', 'register']);

const title = computed(() => {
  return `app用户选择`;
});

const [registerModal, {}] = useModalInner(async () => {
  await clearSelectedRowKeys();
});

const [registerTable, { clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  api: findAccountUnionUser,
  columns: appAccountColums(),
  maxHeight: 435,
  beforeFetch: params => {
    params.signCompanyId = userStore.getUserInfo.companyId;
    params.identityType = 'gj';
    return { ...params };
  },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: appAccountFormSchemas(),
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',

    fixed: undefined,
  },
  immediate: true,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  // showIndexColumn: true,
});

//选择按钮操作
function handleCommentView(record) {
  emit('success', record.userId, record.nickname);
}
</script>
