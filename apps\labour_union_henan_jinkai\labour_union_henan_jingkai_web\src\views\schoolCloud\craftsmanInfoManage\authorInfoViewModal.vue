<template>
   <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >

   </BasicForm>
      
  </BasicModal>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicModal, useModalInner ,useModal} from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { addOrUpdateModalForm } from './data';
defineOptions({ name: 'authorInfoViewModal' });

const emit = defineEmits(['success', 'register', 'cancel']);

const isUpdate = ref(true);

const autoId = ref('');

const disabled = ref(false);

const record = ref();

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.userName || ''}--详情`
    : unref(isUpdate)
      ? `编辑--${unref(record)?.userName || ''}`
      : `新增工匠`;
});
const model = ref<Recordable>();

const field = ref('');
const form = computed(() => {
  return addOrUpdateModalForm(unref(isUpdate),unref(disabled));
});
const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  isUpdate.value = !!data?.isUpdate;
  disabled.value = !!data?.disabled;

  if (unref(isUpdate)) {
    autoId.value = data.record.autoId;
    record.value = data.record;

    const { naturalFileUrl: m } = data.record;
    await setFieldsValue({
      ...data.record,
      naturalFileUrl: m ? m.split(',') : [],
    });
  }

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });

  setProps({
    disabled: unref(disabled),
  });
});

async function handleSubmit() {
  try {
    const values = await validate();
    const { naturalFileUrl } = values;
    setModalProps({ confirmLoading: true });
    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...record.value,
        ...values,
        accountInfo: values.contactPhone,
        naturalFileUrl: naturalFileUrl ? naturalFileUrl.join(',') : '',
      },
    });
    // closeModal();
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
