<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
      <template #button="{ model, field }">
        <a-input
          :disabled="disabled || isUpdate"
          type="primary"
          @click="choiceModel(model, field)"
          v-model:value="model[field]"
          placeholder="请选择工匠用户"
        ></a-input>
      </template>
      <template #comButton="{ model, field }">
        <a-input
          type="primary"
          @click="choiceUnion(model, field)"
          v-model:value="model[field]"
          placeholder="请选择工会名称"
        ></a-input>
      </template>
      
    </BasicForm>
  </BasicModal>

  <modelListModals
    @register="registermodelListModal"
    :canFullscreen="false"
    width="60%"
    @success="handleModel"
  />
  <UnionListModal
    @register="registerCommentModal"
    :canFullscreen="false"
    width="60%"
    @success="handleSuccess"
  >
  </UnionListModal>
</template>

<script lang="ts" setup>
import { ref, computed, unref, watch } from 'vue';
import modelListModals from '../../workStar/info/modelList.vue';
import { BasicModal, useModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { addOrUpdateModalForm } from './data';

import UnionListModal from '../../workStar/info/UnionListModal.vue';
const isUpdate = ref(true);

const emit = defineEmits(['success', 'register']);

const disabled = ref(false);

const record = ref<Recordable>();

const autoId = ref('');

const model = ref<Recordable>();

const field = ref('');

const userId = ref('');

const userName = ref('');

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.userName || ''}-- 详情`
    : unref(isUpdate)
      ? `编辑--${unref(record)?.userName || ''}`
      : '新增工匠信息';
});

// 用户列表页面
const [registermodelListModal, { openModal: openModal, closeModal: closeGridListModal }] =
  useModal();


const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  isUpdate.value = !!data?.isUpdate;

  disabled.value = !!data?.disabled;

  record.value = data.record;

  autoId.value = data.record?.autoId;

  if (unref(isUpdate)) {
    // const { otherFilePath, aidApplyFilePath } = data.record
    setFieldsValue({
      ...data.record,
      // aidApplyFilePath: aidApplyFilePath ? aidApplyFilePath.split(',') : [],
      // otherFilePath: otherFilePath ? otherFilePath.split(',') : [],
    });
  }
  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });

  setProps({ disabled: unref(disabled) });
});

const form = computed(() => {
  return addOrUpdateModalForm(unref(isUpdate),unref(disabled));
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });
    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...record.value,
        ...values,
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

// 打开用户列表
function choiceModel(m,f) {
  openModal(true);
  model.value = m;
  field.value = f;
}


const modelRecord=ref<Recordable>()
function handleModel({record}) {
  modelRecord.value=record
  closeGridListModal()
}

//工会组织分级
const temporaryIssueGrading = ref('');
//所有工会
const [registerCommentModal, { openModal: openUnionListModal, closeModal }] = useModal();
watch(modelRecord, () => {
  if (modelRecord.value) {
    model.value[unref(field)] = unref(modelRecord).userName;
    model.value['userId'] = unref(modelRecord).userId;
    model.value['companyName'] = companyName.value? companyName.value:unref(modelRecord).companyName;
    model.value['companyId'] = companyId.value? companyId.value:unref(modelRecord).companyId;
    model.value['identityCardNumber'] =unref(modelRecord).identityCardNumber;
    model.value['phone'] =unref(modelRecord).phone;
    model.value['dateOfBirth'] =unref(modelRecord).dateOfBirth;
    model.value['gender'] =unref(modelRecord).gender;
  }
});

const companyName = ref('');
//工会id
const companyId = ref('');
//所属工会
function choiceUnion(m, f) {
  openUnionListModal(true);
  model.value = m;
  field.value = f;
}

function handleSuccess({ unionName, unionId, issueGrading }) {
  companyName.value = unionName;
  companyId.value = unionId;
  temporaryIssueGrading.value = issueGrading;
  closeModal();
}

watch(companyName, () => {
  if (model.value) {
    model.value[unref(field)] = unref(companyName);
  }
});
</script>
