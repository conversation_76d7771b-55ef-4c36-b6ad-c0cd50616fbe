import { BasicColumn, FormSchema } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import { Tooltip } from 'ant-design-vue';
import { h } from 'vue';
import { Tinymce } from '@/components/Tinymce';
import { list } from '/@/api/authorInfoManage';
import { uploadApi } from '/@/api/sys/upload';
import { validateIdNum, validatePhone } from '@monorepo-yysz/utils';
import {modelTypeFindList} from '@/api/work/index'
import { nextTick } from 'vue';
const dictionary = useDictionary();

//列表配置
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '工匠姓名',
      dataIndex: 'userName',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      width: 120,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`gender_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '数据来源',
      dataIndex: 'dataSources',
      ellipsis: true,
      customRender: ({ record, text }) => {
        return dictionary.getDictionaryMap.get(`dataSources_${text}`)?.dictName;
      },
    },
    {
      title: '是否在荣誉版单展示',
      dataIndex: 'whetherShow',
      width: 150,
      customRender: ({ record, text }) => {
        return dictionary.getDictionaryMap.get(`YesOrNo_${text}`)?.dictName;
        
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

//顶部菜单栏搜索条件配置
export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '工匠姓名',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请输入工匠姓名',
        autocomplete: 'off',
      },
    },
    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: { span: 3 },
      defaultValue: true,
    },
  ];
};


//新增或者编辑弹框配比
export const addOrUpdateModalForm = (isUpdate: boolean,disabled): FormSchema[] => {
  return [
    {
      field: 'avatar',
      label: '工匠照片',
      required: true,
      component: 'CropperForm',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      // ifShow({ values }) {
      //   return 'y' === values.whetherShow;
      // },
      componentProps: {
        operateType: 82,
        imgSize:251/301
      },
    },
    {
      field: 'userName',
      label: '姓名',
      required: true,
      // dynamicDisabled: isUpdate,
      colProps: { span: 12 },
      component: 'Input',
      slot: 'button',
      componentProps: {
        placeholder: '请选择用户',
      },
      ifShow: !disabled,
    },
    {
      field: 'userName',
      component: 'Input',
      label: '姓名',
      colProps: {
        span: 12,
      },
      required: true,
      dynamicDisabled: true,
      ifShow: disabled,
    },
    {
      field: 'userId',
      label: '用户id',
      required: false,
      colProps: { span: 12 },
      component: 'Input',
      componentProps: {
        placeholder: '用户id',
      },
      ifShow: false,
    },
    {
      field: 'companyName',
      label: '工会名称',
      required: true,
      colProps: { span: 12 },
      slot: 'comButton',
      componentProps: {
        placeholder: '请选择所属工会',
        showCount: true,
        maxlength: 50,
      },
      ifShow: !disabled,
    },
    {
      field: 'companyName',
      label: '工会名称',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: true,
      ifShow: disabled,
    },
    {
      field: 'companyId',
      label: '所属工会',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      componentProps: {
        placeholder: '请输入所属工会',
        showCount: true,
        maxlength: 50,
      },
      ifShow:false
    },
    {
      field: 'gender',
      label: '性别',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      componentProps: function () {
        return {
          fieldNames: { label: 'dictName', value: 'dictCode' },
          options: dictionary.getDictionaryOBJMap.get('gender'),
        };
      },
    },
    {
      field: 'phone',
      label: '联系电话',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      componentProps: {
        placeholder: '请输入联系电话',
        showCount: true,
        maxlength: 11,
      },
      rules: [{ required: true, validator: validatePhone, trigger: ['change', 'blur'] }],
    },
    {
      field: 'identityCardNumber',
      label: '身份证号码',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      componentProps: {
        showCount: true,
        maxlength: 18,
      },
      rules: [{ required: true, validator: validateIdNum, trigger: ['change', 'blur'] }],
    },
    {
      field: 'typeBizId',
      label: '所属类别',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          placeholder: '请选择所属类别',
          api: modelTypeFindList,
          resultField: 'data',
          params: {
            pageSize: 10,
            pageNum: 1,
            modelType:1
          },
          alwaysLoad: true,
          immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.typeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'typeName', value: 'typeBizId' },
        };
      },
    },
    {
      field: 'evidentiaryMaterial',
      label: '证明材料',
      component: 'Upload',
      colProps: { span: 12 },
      dynamicDisabled: true,
      ifShow({ values }) {
        return disabled && values.evidentiaryMaterial && values.evidentiaryMaterial.length > 0;
      },
      componentProps: {
        api: uploadApi,
      },
    },
    {
      field: 'personalStyle',
      label: '个人摘要',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 200
      },
    },
    {
      field: 'whetherShow',
      label: '荣誉榜单展示',
      component: 'RadioGroup',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      defaultValue: 'n',
      componentProps: function ({ formModel }) {
        return {
          options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          // onChange: e => {
          //   //新增时才进行操作
          //   if (!isUpdate) {
          //     if ('y' === e.target.value) {
          //       getMaxSortNumber().then(res => {
          //         if (200 === res.code) {
          //           formModel['sortNumber'] = res.data;
          //         }
          //       });
          //     } else {
          //       formModel['personalStyle'] = undefined;
          //       formModel['sortNumber'] = undefined;
          //       formModel['personalProfile'] = undefined;
          //     }
          //   }
          // },
        };
      },
    },
    
    // {
    //   field: 'personalStyle',
    //   label: '个人风采',
    //   colProps: { span: 12 },
    //   component: 'Upload',
    //   ifShow({ values }) {
    //     return 'y' === values.whetherShow;
    //   },
    //   componentProps: {
    //     maxSize: 10,
    //     maxNumber: 3,
    //     api: uploadApi,
    //     accept: ['image/*'],
    //     uploadParams: {
    //       operateType: 82,
    //     }, 
    //   },
    //   rulesMessageJoinLabel: true,
    // },
    
    {
      field: 'personalProfile',
      component: 'Input',
      label: '个人简介',
      required: true,
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return 'y' === values.whetherShow;
      },
      render: ({ model, field, disabled }) => {
        return h(Tinymce, {
          value: model[field],
          onChange: (value: string) => {
            model[field] = value;
          },
          showImageUpload: false,
          operateType: 82,
          options: {
            readonly: disabled,
          },
        });
      },
    },
    
    {
      field: 'evidentiaryMaterial',
      label: '证明材料',
      component: 'Upload',
      colProps: { span: 12 },
      dynamicDisabled: true,
      ifShow({ values }) {
        return disabled && values.evidentiaryMaterial && values.evidentiaryMaterial.length > 0;
      },
      componentProps: {
        api: uploadApi,
      },
    },
  ];
};

//app注册用户列表
export const appAccountColums = (): BasicColumn[] => {
  return [
    {
      title: '用户名',
      dataIndex: 'nickname',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
    },
  ];
};

//选择app注册用户列表筛选条件
export const appAccountFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'nickname',
      label: '用户名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话号码',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

//工作室列表
export const studioColums = (): BasicColumn[] => {
  return [
    {
      title: '工作室名称',
      dataIndex: 'studioUserName',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
    },
  ];
};

//选择工作室列表筛选条件
export const studioFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '工作室名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};
