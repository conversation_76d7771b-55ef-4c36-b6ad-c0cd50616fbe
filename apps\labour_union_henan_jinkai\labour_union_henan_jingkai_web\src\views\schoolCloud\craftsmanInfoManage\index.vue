<template>
  <div>
    <BasicTable
      @register="registerTable"
      :clickToRowSelect="false"
    >
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/craftsmanInfoManage/add"
        >
          新增工匠
        </a-button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/craftsmanInfoManage/viewInfo',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                // auth: '/craftsmanInfoManage/update',
                // ifShow: record.source === 'adminInsert' && record.showFlag,
              },
              {
                icon: 'fluent:delete-20-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/craftsmanInfoManage/delete',
                ifShow: record.showFlag,
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <AuthorInfoModal
      @register="registerModal"
      :canFullscreen="false"
      width="50%"
    />
    <AuthorInfoUpdateModal
      @register="registerAddUpdateModal"
      :canFullscreen="false"
      width="50%"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import AuthorInfoModal from './authorInfoViewModal.vue';
import AuthorInfoUpdateModal from './craftsAddOrUpdateModal.vue';
import { useModal } from '@/components/Modal';
import { getDetails, list, getDelete, updateInfo, adminInsert } from '@/api/craftsmanInfoManage';
import {
  findVoList,
  saveOrUpdate,
  getById,
  updateLogicDeleteById,
  exportFail,
  exportModelWorker,
  getImportTemplateURL,
} from '@/api/workStar/modelWorkerInfo';
import { columns, formSchemas } from './data';
import { message, Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useMessage } from '@monorepo-yysz/hooks';

const { createSuccessModal, createErrorModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: '/craftsmanInfoManage/add',
  showIndexColumn: false,
  api: findVoList,
  beforeFetch: params => {
    return params;
  },
  searchInfo: {
    modelType:1
  },
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 260,
    dataIndex: 'action',

    fixed: undefined,
    auth: [
      '/craftsmanInfoManage/update',
      '/craftsmanInfoManage/viewInfo',
      '/craftsmanInfoManage/delete',
    ],
    align: 'left',
    class: '!text-center',
  },
});

//详情
const [registerModal, { openModal }] = useModal();
//新增或者修改
const [registerAddUpdateModal, { openModal: openUpdateAddModal, closeModal }] = useModal();

//查询详情
function handleView(record) {
  getById({ workerId: record.workerId }).then(res => {
    if (res.code === 200) {
      openModal(true, {
        record: res?.data,
        isUpdate: true,
        disabled: true,
      });
    }
  });
}

//修改信息
function handleEdit(record) {
  getById({ workerId: record.workerId }).then(res => {
    if (res.code === 200) {
      openUpdateAddModal(true, {
        record: res?.data,
        isUpdate: true,
        disabled: false,
      });
    }
  });
}

function handleClick() {
  openUpdateAddModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

//逻辑删除
function handleDelete(record) {
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要删除` + '"' + record.userName + '"' + '?',
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          updateLogicDeleteById(record.workerId).then(res => {
            if (res.code === 200) {
              message.success('删除成功');
            } else {
              message.error('删除失败');
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}

//新增或编辑表单提交后调用
function handleSuccess({ values, isUpdate }) {
  values.modelType=1;
  if (values.evidentiaryMaterial) {
    values.evidentiaryMaterial = values.evidentiaryMaterial.toString();
  }
  if (isUpdate) {
    saveOrUpdate(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: `编辑成功` });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `编辑失败!${message}` });
      }
    });
  } else {
    saveOrUpdate(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: `新增成功` });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `新增失败!${message}` });
      }
    });
  }
}
</script>
