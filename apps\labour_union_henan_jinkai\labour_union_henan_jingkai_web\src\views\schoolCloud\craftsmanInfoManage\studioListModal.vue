<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '选择',
                type: 'default',
                onClick: handleCommentView.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useTable, BasicTable, TableAction } from '/@/components/Table';
import { computed } from 'vue';
import { findVoListManage } from '/@/api/authorInfoManage';
import { studioColums, studioFormSchemas } from './data';

const emit = defineEmits(['success', 'register']);

const title = computed(() => {
  return `所属工作室选择`;
});

const [registerModal, {}] = useModalInner(async () => {
  await clearSelectedRowKeys();
});

const [registerTable, { clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  api: findVoListManage,
  columns: studioColums(),
  maxHeight: 435,
  beforeFetch: params => {
    params.identityType = 'gzs';
    params.logicalDelete = 'n';
    return { ...params };
  },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: studioFormSchemas(),
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
  },
  immediate: true,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
});

//选择按钮操作
function handleCommentView(record) {
  emit('success', record.userId, record.studioUserName);
}
</script>
