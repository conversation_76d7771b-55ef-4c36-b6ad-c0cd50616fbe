<template>
  <div>
    <BasicTable
      @register="registerTable"
      :clickToRowSelect="false"
    >
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/serviceCheck/add"
          >新增敏感词校验</a-button
        >
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record, true),
                auth: '/serviceCheck/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleView.bind(null, record, false),
                auth: '/serviceCheck/modify',
              },
              {
                icon: 'fluent:delete-20-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/serviceCheck/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ServiceModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import ServiceModal from './CheckModal.vue';
import { useModal } from '/@/components/Modal';
import { checkColumns, checkFormSchemas } from '../data';
import { list, deleteLine, saveOrUpdate } from '/@/api/sensitive/check';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  authInfo: ['/serviceDeploy/add'],
  columns: checkColumns(),
  searchInfo: {
    orderBy: 'create_time',
    sortType: 'desc',
  },
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: checkFormSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 250,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

function handleDelete({ name, autoId }: Recordable) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除:${name}`,
    onOk: function () {
      deleteLine(autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleView(record, disabled) {
  const { imgScene, textScene } = record;
  record.textSceneArr = textScene ? textScene.split(',') : [];
  record.imgSceneArr = imgScene ? imgScene.split(',') : [];
  openModal(true, {
    record,
    isUpdate: true,
    disabled,
  });
}

function handleSuccess({ isUpdate, values }) {
  const { textSceneArr, imgSceneArr } = values;
  values.textScene = textSceneArr?.join(',') || '';
  values.imgScene = imgSceneArr?.join(',') || '';
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '修改' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '修改' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
