import { BasicColumn, FormSchema } from '/@/components/Table';
import { Switch } from 'ant-design-vue';
import { saveOrUpdate } from '@/api/sensitive/word';
import { useMessage } from '@monorepo-yysz/hooks';

//列表展示数据
export const checkColumns = (): BasicColumn[] => {
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '编码',
      dataIndex: 'code',
      width: 300,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      width: 160,
    },
  ];
};

//顶部搜索条件
export const checkFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'name',
      label: '名称',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
  ];
};

export const checkModalForm = (): FormSchema[] => {
  return [
    {
      field: 'autoId',
      label: '主键id',
      component: 'Input',
      ifShow: false,
    },
    {
      field: 'name',
      label: '名称',
      required: true,
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入名称',
        maxlength: 50,
        showCount: true,
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'code',
      label: '编码',
      required: true,
      component: 'Input',
      dynamicDisabled: ({ values: { autoId } }) => {
        return !!autoId;
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入编码',
        maxlength: 50,
        showCount: true,
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'checkLocal',
      label: '文本校验',
      component: 'Switch',
      colProps: {
        span: 24,
      },
      defaultValue: false,
      componentProps: {
        checkedValue: true,
        checkedChildren: '开启',
      },
    },
    // {
    //   field: 'checkText',
    //   label: '文本AI辅助校验',
    //   component: 'Switch',
    //   colProps: {
    //     span: 24,
    //   },
    //   defaultValue: false,
    //   componentProps: {
    //     checkedValue: true,
    //     checkedChildren: '开启',
    //   },
    // },
    // {
    //   field: 'textSceneArr',
    //   label: '文本辅助校验场景',
    //   required: true,
    //   component: 'CheckboxGroup',
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get('textScene'),
    //   },
    //   colProps: {
    //     span: 24,
    //   },
    //   ifShow:({values:{checkText}})=>{
    //     return checkText;
    //   },
    // },
    // {
    //   field: 'checkImg',
    //   label: '图片AI辅助校验',
    //   component: 'Switch',
    //   colProps: {
    //     span: 24,
    //   },
    //   defaultValue: false,
    //   componentProps: {
    //     checkedValue: true,
    //     checkedChildren: '开启',
    //   },
    // },
    // {
    //   field: 'imgSceneArr',
    //   label: '图片辅助校验场景',
    //   required: true,
    //   component: 'CheckboxGroup',
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get('imgScene'),
    //   },
    //   colProps: {
    //     span: 24,
    //   },
    //   ifShow:({values:{checkImg}})=>{
    //     return checkImg;
    //   },
    // },
  ];
};

//列表展示数据
export const columns = (): BasicColumn[] => {
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '组别名称',
      dataIndex: 'groupName',
    },
    {
      title: '敏感词',
      dataIndex: 'content',
    },
    {
      title: '创建日期',
      dataIndex: 'createTime',
      width: 160,
    },
    {
      title: '更新日期',
      dataIndex: 'updateTime',
      width: 160,
    },
    {
      dataIndex: 'state',
      title: '状态',
      customRender: ({ record }) => {
        const { createSuccessModal, createErrorModal } = useMessage();
        const el = (
          <Switch
            checked={record.state}
            onClick={() => {
              const stateName = record.state ? '禁用' : '启用';
              const state = !record.state;
              saveOrUpdate({
                ...record,
                state: state,
              }).then(({ code, message }) => {
                if (code === 200) {
                  createSuccessModal({ content: `${stateName}成功` });
                  record.state = state;
                } else {
                  createErrorModal({ content: `${stateName}失败，${message}` });
                }
              });
            }}
          ></Switch>
        );
        return el;
      },
    },
  ];
};

//顶部搜索条件
export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'columnName',
      label: '组别名称',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请输入组别名称',
      },
    },
  ];
};

export const modalForm = (): FormSchema[] => {
  return [
    {
      field: 'groupName',
      label: '组别名称',
      required: true,
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入栏目名称',
        maxlength: 20,
        showCount: true,
      },
    },
    {
      field: 'content',
      label: '敏感词',
      required: true,
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入敏感词,以英文逗号分割',
        maxlength: 10000,
        showCount: true,
      },
    },
    {
      field: 'state',
      label: '启用',
      component: 'Switch',
      colProps: {
        span: 24,
      },
      defaultValue: false,
      componentProps: {
        checkedValue: true,
        checkedChildren: '开启',
      },
    },
  ];
};
