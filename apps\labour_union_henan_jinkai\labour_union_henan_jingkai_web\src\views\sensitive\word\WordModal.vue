<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
    :wrap-class-name="$style['channel-topic-modal']"
  >
    <BasicForm
      @register="registerForm"
      :class="clazz"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { modalForm } from '../data';

const emit = defineEmits(['success', 'register', 'cancel']);

const isUpdate = ref(true);

const autoId = ref('');

const url = ref('');

const disabled = ref(false);

const name = ref();

const clazz = computed(() => {
  return unref(disabled) ? 'back-transparent' : undefined;
});

const title = computed(() => {
  return unref(disabled)
    ? `${unref(name)}--详情`
    : unref(isUpdate)
      ? `修改--${unref(name)}`
      : '新增敏感词组别';
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 120,
  schemas: modalForm(),
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  url.value = '';

  isUpdate.value = !!data?.isUpdate;

  disabled.value = !!data?.disabled;

  if (unref(isUpdate)) {
    autoId.value = data.record.autoId;
    name.value = data.record.groupName;
    await setFieldsValue({
      ...data.record,
    });
    url.value = data.record.servicePic;
  }

  setProps({
    disabled: unref(disabled),
  });

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });
    // TODO custom api
    emit('success', {
      isUpdate: unref(isUpdate),
      values: { ...values, autoId: isUpdate.value ? autoId.value : undefined },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" module>
.channel-topic-modal {
  :global {
    .ant-input-number,
    .ant-picker {
      width: 100% !important;
    }
  }
}
</style>
