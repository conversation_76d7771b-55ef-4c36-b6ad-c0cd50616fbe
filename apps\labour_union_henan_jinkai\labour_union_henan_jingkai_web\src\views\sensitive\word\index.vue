<template>
  <div>
    <BasicTable
      @register="registerTable"
      :clickToRowSelect="false"
    >
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/serviceDeploy/add"
        >
          新增敏感词组别
        </a-button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/serviceDeploy/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/serviceDeploy/modify',
              },
              {
                icon: 'fluent:delete-20-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/serviceDeploy/comment',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ServiceModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import ServiceModal from './WordModal.vue';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from '../data';
import { list, deleteLine, saveOrUpdate } from '@/api/sensitive/word';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  authInfo: ['/serviceDeploy/add'],
  columns: columns(),
  searchInfo: {
    systemQueryType: 'manage',
    orderBy: 'auto_id',
    sortType: 'desc',
  },
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 250,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

function handleDelete({ autoId, groupName }: Recordable) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除:${groupName}`,
    onOk: function () {
      deleteLine(autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除栏目成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleEdit(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: false,
  });
}

function handleView(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
    disabled: true,
  });
}

function handleSuccess({ isUpdate, values }) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '修改' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '修改' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
