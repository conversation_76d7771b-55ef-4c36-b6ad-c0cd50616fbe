import { BasicColumn } from '@/components/Table';
import { Switch } from 'ant-design-vue';
import { changeState } from '@/api/signIn/signInRule';
import { useMessage } from "../../../../../../../packages/hooks";

export const columns = (): BasicColumn[] => {
    return [
        {
            title: '规则名称',
            dataIndex: 'ruleName',
        },
        {
            title: '规则描述',
            dataIndex: 'ruleDescription',
        },
        {
            title: '规则值',
            edit: true,
            editComponent: 'InputNumber',
            editComponentProps: {
                min: 1,
                max: 100,
                step: 1,
            },
            dataIndex: 'ruleValue',
        },
        {
            title: '是否启用',
            dataIndex: 'state',
            customRender: ({ value, record }) => {
                const { createConfirm, createSuccessModal, createErrorModal } = useMessage();
                const flag = value;
                const stateName = value ? '禁用' : '启用';
                const color = value ? '#67C23A' : '#F56C6C';

                const el = (
                    <div>
                        <Switch
                            checked={flag}
                            style={{ backgroundColor: color }}
                            onClick={() => {
                                const text = `是否${stateName}${record.ruleName}`;
                                const state = !value;
                                createConfirm({
                                    iconType: 'warning',
                                    content: text,
                                    onOk: () => {
                                        changeState({
                                            autoId: record.autoId,
                                            state: state,
                                        }).then(({ code, message }) => {
                                            if (code === 200) {
                                                createSuccessModal({ content: `${stateName}成功` });
                                                record.state = state;
                                            } else {
                                                createErrorModal({ content: `${stateName}失败，${message}` });
                                            }
                                        });
                                    },
                                });
                            }}
                        >
                            {stateName}
                        </Switch>
                    </div>
                );
                return el;
            },
        },
    ];
};
