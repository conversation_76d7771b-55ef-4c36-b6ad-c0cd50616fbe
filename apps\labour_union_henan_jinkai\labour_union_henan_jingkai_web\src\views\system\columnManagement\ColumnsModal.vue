<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref, computed, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { modalForm } from './columnManagement';
import { maxSortNumber } from '/@/api/category';

defineOptions({ name: 'ColumnModal' });

const emit = defineEmits(['success', 'register']);

const isUpdate = ref(true);

const categoryId = ref('');

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.categoryName || ''}详情`
    : unref(isUpdate)
      ? `编辑${unref(record)?.categoryName || ''}`
      : '新增栏目';
});

// const fileList = ref<string[] | undefined>([])

const disabled = ref(false);

const record = ref<Recordable>();

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalForm(unref(record));
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  isUpdate.value = !!data?.isUpdate;

  disabled.value = !!data?.disabled;

  record.value = data.record;

  if (unref(isUpdate)) {
    const { categoryId: cat, categoryLogo } = data.record;
    categoryId.value = cat;
    // fileList.value = categoryLogo ? categoryLogo.split(',') : []

    setFieldsValue({
      ...data.record,
      platformType: data.record.platformTypeList || [],
      // // categoryDefault: categoryDefault ? categoryDefault.split(',') : [],
      // // categoryClick: categoryClick ? categoryClick.split(',') : [],
      categoryLogo: categoryLogo ? categoryLogo.split(',') : [],
    });
  } else {
    await maxSortNumber({}).then(res => {
      const { data } = res;
      setFieldsValue({
        sort: data,
      });
    });
  }
  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });

  setProps({ disabled: unref(disabled) });
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 100,
  schemas: form,
  showActionButtonGroup: false,
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });
    const { platformType, cuttingRatio, categoryDefault, categoryClick, categoryLogo } = values;
    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...unref(record),
        ...values,
        platformType: platformType.join(','),
        cuttingRatio: cuttingRatio ? cuttingRatio.join(',') : undefined,
        categoryDefault: categoryDefault ? categoryDefault.join(',') : undefined,
        categoryClick: categoryClick ? categoryClick.join(',') : undefined,
        categoryLogo: categoryLogo ? categoryLogo.join(',') : undefined,
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
