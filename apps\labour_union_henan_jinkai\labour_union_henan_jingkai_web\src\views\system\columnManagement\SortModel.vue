<template>
  <BasicModal
    @register="registerModal"
    @ok="handleTopSuccess"
    title="设置排序"
    v-bind="$attrs"
  >
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, unref } from 'vue';
import { useForm, BasicForm } from '@/components/Form';
import { modalSortFormItem } from './columnManagement';
import { useDictionary } from '@/store/modules/dictionary';
import { useModalInner, BasicModal } from '@/components/Modal';
// import { RadioGroup, DatePicker } from 'ant-design-vue';

const dictionary = useDictionary();

const emit = defineEmits(['register', 'success']);

const autoId = ref('');

const schemas = computed(() => {
  return modalSortFormItem(unref(autoId));
});

const [registerForm, { validate, resetFields, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: schemas,
  showActionButtonGroup: false,
});

const [registerModal, {}] = useModalInner(async data => {
  await resetFields();
  autoId.value = data.autoId || data.record?.autoId;
  if (data.record) {
    setFieldsValue({
      ...data.record,
    });
  }
});

async function handleTopSuccess() {
  const values = await validate();
  emit('success', { values, autoId: autoId.value });
}
</script>
