<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/columnManagement/add"
        >
          新增栏目
        </a-button>
        <a-button
          type="primary"
          @click="handleUnionClick"
          auth="/columnManagement/unionmManage"
        >
          发布工会管理
        </a-button>
        <span style="color: #ef3333">注：APP栏目排序按照序号从小到大排序！</span>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/columnManagement/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/columnManagement/modify',
              },
              {
                icon: 'mdi:sort',
                label: '排序',
                type: 'primary',
                onClick: handleSort.bind(null, record),
                auth: '/columnManagement/sort',
              },
              {
                icon: 'fluent:delete-20-filled',
                label: '删除',
                type: 'primary',
                onClick: handleDelete.bind(null, record),
                danger: true,
                auth: '/columnManagement/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ColumnsModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="50%"
    />
    <SortModel
      @register="registerSort"
      :can-fullscreen="false"
      width="40%"
      @success="handleSortSuccess"
    />
    <UnionTableModel
      @register="registerUnionModal"
      :canFullscreen="false"
      width="88%"
    />
  </div>
</template>

<script lang="ts" setup>
import { createVNode } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { columns, formSchemas } from './columnManagement';
import { Modal } from 'ant-design-vue';
import { CloseCircleFilled, CheckCircleOutlined } from '@ant-design/icons-vue';
import ColumnsModal from './ColumnsModal.vue';
import { useModal } from '/@/components/Modal';
import {
  addCategory,
  deleteCategory,
  getOneColumn,
  listTree,
  updateCategory,
  setCategorySort,
} from '/@/api/category';
import UnionTableModel from './union/unionTableModel.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import SortModel from './SortModel.vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

const { createConfirm, createErrorModal, createSuccessModal, createMessage } = useMessage();

const [registerTable, { reload, updateTableDataRecord }] = useTable({
  rowKey: 'categoryId',
  columns: columns(),
  showIndexColumn: false,
  authInfo: ['/columnManagement/add'],
  api: listTree,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  pagination: false,
  isTreeTable: true,
  actionColumn: {
    title: '操作',
    width: 360,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/columnManagement/modify',
      '/columnManagement/view',
      '/columnManagement/delete',
      '/columnManagement/sort',
    ],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();
const [registerUnionModal, { openModal: openUnionModal }] = useModal();
const [registerSort, { openModal: openSort, closeModal: closeSort }] = useModal();

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}
function handleUnionClick() {
  openUnionModal(true);
}

function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确定删除${record?.categoryName || ''}`,
    onOk: function () {
      deleteCategory({ autoId: record?.autoId }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: '删除成功' });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

//设置排序
function handleSort(record) {
  openSort(true, { record });
}

function handleEdit(record) {
  getOneColumn({ autoId: record.autoId }).then(res => {
    openModal(true, {
      record: res.data,
      isUpdate: true,
      disabled: false,
    });
  });
}

function handleView(record) {
  getOneColumn({ autoId: record.autoId }).then(res => {
    openModal(true, {
      record: res.data,
      isUpdate: true,
      disabled: true,
    });
  });
}

//设置排序回调
function handleSortSuccess({ values, autoId }) {
  const { categoryName, referToAutoId, sequentialOptions } = values;
  const newsSequentialOptionsName = 'before' === sequentialOptions ? '之前' : '之后';
  //根据新闻业务id查询新闻信息
  getOneColumn({ autoId: referToAutoId }).then(res => {
    const { code, data, message: mes } = res;
    if (code === 200) {
      createConfirm({
        title: '操作提示',
        icon: createVNode(ExclamationCircleOutlined),
        content: `确定将[${categoryName}]设置在[${data?.categoryName}]${newsSequentialOptionsName}嘛?确认后系统将自动修改排序号!`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
          try {
            return await new Promise<void>(resolve => {
              setCategorySort({ autoId, sequentialOptions, referToAutoId }).then(res => {
                resolve();
                if (res.code === 200) {
                  createMessage.success('设置成功!');
                  reload();
                  closeSort();
                } else {
                  createMessage.error(`设置失败!${res.message}`);
                }
              });
            });
          } catch {
            return console.log('Oops errors!');
          }
        },
      });
    } else {
      createMessage.error(`设置失败!${mes}`);
    }
  });
}

function handleSuccess({ isUpdate, values }) {
  if (isUpdate) {
    updateCategory(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        Modal.success({
          title: '提示',
          icon: createVNode(CheckCircleOutlined),
          content: '编辑成功!' || message,
          okText: '确认',
          closable: true,
        });
        // 刷新编辑数据的缓存值
        updateTableDataRecord(values.autoId, values);
        closeModal();
        reload();
      } else {
        Modal.error({
          title: '提示',
          icon: createVNode(CloseCircleFilled),
          content: `编辑失败!${message}`,
          okText: '确认',
          closable: true,
        });
      }
    });
  } else {
    addCategory(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        closeModal();
        Modal.success({
          title: '提示',
          icon: createVNode(CheckCircleOutlined),
          content: '新增成功!' || message,
          okText: '确认',
          closable: true,
        });
        reload();
      } else {
        Modal.error({
          title: '提示',
          icon: createVNode(CloseCircleFilled),
          content: `新增失败!${message}`,
          okText: '确认',
          closable: true,
        });
      }
    });
  }
}
</script>
