import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';

export const modalColumns = (): BasicColumn[] => {
  return [
    {
      title: '工会名称',
      dataIndex: 'companyName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const columnSchemas = (): FormSchema[] => {
  return [
    {
      field: 'companyName',
      label: '工会名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

//选择所属工会弹框筛选条件
export const UnionFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'un',
      label: '工会名称',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

//筛选工会列表
export const Unioncolumns = (): BasicColumn[] => {
  return [
    {
      title: '工会名称',
      dataIndex: 'c0100',
    },
  ];
};
