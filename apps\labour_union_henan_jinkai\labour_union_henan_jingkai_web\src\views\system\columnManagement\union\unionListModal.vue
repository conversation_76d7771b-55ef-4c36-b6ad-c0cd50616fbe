<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '选择',
                type: 'default',
                onClick: handleCommentView.bind(null, record),
                // auth: '/difficultEmployees/choice',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useTable, BasicTable, TableAction } from '/@/components/Table';
import { computed } from 'vue';
import { findUnionList } from '@/api/category';
import { UnionFormSchemas, Unioncolumns } from './unionData';
import { useUserStore } from '/@/store/modules/user';

const userStore = useUserStore();

const emit = defineEmits(['success', 'register']);

const title = computed(() => {
  return `发布工会选择`;
});

const [registerModal, {}] = useModalInner(async () => {
  await clearSelectedRowKeys();
});

const [registerTable, { clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  api: findUnionList,
  columns: Unioncolumns(),
  maxHeight: 425,
  beforeFetch: params => {
    params.pi = params.pageNum;
    params.ps = params.pageSize;
    params.puid = userStore.getUserInfo.companyId;
    return { ...params };
  },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: UnionFormSchemas(),
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
    // auth: ['/difficultEmployees/choice']
  },
  immediate: true,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
});

//选择按钮操作
function handleCommentView(record) {
  emit('success', {
    companyName: record.c0100,
    companyId: record.id,
    companyClassicIds: record.c0203,
  });
}
</script>
