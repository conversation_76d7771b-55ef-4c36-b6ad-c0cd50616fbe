<template>
  <BasicModal
    @register="registerTableModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
  >
    <BasicTable
      @register="registerTable"
      v-if="getOpen"
    >
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增发布工会
        </a-button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <UnionListModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { modalColumns, columnSchemas } from './unionData';
import UnionListModal from './unionListModal.vue';
import { list, deleteLine, saveOrUpdate } from '@/api/category/newsReleaseUnion';
import { useModal } from '/@/components/Modal';
import { useMessage } from '@monorepo-yysz/hooks';

defineEmits(['register']);

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const record = ref<Recordable>();

const title = computed(() => {
  return '发布工会管理';
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: modalColumns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: columnSchemas(),
    autoSubmitOnEnter: true,
  },
  beforeFetch(params) {
    // params.companyInfoId = userStore.getUserInfo.companyInfoId
    return params;
  },
  searchInfo: {},
  maxHeight: 380,
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

const [registerTableModal, { setModalProps, getOpen }] = useModalInner(async data => {
  record.value = data.record;
  setModalProps({ confirmLoading: false });
});

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}
// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.companyName}`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess(values) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `新增成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `新增失败! ${message}`,
      });
    }
  });
}
</script>
