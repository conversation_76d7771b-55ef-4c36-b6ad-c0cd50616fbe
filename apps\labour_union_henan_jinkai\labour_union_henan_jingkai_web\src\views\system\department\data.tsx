import { toNumber } from 'lodash-es';
import { BasicColumn, FormSchema } from '/@/components/Table';

export function columns(): BasicColumn[] {
  return [
    {
      title: '排序号',
      dataIndex: 'sort',
      width: 120,
    },
    {
      dataIndex: 'deptName',
      title: '部门名称',
      width: 200,
    },
    {
      dataIndex: 'deptAccount',
      title: '部长',
      width: 200,
    },
    {
      dataIndex: 'deptDescribe',
      title: '备注',
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
      width: 150,
    },
  ];
}

export function formSchemas(): FormSchema[] {
  return [
    {
      field: 'deptName',
      label: '部门名称',
      component: 'Input',
      colProps: {
        span: 6,
      },
      rulesMessageJoinLabel: true,
    },
  ];
}

export function modalFormSchema(): FormSchema[] {
  return [
    {
      field: 'deptName',
      component: 'Input',
      label: '部门名称',
      required: true,
      componentProps: {
        placeholder: '请输入部门名称',
        autocomplete: 'off',
        showCount: true,
        maxlength: 40,
      },
    },
    {
      field: 'pid',
      label: '父级',
      component: 'TreeSelect',
      rulesMessageJoinLabel: true,
      show: ({ values }) => toNumber(values.pid) !== 0,
      slot: 'pid',
    },
    {
      field: 'deptAccount',
      component: 'Input',
      label: '部长',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'sort',
      label: '排序号(展示按照序号从小到大排列)',
      component: 'InputNumber',
      className: '!w-full',
      labelWidth: 240,
      componentProps: {
        min: 0,
        placeholder: '请输入排序号,展示按照序号从小到大排列',
      },
    },
    {
      field: 'deptDescribe',
      component: 'InputTextArea',
      label: '部门描述',
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 200,
      },
    },
  ];
}
