<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, unref } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { modalForm } from './data';

const emit = defineEmits(['success', 'register']);

const isUpdate = ref(true);

const record = ref<Recordable>();

const type = ref('job');

const title = computed(() => {
  return unref(isUpdate) ? `编辑行业${unref(record)?.industryName || ''}` : '新增行业';
});

const form = computed(() => {
  return modalForm(unref(type));
});

const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  labelWidth: 100,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  isUpdate.value = !!data?.isUpdate;

  record.value = data.record;

  type.value = data.type;

  if (unref(isUpdate)) {
    await setFieldsValue({
      ...data.record,
    });
  }

  setModalProps({
    confirmLoading: false,
  });
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });
    emit('success', {
      isUpdate: unref(isUpdate),
      values: { ...unref(record), ...values },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
