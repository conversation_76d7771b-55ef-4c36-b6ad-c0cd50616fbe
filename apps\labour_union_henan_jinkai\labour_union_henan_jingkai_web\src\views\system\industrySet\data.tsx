import { BasicColumn, FormSchema } from '@/components/Table';
import { Image } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/user';
import { listTree } from '@/api/industrySet';

export const columns = (): BasicColumn[] => {
  const userStore = useUserStore();
  return [
    {
      title: '行业名称',
      dataIndex: 'industryName',
      width: 120,
    },
    {
      title: '图标',
      dataIndex: 'industryIcon',
      width: 100,
      customRender: ({ text }) => {
        return text ? (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          />
        ) : (
          ''
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'industryName',
      label: '行业名称',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
  ];
};

export const modalForm = (): FormSchema[] => {
  return [
    {
      field: 'pid',
      label: '上级行业',
      component: 'ApiTreeSelect',
      rulesMessageJoinLabel: true,
      componentProps() {
        return {
          api: listTree,
          fieldNames: { label: 'industryName', value: 'autoId', children: 'children' },
          showSearch: true,
          treeDefaultExpandAll: true,
          filterTreeNode(input: string, option: any) {
            return option.industryName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          getPopupContainer: () => document.body,
        };
      },
    },
    {
      field: 'industryName',
      label: '行业名称',
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 100,
      },
    },
    {
      field: 'industryIcon',
      label: '图标',
      component: 'CropperForm',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        operateType: 60,
      },
    },
  ];
};
