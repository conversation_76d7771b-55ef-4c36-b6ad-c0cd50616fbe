<template>
  <div>
    <BasicTable
      @register="registerTable"
      :clickToRowSelect="false"
    >
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/industryMerchant/add"
        >
          新增行业
        </a-button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                auth: '/industryMerchant/modify',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'fluent:delete-12-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                auth: '/industryMerchant/delete',
                onClick: handleDelete.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <IndustryModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import IndustryModal from './IndustryModal.vue';
import { useModal } from '@/components/Modal';
import { list, saveOrUpdate, deleteIndustry } from '@/api/industrySet';
import { columns, formSchemas } from './data';
import { useMessage } from '@monorepo-yysz/hooks';

const { createSuccessModal, createErrorModal, createConfirm } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  searchInfo: {
    orderBy: 'createTime',
    sortType: 'desc',
    treeFlag: true,
  },
  authInfo: ['/industryMerchant/add'],
  useSearchForm: true,
  isTreeTable: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/industryMerchant/modify', '/industryMerchant/delete'],
  },
});

function handleDelete(record) {
  const name = record.industryName || '';

  createConfirm({
    iconType: 'warning',
    content: `确定要删除行业${name}?`,
    async onOk() {
      try {
        deleteIndustry(record.autoId).then(({ message, code }) => {
          if (code === 200) {
            createSuccessModal({
              content: `删除成功`,
            });
            reload();
          } else {
            createErrorModal({
              content: `删除失败! ${message}`,
            });
          }
        });
      } catch {
        console.log(' errors!');
      }
    },
  });
}

//新增
function handleClick() {
  openModal(true, {
    isUpdate: false,
  });
}

//编辑
function handleEdit(record) {
  openModal(true, {
    record: record,
    isUpdate: true,
  });
}

function handleSuccess({ isUpdate, values }) {
  saveOrUpdate({ ...values }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
