import { BasicColumn, FormSchema } from '/@/components/Table';

//列表展示信息
export function columns(): BasicColumn[] {
  return [
    {
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      dataIndex: 'logDatetime',
      title: '日志时间',
    },
    {
      dataIndex: 'message',
      title: '日志信息',
    },
  ];
}

//顶部搜索条件
export function formSchemas(): FormSchema[] {
  return [
    {
      field: 'nickname',
      label: '用户名',
      component: 'Input',
      colProps: {
        span: 6,
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入用户名',
      },
    },
    {
      field: 'createTime',
      label: '操作时间',
      component: 'RangePicker',
      colProps: {
        span: 8,
      },
      componentProps: {
        showTime: true,
        valueFormat: 'YYYY-MM-DDTHH:mm:ss',
      },
    },
  ];
}

//详情展示数据
export function modalFormItem(): FormSchema[] {
  return [
    {
      field: 'logDatetime',
      label: '日志时间',
      component: 'Input',
      colProps: {
        span: 24,
      },
    },
    {
      field: 'message',
      label: '日志信息',
      component: 'InputTextArea',
      colProps: {
        span: 24,
      },
      componentProps: {
        autoSize: { minRows: 10, maxRows: 10 },
      },
    },
  ];
}
