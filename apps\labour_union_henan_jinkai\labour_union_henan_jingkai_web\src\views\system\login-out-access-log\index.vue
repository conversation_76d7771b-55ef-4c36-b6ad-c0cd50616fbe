<template>
  <div :class="$style.operate">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <SystemLogModal
      @register="registerModule"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { columns } from './data';
import { useModal } from '/@/components/Modal';
import SystemLogModal from './loginAndOutAccessLogModel.vue';
import { map } from 'lodash-es';
import { systemLogFindList } from '@/api/system/log';

const [registerModule, { openModal }] = useModal();

const [registerTable, {}] = useTable({
  rowKey: 'id',
  columns: columns(),
  showIndexColumn: false,
  api: systemLogFindList,
  searchInfo: {
    systemType: 'MANAGE',
  },
  afterFetch(data) {
    const m = map(data, v => {
      const { source, ...other } = v;
      return { ...source, ...other };
    });
    return m;
  },

  useSearchForm: false,
  bordered: true,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    width: 250,
    fixed: undefined,
  },
});

function handleView(record) {
  openModal(true, { record });
}
</script>
<style lang="less" module>
.operate {
  :global {
    .ant-picker {
      width: 100% !important;
    }
  }
}
</style>
