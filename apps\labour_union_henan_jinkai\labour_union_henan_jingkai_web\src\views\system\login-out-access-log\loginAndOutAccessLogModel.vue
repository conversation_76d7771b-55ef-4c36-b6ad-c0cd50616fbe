<template>
  <BasicModal
    @register="registerModule"
    :show-ok-btn="false"
    :can-fullscreen="false"
    :title="title"
  >
    <BasicForm
      @register="registerForm"
      class="back-transparent"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicForm, useForm } from '/@/components/Form';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { modalFormItem } from './data';
import { computed, ref, unref } from 'vue';

const name = ref('');

const title = computed(() => {
  return `${unref(name)}--操作日志`;
});

const [registerForm, { resetFields, setProps, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: modalFormItem(),
  showActionButtonGroup: false,
});

const [registerModule, {}] = useModalInner(async function (data) {
  await resetFields();
  if (data.record) {
    name.value = data.record.title;
    setFieldsValue({
      ...data.record,
    });
  }

  setProps({
    disabled: true,
  });
});
</script>
