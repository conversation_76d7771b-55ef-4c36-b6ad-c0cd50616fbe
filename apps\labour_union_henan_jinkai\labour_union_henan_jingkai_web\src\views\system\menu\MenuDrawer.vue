<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    showFooter
    :title="getTitle"
    width="50%"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref, computed, unref } from 'vue';
import { BasicForm, useForm } from '@/components/Form/index';
import { formSchema } from './menu.data';
import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
import { MENUTYPE_UPPER } from '@monorepo-yysz/enums';

defineOptions({ name: 'MenuDrawer' });

const emit = defineEmits(['success', 'register']);
const isUpdate = ref(true);

const record = ref<Recordable>();

const isLineCreate = ref<boolean>(false);

const isTopCreate = ref<boolean>(false);

const pid = ref<number>(0);

const form = computed(() => {
  return formSchema(unref(isLineCreate));
});

const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
  labelWidth: 180,
  schemas: form,
  showActionButtonGroup: false,
  baseColProps: { lg: 12, md: 24 },
});

const [registerDrawer, { setDrawerProps }] = useDrawerInner(async data => {
  await resetFields();
  setDrawerProps({ confirmLoading: false });
  isUpdate.value = !!data?.isUpdate;
  record.value = data.record;
  pid.value = data.pid;
  isLineCreate.value = !!data.isLineCreate;

  isTopCreate.value = !data.isLineCreate && !unref(isUpdate);

  console.log(record.value);

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
      pid: unref(pid),
    });
  }
});

const getTitle = computed(() =>
  !unref(isUpdate) ? '新增菜单' : `编辑菜单-${unref(record)?.title || ''}`
);

async function handleSubmit() {
  try {
    const values = await validate();
    setDrawerProps({ confirmLoading: true });
    const { pid: p, menuType, ifTop } = values;

    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...unref(record),
        ...values,
        pid:
          (unref(isTopCreate) && menuType === MENUTYPE_UPPER.MENU) || ifTop
            ? 0
            : unref(isLineCreate)
              ? unref(pid)
              : p,
      },
    });
  } finally {
    setDrawerProps({ confirmLoading: false });
  }
}
</script>
