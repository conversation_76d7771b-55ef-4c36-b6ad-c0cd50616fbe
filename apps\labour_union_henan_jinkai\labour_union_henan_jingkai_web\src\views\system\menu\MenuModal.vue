<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalFormCopy } from './menu.data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const title = computed(() => {
  return `复制${unref(record)?.title || ''}`;
});

const form = computed(() => {
  return modalFormCopy();
});

const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
  labelWidth: 160,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  setFieldsValue({
    ...data.record,
  });

  setModalProps({ confirmLoading: false });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        autoId: null,
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
