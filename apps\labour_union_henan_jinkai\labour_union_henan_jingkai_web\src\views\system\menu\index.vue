<template>
  <div class="w-full h-full">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleCreate(null)"
        >
          新增菜单
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'material-symbols:note-add',
                onClick: handleCreate.bind(null, record),
                tooltip: '新增下级',
              },
              {
                icon: 'clarity:note-edit-line',
                onClick: handleEdit.bind(null, record),
                tooltip: '编辑',
              },
              {
                icon: 'mingcute:copy-fill',
                onClick: handleCopy.bind(null, record),
                tooltip: '复制',
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除',
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <MenuDrawer
      @register="registerDrawer"
      @success="handleSuccess"
    />
    <MenuModal
      @register="registerModal"
      @success="handleCopySuccess"
      width="60%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { getMenuList, deleteLine, menuSaveByDTO, menuUpdateByDTO } from '@/api/sys/menu';
import { useDrawer } from '@/components/Drawer';
import MenuDrawer from './MenuDrawer.vue';
import { columns, searchFormSchema } from './menu.data';
import { useMessage } from '@monorepo-yysz/hooks';
import MenuModal from './MenuModal.vue';
import { useModal } from '@/components/Modal';
import { sortMenuData } from '@/store/modules/permission';

const [registerDrawer, { openDrawer, closeDrawer }] = useDrawer();

const [registerModal, { openModal, closeModal }] = useModal();

const { createErrorModal, createConfirm } = useMessage();

const [registerTable, { reload, expandRows }] = useTable({
  rowKey: 'menuId',
  api: getMenuList,
  columns: columns(),
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema(),
    submitOnChange: true,
  },
  afterFetch: data => {
    return sortMenuData(data);
  },
  pagination: false,
  useSearchForm: false,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    fixed: undefined,
  },
});

function handleCreate(record: Recordable | null) {
  openDrawer(true, {
    isUpdate: false,
    pid: record ? record.autoId : 0,
    isLineCreate: !!record,
  });
}

function handleEdit(record: Recordable) {
  openDrawer(true, {
    record,
    pid: record.pid,
    isUpdate: true,
  });
}

// 复制
function handleCopy(record: Recordable) {
  openModal(true, { record });
}

function handleCopySuccess({ values }) {
  menuSaveByDTO({
    ...values,
    systemType: 'unionSystem',
    autoId: null,
  }).then(({ code, message }) => {
    if (code === 200) {
      reload();
      closeModal();

      // 展开当前
      values.pid && expandRows([values.pid]);
      setTimeout(() => {
        createConfirm({
          content: `复制成功菜单结构已经变化，是否重新加载菜单？`,
          iconType: 'success',
          onOk: function () {
            window.location.reload();
          },
        });
      }, 500);
    } else {
      createErrorModal({
        content: `复制失败! ${message}`,
      });
    }
  });
}

// 删除
function handleDelete(record: Recordable) {
  deleteLine(record.autoId).then(({ code, message }) => {
    if (code === 200) {
      reload();

      setTimeout(() => {
        createConfirm({
          content: '删除成功,菜单结构已经变化，是否重新加载菜单？',
          iconType: 'success',
          onOk: function () {
            window.location.reload();
          },
        });
      }, 500);
    } else {
      createErrorModal({
        content: `删除失败! ${message}`,
      });
    }
  });
}

function handleSuccess({ values, isUpdate }: Recordable) {
  const api = isUpdate ? menuUpdateByDTO : menuSaveByDTO;

  api({
    ...values,
    systemType: 'unionSystem',
  }).then(({ code, message }) => {
    if (code === 200) {
      reload();
      closeDrawer();
      // 展开当前
      values.pid && expandRows([values.pid]);
      setTimeout(() => {
        createConfirm({
          content: `${isUpdate ? '编辑' : '新增'}成功菜单结构已经变化，是否重新加载菜单？`,
          iconType: 'success',
          onOk: function () {
            window.location.reload();
          },
        });
      }, 500);
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
