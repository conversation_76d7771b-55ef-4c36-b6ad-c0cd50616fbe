import { BasicColumn, FormSchema } from '/@/components/Table';

export function columns(): BasicColumn[] {
  return [
    {
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      dataIndex: 'title',
      title: '标题',
    },
    {
      dataIndex: 'ipLocation',
      title: 'IP地址',
    },
    {
      dataIndex: 'ipLocation',
      title: 'IP',
      customRender({ record }) {
        return <span title={record.ipAddr}>{record.ipAddr}</span>;
      },
    },
    {
      dataIndex: 'userNickname',
      title: '操作人员',
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
    },
  ];
}

export function formSchemas(): FormSchema[] {
  return [
    {
      field: 'title',
      label: '标题',
      component: 'Input',
      colProps: {
        span: 6,
      },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入标题',
      },
    },
    // {
    //   field: 'account',
    //   label: '操作人员',
    //   component: 'Input',
    //   colProps: {
    //     span: 6,
    //   },
    //   componentProps: {
    //     autocomplete: 'off',
    //     placeholder: '请输入操作人员',
    //   },
    // },
    // {
    //   field: 'createTime',
    //   label: '操作时间',
    //   component: 'RangePicker',
    //   colProps: {
    //     span: 8,
    //   },
    //   componentProps: {
    //     showTime: true,
    //     valueFormat: 'YYYY-MM-DDTHH:mm:ss',
    //   },
    // },
  ];
}

export function modalFormItem(): FormSchema[] {
  return [
    {
      field: 'title',
      label: '标题',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'userNickname',
      label: '操作人员',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'ipAddr',
      label: '请求URL',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'ipLocation',
      label: '主机地址',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'code',
      label: '操作状态',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'createTime',
      label: '操作时间',
      component: 'Input',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'errorMsg',
      label: '错误消息',
      component: 'InputTextArea',
      componentProps: {
        autoSize: true,
      },
    },
    {
      field: 'requestBody',
      label: '请求参数',
      component: 'InputTextArea',
      componentProps: {
        autoSize: true,
      },
    },
    {
      field: 'jsonResult',
      label: '请求参数',
      component: 'InputTextArea',
      componentProps: {
        autoSize: true,
      },
    },
  ];
}
