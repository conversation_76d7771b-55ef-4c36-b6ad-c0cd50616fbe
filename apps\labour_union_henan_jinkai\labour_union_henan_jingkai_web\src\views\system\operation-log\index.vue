<template>
  <div :class="$style.operate">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <OperateModal
      @register="registerModule"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { columns, formSchemas } from './data';
import { useModal } from '@/components/Modal';
import OperateModal from './operationLogModel.vue';
import { useUserStore } from '@/store/modules/user';
import { map } from 'lodash-es';
import { operateLogFindList } from '@/api/system/log';

const user = useUserStore();

const [registerModule, { openModal }] = useModal();

const [registerTable, {}] = useTable({
  rowKey: 'id',
  columns: columns(),
  showIndexColumn: false,
  api: operateLogFindList,
  searchInfo: {
    companyId: user.getUserInfo.companyId,
  },
  beforeFetch: params => {
    if (params.createTime) {
      params.startTime = params.createTime[0];
      params.endTime = params.createTime[1];
    }
    params.customAgent = 'ncadmin'
    params.createTime = undefined;
    return params;
  },
  afterFetch(data) {
    const m = map(data, v => {
      const { source, ...other } = v;
      return { ...source, ...other };
    });
    return m;
  },
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    width: 250,
    fixed: undefined,
  },
});

function handleView(record) {
  openModal(true, { record });
}
</script>
<style lang="less" module>
.operate {
  :global {
    .ant-picker {
      width: 100% !important;
    }
  }
}
</style>
