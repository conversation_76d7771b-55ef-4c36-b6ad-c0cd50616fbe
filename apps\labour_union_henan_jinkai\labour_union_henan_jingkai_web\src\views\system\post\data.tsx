import { getPostTreeList } from '@/api/system/post';
import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { list } from '@/api/system/dept';
import { useUserStore } from '@/store/modules/user';

const userStore = useUserStore();

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '所属部门',
      dataIndex: 'deptId',
      width: 100,
    },
    {
      title: '岗位名',
      dataIndex: 'postName',
      width: 100,
    },
    {
      title: '岗位描述',
      dataIndex: 'postDescribe',
      width: 100,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'postName',
      label: '岗位名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'deptId',
      label: '所属部门',
      component: 'ApiTreeSelect',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        api: list,
        resultField: 'data',
        fieldNames: {
          label: 'deptName',
          value: 'deptId',
          children: 'children',
        },
        placeholder: '请选择所属部门',
        params: {
          pageSize: 999,
        },
      },
    },
    {
      field: 'postName',
      label: '岗位名',
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'parentId',
      label: '父级岗位',
      required: false,
      component: 'ApiTreeSelect',
      rulesMessageJoinLabel: true,
      componentProps: function ({ formModel }) {
        return {
          api: getPostTreeList,
          fieldNames: { label: 'postName', value: 'postId', children: 'children' },
          params: {
            companyId: userStore.getUserInfo.companyId,
          },
          immediate: false,
          alwaysLoad: true,
          showSearch: true,
          labelField: 'postName',
          valueField: 'postId',
          resultField: 'data',
          getPopupContainer: () => document.body,
          filterTreeNode(input: string, option: any) {
            return option.postName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
        };
      },
    },
    {
      field: 'postDescribe',
      label: '岗位描述',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      componentProps: {
        placeholder: '请输入岗位描述',
        rows: 3,
      },
    },
  ];
};
