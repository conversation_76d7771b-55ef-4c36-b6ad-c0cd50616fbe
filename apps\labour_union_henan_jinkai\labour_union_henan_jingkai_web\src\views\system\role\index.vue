<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleCreate"
        >
          新增角色
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'eos-icons:cluster-role-binding',
                label: '选择干部',
                type: 'primary',
                onClick: handleCadre.bind(null, record),
              },
              {
                icon: 'simple-icons:authy',
                label: '栏目权限',
                type: 'primary',
                onClick: handleColumn.bind(null, record),
              },
              {
                icon: 'fluent:delete-20-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <RoleModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="35%"
    />

    <CadreModal
      @register="registerCadreModal"
      width="60%"
      :canFullscreen="false"
      @success="handleCadreSuccess"
    />

    <AuthorizeModal
      @register="registerColumnModal"
      width="50%"
      :canFullscreen="false"
      @success="handleColumnSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, TableAction, useTable } from '@/components/Table';
import { columns, formSchemas } from './role.data';
import { useModal } from '/@/components/Modal';
import RoleModal from './RoleModal.vue';
import {
  addRole,
  updateRole,
  getRoleList,
  deleteRole,
  roleBindOfficer,
  getUserIdsByRoleId,
  roleNewsAdd,
  findRolePermissionList,
} from '@/api/system/role';
import CadreModal from '../../union/cadre/CadreModal.vue';
import AuthorizeModal from '../columnAuthorize/AuthorizeModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';

const [registerModal, { openModal, closeModal }] = useModal();

const [registerCadreModal, { openModal: openCadreModal, closeModal: closeCadreModal }] = useModal();

const [registerColumnModal, { openModal: openColumnModal, closeModal: closeColumnModal }] =
  useModal();

const { createConfirm, createSuccessModal, createErrorModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  api: getRoleList,
  columns: columns(),
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: {
      span: 12,
    },
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    fixed: undefined,
    width: 570,
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
  },
});

function handleCreate() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

// delete接口
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确定删除${record.roleName}?`,
    onOk: function () {
      deleteRole(record.autoId).then(res => {
        const { code, message } = res;
        if (code === 200) {
          createSuccessModal({ content: '删除成功！' });

          reload();
        } else {
          createErrorModal({ content: `删除失败！${message} ` });
        }
      });
    },
  });
}

// 编辑
function handleEdit(record: Recordable<any>) {
  findRolePermissionList({ roleId: record.roleId }).then(data => {
    openModal(true, { isUpdate: true, disabled: false, record: { ...record, ...data } });
  });
}

// 详情
function handleView(record: Recordable<any>) {
  findRolePermissionList({ roleId: record.roleId }).then(data => {
    openModal(true, { isUpdate: true, disabled: true, record: { ...record, ...data } });
  });
}

function handleSuccess({ isUpdate, values }) {
  if (isUpdate) {
    updateRole(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({
          content: '编辑成功!',
        });

        // 刷新编辑数据的缓存值
        closeModal();
        reload();
      } else {
        createErrorModal({
          content: `编辑失败!${message}`,
        });
      }
    });
  } else {
    addRole(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({
          content: '新增成功!',
        });
        closeModal();
        reload();
      } else {
        createErrorModal({
          content: `新增失败!${message}`,
        });
      }
    });
  }
}

// 干部
function handleCadre(record: Recordable) {
  getUserIdsByRoleId({ roleId: record.autoId, accountType: 'unionCadre' }).then(res => {
    if (res.code === 200) {
      openCadreModal(true, {
        isUpdate: true,
        record,
        userArr: res.data,
      });
    }
  });
}

function handleCadreSuccess(values) {
  roleBindOfficer(values).then(res => {
    const { code, message } = res;
    if (code === 200) {
      createSuccessModal({
        content: '绑定干部成功!',
      });
      closeCadreModal();
      reload();
    } else {
      createErrorModal({
        content: `绑定失败!${message}`,
      });
    }
  });
}

// 栏目
function handleColumnSuccess(values) {
  roleNewsAdd(values).then(res => {
    const { code, message } = res;
    if (code === 200) {
      createSuccessModal({
        content: '绑定栏目成功!',
      });
      closeColumnModal();
      reload();
    } else {
      createErrorModal({
        content: `绑定栏目失败!${message}`,
      });
    }
  });
}

function handleColumn(record) {
  openColumnModal(true, {
    isUpdate: true,
    record,
    columnList: record.categoryIds?.split(',') || [],
  });
}
</script>
