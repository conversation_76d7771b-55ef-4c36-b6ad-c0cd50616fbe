import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { Switch } from 'ant-design-vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { roleEnableOrDisable } from '@/api/system/role';
import { getMenuList } from '@/api/sys/menu';
import { ApiBasicTree } from '@/components/Form';

function sortMenuData(menuData) {
  // 使用 JavaScript 的数组排序方法，按照 orderNum 字段升序排序
  menuData.sort((a, b) => {
    return a.orderNum - b.orderNum;
  });
  // 如果每个菜单项还有子菜单，需要对子菜单递归排序
  menuData.forEach(menu => {
    if (menu.children && menu.children.length > 0) {
      menu.children = sortMenuData(menu.children);
    }
  });
  return menuData;
}

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '角色名称',
      dataIndex: 'roleName',
    },
    // {
    //   title: '所属企业',
    //   dataIndex: 'companyName',
    //   width: 200,
    // },
    // {
    //   title: '角色类型',
    //   dataIndex: 'createType',
    //   width: 180,
    //   customRender({ text }) {
    //     const name = dictionary.getDictionaryMap.get(`createType_${text}`)?.dictName;
    //     return <span title={name}>{name}</span>;
    //   },
    // },

    {
      title: '角色描述',
      dataIndex: 'remark',
    },
    {
      title: '角色状态',
      dataIndex: 'roleState',
      width: 120,
      customRender: ({ value, record }) => {
        const { createConfirm, createSuccessModal, createErrorModal } = useMessage();
        const name = dictionary.getDictionaryMap.get(`state_${value}`)?.dictName;
        const color = dictionary.getDictionaryMap.get(`state_${value}`)?.remark;

        const flg = value === 'NORMAL';
        const el = (
          <div>
            <Switch
              checked-children={name}
              unCheckedChildren={name}
              checked={flg}
              class={`cursor-pointer`}
              style={{ backgroundColor: color }}
              onClick={() => {
                const stateName = value === 'NORMAL' ? '禁用' : '启用';
                const roleState = value === 'NORMAL' ? 'BAN' : 'NORMAL';
                const text = `是否${stateName}${record.roleName}`;

                createConfirm({
                  iconType: 'warning',
                  content: text,
                  onOk: () => {
                    roleEnableOrDisable({
                      roleId: record.roleId,
                      operateState: roleState === 'NORMAL',
                    }).then(({ code, message }) => {
                      if (code === 200) {
                        createSuccessModal({ content: `${stateName}成功` });
                        record.roleState = roleState;
                      } else {
                        createErrorModal({ content: `${stateName}失败，${message}` });
                      }
                    });
                  },
                });
              }}
            >
              {name}
            </Switch>
          </div>
        );
        return el;
      },
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  return [
    {
      field: 'roleName',
      label: '角色名称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'roleState',
      label: '角色状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('state'),
        };
      },
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'roleName',
      label: '角色名称',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入角色名称',
        showCount: true,
        maxlength: 40,
      },
      component: 'Input',
    },
    {
      field: 'allCheckMenuIdList',
      label: '角色菜单',
      component: 'ApiTree',
      itemProps: {
        autoLink: false,
      },
      rest: true,
      render({ field, model, disabled }) {
        return (
          <ApiBasicTree
            checkValue={model[field]}
            disabled={disabled}
            toolbar={true}
            expandOnSearch={true}
            fieldNames={{ children: 'children', title: 'title', key: 'menuId' }}
            search={true}
            checkable={true}
            showLine={true}
            placeholderSearch="请输入菜单名称"
            api={getMenuList}
            onCheck={(checkedKeys, e) => {
              const { halfCheckedKeys } = e || {};

              model[field] = checkedKeys;
              model['halfCheckMenuIdList'] = halfCheckedKeys;
            }}
            afterFetch={res => {
              return sortMenuData(res || []);
            }}
          />
        );
      },
    },
    {
      label: '菜单半选',
      field: 'halfCheckMenuIdList',
      component: 'ShowSpan',
      show: false,
      rulesMessageJoinLabel: true,
    },
    {
      label: '角色类型',
      field: 'createType',
      component: 'ShowSpan',
      show: false,
      rulesMessageJoinLabel: true,
    },
    {
      label: '角色描述',
      field: 'remark',
      component: 'InputTextArea',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      componentProps: {
        autoSize: { minRows: 3, maxRows: 5 },
        showCount: true,
        maxlength: 200,
      },
    },
  ];
};
