<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :showOkBtn="false"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record: r }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'mdi:unlocked-variant',
                label: '解绑',
                type: 'primary',
                disabled: !hasVal(r),
                onClick: handleUnlocked.bind(null, r),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import {
  getThirdConfigList,
  cancelBandingThirdPlatform,
  getThirdPlatformList,
} from '@/api/system/user';
import { useMessage } from '@monorepo-yysz/hooks';
import { includes } from 'lodash-es';

defineEmits(['register']);

const { createConfirm, createSuccessModal, createErrorModal } = useMessage();

const record = ref<Recordable>();

const checkboxVal = ref<string[]>([]);

const title = computed(() => {
  return `解绑${unref(record)?.account || ''}`;
});

const [registerTable] = useTable({
  rowKey: 'thirdId',
  columns: [{ title: '名称', dataIndex: 'platformName' }],
  showIndexColumn: false,
  api: getThirdConfigList,
  beforeFetch(params) {
    params.pageSize = 0;
    return params;
  },
  maxHeight: 410,
  useSearchForm: true,
  bordered: true,
  pagination: false,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  record.value = data.record;
  await getBind();
  setModalProps({ confirmLoading: false });
});

async function getBind() {
  checkboxVal.value = await getThirdPlatformList({ account: unref(record)?.account });
}

const hasVal = item => includes(unref(checkboxVal), item.platformCode);

// 详情
function handleUnlocked(item: Recordable<any>) {
  createConfirm({
    iconType: 'warning',
    content: `请确定是否解绑${unref(record)?.platformName || ''}?`,
    onOk: function () {
      cancelBandingThirdPlatform({
        account: unref(record)?.account,
        platformCode: item?.platformCode,
      }).then(res => {
        const { code, message } = res;
        if (code === 200) {
          getBind();
          createSuccessModal({ content: '解绑成功！' });
        } else {
          createErrorModal({
            content: `解绑失败！${message} `,
          });
        }
      });
    },
  });
}
</script>
