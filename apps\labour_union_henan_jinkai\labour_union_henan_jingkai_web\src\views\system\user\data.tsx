import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useUserStore } from '@/store/modules/user';
import { useDictionary } from '@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { Switch } from 'ant-design-vue';
import { accountEnableOrDisable } from '@/api/system/user';
import { useMessage } from '@monorepo-yysz/hooks';
import { validatePassWithOthers } from '@monorepo-yysz/utils';
import { AccountTypeEnum } from '@monorepo-yysz/enums';
import { RuleObject } from 'ant-design-vue/lib/form';
import { AuthSystemEnums } from '@/enums/appEnum';
import CompanySelect from '@/views/components/company-select/index.vue';
import { getRoleList } from '@/api/system/role';
import { list } from '@/api/system/dept';

const dictionary = useDictionary();

const userStore = useUserStore();

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '帐号',
      dataIndex: 'account',
    },
    {
      title: '帐号类型',
      dataIndex: 'accountType',
      width: 160,
      customRender({ text }) {
        const name =
          text === AccountTypeEnum.ADMIN
            ? '管理员'
            : text === AccountTypeEnum.MANAGE
              ? '管理人员'
              : text === AccountTypeEnum.NORMAL
                ? '普通用户'
                : '未知类型';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      width: 160,
    },
    // 联系电话
    {
      dataIndex: 'phone',
      title: '联系电话',
      width: 160,
    },
    {
      dataIndex: 'accountState',
      title: '状态',
      customRender: ({ value, record }) => {
        const { createConfirm, createSuccessModal, createErrorModal } = useMessage();
        const name = dictionary.getDictionaryMap.get(`commonStatus_${value}`)?.dictName;
        const color = dictionary.getDictionaryMap.get(`commonStatus_${value}`)?.remark;

        const flg = value === 'NORMAL';
        const agree = userStore.getUserInfo.account === record.account;
        const el = (
          <div>
            <Switch
              checked-children={name}
              unCheckedChildren={name}
              disabled={'ADMIN' === record.accountType}
              checked={flg}
              class={`${agree ? '' : 'cursor-pointer'} `}
              style={{ backgroundColor: color }}
              onClick={() => {
                if (agree) {
                  return false;
                }
                const stateName = value === 'NORMAL' ? '禁用' : '启用';
                const accountState = value === 'NORMAL' ? 'BAN' : 'NORMAL';
                const text = `是否${stateName}${record.account}`;

                createConfirm({
                  iconType: 'warning',
                  content: text,
                  onOk: () => {
                    accountEnableOrDisable({
                      openId: record.openId,
                      operateState: accountState === 'NORMAL',
                    }).then(({ code, message }) => {
                      if (code === 200) {
                        createSuccessModal({ content: `${stateName}成功` });
                        record.accountState = accountState;
                      } else {
                        createErrorModal({ content: `${stateName}失败，${message}` });
                      }
                    });
                  },
                });
              }}
            >
              {name}
            </Switch>
          </div>
        );
        return el;
      },
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'account',
      label: '帐号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const modalFormItem = (isUpdate: boolean): FormSchema[] => {
  return [
    {
      field: 'accountType',
      label: '帐号类型',
      colProps: { span: 24 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      show: false,
      // show: userStore.getUserInfo.accountType === AccountTypeEnum.ADMIN,
      // defaultValue:
      //   userStore.getUserInfo.accountType === AccountTypeEnum.ADMIN
      //     ? AccountTypeEnum.MANAGE
      //     : AccountTypeEnum.NORMAL,
      defaultValue: AccountTypeEnum.NORMAL,
      componentProps: {
        options: (
          dictionary.getDictionaryOpt.get('accountType') as RadioGroupChildOption[]
        )?.filter(item => item.value !== 'ADMIN'),
      },
    },
    {
      field: 'account',
      label: '帐号',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      // rules: [{ required: true, validator: validatePhone }],
      rulesMessageJoinLabel: true,
      // ifShow({ values }) {
      //   return values.accountType === AccountTypeEnum.NORMAL;
      // },
    },

    {
      field: 'pwd',
      label: '密码',
      // required: true,
      rules: [{ required: true, validator: validatePassWithOthers }],
      colProps: { span: 24 },
      component: 'StrengthMeter',
      ifShow: !isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'confirmPassword',
      label: '重输密码',
      dynamicRules: ({ values }) => {
        return [
          {
            required: true,
            validator: async (_: RuleObject, value: string) => {
              if (!value) {
                return Promise.reject('请再次输入密码');
              }
              if (value !== values.pwd) {
                return Promise.reject('两次输入的密码不一致');
              }
              return Promise.resolve();
            },
          },
        ];
      },
      colProps: { span: 24 },
      component: 'InputPassword',
      ifShow: !isUpdate,
      rulesMessageJoinLabel: true,
      componentProps: {
        visibilityToggle: true,
      },
    },
    {
      field: 'authSystem',
      label: '授权系统',
      colProps: { span: 24 },
      component: 'Select',
      required: true,
      rulesMessageJoinLabel: true,
      defaultValue: [AuthSystemEnums.BACKEND],
      componentProps: {
        options: [
          { label: '小程序', value: AuthSystemEnums.APP },
          { label: '后台管理系统', value: AuthSystemEnums.BACKEND },
          { label: '三方商户系统', value: AuthSystemEnums.OPEN },
        ],
        mode: 'multiple',
      },
    },
    {
      field: 'nickname',
      label: '昵称',
      required: true,
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      required: true,
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyId',
      label: '所属工会',
      show: false,
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyName',
      label: '所属工会',
      colProps: { span: 24 },
      component: 'ApiTreeSelect',
      required: true,
      rest: true,
      rulesMessageJoinLabel: true,
      render: ({ model, values }) => {
        return (
          <CompanySelect
            value={values.companyName}
            onChange={(v: { companyId: string; companyName: string; record: Recordable }) => {
              const { companyId, companyName } = v;

              model['companyId'] = companyId;
              model['companyName'] = companyName;
            }}
          />
        );
      },
    },
    {
      field: 'deptIdList',
      label: '所属部门',
      colProps: { span: 24 },
      component: 'ApiTreeSelect',
      rulesMessageJoinLabel: true,
      componentProps({}) {
        return {
          api: list,
          fieldNames: {
            label: 'deptName',
            value: 'deptId',
          },
          resultField: 'data',
          params: {
            companyId: userStore.getUserInfo.companyId,
          },
          multiple: true,
          alwaysLoad: true,
          showSearch: true,
          treeDefaultExpandAll: true,
          filterTreeNode(input: string, option: any) {
            return option.deptName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          getPopupContainer: () => document.body,
        };
      },
    },
    {
      field: 'roleIdList',
      label: '相关角色',
      colProps: { span: 24 },
      component: 'ApiTreeSelect',
      required: false,
      rulesMessageJoinLabel: true,
      componentProps({}) {
        return {
          api: getRoleList,
          resultField: 'data',
          fieldNames: {
            label: 'roleName',
            value: 'roleId',
          },
          params: {
            companyId: userStore.getUserInfo.companyId,
            pageSize: 999,
          },
          immediate: userStore.getUserInfo.accountType !== AccountTypeEnum.ADMIN,
          multiple: true,
          alwaysLoad: true,
          showSearch: true,
          treeDefaultExpandAll: true,
          filterTreeNode(input: string, option: any) {
            return option.roleName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          getPopupContainer: () => document.body,
        };
      },
    },
  ];
};
