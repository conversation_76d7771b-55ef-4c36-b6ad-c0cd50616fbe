import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { ActivityTypeZh } from '@/views/activities/activities.d';
import dayjs from 'dayjs';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '领取用户',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '联系电话',
      dataIndex: 'userMobile',
      width: 150,
    },
    {
      title: '票券批次号',
      dataIndex: 'ticketId',
      width: 150,
    },
    {
      title: '领取时间',
      dataIndex: 'createTime',
      width: 120,
    },
    {
      title: '到期时间',
      dataIndex: 'ticketDeadlineTime',
      width: 120,
    },
    {
      title: '使用状态',
      dataIndex: 'ticketStatus',
      width: 80,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`couponUseState_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '核销时间',
      dataIndex: 'useTime',
      width: 120,
    },
    {
      title: '核销人',
      dataIndex: 'ckUserName',
      width: 120,
    },
  ];
};

export const groupColumns = (): BasicColumn[] => {
  return [
    {
      title: '活动名称',
      dataIndex: 'activityName',
      width: 150,
    },
    {
      title: '活动类型',
      dataIndex: 'activityMode',
      width: 150,
      customRender: ({ text }) => {
        return <span>{ActivityTypeZh[text]}</span>;
      },
    },
    {
      title: '组织单位',
      dataIndex: 'companyName',
      width: 150,
    },
    {
      title: '活动状态',
      dataIndex: 'activityEndTime',
      width: 150,
      customRender: ({ text }) => {
        return <span>{dayjs(text) > dayjs() ? '进行中' : '已结束'}</span>;
      },
    },
    {
      title: '普惠群体',
      dataIndex: 'groupName',
      width: 150,
    },
    {
      title: '关联日期',
      dataIndex: 'createTime',
      width: 120,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'activityName',
      component: 'Input',
      label: '活动名称',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 6,
      },
    },
  ];
};

export const recordSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'userName',
      component: 'Input',
      label: '用户姓名',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 6,
      },
    },
    {
      field: 'userMobile',
      component: 'Input',
      label: '联系电话',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 6,
      },
    },
    {
      field: 'ticketStatus',
      label: '核销状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`couponUseState`),
        placeholder: '请选择核销状态',
      },
    },
  ];
};
