<template>
  <BasicTree
    :selectedKeys="selectedKeys"
    expandOnSearch
    :search="true"
    :treeData="treeData"
    :showLine="true"
    @select="handleSelect"
    :load-data="onLoadData"
    :class="`tree-info ${$style['tree-info']}`"
    placeholderSearch="请输入工会名称"
    ref="treeRef"
    :defaultExpandLevel="1"
    v-bind="$attrs"
  >
    <template #other>
      <div class="text-xl text-[#2172f1] font-bold pb-2">工会组织架构</div>
    </template>
    <template #title="{ title }">
      <Tooltip class="!truncate text-15px w-9/10">
        <template #title>{{ title }}</template>
        {{ title }}
      </Tooltip>
    </template>
  </BasicTree>
</template>

<script lang="ts" setup>
import { isArray, map, orderBy, uniq } from 'lodash-es';
import { nextTick, onBeforeMount, onMounted, onUnmounted, ref, unref } from 'vue';
import { unionFind, unionNextLevel } from '@/api';
import { BasicTree, TreeItem, KeyType, TreeActionType } from '@/components/Tree';
import { useUserStore } from '@/store/modules/user';
import { Tooltip } from 'ant-design-vue';

const user = useUserStore();

const emit = defineEmits(['selectInfo']);

const treeData = ref<TreeItem[]>([]);

const selectedKeys = ref<KeyType[]>([]);

const treeRef = ref<Nullable<TreeActionType>>(null);

function handleSelect(_, { node, selected }) {
  selectedKeys.value = selected ? [node.companyId] : [user.getUserInfo.companyId];
  emit('selectInfo', {
    companyId: selected ? node.companyId : undefined,
    companyName: selected ? node.companyName : '',
  });
}

function onLoadData(treeNode) {
  const { companyId } = treeNode;

  return unionNextLevel({ pid: companyId }).then(({ data }) => {
    if (isArray(treeNode.children) && treeNode.children.length > 0) {
      return;
    }

    const asyncTreeAction: TreeActionType | null = unref(treeRef);
    if (asyncTreeAction) {
      if (treeNode.dataRef) {
        asyncTreeAction.updateNodeByKey(treeNode.eventKey, {
          children: map(data || [], v => ({ ...v, key: v.companyId, title: v.companyName })),
        });
        asyncTreeAction.setExpandedKeys(
          uniq([treeNode.eventKey, ...asyncTreeAction.getExpandedKeys()])
        );
      }
    }
  });
}

//定义tree
function getTree() {
  const tree = unref(treeRef);
  if (!tree) {
    throw new Error('tree is null!');
  }
  return tree;
}

function handleLevel(level: number) {
  getTree().filterByLevel(level);
}

onBeforeMount(async () => {
  const companyId = user.getUserInfo.companyId;

  const { data: firstData } = await unionFind({ companyId });

  const { data = [] } = await unionNextLevel({ pid: companyId });

  treeData.value = [
    {
      companyId: companyId,
      title: firstData?.companyName || '顶级工会',
      key: companyId,
      children: orderBy(
        map(data, v => ({ ...v, key: v.companyId, title: v.companyName })),
        ['createTime'],
        ['asc']
      ),
    },
  ] as TreeItem[];

  selectedKeys.value = [companyId];
});

//初始化树
onMounted(async () => {
  await nextTick(() => {
    setTimeout(() => {
      handleLevel(1);
    }, 500);
  });
});

onUnmounted(() => {
  treeData.value = [];
});
</script>

<style lang="less" module>
.tree-info {
  :global {
    .ant-tree {
      height: 75vh;
      overflow-x: hidden;

      .ant-tree-treenode {
        margin-top: 5px;

        .ant-tree-title {
          @apply text-15px !h-full;
        }

        svg {
          color: #184564;
        }
      }
    }
  }
}
</style>
