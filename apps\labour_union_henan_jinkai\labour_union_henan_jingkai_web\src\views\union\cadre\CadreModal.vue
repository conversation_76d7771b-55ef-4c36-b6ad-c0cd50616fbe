<template>
  <BasicModal
    @register="register"
    @ok="handleSubmit"
    :title="title"
  >
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>

<script lang="ts">
import { computed, defineComponent, ref, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicTable, useTable } from '/@/components/Table';
import { getColmns } from './data';
import { useUserStore } from '/@/store/modules/user';
import { map, filter } from 'lodash-es';
import { getUnionAllCadreByUnionId, getUnionAllDeptByUnionId } from '/@/api/system/role';

export default defineComponent({
  name: 'CadreModal',
  components: { BasicModal, BasicTable },
  emits: ['success', 'register'],
  setup(_, { emit }) {
    const user = useUserStore();

    const roleName = ref('');

    const autoId = ref<number | undefined>(undefined);

    const title = computed(() => {
      return `${unref(roleName)}--选择【单位领导】干部`;
    });

    const userArr = ref<string[]>([]);
    const dataAll = ref([]);

    //注册窗口
    const [register, { setModalProps }] = useModalInner(async function (data) {
      setModalProps({ confirmLoading: false });
      userArr.value = data.userArr || [];

      if (data.record) {
        roleName.value = data.record.roleName;
        autoId.value = data.record.autoId;
        setSelectedRowKeys([...unref(userArr)]);
      }
    });

    const [registerTable, { getSelectRows, setSelectedRowKeys, getSelectRowKeys }] = useTable({
      rowKey: 'cadreAccountId',
      bordered: true,
      columns: getColmns(),
      pagination: false,
      useSearchForm: true,
      formConfig: {
        labelWidth: 120,
        schemas: [
          {
            field: 'cadreName',
            label: '干部姓名',
            component: 'Input',
            colProps: {
              span: 6,
            },
            componentProps: {
              autocomplete: 'off',
              placeholder: '请输入干部姓名',
            },
          },
          {
            field: 'tel',
            label: '联系方式',
            component: 'Input',
            colProps: {
              span: 6,
            },
            rulesMessageJoinLabel: true,
          },
          {
            field: 'deptid',
            label: '部门名称',
            component: 'ApiSelect',
            colProps: { span: 7 },
            rulesMessageJoinLabel: true,
            componentProps: ({ formActionType }) => {
              return {
                api: getUnionAllDeptByUnionId,
                resultField: 'data',
                params: { companyId: user.getUserInfo.companyId },
                alwaysLoad: true,
                immediate: true,
                showSearch: true,
                filterOption: (input: string, option: any) => {
                  return option.userName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                },
                fieldNames: { label: 'deptName', value: 'id' },
              };
            },
          },
        ],
        autoSubmitOnEnter: true,
        actionColOptions: {
          span: 4,
        },
      },
      searchInfo: {
        companyId: user.getUserInfo.companyId,
      },
      afterFetch: dataSource => {
        const { data } = dataSource;
        dataAll.value = dataSource;
        return data;
      },
      api: getUnionAllCadreByUnionId,
      rowSelection: {
        type: 'checkbox',
      },
    });

    async function handleSubmit() {
      setModalProps({ confirmLoading: true });
      try {
        let selectData = getSelectRows();
        const selectRowKeys = getSelectRowKeys();
        if (selectRowKeys.length > 0 && selectData.length === 0) {
          selectData = filter(unref(dataAll), v => getSelectRowKeys().includes(v.cadreAccountId));
        }

        const dataSource = unref(userArr);

        // const selectArr = map(selectData, v => v.cadreAccountId);
        // console.log(selectArr, 'selectArr');

        const cancel: string[] = [];

        map(dataSource, v => {
          if (!selectRowKeys.includes(v)) {
            cancel.push(v);
          }
        });
        emit('success', {
          // confirmList: selectArr,
          confirmList: selectData,
          roleId: unref(autoId),
          companyId: user.getUserInfo.companyId,
          cancelList: cancel,
          accountType: 'unionCadre',
        });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    }

    return {
      register,
      registerTable,
      handleSubmit,
      title,
    };
  },
});
</script>
