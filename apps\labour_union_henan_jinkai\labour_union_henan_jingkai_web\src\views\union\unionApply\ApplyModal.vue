<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerBasicForm"
      class="back-transparent"
      :disabled="true"
    />
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormBasic, modalFormItem } from './data';
import { split } from 'lodash-es';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const title = computed(() => {
  return `${unref(record)?.companyName || ''}--详情`;
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(unref(disabled));
});

const [registerBasicForm, { resetFields: resetBasicFields, setFieldsValue: setBasicFieldsValue }] =
  useForm({
    labelWidth: 120,
    schemas: modalFormBasic,
    showActionButtonGroup: false,
  });

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetBasicFields();
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    await setBasicFieldsValue({
      ...data.record,
      sqcl: data.record.sqcl ? split(data.record.sqcl, ',') : [],
    });

    await setFieldsValue({
      ...data.record,
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();

    emit('success', {
      values: {
        ...values,
        batchList: [unref(record)?.applyId],
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
