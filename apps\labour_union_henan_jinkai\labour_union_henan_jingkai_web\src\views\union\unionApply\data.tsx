import { uploadApi } from '@/api/sys/upload';
import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import { cloneDeep, filter } from 'lodash-es';
import { RadioGroupChildOption } from 'ant-design-vue/lib/radio/Group';

const dictionary = useDictionary();

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '单位名',
      dataIndex: 'companyName',
      width: 180,
    },
    {
      title: '经办人',
      dataIndex: 'jbr',
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'lxdh',
      width: 120,
    },
    {
      title: '详细地址',
      dataIndex: 'xxdz',
      width: 180,
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
      width: 150,
    },
    {
      title: '审核状态',
      dataIndex: 'auditState',
      width: 100,
      customRender: ({ record }) => {
        const state = dictionary.getDictionaryMap.get(`auditState_${record.auditState}`);
        return <span style={{ color: state?.remark || '' }}>{state?.dictName || '-'}</span>;
      },
    },
    {
      title: '审核账号',
      dataIndex: 'auditOpenId',
      width: 150,
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'companyName',
      label: '单位名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'jbr',
      label: '经办人',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'lxdh',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'dwxz',
      label: '单位性质',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('dwxz') || [],
      },
    },
    {
      field: 'jjlx',
      label: '经济类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('jjlx') || [],
      },
    },
    {
      field: 'sshy',
      label: '所属行业',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('sshy') || [],
      },
    },
    {
      field: 'tyshxydm',
      label: '统一社会信用代码',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'auditState',
      label: '审核状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('auditState') || [],
      },
    },
    {
      field: 'dateTime',
      label: '审核时间',
      colProps: { span: 8 },
      component: 'RangePicker',
      rulesMessageJoinLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
    },
  ];
};

export const modalFormItem = (disabled?: boolean): FormSchema[] => {
  return [
    {
      field: '',
      component: 'Divider',
      label: '审核信息',
    },
    {
      field: 'auditOpenId',
      label: '审核账号',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow: disabled,
    },
    {
      field: 'auditCompanyId',
      label: '审核单位',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow: disabled,
    },
    {
      field: 'auditTime',
      label: '审核时间',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow: disabled,
    },
    {
      field: 'auditState',
      label: '审核状态',
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: {
        options:
          (filter(
            cloneDeep(dictionary.getDictionaryOpt.get('auditState')),
            v => v.value !== 'WAIT'
          ) as RadioGroupChildOption[]) || [],
      },
    },
    {
      field: 'auditRemark',
      label: '审核备注',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      componentProps: {
        rows: 3,
      },
    },
  ];
};

export const modalFormBasic: FormSchema[] = [
  {
    field: '',
    component: 'Divider',
    label: '单位信息',
  },
  {
    field: 'companyName',
    label: '单位名',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'pid',
    label: '上级单位id',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'jbr',
    label: '经办人',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'lxdh',
    label: '联系电话',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'ssqy',
    label: '所属区域',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'xxdz',
    label: '详细地址',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'zgsl',
    label: '职工数量',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'dwxz',
    label: '单位性质',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('dwxz') || [],
    },
  },
  {
    field: 'jjlx',
    label: '经济类型',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('jjlx') || [],
    },
  },
  {
    field: 'sshy',
    label: '所属行业',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('sshy') || [],
    },
  },
  {
    field: 'tyshxydm',
    label: '统一社会信用代码',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'sqcl',
    label: '申请材料',
    colProps: { span: 12 },
    component: 'Upload',
    rulesMessageJoinLabel: true,
    componentProps: {
      api: uploadApi,
    },
  },
  {
    field: 'createTime',
    label: '申请时间',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
];
