<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleBatchReview()"
        >
          批量审核
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                disabled: record.auditState !== 'WAIT',
                onClick: handleBatchReview.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ApplyModal
      @register="registerModal"
      :can-fullscreen="false"
      @success="handleSuccess"
      width="50%"
    />
    <BasicSimpleAudit
      @register="registerBasicSimpleAudit"
      :can-fullscreen="false"
      @success="handleSuccess"
      width="40%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { columns, formSchemas } from './data';
import ApplyModal from './ApplyModal.vue';
import { list, auditBatch } from '@/api/union/audit';
import { useMessage } from '@monorepo-yysz/hooks';
import { isEmpty, map } from 'lodash-es';
import BasicSimpleAudit from '@/views/components/basic-simple-audit.vue';

const { createErrorModal, createSuccessModal, createWarningModal } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();

const [
  registerBasicSimpleAudit,
  { openModal: openBasicSimpleAudit, closeModal: closeBasicSimpleAudit },
] = useModal();

const [registerTable, { reload, getSelectRows }] = useTable({
  rowKey: 'applyId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    actionColOptions: {
      span: 16,
    },
  },
  rowSelection: {
    type: 'checkbox',
    fixed: 'left',
    getCheckboxProps: record => ({
      disabled: record.auditState !== 'WAIT',
    }),
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 190,
    dataIndex: 'action',
    fixed: 'right',
  },
});

// 审核
function handleBatchReview(record?: Recordable<any>) {
  if (isEmpty(record)) {
    const selectedRows = getSelectRows();

    if (isEmpty(selectedRows)) {
      createWarningModal({
        content: '请选择需要审核的申请！',
      });
      return;
    }

    const batchList = map(selectedRows, 'applyId');

    openBasicSimpleAudit(true, { batchList });
  } else {
    openModal(true, { isUpdate: true, disabled: false, record });
  }
}

// 详情
function handleView(record: Recordable<any>) {
  openModal(true, { isUpdate: true, disabled: true, record });
}

function handleSuccess({ values }: Recordable<any>) {
  auditBatch(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `审核成功！`,
      });
      reload();
      closeModal();
      closeBasicSimpleAudit();
    } else {
      createErrorModal({
        content: `审核失败！${message}。`,
      });
    }
  });
}
</script>
