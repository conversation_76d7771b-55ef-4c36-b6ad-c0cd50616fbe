import { validate4phone } from '@monorepo-yysz/utils';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/lib/radio/Group';
import cascaderOptions, { DivisionUtil } from '@pansy/china-division';
import CompanySelect from '@/views/components/company-select/index.vue';

const dictionary = useDictionary();

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '工会名称',
      dataIndex: 'companyName',
      width: 150,
      fixed: 'left',
    },
    {
      title: '工会类型',
      dataIndex: 'ghlx',
      width: 100,
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`ghlx_${text}`)?.dictName || text;
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '工会负责人',
      dataIndex: 'fzr',
      width: 100,
    },
    {
      title: '联系电话',
      dataIndex: 'fzrlxdh',
      width: 130,
      customRender({ text }) {
        return <span title={text}>{text ? validate4phone(text) : ''}</span>;
      },
    },
    {
      title: '详细地址',
      dataIndex: 'xxdz',
      width: 150,
      ellipsis: true,
    },
    {
      title: '职工数量',
      dataIndex: 'zgsl',
      width: 100,
    },
    {
      title: '单位性质',
      dataIndex: 'dwxz',
      width: 100,
      defaultHidden: true,
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`dwxz_${text}`)?.dictName || text;
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '经济类型',
      dataIndex: 'jjlx',
      width: 100,
      defaultHidden: true,
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`jjlx_${text}`)?.dictName || text;
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '所属行业',
      dataIndex: 'sshy',
      width: 120,
      defaultHidden: true,
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`sshy_${text}`)?.dictName || text;
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'tyshxydm',
      width: 160,
      defaultHidden: true,
      ellipsis: true,
    },
    {
      title: '工会成立时间',
      dataIndex: 'clsj',
      width: 120,
      defaultHidden: true,
    },
  ];
};

export const searchFormSchema = (): FormSchema[] => {
  return [
    {
      field: 'companyName',
      label: '工会名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'fzrlxdh',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'dwxz',
      label: '单位性质',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('dwxz') || [],
      },
    },
    {
      field: 'jjlx',
      label: '经济类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('jjlx') || [],
      },
    },
    {
      field: 'sshy',
      label: '所属行业',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('sshy') || [],
      },
    },

    {
      field: 'isNewWorker',
      component: 'RadioGroup',
      label: '新就业形态工会工会',
      labelWidth: 150,
      colProps: {
        span: 8,
      },
      componentProps: {
        options: [
          { label: '全部', value: undefined },
          ...((dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]) || []),
        ],
      },
    },
    {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: {
        span: 3,
      },
      defaultValue: true,
    },
  ];
};

export const modalForm = (): FormSchema[] => {
  const dictionary = useDictionary();

  const divisionUtil = new DivisionUtil(cascaderOptions);

  const cityData = divisionUtil.getSourceData();

  return [
    {
      field: '',
      component: 'Divider',
      label: '工会档案',
    },
    {
      field: 'companyName',
      label: '工会名',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: true,
    },
    {
      field: 'pid',
      label: '上级',
      colProps: { span: 8 },
      component: 'Input',
      show: false,
      defaultValue: '0',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'pName',
      label: '上级工会',
      colProps: { span: 12 },
      component: 'ApiTreeSelect',
      required: false,
      rest: true,
      rulesMessageJoinLabel: true,
      render: ({ model, values }) => {
        return (
          <CompanySelect
            value={values.pName}
            onChange={(v: { companyId: string; companyName: string; record: Recordable }) => {
              const { companyId, companyName } = v;

              model['pid'] = companyId;
              model['pName'] = companyName;
            }}
          />
        );
      },
    },
    {
      field: 'ghlx',
      label: '工会类型',
      colProps: { span: 8 },
      component: 'Select',
      required: true,
      rulesMessageJoinLabel: true,
      defaultValue: 'qt',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('ghlx') || [],
      },
    },
    {
      field: 'fzr',
      label: '工会负责人',
      colProps: { span: 8 },
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'fzrlxdh',
      label: '联系电话',
      colProps: { span: 8 },
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'ssqy',
      label: '所属区域',
      colProps: { span: 8 },
      component: 'Cascader',
      required: true,
      rulesMessageJoinLabel: true,
      defaultValue: ['410000', '410100', '410171'],
      componentProps: function () {
        return {
          options: cityData,
        };
      },
    },
    {
      field: 'xxdz',
      label: '详细地址',
      colProps: { span: 8 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'zgsl',
      label: '职工数量',
      colProps: { span: 8 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'dwxz',
      label: '单位性质',
      colProps: { span: 8 },
      component: 'Select',
      required: true,
      rulesMessageJoinLabel: true,
      defaultValue: 'qtdw',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('dwxz') || [],
      },
    },
    {
      field: 'jjlx',
      label: '经济类型',
      colProps: { span: 8 },
      component: 'Select',
      required: true,
      rulesMessageJoinLabel: true,
      defaultValue: 'qtjjl',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('jjlx') || [],
      },
    },
    {
      field: 'sshy',
      label: '所属行业',
      colProps: { span: 8 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: 'ggglshbzshzz',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('sshy') || [],
      },
    },
    {
      field: 'tyshxydm',
      label: '社会统一信用代码',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'clsj',
      label: '工会成立时间',
      colProps: { span: 8 },
      component: 'DatePicker',
      rulesMessageJoinLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'zxXm',
      label: '主席姓名',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'zxLxfs',
      label: '主席联系电话',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'zxQmsj',
      label: '主席期满时间',
      colProps: { span: 8 },
      component: 'DatePicker',
      rulesMessageJoinLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'zxHjsj',
      label: '主席换届时间',
      colProps: { span: 8 },
      component: 'DatePicker',
      rulesMessageJoinLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'gbzxdt',
      label: '干部最新动态',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const pagedColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '姓名',
      dataIndex: 'cadreName',
      width: 80,
    },
    {
      title: '性别',
      dataIndex: 'gender',
      width: 80,
      customRender({ text }) {
        const name = text === 2 ? '女' : '男';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '所属部门',
      dataIndex: 'deptName',
      width: 80,
    },
    {
      title: '所属职务',
      dataIndex: 'postName',
      width: 200,
    },
    {
      title: '干部类型',
      dataIndex: 'cadreType',
      width: 80,
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`gblx_${text}`)?.dictName;
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '联系电话',
      dataIndex: 'contractPhone',
      width: 120,
      customRender({ text }) {
        return <span title={text}>{text ? validate4phone(text) : ''}</span>;
      },
    },
  ];
};

export const pagedFormSchema = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'un',
      component: 'Input',
      label: '干部名称',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 6,
      },
    },
    {
      field: 'deptid',
      component: 'Select',
      label: '所属部门',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 6,
      },
      componentProps: () => {
        return {
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.deptName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'deptName', value: 'deptName' },
          options: [],
        };
      },
    },
    {
      field: 'ct',
      component: 'Select',
      label: '干部类型',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 6,
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('gblx'),
      },
    },
  ];
};
