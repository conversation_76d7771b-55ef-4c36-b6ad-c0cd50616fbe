<template>
  <div :class="$style['union-info']">
    <a-row>
      <a-col :span="4">
        <UnionNextLevel @selectInfo="handleNext" />
      </a-col>
      <a-col :span="20">
        <PageWrapper
          :title="`${companyName}下级工会信息`"
          contentClass="!m-0"
        >
          <BasicTable @register="registerTable">
            <template #toolbar>
              <a-button
                type="primary"
                :loading="loading"
                @click="handleAdd"
                >新增工会</a-button
              >

              <a-button
                type="primary"
                :loading="loading"
                @click="handleDown"
                v-if="false"
                >导出下级本工会信息</a-button
              >
            </template>
            <template #bodyCell="{ record, column }">
              <template v-if="column.dataIndex === 'action'">
                <TableAction
                  :actions="[
                    {
                      icon: 'fa6-solid:pen-to-square',
                      label: '编辑',
                      type: 'primary',
                      onClick: handleEdit.bind(null, record),
                    },
                    {
                      icon: 'carbon:task-view',
                      label: '详情',
                      type: 'default',
                      onClick: handleView.bind(null, record),
                    },
                  ]"
                />
              </template>
            </template>
          </BasicTable>
        </PageWrapper>
      </a-col>
    </a-row>
    <UnionInfoModal
      @register="registerModal"
      width="80%"
      :canFullscreen="false"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { Row, Col } from 'ant-design-vue';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { columns, searchFormSchema } from './data';
import { downUnion } from '@/api';
import { useUserStore } from '@/store/modules/user';
import UnionNextLevel from '../UnionNextLevel/index.vue';
import { PageWrapper } from '@/components/Page';
import { ref, unref, watch } from 'vue';
import { useModal } from '@/components/Modal';
import UnionInfoModal from './UnionInfoModal.vue';
import { downloadByData } from '@monorepo-yysz/utils';
import { useMessage } from '@monorepo-yysz/hooks';
import { saveApi, updateApi, view, list } from '@/api/union/company';

const ARow = Row;
const ACol = Col;

const userStore = useUserStore();

const { createErrorModal, createSuccessModal } = useMessage();

const loading = ref(false);

const companyId = ref<string | number>(userStore.getUserInfo.companyId);

const companyName = ref(userStore.getUserInfo.companyName || '');

const [registerModal, { openModal, closeModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'companyAutoId',
  api: list,
  columns: columns(),
  formConfig: {
    labelWidth: 120,
    schemas: searchFormSchema(),
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    actionColOptions: {
      span: 7,
    },
  },
  beforeFetch: (params: Recordable) => {
    params.pid = unref(companyId) === '0' ? undefined : unref(companyId);
    return params;
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    width: 100,
    fixed: false,
  },
});

function handleNext({ companyId: id, companyName: name }) {
  companyId.value = id;
  companyName.value = name || '';
}

// 新增
function handleAdd() {
  openModal(true, { isUpdate: false, disabled: false });
}

// 编辑
function handleEdit(record) {
  view({ companyId: record.companyId }).then(res => {
    if (res.code === 200) {
      openModal(true, { record: res.data, disabled: false, isUpdate: true });
    } else {
      createErrorModal({ content: `查看详情失败! ${res.message}` });
    }
  });
}

async function handleView(record) {
  view({ companyId: record.companyId }).then(({ code, data }) => {
    code === 200 &&
      openModal(true, {
        record: data,
        disabled: true,
        isUpdate: true,
      });
  });
}

function handleSuccess({ values, isUpdate }) {
  const api = isUpdate ? updateApi : saveApi;
  api({ ...values }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}

function handleDown() {
  loading.value = true;
  downUnion({ puid: unref(companyId) }).then(data => {
    downloadByData(data, `${unref(companyName)}工会信息.xlsx`);
    loading.value = false;
  });
}

watch(companyId, () => {
  reload();
});
</script>

<style lang="less" module>
.union-info {
  :global {
    background-color: #fff;

    .ant-form {
      @apply px-6px;
    }

    .ant-page-header {
      @apply !py-0 !pl-36px;

      span {
        @apply !text-[#2172f1] font-bold;
      }
    }
  }
}
</style>
