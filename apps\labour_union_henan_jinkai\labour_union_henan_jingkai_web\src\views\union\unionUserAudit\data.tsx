import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/lib/radio/Group';
import { cloneDeep, filter, includes } from 'lodash-es';

const dictionary = useDictionary();

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '会员名',
      dataIndex: 'nickname',
      width: 120,
    },
    {
      title: '会员性别',
      dataIndex: 'xb',
      width: 100,
      customRender({ text }) {
        const name = text === 'man' ? '男' : '女';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '认证组织名',
      dataIndex: 'authCompanyName',
      width: 180,
    },
    {
      title: '入会时间',
      dataIndex: 'companyJoinTime',
      width: 150,
    },

    {
      title: '证件号',
      dataIndex: 'identityNum',
      width: 120,
    },

    {
      title: '联系电话',
      dataIndex: 'lxdh',
      width: 100,
    },
    {
      title: '现居地',
      dataIndex: 'xjd',
      width: 100,
    },
    {
      title: '审核状态',
      dataIndex: 'auditState',
      width: 100,
    },

    {
      title: '审核账号',
      dataIndex: 'auditOpenId',
      width: 100,
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
      width: 150,
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'nickname',
      label: '会员名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },

    {
      field: 'authCompanyName',
      label: '认证组织名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'xb',
      label: '会员性别',
      colProps: { span: 6 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: [
          { label: '全部', value: undefined },
          { label: '男', value: 'man' },
          { label: '女', value: 'female' },
        ],
      },
    },
    {
      field: 'lxdh',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'hjlx',
      label: '户籍类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('hjlx') || [],
      },
    },
    {
      field: 'zzmm',
      label: '政治面貌',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('zzmm') || [],
      },
    },
    {
      field: 'xl',
      label: '学历',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('xlcc') || [],
      },
    },

    {
      field: 'sfxjyxtldz',
      label: '新就业形态劳动者',
      colProps: { span: 6 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: [
          { label: '全部', value: undefined },
          ...((dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]) || []),
        ],
      },
    },
    {
      field: 'ghgbzw',
      label: '工会干部职务',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('ghgbzw') || [],
      },
    },
    {
      field: 'auditState',
      label: '审核状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('auditState') || [],
      },
    },
    {
      field: 'dateTime',
      label: '审核时间',
      colProps: { span: 8 },
      component: 'RangePicker',
      rulesMessageJoinLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
    },
  ];
};

export const modalFormItem = (disabled?: boolean): FormSchema[] => {
  return [
    {
      field: '',
      component: 'Divider',
      label: '审核信息',
    },
    {
      field: 'auditOpenId',
      label: '审核账号',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow: disabled,
    },
    {
      field: 'auditCompanyId',
      label: '审核单位',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow: disabled,
    },
    {
      field: 'auditTime',
      label: '审核时间',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow: disabled,
    },
    {
      field: 'auditState',
      label: '审核状态',
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: {
        options:
          (filter(
            cloneDeep(dictionary.getDictionaryOpt.get('auditState')),
            v => v.value !== 'WAIT'
          ) as RadioGroupChildOption[]) || [],
      },
    },
    {
      field: 'auditRemark',
      label: '审核备注',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      componentProps: {
        rows: 3,
      },
    },
  ];
};

export const modalFormBasic: FormSchema[] = [
  { field: '', component: 'Divider', label: '基础信息' },
  {
    field: 'nickname',
    label: '会员名',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'authCompanyName',
    label: '认证组织名',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'companyJoinTime',
    label: '入会(单位)时间',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'memberArea',
    label: '会员所属国家或地区',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'memberNation',
    label: '民族',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('mz') || [],
    },
  },
  {
    field: 'identityType',
    label: '证件类型',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('zjlx') || [],
    },
  },
  {
    field: 'identityNum',
    label: '证件号',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'identityExpired',
    label: '证件有效期（注意长期）',
    colProps: { span: 12 },
    component: 'ShowSpan',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'xb',
    label: '会员性别',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: [
        { label: '男', value: 'man' },
        { label: '女', value: 'female' },
      ],
    },
  },
  {
    field: 'lxdh',
    label: '联系电话',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },

  {
    field: 'hjlx',
    label: '户籍类型',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('hjlx') || [],
    },
  },
  {
    field: 'hj',
    label: '户籍',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'hjxxdz',
    label: '户籍详细地址',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'xjd',
    label: '现居地',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'zzmm',
    label: '政治面貌',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('zzmm') || [],
    },
  },
  {
    field: 'jyzt',
    label: '就业状态',
    colProps: { span: 12 },
    component: 'RadioGroup',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: (dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]) || [],
    },
  },
  {
    field: 'xl',
    label: '学历',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('xlcc') || [],
    },
  },
  {
    field: 'xw',
    label: '学位',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('xwdj') || [],
    },
  },
  {
    field: 'jndj',
    label: '技能等级',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('jndj') || [],
    },
  },
  {
    field: 'hyzk',
    label: '婚姻状况',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('hyzk') || [],
    },
  },
  {
    field: 'qtlxfs',
    label: '其他联系方式',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'gzzw',
    label: '工作职务',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'zcdj',
    label: '职称等级',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('zcdj') || [],
    },
  },
  {
    field: 'hjzt',
    label: '会籍状态',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('hjzt') || [],
    },
  },
  {
    field: 'hjbhyya',
    label: '会籍变化原由',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'hjbhyyb',
    label: '会籍变化原因',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
  {
    field: 'hjbhsj',
    label: '会籍变化时间',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },

  {
    field: 'sflm',
    label: '是否劳模',
    colProps: { span: 12 },
    component: 'RadioGroup',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: (dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]) || [],
    },
  },
  {
    field: 'sfcjzghzbz',
    label: '是否参加职工互助保障',
    colProps: { span: 12 },
    component: 'RadioGroup',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: (dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]) || [],
    },
  },
  {
    field: 'sfknzg',
    label: '是否困难职工',
    colProps: { span: 12 },
    component: 'RadioGroup',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: (dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]) || [],
    },
  },
  {
    field: 'sfyczfc',
    label: '是否有城镇房产',
    colProps: { span: 12 },
    component: 'RadioGroup',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: (dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]) || [],
    },
  },
  {
    field: 'sfxjyxtldz',
    label: '是否新就业形态劳动者',
    colProps: { span: 12 },
    component: 'RadioGroup',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: [
        { label: '全部', value: undefined },
        ...((dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]) || []),
      ],
    },
  },
  {
    field: 'yjzylx',
    label: '一级职业类型',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('zyfl') || [],
    },
  },
  {
    field: 'ejzylx',
    label: '二级职业类型',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    ifShow: ({ values }) => values.yjzylx === 'lhjy',
    componentProps: {
      options: dictionary.getDictionaryOpt.get('lhjyzw') || [],
    },
  },
  {
    field: 'ghgbzw',
    label: '工会干部职务',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    componentProps: {
      options: dictionary.getDictionaryOpt.get('ghgbzw') || [],
    },
  },
  {
    field: 'ghjczw',
    label: '工会基层职务',
    colProps: { span: 12 },
    component: 'Select',
    rulesMessageJoinLabel: true,
    ifShow: ({ values }) => includes(['shhghgz', 'jcghgbzz', 'jcghgbjz'], values.yjzylx),
    componentProps: {
      options: dictionary.getDictionaryOpt.get('ghjczw') || [],
    },
  },
  {
    field: 'bzxx',
    label: '备注信息（关于会员,非审核）',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },

  {
    field: 'createTime',
    label: '申请时间',
    colProps: { span: 12 },
    component: 'Input',
    rulesMessageJoinLabel: true,
  },
];
