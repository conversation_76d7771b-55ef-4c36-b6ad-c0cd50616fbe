<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
    :wrap-class-name="$style['union-user-modal']"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';
import { includes, isEmpty, join, split } from 'lodash-es';
import { YESNOEnum } from '@monorepo-yysz/enums';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.nickname || ''}--详情`
      : `编辑${unref(record)?.nickname || ''}`
    : '新增会员信息';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
      ifIdentityExpired: includes(data.record.identityExpired, '长期')
        ? YESNOEnum.YES
        : YESNOEnum.NO,
      identityExpired:
        includes(data.record.identityExpired, '长期') || isEmpty(data.record.identityExpired)
          ? undefined
          : data.record.identityExpired,
      memberArea: data.record.memberArea
        ? split(data.record.memberArea, ',')
        : ['410000', '410100', '410171'],
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const { identityExpired, memberArea, ifIdentityExpired, ...values } = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        identityExpired: ifIdentityExpired === YESNOEnum.YES ? '长期' : identityExpired,
        memberArea: join(memberArea || ['410000', '410100', '410171'], ','),
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

<style lang="less" module>
.union-user-modal {
  :global {
    .ant-modal-body {
      max-height: 70vh;
      overflow: auto;
    }
  }
}
</style>
