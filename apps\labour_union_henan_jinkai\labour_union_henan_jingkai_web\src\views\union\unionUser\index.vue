<template>
  <div :class="$style['union-user']">
    <a-row>
      <a-col :span="4">
        <UnionNextLevel @selectInfo="handleNext" />
      </a-col>
      <a-col :span="20">
        <PageWrapper :title="`${companyName}会员信息`">
          <BasicTable @register="registerTable">
            <template #toolbar>
              <a-button
                type="primary"
                :loading="loading"
                @click="handleAdd"
                >新增{{ companyName }}会员</a-button
              >

              <a-button
                type="primary"
                :loading="loading"
                @click="handleDown"
                >导出本工会会员</a-button
              >
            </template>
            <template #bodyCell="{ record, column }">
              <template v-if="column.dataIndex === 'action'">
                <TableAction
                  :actions="[
                    // {
                    //   icon: 'fa6-solid:pen-to-square',
                    //   label: '标签管理',
                    //   type: 'primary',
                    //   onClick: handleLabel.bind(null, record),
                    // },
                    {
                      icon: 'fa6-solid:pen-to-square',
                      label: '编辑',
                      type: 'primary',
                      ifShow: ifRoot,
                      onClick: handleEdit.bind(null, record),
                    },
                    {
                      icon: 'carbon:task-view',
                      label: '详情',
                      type: 'default',
                      onClick: handleView.bind(null, record),
                    },
                  ]"
                />
              </template>
            </template>
          </BasicTable>
        </PageWrapper>
      </a-col>
    </a-row>
    <UnionUserModal
      @register="registerModal"
      width="80%"
      :canFullscreen="false"
      @success="handleUserSuccess"
    />
    <LabelUpdateModal
      @register="registerLabelModal"
      width="50%"
      :canFullscreen="false"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { Row, Col } from 'ant-design-vue';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { columns, formSchemas } from './data';
import { unionUser, downUser } from '/@/api';
import { queryUserLabelList, batchUpdateLabel } from '@/api/system/userLabel';
import { useUserStore } from '@/store/modules/user';
import UnionNextLevel from '../UnionNextLevel/index.vue';
import { PageWrapper } from '@/components/Page';
import { computed, ref, unref, watch } from 'vue';
import { filter, map } from 'lodash-es';
import { useModal } from '@/components/Modal';
import UnionUserModal from './UnionUserModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { downloadByData } from '@monorepo-yysz/utils';
import LabelUpdateModal from './labelUpdateModal.vue';
import { saveApi, updateApi, view } from '@/api/union/user';
import { AccountTypeEnum } from '@monorepo-yysz/enums';

const ARow = Row;
const ACol = Col;

const userStore = useUserStore();

const loading = ref(false);

const companyId = ref(userStore.getUserInfo.companyId);

const companyName = ref(userStore.getUserInfo.companyName || '');

const ifRoot = computed(() => {
  return (
    userStore.getUserInfo.accountType === AccountTypeEnum.MANAGE ||
    userStore.getUserInfo.accountType === AccountTypeEnum.ADMIN
  );
});

const [registerModal, { openModal, closeModal: closeUserModal }] = useModal();
const [registerLabelModal, { openModal: openLabelUpdateModal, closeModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  api: unionUser,
  columns: columns(),
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    actionColOptions: {
      span: 4,
    },
  },
  beforeFetch: (params: Recordable) => {
    params.companyId = unref(companyId) === '0' ? undefined : unref(companyId);
    return params;
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  showIndexColumn: false,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    width: 160,
    fixed: 'right',
  },
});

const { createSuccessModal, createErrorModal } = useMessage();

function handleNext({ companyId: id, companyName: name }) {
  companyId.value = id;
  companyName.value = name || '';
}

// 新增
function handleAdd() {
  openModal(true, { isUpdate: false, disabled: false });
}

// 编辑
function handleEdit(record) {
  view({ memberId: record.memberId }).then(res => {
    if (res.code === 200) {
      openModal(true, { record: res.data, disabled: false, isUpdate: true });
    } else {
      createErrorModal({ content: `查看详情失败! ${res.message}` });
    }
  });
}

// 下载
function handleDown() {
  loading.value = true;
  downUser({ uid: unref(companyId) }).then(data => {
    downloadByData(data, `${unref(companyName)}会员信息.xlsx`);
    loading.value = false;
  });
}

//查看详情
async function handleView(row) {
  view({ memberId: row.memberId }).then(res => {
    if (res.code === 200) {
      openModal(true, { record: res.data, disabled: true, isUpdate: true });
    } else {
      createErrorModal({ content: `查看详情失败! ${res.message}` });
    }
  });
}

//打开标签管理弹窗
async function handleLabel(row) {
  row.phone = row.a0115;
  row.systemQueryType = 'manage';
  await queryUserLabelList(row).then(res => {
    if (res.data) {
      row.builtUserLabelList = map(
        filter(res.data as Recordable[], item => item.labelType === 'builtIn'),
        i => i.labelCode
      );
      row.customUserLabelList = map(
        filter(res.data as Recordable[], item => item.labelType === 'custom'),
        i => i.labelCode
      );
    }
  });
  openLabelUpdateModal(true, {
    record: row,
    isUpdate: true,
    disabled: false,
  });
}

//修改标签
function handleSuccess({ values }) {
  values.labelCodeList = [...values.builtUserLabelList, ...values.customUserLabelList];
  batchUpdateLabel(values).then(res => {
    const { code, message } = res;
    if (code === 200) {
      createSuccessModal({ content: `设置成功` });
      reload();
      closeModal();
    } else {
      createErrorModal({ content: `设置失败!${message}` });
    }
  });
}

function handleUserSuccess({ values, isUpdate }) {
  const api = isUpdate ? updateApi : saveApi;
  api({ ...values }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeUserModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}

watch(companyId, () => {
  reload();
});
</script>

<style lang="less" module>
.union-user {
  :global {
    background-color: #fff;

    .ant-form {
      @apply px-6px;
    }

    .ant-page-header {
      @apply !py-0 !pl-36px !pb-0;

      span {
        @apply !text-[#2172f1] font-bold;
      }
    }
  }
}
</style>
