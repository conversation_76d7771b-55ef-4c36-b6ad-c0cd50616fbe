import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useUserStore } from '/@/store/modules/user';
import { uploadApi } from '/@/api/sys/upload';
import { Image } from 'ant-design-vue';
import { useDictionary } from '/@/store/modules/dictionary';

export const columns = (): BasicColumn[] => {
  const userStore = useUserStore();
  const dictionary = useDictionary();
  return [
    {
      title: '排序号',
      dataIndex: 'sortNumber',
      width: 80,
    },
    {
      title: '标签名称',
      dataIndex: 'labelName',
    },
    {
      title: '标签图片',
      dataIndex: 'labelImg',
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            style={{ maxWidth: '180px', maxHeight: '42px' }}
          ></Image>
        );
      },
    },
    {
      title: '标签状态',
      dataIndex: 'labelState',
      customRender: ({ text }) => {
        return <span title={text === 'y' ? '启用' : '禁用'}>{text === 'y' ? '启用' : '禁用'}</span>;
      },
      width: 120,
    },
    {
      title: '标签类型',
      dataIndex: 'labelType',
      width: 110,
      customRender: ({ text }) => {
        const labelType = dictionary.getDictionaryMap.get(`labelType_${text}`)?.dictName;
        return <span title={labelType}>{labelType}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '人员数量',
      dataIndex: 'userCount',
      width: 120,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'labelName',
      label: '标签名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'labelState',
      label: '标签状态',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '启用', value: 'y' },
            { label: '禁用', value: 'n' },
          ],
        };
      },
    },
    {
      field: 'labelType',
      label: '标签类型',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('labelType'),
        };
      },
    },
  ];
};

export const modalFormItem = (isUpdate: boolean): FormSchema[] => {
  return [
    {
      field: 'labelType',
      label: '标签类型',
      colProps: { span: 12 },
      component: 'Input',
      ifShow:false,
    },
    {
      field: 'labelName',
      label: '标签名称',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'labelCode',
      label: '标签编码',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 20 },
      ifShow: !isUpdate,
    },
    {
      field: 'labelCode',
      label: '标签编码',
      component: 'ShowSpan',
      colProps: { span: 12 },
      ifShow: isUpdate,
    },
    {
      field: 'labelImg',
      label: '标签图片',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      component: 'Upload',
      required: true,
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 150,
        },
        maxNumber: 1,
        maxSize: 10,
        accept: ['image/*'],
      },
    },
    {
      field: 'ruleValue',
      label: '参加活动次数',
      colProps: { span: 12 },
      component: 'InputNumber',
      required: true,
      rulesMessageJoinLabel: true,
      ifShow({values}){
        console.log(values)
        return values.labelType == 'behavior'
      }
    },
    {
      field: 'sortNumber',
      label: '排序号',
      colProps: { span: 24 },
      component: 'InputNumber',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 8,
      },
    },
    {
      field: 'ruleDescription',
      label: '获得规则说明',
      colProps: { span: 24 },
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 600,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
  ];
};

//人员列表
export const userColumns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'nickName',
    },
    {
      title: '性别',
      dataIndex: 'genderCn',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
    },

    {
      title: '获得时间',
      dataIndex: 'obtainTime',
    },
  ];
};
//人员列表搜索条件
export const userFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'nickName',
      label: '姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      slot: 'nickName',
    },
    {
      field: 'phone',
      label: '手机号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      slot: 'phone',
    },
  ];
};

//数据库选择人员列表
export const dataUserColumns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'nickname',
    },
    {
      title: '性别',
      dataIndex: 'genderCn',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
    },
  ];
};

//数据库人员列表搜索条件
export const dataUserFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'nickname',
      label: '姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '手机号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const recordModalFormItem = (isUpdate: boolean): FormSchema[] => {
  return [
    {
      field: 'nickName',
      label: '姓名',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: { showCount: true, maxlength: 10 },
    },
    {
      field: 'genderCn',
      label: '性别',
      colProps: { span: 12 },
      component: 'Select',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '男', value: '男' },
            { label: '女', value: '女' },
          ],
        };
      },
    },
    {
      field: 'phone',
      label: '手机号',
      component: 'ShowSpan',
      colProps: { span: 12 },
      ifShow: !isUpdate,
    },
    {
      field: 'phone',
      label: '手机号',
      component: 'ShowSpan',
      colProps: { span: 12 },
      ifShow: isUpdate,
    },
    {
      field: 'obtainTime',
      label: '获得时间',
      component: 'ShowSpan',
      colProps: { span: 12 },
    },
  ];
};
