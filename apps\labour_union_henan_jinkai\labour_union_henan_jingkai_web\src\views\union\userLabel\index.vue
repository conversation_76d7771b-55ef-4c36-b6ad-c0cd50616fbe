<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/userLabel/add"
        >
          新增自定义标签
        </a-button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/userLabel/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/userLabel/update',
                //ifShow: record.labelType !== 'builtIn',
              },
              {
                icon: 'icon-park-outline:remind-disable',
                label: '禁用',
                type: 'primary',
                danger: true,
                onClick: EnableDisable.bind(null, record, '禁用', 'n'),
                auth: '/userLabel/disable',
                ifShow: record.labelState !== 'n' && record.labelType == 'custom',
              },
              {
                icon: 'icon-park-outline:remind',
                label: '启用',
                type: 'primary',
                onClick: EnableDisable.bind(null, record, '启用', 'y'),
                auth: '/userLabel/ToEnableThe',
                ifShow: record.labelState !== 'y' && record.labelType == 'custom',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                ifShow: record.labelType == 'custom',
                auth: '/userLabel/delete',
              },
              {
                icon: 'mdi:user',
                label: '人员',
                type: 'primary',
                onClick: openUserLabelList.bind(null, record),
                auth: '/userLabel/userList',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <UserLabelModel
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
    <UserList
      @register="userListRegisterModal"
      :can-fullscreen="false"
      width="88%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import UserLabelModel from './userLabelModel.vue';
import UserList from './userList.vue';
import { list, view, deleteLine, saveOrUpdate, setEnableDisable } from '@/api/system/userLabel';
import { message, Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  authInfo: ['/userLabel/add'],
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  searchInfo: { orderBy: 'sort_number', sortType: 'asc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 400,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/userLabel/view',
      '/userLabel/update',
      '/userLabel/disable',
      '/userLabel/ToEnableThe',
      '/userLabel/userList',
      '/userLabel/delete',
    ],
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
  },
});

const [registerModal, { openModal, closeModal }] = useModal();
const [userListRegisterModal, { openModal: userListOpenModal }] = useModal();

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record) {
  view({ ...record }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

//打开用户列表
function openUserLabelList(record) {
  userListOpenModal(true, { record: record });
}

function EnableDisable(record, title, value) {
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定${title},[${record.labelName}]吗?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          setEnableDisable({ autoId: record.autoId, labelState: value }).then(res => {
            const { code, message: mes } = res;
            console.log(res);
            if (code === 200) {
              message.success(`${title}成功`);
            } else {
              message.error(`${title}失败!${mes}`);
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}

//详情
function handleView(record) {
  view({ ...record }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除[${record.labelName}]标签?确认删除后将删除标签关联的人员信息,请谨慎操作!`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ values, isUpdate }) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
