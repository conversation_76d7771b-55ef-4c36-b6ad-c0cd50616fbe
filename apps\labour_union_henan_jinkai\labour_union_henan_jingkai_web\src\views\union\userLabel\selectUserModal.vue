<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '设置',
                type: 'default',
                onClick: handleCommentView.bind(null, record),
                ifShow: record.nickname !== null && record.phone !== null,
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '@/components/Modal';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { computed } from 'vue';
import { selectUser } from '@/api/system/userLabel';
import { dataUserColumns, dataUserFormSchemas } from './data';

const emit = defineEmits(['success', 'register']);

const title = computed(() => {
  return `选择人员`;
});

const [registerModal, {}] = useModalInner(async () => {
  await clearSelectedRowKeys();
});

const [registerTable, { clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  api: selectUser,
  columns: dataUserColumns(),
  maxHeight: 435,
  searchInfo: { accountType: 'unionAppUser', orderBy: 'update_time', sortType: 'desc' },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: dataUserFormSchemas(),
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',

    fixed: undefined,
    // auth: ['/difficultEmployees/choice']
  },
  immediate: true,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
});

//选择按钮操作
function handleCommentView(record) {
  console.log(record);

  emit('success', {
    ...record,
  });
}
</script>
