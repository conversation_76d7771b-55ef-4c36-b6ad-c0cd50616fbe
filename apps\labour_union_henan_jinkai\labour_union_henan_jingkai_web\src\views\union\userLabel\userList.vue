<template>
  <Spin :spinning="spinning">
    <BasicModal
      @register="basicRegisterModal"
      :title="title"
      v-bind="$attrs"
      :show-ok-btn="false"
      cancelText="关闭"
      :wrap-class-name="`${$style['video-modal']}`"
    >
      <div :class="$style.user">
        <BasicTable @register="registerTable">
          <template #form-nickName="{ model, field }">
            <Input
              v-model:value="model[field]"
              autocomplete="off"
              placeholder="请输入姓名"
              @change="changeNickName"
            />
          </template>
          <template #form-phone="{ model, field }">
            <Input
              v-model:value="model[field]"
              autocomplete="off"
              placeholder="请输入手机号"
              @change="changePhone"
            />
          </template>
          <template #toolbar>
            <a-button
              type="primary"
              @click="handleSelectUser"
              :disabled="'n' === labelState"
              v-show="'custom' === labelType"
              auth="/userList/add"
            >
              选择标签人员
            </a-button>
            <a-button
              type="primary"
              @click="handleDown"
              :disabled="'n' === labelState"
              v-show="'custom' === labelType"
              auth="/userList/download"
            >
              下载标签人员导入模板
            </a-button>
            <Upload
              name="file"
              accept=".xlsx,.xls"
              @change="handleImport"
              :before-upload="beforeUpload"
              :action="action"
              :headers="{ token: userStore.getToken }"
              :data="{ labelCode: labelCode }"
              v-if="ifImport"
            >
              <a-button
                type="primary"
                :disabled="'n' === labelState"
                v-show="'custom' === labelType"
                auth="/userList/import"
              >
                导入标签人员信息
              </a-button>
            </Upload>

            <a-button
              type="primary"
              @click="handleDownInfo"
              auth="/userList/export"
            >
              导出标签人员信息
            </a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    icon: 'carbon:task-view',
                    label: '详情',
                    type: 'default',
                    onClick: handleView.bind(null, record),
                    auth: '/userList/view',
                  },
                  {
                    icon: 'fa6-solid:pen-to-square',
                    label: '编辑',
                    type: 'primary',
                    onClick: handleEdit.bind(null, record),
                    auth: '/userList/update',
                    ifShow: 'custom' === labelType,
                  },
                  {
                    icon: 'fluent:delete-16-filled',
                    label: '删除',
                    type: 'primary',
                    danger: true,
                    onClick: handleDelete.bind(null, record),
                    ifShow: 'custom' === labelType,
                    auth: '/userList/delete',
                  },
                ]"
              />
            </template>
          </template>
        </BasicTable>
      </div>
    </BasicModal>
    <SelectUserModal
      @register="selectUserModal"
      :canFullscreen="false"
      width="60%"
      @success="handleSelectSuccess"
    >
    </SelectUserModal>
    <UserUpdateModel
      @register="registerModal"
      :canFullscreen="false"
      width="60%"
      @success="handleSuccess"
    >
    </UserUpdateModel>
  </Spin>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { userColumns, userFormSchemas } from './data';
import {
  userList,
  saveOrUpdateUserLabelRecord,
  removeUserLabelRecord,
  recordview,
  saveOrUpdateRecord,
  getUserLabelRecordTemplateURL,
  exportUserLabelRecord,
} from '/@/api/system/userLabel';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { computed, ref, unref } from 'vue';
import { useModal } from '/@/components/Modal';
import SelectUserModal from './selectUserModal.vue';
import UserUpdateModel from './userUpdateModel.vue';
import { useUserStore } from '/@/store/modules/user';
import { usePermission } from '/@/hooks/web/usePermission';
import { useGlobSetting } from '/@/hooks/setting';
import { UploadProps, Spin } from 'ant-design-vue';
import { Upload, Input } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useMessage } from '@monorepo-yysz/hooks';
import { downloadByUrl } from '@monorepo-yysz/utils';

//选择人员弹窗
const [selectUserModal, { openModal: openSelectUserModal, closeModal: closeSelectUserModal }] =
  useModal();

//人员信息弹窗
const [registerModal, { openModal, closeModal }] = useModal();

const { createErrorModal, createSuccessModal, createConfirm } = useMessage();

const { uploadUrl } = useGlobSetting();

const action = ref(`${uploadUrl}/dataCenter/userLabelPublicMethod/importUserLabelRecord`);

//loading加载
const spinning = ref<boolean>(false);

const { hasPermission } = usePermission();

const userStore = useUserStore();

const labelState = ref();
const labelType = ref();
const labelName = ref();
const labelCode = ref();

const search = ref();

const title = computed(() => {
  return `获得--${unref(labelName)}--标签人员列表`;
});

const [basicRegisterModal, {}] = useModalInner(async data => {
  labelState.value = data?.record?.labelState;
  labelType.value = data?.record?.labelType;
  labelName.value = data?.record?.labelName;
  labelCode.value = data?.record?.labelCode;
  reload();
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: userColumns(),
  authInfo: ['/userList/add', '/userList/import', '/userList/download', '/userList/export'],
  showIndexColumn: false,
  api: userList,
  beforeFetch: params => {
    params.labelCode = unref(labelCode);
    params.recordState = 'y';
    params.orderBy = 'obtain_time';
    params.sortType = 'desc';
    search.value = params;
    return params;
  },
  formConfig: {
    labelWidth: 120,
    schemas: userFormSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  isCanResizeParent: false,
  maxHeight: 530,
  canResize: true,
  pagination: true,
  useSearchForm: true,
  bordered: true,
  immediate: false,
  actionColumn: {
    title: '操作',
    width: 260,
    dataIndex: 'action',

    fixed: undefined,
    auth: ['/userList/view', '/userList/update', '/userList/delete'],
  },
});

function changeNickName(e) {
  search.value.nickName = e.target.value;
}
function changePhone(e) {
  search.value.phone = e.target.value;
}

const ifImport = computed(() => {
  return hasPermission('/modelWorkerInfo/import');
});

function handleDownInfo() {
  spinning.value = true;
  exportUserLabelRecord(unref(search)).then(res => {
    spinning.value = false;
    const url = window.URL.createObjectURL(res.data);
    const day = dayjs().format('YYYY-MM-DD HH:mm');
    downloadByUrl({
      url,
      fileName: unref(labelName) + '标签获取记录' + day + '.xlsx',
    });
  });
}

const beforeUpload: UploadProps['beforeUpload'] = file => {
  const { name } = file;
  const fileName = name?.split('.') || [''];
  const isExcel = ['xls', 'xlsx'].includes(fileName[fileName.length - 1]);
  if (!isExcel) {
    createErrorModal({ content: '只能上传Excel表格' });
  }
  spinning.value = true;
  return isExcel || Upload.LIST_IGNORE;
};

function handleImport({ file }) {
  if (file.status === 'done') {
    spinning.value = false;
    const { message, code } = file?.response;
    if (code === 200) {
      createSuccessModal({ content: `导入成功` });
    } else {
      createErrorModal({ content: `导入失败!${message}` });
    }
    reload();
  }
}

//详情
function handleView(record) {
  recordview({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

//编辑
function handleEdit(record) {
  recordview({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除[${record.nickName}]?`,
    onOk: function () {
      removeUserLabelRecord(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

//获取导入人员模板
function handleDown() {
  spinning.value = true;
  getUserLabelRecordTemplateURL().then(res => {
    spinning.value = false;

    const url = window.URL.createObjectURL(res.data);
    downloadByUrl({
      url,
      fileName: '标签人员导入模板' + '.xlsx',
    });

    // if (res) {
    //   const url = userStore.getPrefix + res;
    //   window.open(url);
    // }
  });
}

//选择人员
function handleSelectUser() {
  openSelectUserModal(true);
}

//选择人员框回调
function handleSelectSuccess({ nickname, genderCn, phone }) {
  saveOrUpdateUserLabelRecord({
    nickName: nickname,
    genderCn: genderCn,
    phone: phone,
    labelCode: unref(labelCode),
    recordState: 'y',
  }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: '设置成功',
      });
      reload();
      closeSelectUserModal();
    } else {
      createErrorModal({
        content: `设置失败! ${message}`,
      });
    }
  });
}

//更新人员标签记录
function handleSuccess({ values, isUpdate }) {
  saveOrUpdateRecord(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>

<style lang="less" module>
.video-modal {
  :global {
    .footer-group {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.user {
  :global {
    background-color: #fff;

    .ant-upload-list {
      display: none;
    }

    .ant-form {
      @apply px-6px;
    }
  }
}
</style>
