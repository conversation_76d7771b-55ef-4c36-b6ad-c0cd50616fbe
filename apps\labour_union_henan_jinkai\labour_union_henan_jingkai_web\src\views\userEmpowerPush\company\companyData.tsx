import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import dayjs from 'dayjs';
import { useMessage } from '@monorepo-yysz/hooks';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

export const modalColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  return [
    {
      title: '公司名称',
      dataIndex: 'companyName',
      customRender({ text }) {
        return <span title={text}>{text}</span>;
      },
    },
    {
      title: 'token类型',
      dataIndex: 'tokenType',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`tokenType_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
      width: 200,
    },
    {
      title: '有效时间',
      dataIndex: '',
      customRender: ({ record }) => {
        const { startEffectiveTime, endEffectiveTime } = record;
        let name = '';
        if (startEffectiveTime && endEffectiveTime) {
          name =
            dayjs(startEffectiveTime).format(`YYYY-MM-DD`) +
            '~' +
            dayjs(endEffectiveTime).format(`YYYY-MM-DD`);
        }
        return <span title={name}>{name}</span>;
      },
      width: 200,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
    },
  ];
};

export const modalFormItem = (isUpdate: boolean, disabled: boolean): FormSchema[] => {
  const dictionary = useDictionary();
  const { createSuccessModal } = useMessage();

  return [
    {
      field: 'companyName',
      label: '公司名称',
      colProps: { span: 24 },
      component: 'InputTextArea',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 70,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    {
      field: 'tokenType',
      label: 'token类型',
      required: true,
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('tokenType') as RadioGroupChildOption[],
        };
      },
    },
    {
      field: 'tokenFieldData',
      label: 'token字段',
      required: true,
      colProps: { span: 24 },
      component: 'CheckboxGroup',
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return '2' === values.tokenType;
      },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('tokenField') as RadioGroupChildOption[],
        };
      },
    },
    {
      field: 'effectiveTime',
      label: '有效时间',
      colProps: { span: 12 },
      component: 'RangePicker',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          valueFormat: `YYYY-MM-DD HH:mm:ss`,
          format: `YYYY-MM-DD`,
          getPopupContainer: () => document.body,
          showTime: {
            hideDisabledOptions: true,
            defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
          },
        };
      },
    },
    {
      field: 'whetherHistory',
      label: '同步修改历史记录',
      required: true,
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      ifShow: isUpdate && !disabled,
      // helpMessage: 'xunzhe',
      labelWidth: 140,
      componentProps: function () {
        return {
          onChange: e => {
            if ('y' === e.target.value) {
              createSuccessModal({
                content: `修改公司信息时,将会同步修改绑定该公司的历史授权记录,望您知晓!`,
              });
            } else if ('n' === e.target.value) {
              createSuccessModal({
                content: `只修改公司信息,不做加历史密记录修改,望您知晓!`,
              });
            }
          },
          options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
        };
      },
    },
  ];
};

export const columnSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  return [
    {
      field: 'companyName',
      label: '公司名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'tokenType',
      label: 'token类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('tokenType'),
        };
      },
    },
  ];
};
