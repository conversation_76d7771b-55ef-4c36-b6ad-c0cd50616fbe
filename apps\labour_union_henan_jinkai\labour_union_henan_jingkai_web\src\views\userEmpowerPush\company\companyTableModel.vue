<template>
  <BasicModal
    @register="registerTableModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
    :mask="false"
    cancelText="关闭"
    :wrap-class-name="`full-modal-self ${$style['chanel--news-modal']}`"
  >
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增公司
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <CompanyModel
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { modalColumns, columnSchemas } from './companyData';
import { useUserStore } from '@/store/modules/user';
import CompanyModel from './companyModel.vue';
import { list, view, deleteLine, saveOrUpdate } from '@/api/userEmpowerPush/company';
import { useMessage } from '@monorepo-yysz/hooks';
import { useModal } from '@/components/Modal';

defineEmits(['register']);

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const userStore = useUserStore();

const record = ref<Recordable>();

const title = computed(() => {
  return '公司管理';
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: modalColumns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: columnSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  beforeFetch(params) {
    // params.companyInfoId = userStore.getUserInfo.companyInfoId
    return params;
  },
  searchInfo: {
    orderBy: 'create_time',
    sortType: 'desc',
  },
  maxHeight: 530,
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 260,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

const [registerTableModal, { setModalProps, getVisible }] = useModalInner(async data => {
  record.value = data.record;
  setModalProps({ confirmLoading: false });
});

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record) {
  view({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

//详情
function handleView(record) {
  view({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.companyName}`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ values, isUpdate }) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>
<style module lang="less">
.chanel--news-modal {
  :global {
    .ant-collapse-header {
      background-color: @primary-color;
      color: #ffffff !important;
    }

    .footer-group {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
