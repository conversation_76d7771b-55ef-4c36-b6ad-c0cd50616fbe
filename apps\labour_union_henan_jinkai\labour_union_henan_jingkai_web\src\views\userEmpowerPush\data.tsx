import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useUserStore } from '/@/store/modules/user'
import { useDictionary } from '/@/store/modules/dictionary'
import dayjs from 'dayjs'
import { list as companyList, view as companyVive } from '/@/api/userEmpowerPush/company'
import { getNewsSpecialList } from '/@/api/newsSpecial'
import { list as activitiesList } from '/@/api/activities'
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary()

  const userStore = useUserStore()

  return [
    {
      title: '所属公司',
      dataIndex: 'companyName',
      customRender({ text }) {
        return <span title={text}>{text}</span>
      },
    },
    {
      title: '数据名称',
      dataIndex: 'dataName',
      customRender({ text }) {
        return <span title={text}>{text}</span>
      },
    },
    {
      title: '数据源类型',
      dataIndex: 'sourceType',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`sourceType_${text}`)?.dictName || ''
        return <span title={name}>{name}</span>
      },
      width: 140,
    },
    {
      title: 'token类型',
      dataIndex: 'tokenType',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`tokenType_${text}`)?.dictName || ''
        return <span title={name}>{name}</span>
      },
      width: 140,
    },
    {
      title: '有效时间',
      dataIndex: '',
      customRender: ({ record }) => {
        const { startEffectiveTime, endEffectiveTime } = record
        let name = ''
        if (startEffectiveTime && endEffectiveTime) {
          name =
            dayjs(startEffectiveTime).format(`YYYY-MM-DD`) +
            '~' +
            dayjs(endEffectiveTime).format(`YYYY-MM-DD`)
        }
        return <span title={name}>{name}</span>
      },
      width: 200,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary()

  const userStore = useUserStore()

  return [
    {
      field: 'companyName',
      label: '所属公司',
      colProps: { span: 6 },
      component: 'ApiSelect',
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          api: companyList,
          resultField: 'data',
          params: {
            pageSzie: 0,
          },
          getPopupContainer: () => document.body,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          fieldNames: { label: 'companyName', value: 'companyId' },
        }
      },
    },
    {
      field: 'sourceType',
      label: '数据源类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('sourceType'),
        }
      },
    },
    {
      field: 'dataName',
      label: '数据名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'tokenType',
      label: 'token类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('tokenType'),
        }
      },
    },
  ]
}

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary()

  const userStore = useUserStore()

  return [
    {
      field: 'sourceType',
      label: '数据源类型',
      required: true,
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('sourceType')as RadioGroupChildOption[],
        }
      },
    },
    {
      field: 'recordId',
      label: '数据名称',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return 'newsSpecial' === values.sourceType
      },
      componentProps: () => {
        return {
          api: getNewsSpecialList,
          resultField: 'data',
          params: {
            pageType: 'customSinglePage',
            skipType: 'external',
            orderBy: 'sort',
            sortType: 'desc',
            pageSize: 0,
            specialType: 'custom',
          },
          getPopupContainer: () => document.body,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.specialName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          fieldNames: { label: 'specialName', value: 'specialId' },
        }
      },
    },
    {
      field: 'dataName',
      label: '数据名称',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return 'activity' === values.sourceType
      },
      // componentProps: () => {
      //   return {
      //     api: activitiesList,
      //     resultField: 'data',
      //     params: {
      //       externalLink: 'y',
      //       pageSize: 0,
      //     },
      //     getPopupContainer: () => document.body,
      //     showSearch: true,
      //     filterOption: (input: string, option: any) => {
      //       return option.activityName.toLowerCase().indexOf(input.toLowerCase()) >= 0
      //     },
      //     fieldNames: { label: 'activityName', value: 'activityId' },
      //   }
      // },
      slot: 'activitieRecordName',
    },
    {
      field: 'companyId',
      label: '所属公司',
      component: 'ApiSelect',
      // required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          api: companyList,
          resultField: 'data',
          params: {
            pageSzie: 0,
          },
          getPopupContainer: () => document.body,
          onChange: async (e, node) => {
            if (e) {
              const { data } = await companyVive({ companyId: e })
              formModel['tokenType'] = data?.tokenType
              ;(formModel['tokenFieldData'] = data?.tokenField ? data?.tokenField.split(',') : []),
                (formModel['effectiveTime'] = [data?.startEffectiveTime, data?.endEffectiveTime])
            }
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0
          },
          fieldNames: { label: 'companyName', value: 'companyId' },
        }
      },
    },
    {
      field: 'tokenType',
      label: 'token类型',
      required: true,
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('tokenType')as RadioGroupChildOption[],
        }
      },
    },
    {
      field: 'tokenFieldData',
      label: 'token字段',
      required: true,
      colProps: { span: 24 },
      component: 'CheckboxGroup',
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return '2' === values.tokenType
      },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('tokenField')as RadioGroupChildOption[],
        }
      },
    },
    {
      field: 'effectiveTime',
      label: '有效时间',
      colProps: { span: 12 },
      component: 'RangePicker',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          valueFormat: `YYYY-MM-DD HH:mm:ss`,
          format: `YYYY-MM-DD`,
          getPopupContainer: () => document.body,
          showTime: {
            hideDisabledOptions: true,
            defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
          },
        }
      },
    },
    {
      field: 'registrationCode',
      label: '授权码',
      component: 'Input',
      rulesMessageJoinLabel: true,
      dynamicDisabled: true,
      required: false,
    },
  ]
}
