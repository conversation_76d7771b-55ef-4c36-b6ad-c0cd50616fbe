<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
      <template #activitieRecordName="{ model, field }">
        <ResourcesSelect
          :value="model[field]"
          externalLink="y"
          name="选择数据"
          :disabled="!disabled"
          @change="resource => handleResources(resource, model)"
        />
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';
import { generateRegistrationCode } from '/@/api/userEmpowerPush/userinfo';
import ResourcesSelect from '/@/views/news/channelNews/ResourcesSelect.vue';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.dataName || ''}--详情`
      : `编辑${unref(record)?.dataName || ''}`
    : '新增授权信息';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem();
});

// 内部资源
function handleResources({ activityName, activityId }, model) {
  model['dataName'] = activityName;
  model['recordId'] = activityId;
}

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    const { tokenField, startEffectiveTime, endEffectiveTime, registrationCode } = data.record;
    let code = registrationCode;
    if (!registrationCode) {
      code = await generateRegistrationCode();
    }
    setFieldsValue({
      ...data.record,
      effectiveTime: [startEffectiveTime, endEffectiveTime],
      tokenFieldData: tokenField ? tokenField.split(',') : [],
      registrationCode: code,
    });
  } else {
    const registrationCode = await generateRegistrationCode();
    setFieldsValue({
      registrationCode,
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    const { tokenFieldData, effectiveTime } = values;
    emit('success', {
      values: {
        ...unref(record),
        ...values,
        startEffectiveTime: effectiveTime[0],
        endEffectiveTime: effectiveTime[1],
        tokenField: tokenFieldData?.length > 0 ? tokenFieldData.join(',') : undefined,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
