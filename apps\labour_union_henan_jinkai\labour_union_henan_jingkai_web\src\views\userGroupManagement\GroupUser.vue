<template>
  <PageWrapper :title="title" @back="router.go(-1)" :class="$style['user-info']">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleClick"> 增加人员 </a-button>
        <a-button class="ml-1" type="primary" @click="handleDownFile"> 下载模板 </a-button>
        <Upload
            :action="action"
            :before-upload="beforeUpload"
            :data="{ groupId: record?.groupId }"
            :headers="{ token: userStore.getToken }"
            accept=".csv,.xlsx,.xls"
            method="post"
            name="multipartFile"
            @change="handleImport"
        >
          <Tooltip placement="bottom" title="请先确保已下载导入模板，并按照模板进行数据导入">
            <a-button :loading="spinning" type="primary"> 导入人员 </a-button>
          </Tooltip>
        </Upload>

      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'fluent:delete-16-filled',
              label: '删除',
              type: 'primary',
              danger: true,
              onClick: handleDelete.bind(null, record),
            },
          ]"
        />
      </template>
    </BasicTable>
    <UserModal
      :can-fullscreen="false"
      width="50%"
      @register="registerUserModal"
      @success="handleUserSuccess"
    />
  </PageWrapper>
</template>

<script lang="ts" setup>
import {BasicTable, TableAction, useTable} from '/@/components/Table'
import {useModal} from '/@/components/Modal'
import {columns, formSchemas} from './data'
import UserModal from './UserModal.vue'

import {deleteLine, list, saveOrUpdate} from '/@/api/userGroup/user'
import {PageWrapper} from '/@/components/Page'
import {computed, onMounted, ref, unref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {useMessage} from "@monorepo-yysz/hooks";
import {Tooltip, Upload, UploadProps} from "ant-design-vue";
import {download} from "@/api/sys/upload";
import {downloadByUrl} from "@monorepo-yysz/utils";
import {useUserStore} from "@/store/modules/user";
import {useGlobSetting} from "@/hooks/setting";


const { createConfirm, createErrorModal, createSuccessModal } = useMessage()
const { apiUrl } = useGlobSetting()
const action = ref(`${apiUrl}/h5/activityInclusiveGroupUser/importUser`)

const route = useRoute()
const userStore = useUserStore()
const router = useRouter()

const record = ref<Recordable>()

const spinning = ref<boolean>(false)

const title = computed(() => `${unref(record)?.groupName || ''}人员信息`)

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
  },
  beforeFetch(params){
    params.sortType = 'desc'
    params.orderBy = 'create_time'
    params.groupId = unref(record)?.groupId
    return params;
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 330,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
  },
})

const [registerUserModal, { openModal: openUserModal, closeModal: closeUserModal }] = useModal()

function handleImport({ file }) {
  if (file.status === 'done') {
    const { message, code } = file?.response
    if (code !== 200) {
      createErrorModal({ content: `导入失败!${message}` })
      return
    } else {
      createSuccessModal({ content: `导入成功` })
      record()
    }
  }
  spinning.value = false
}

const beforeUpload: UploadProps['beforeUpload'] = file => {
  const { name } = file
  const fileName = name?.split('.') || ['']
  const isExcel = ['xls', 'xlsx', 'csv'].includes(fileName[fileName.length - 1])
  if (!isExcel) {
    createErrorModal({ content: '只能上传Excel表格' })
  }
  spinning.value = true

  return isExcel || Upload.LIST_IGNORE
}

async function handleDownFile() {

  download({
    filenames: [`common_0_groupUserTemp.xlsx`],
  }).then(res => {
    const url = window.URL.createObjectURL(res)
    downloadByUrl({
      url: url,
      fileName: `${unref(record)?.groupName || ''}-人员导入模板.xlsx`,
    })
  })
}


//新增
function handleClick() {
  openUserModal(true, { isUpdate: false, disabled: false })
}

//编辑
async function handleEdit(record) {
  openUserModal(true, { isUpdate: true, disabled: false, record })
}

//详情
async function handleView(record) {
  openUserModal(true, { isUpdate: true, disabled: true, record })
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.userName}`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` })
          reload()
        } else {
          createErrorModal({ content: `删除失败，${message}` })
        }
      })
    },
  })
}

function handleUserSuccess({ values, isUpdate }) {
  saveOrUpdate({ ...values, groupId: unref(record)?.groupId }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      })
      closeUserModal()
      reload()
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      })
    }
  })
}

onMounted(() => {
  record.value = JSON.parse(route.query.record as string) as Recordable
})
</script>
<style lang="less" module>
.user-info {
  :global {
    .ant-upload-list {
      display: none;
    }
  }
}
</style>
