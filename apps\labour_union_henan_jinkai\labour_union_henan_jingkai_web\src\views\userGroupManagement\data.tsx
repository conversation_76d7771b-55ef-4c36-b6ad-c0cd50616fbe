import {FormSchema} from '/@/components/Form'
import {BasicColumn} from '/@/components/Table'
import {useDictionary} from '/@/store/modules/dictionary'
import {validatePhone} from "@monorepo-yysz/utils";


export const columns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'userName',
    },
    {
      title: '联系方式',
      dataIndex: 'phone',
    },
    {
      title: '单位名称',
      dataIndex: 'companyName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系方式',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}

export const modalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'groupName',
      label: '群体名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'groupDesc',
      label: '群体描述',
      colProps: { span: 24 },
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
    },
  ]
}

export const modalUserFormItem = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '姓名',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 20,
        showCount: true,
      },
    },
    {
      field: 'phone',
      label: '联系方式',
      colProps: { span: 24 },
      component: 'Input',
      rules: [{ required: true, validator: validatePhone, trigger: ['change', 'blur'] }],
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 11,
        showCount: true,
      },
    },

    {
      field: 'companyName',
      label: '单位名称',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      componentProps: {
        maxlength: 50,
      },
    },
  ]
}

const SchemasFieldKey = {
  cadre: 'un',
  union: 'key',
}

export function modalColumns(activeKey: string): BasicColumn[] {
  const dictionary = useDictionary()
  return [
    {
      dataIndex: 'userName',
      title: '姓名',
    },
    {
      dataIndex: 'phone',
      title: '联系方式',
    },
    {
      title: '所属部门',
      dataIndex: 'deptName',
      width: 80,
      ifShow: 'cadre' === activeKey,
    },
    {
      title: '所属职务',
      dataIndex: 'postName',
      ifShow: 'cadre' === activeKey,
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
      ifShow: 'union' === activeKey,
    },
  ]
}

export function columnSchemas(activeKey: string): FormSchema[] {
  return [
    {
      field: SchemasFieldKey[activeKey] || 'nickName',
      component: 'Input',
      label: '姓名',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 6,
      },
    },
    {
      field: activeKey === 'union' ? 'p' : 'phone',
      component: 'Input',
      label: '联系方式',
      rulesMessageJoinLabel: true,
      ifShow: activeKey !== 'cadre',
      colProps: {
        span: 6,
      },
    },
  ]
}

export const groupColumns = (): BasicColumn[] => {
  return[
    { dataIndex: 'groupName', title: '群体名称' },
    { dataIndex: 'companyName', title: '组织单位' },
    { dataIndex: 'createTime', title: '创建时间', width: 150 },
    { dataIndex: 'userCount', title: '人员数量', width: 150 },
  ]
}
