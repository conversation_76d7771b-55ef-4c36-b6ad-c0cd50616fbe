<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
<!-- auth="/groupManage/add"       -->
        <a-button  type="primary" @click="handleClick"> 新增群体 </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record),
              //auth: '/groupManage/view',
            },
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              onClick: handleEdit.bind(null, record),
             // auth: '/groupManage/edit',
            },
            {
              icon: 'mdi:user-box',
              label: '人员',
              type: 'primary',
              onClick: handleUser.bind(null, record),
             // auth: '/groupManage/user',
            },
            {
              icon: 'fluent:delete-16-filled',
              label: '删除',
              type: 'primary',
              danger: true,
              onClick: handleDelete.bind(null, record),
            //  auth: '/groupManage/del',
            },
          ]"
        />
      </template>
    </BasicTable>
    <GroupModal
      :can-fullscreen="false"
      width="50%"
      @register="registerModal"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import {BasicTable, TableAction, useTable} from '/@/components/Table'
import {useModal} from '/@/components/Modal'
import GroupModal from './GroupModal.vue'

import { useMessage } from '@monorepo-yysz/hooks';
import {deleteLine, list, saveOrUpdate} from '/@/api/userGroup'
import {useRouter} from 'vue-router'
import {useUserStore} from '/@/store/modules/user'
import {groupColumns} from "/@/views/userGroupManagement/data";

const { createConfirm, createErrorModal, createSuccessModal } = useMessage()
const userStore = useUserStore()
const router = useRouter()

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: groupColumns(),
  beforeFetch: params => {
    params.sortType = 'desc'
    params.orderBy = 'auto_id'
    return params
  },
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: [
      {
        field: 'groupName',
        label: '群体名称',
        component: 'Input',
        rulesMessageJoinLabel: true,
        colProps: { span: 6 },
      },
    ],
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 390,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
  },
})

const [registerModal, { openModal, closeModal }] = useModal()

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false })
}

//编辑
function handleEdit(record) {
  openModal(true, { isUpdate: true, disabled: false, record })
}

//详情
function handleView(record) {
  openModal(true, { isUpdate: true, disabled: true, record })
}


//人员管理
function handleUser({ groupId, groupName }) {
  router.push({
    path: '/group/user',
    query: { record: JSON.stringify({ groupId, groupName }) },
  })
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.groupName}，并解除群体下已关联人员信息`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` })
          reload()
        } else {
          createErrorModal({ content: `删除失败，${message}` })
        }
      })
    },
  })
}

function handleSuccess({ values, isUpdate }) {
  saveOrUpdate(values).then(({ code, message,data }) => {
    if (code === 200) {
      if(isUpdate){
        createSuccessModal({
          content: '编辑成功',
        })
        reload()
        closeModal()
      }else{
        createSuccessModal({
          content: '新增成功',
        })
        reload()
        closeModal()
      }
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      })
    }
  })
}
</script>
