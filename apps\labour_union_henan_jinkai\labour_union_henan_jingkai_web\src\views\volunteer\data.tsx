import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary'
import { analysisIdCardNumber } from '@/api/workStar/modelWorkerInfo';
import { validatePhone, validateIdNum } from '@monorepo-yysz/utils';
import { searchNextUnionForm } from '@/utils/searchNextUnion';
export function volunteerColumns(): BasicColumn[] {
  const dictionary = useDictionary()
  return [
    {
      dataIndex: 'companyName',
      title: '工会名称',
    },
    {
      dataIndex: 'userName',
      title: '姓名',
    },
    {
      dataIndex: 'userMobile',
      title: '电话',
    },
    {
      dataIndex: 'userBirth',
      title: '出生日期',
    },
    {
      dataIndex: 'userSex',
      title: '性别',
      customRender({ text }) {
        return dictionary.getDictionaryMap.get(`gender_${text}`)?.dictName || ''
      },
    },
    {
      dataIndex: 'enableType',
      title: '是否启用',
      customRender({ text }) {
        return dictionary.getDictionaryMap.get(`YesOrNo_${text}`)?.dictName || ''
      },
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
    },
  ];
}
export function typeFormItem(isUpdate: boolean): FormSchema[] {
  return [
    {
      field: 'sort',
      label: '排序',
      colProps: { span: 24 },
      component: 'InputNumber',
      className: '!w-full',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'typeName',
      label: '类型名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'modelType',
      label: '类型',
      required: true,
      colProps: { span: 24 },
      component: 'RadioGroup',
      defaultValue: 0,
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '劳模', value: 0 },
            { label: '工匠', value: 1 },
          ],
        };
      },
    },
  ];
}
//新增编辑详情
export const modalForm = (
  disabled: boolean,
): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'userName',
      label: '志愿者姓名',
      component: 'Input',
      colProps: { span: 12 },
      required: true,
      // dynamicDisabled: isCertification,
      className: '!w-full',
      rulesMessageJoinLabel: true,
      // componentProps: {
      //   showCount: true,
      //   maxlength: 20,
      // },
      slot: 'nameButton',
      ifShow: !disabled,
    },
    {
      field: 'userName',
      component: 'Input',
      label: '姓名',
      colProps: {
        span: 12,
      },
      required: true,
      dynamicDisabled: true,
      ifShow: disabled,
    },
    {
      field: 'userId',
      label: '姓名Id',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      slot: 'userId',
      show: false,
    },
    {
      field: 'companyName',
      label: '所属工会',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
      slot: 'button',
      ifShow: !disabled,
    },
    {
      field: 'companyId',
      label: '工会Id',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      // slot: 'companyId',
      show: false,
    },
    {
      field: 'companyName',
      label: '所属工会',
      component: 'ShowSpan',
      colProps: { span: 12 },
      ifShow: disabled,
    },
    {
      field: 'userIdCard',
      label: '身份证号码',
      component: 'Input',
      colProps: { span: 12 },
      rules: [{ required: true, validator: validateIdNum, trigger: ['change', 'blur'] }],
      rulesMessageJoinLabel: true,
      componentProps: function ({ formModel }) {
        return {
          showCount: true,
          maxlength: 18,
          onChange: e => {
            if (e.target?.value) {
              analysisIdCardNumber({ identityCardNumber: e.target.value }).then(res => {
                const { gender, dateOfBirth } = (res?.data || {}) as Recordable;
                formModel['gender'] = gender;
                formModel['dateOfBirth'] = dateOfBirth;
              });
            }
          },
        };
      },
    },
    {
      field: 'userSex',
      label: '性别',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          options: dictionary.getDictionaryOpt.get('gender'),
        };
      },
    },
    {
      field: 'userBirth',
      label: '出生年月',
      component: 'DatePicker',
      required: true,
      componentProps: {
        valueFormat: `YYYY-MM-DD`,
        format: `YYYY-MM-DD`,
        placeholder: '请选择出生年月',
      },
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      className: '!w-full',
    },
    {
      field: 'userMobile',
      label: '联系电话',
      component: 'Input',
      colProps: { span: 12 },
      rules: [{ required: true, validator: validatePhone, trigger: ['change', 'blur'] }],
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 11,
      },
    },
    {
      field: 'address',
      label: '详细地址',
      component: 'Input',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps:{
        showCount: true,
        maxlength:120
      }
    },
  ];
};

// //选择所属工会弹框筛选条件
// export const UnionFormSchemas = (): FormSchema[] => {
//   return [
//     {
//       field: 'un',
//       label: '工会名称',
//       colProps: { span: 12 },
//       component: 'Input',
//       rulesMessageJoinLabel: true,
//     },
//   ];
// };

// //筛选工会列表
// export const Unioncolumns = (): BasicColumn[] => {
//   return [
//     {
//       title: '工会名称',
//       dataIndex: 'c0100',
//     },
//   ];
// };
//劳模列表
export const modelColumns = (): BasicColumn[] => {
  return [
    {
      title: '名称',
      dataIndex: 'a0100',
    },
    {
      title: '联系电话',
      dataIndex: 'a0115',
    },
    {
      title: '工会名称',
      dataIndex: 'c0100',
    },
  ];
};

//选择劳模弹框筛选条件
export const modelSchemas = (): FormSchema[] => {
  return [
    {
      field: 'p',
      label: '联系电话',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    // {
    //   field: 'idno',
    //   label: '证件号码',
    //   colProps: { span: 8 },
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    // },
  ];
};
export function searchSchemas(): FormSchema[] {
  return [
    {
      field: 'userName',
      label: '志愿者姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'userMobile',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    ...searchNextUnionForm(),
  ];
}
