<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth = "/volunteer/add"
        >
          新增志愿者
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/volunteer/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/volunteer/modify',
              },
              {
                icon:
                  record.enableType === 'n'
                    ? 'material-symbols:lock-open'
                    : 'material-symbols:lock',
                label: record.enableType === 'n' ?  '启用' : '禁用' ,
                type: 'primary',
                onClick: handleConfirm.bind(null, record),
                auth: '/volunteer/disableBth',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/volunteer/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <VolunteerModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="40%"
    >
    </VolunteerModal>
  </div>
</template>

<script lang="ts" setup>
import VolunteerModal from './volunteerModal.vue';
import { useModal } from '@/components/Modal';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { volunteerColumns, searchSchemas } from './data';
import { useMessage } from '@monorepo-yysz/hooks';
import { volunteerInfoFindList, saveOrUpdateByDTO, deleteVolunteerInfo ,enableDisable,getVoByDto} from '@/api/volunteer/index';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerModal, { openModal, closeModal }] = useModal();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: volunteerColumns(),
  showIndexColumn: false,
  authInfo: '/volunteer/add',
  api: volunteerInfoFindList,
  beforeFetch: params => {
    return params;
  },
  formConfig: {
    labelWidth: 120,
    schemas: searchSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  bordered: true,
  useSearchForm: true,
  actionColumn: {
    title: '操作',
    width: 350,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/volunteer/view', '/volunteer/modify','/volunteer/disableBth','/volunteer/delete'],
  },
});

//新增
function handleClick() {
  openModal(true, { isUpdate: false,disabled: false,  });
}

//编辑
function handleEdit(record) {
  getVoByDto({ autoId: record.autoId }).then(res => {
    const { code, data, message: msg } = res;
    if (code === 200) {
      openModal(true, { isUpdate: true,  record: data ,disabled: false,});
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}

//详情
function handleView(record) {
  getVoByDto({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

//删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.userName}`,
    onOk: function () {
      deleteVolunteerInfo(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ isUpdate, values }) {
  if (!isUpdate) {
    //   const { groupCode, groupName } = unref(type);
    //   values.groupCode = groupCode;
    //   values.groupName = groupName;
  }
  saveOrUpdateByDTO(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}

function handleConfirm(record) {
  const name = record.enableType === 'n' ?  '禁用' : '启用' 
  createConfirm({
    iconType: 'warning',
    content: `请确定${name}${record.userName}?`,
    onOk: function () {
      enableDisable({
        autoId: record.autoId,
        enableType: record.enableType === 'y' ? 'n' : 'y',
      }).then(({ code, message }) => {
        if (code === 200) {
          reload();
        }
      });
    },
  });
}
</script>
