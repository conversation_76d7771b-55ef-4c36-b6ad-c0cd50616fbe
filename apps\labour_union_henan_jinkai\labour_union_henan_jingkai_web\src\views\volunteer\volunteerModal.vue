<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" :class="disabledClass">
      <template #nameButton="{ model, field }">
        <a-input
          type="primary"
          @click="choiceModel(model, field)"
          :disabled="disabled || isUpdate"
          v-model:value="model[field]"
          placeholder="请选择志愿者姓名"
          autocomplete="off"
          readonly
        ></a-input>
      </template>
      <template #button="{ model, field }">
        <a-input
          type="primary"
          @click="choiceUnion(model, field)"
          :disabled="disabled"
          v-model:value="model[field]"
          placeholder="请选择所属工会"
          autocomplete="off"
          readonly
        ></a-input>
      </template>
    </BasicForm>
  </BasicModal>
  <UnionListModal
    @register="registerCommentModal"
    :canFullscreen="false"
    width="60%"
    @success="handleSuccess"
  >
  </UnionListModal>
  <modelListModal
    @register="registermodelListModal"
    :canFullscreen="false"
    width="60%"
    @success="handleModel"
  >
  </modelListModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed,watch } from 'vue';
import { useModalInner,useModal, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalForm } from './data';
import UnionListModal from '../workStar/info/UnionListModal.vue';
import modelListModal from '../workStar/info/modelList.vue';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const isUpdate = ref(false);

const disabled = ref(false);

const companyName = ref('');
//工会id
const companyId = ref('');

//工会组织分级
const temporaryIssueGrading = ref('');

const model = ref<Recordable>();

const field = ref('');
const formItem = computed(() => {
  return modalForm(unref(disabled));
});
const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.userName || ''}-详情`
      : `编辑${unref(record)?.userName || ''}`
    : '新增志愿者'
});
//所有工会
const [registerCommentModal, { openModal: openUnionListModal, closeModal }] = useModal();
//所有劳模信息
const [registermodelListModal, { openModal ,closeModal :closeModelModal }] = useModal();
//选择劳模人员列表
function choiceModel(m,f) {
  openModal(true);
  console.log(m,f);
  model.value = m;
  field.value = f;
}
const modelRecord=ref<Recordable>()
function handleModel({record}) {
  modelRecord.value=record
  closeModelModal()
}

// //所有工会
// const [registerCommentModal, { openModal: openUnionListModal, closeModal }] = useModal();

const [registerForm, { resetFields, validate, setFieldsValue, setProps}] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  disabled.value = !!data?.disabled;
  isUpdate.value = !!data.isUpdate;
  const { companyName: name, companyId: id, companyClassicIds: ids } = data.record || {};

  companyName.value = name;
  companyId.value = id;
  temporaryIssueGrading.value = ids;

  if (unref(isUpdate)) {
    setFieldsValue({ ...data.record });
  }
  setModalProps({ confirmLoading: false ,showOkBtn: !unref(disabled)});

  setProps({ disabled: unref(disabled) });
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})

async function handleSubmit() {
  try {
    const { ...values } = await validate();
    setModalProps({ confirmLoading: true });

    emit('success', {
      values: {    
        ...unref(record),
        ...values,
        ...model.value,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
//所属工会
function choiceUnion(m, f) {
  openUnionListModal(true);
  model.value = m;
  field.value = f;
}

function handleSuccess({ unionName, unionId, issueGrading }) {
  companyName.value = unionName;
  companyId.value = unionId;
  temporaryIssueGrading.value = issueGrading;
  closeModal();
}

watch(companyName, () => {
  if (model.value) {
    model.value[unref(field)] = unref(companyName);
    model.value['companyId'] = unref(companyId);
  }
});

watch(modelRecord, () => {
  if (modelRecord.value) {
    model.value['companyName'] =companyName.value?companyName.value:unref(modelRecord).companyName;
    model.value[unref(field)] = modelRecord.value.userName;
    model.value['companyId'] =companyId.value?companyId.value:unref(modelRecord).companyId;
    model.value['userIdCard'] =unref(modelRecord).identityCardNumber;
    model.value['userMobile'] =unref(modelRecord).phone;
    model.value['userBirth'] =unref(modelRecord).dateOfBirth;
    model.value['userSex'] =unref(modelRecord).gender;
    model.value['userId'] =unref(modelRecord).userId;
  }
});
</script>
