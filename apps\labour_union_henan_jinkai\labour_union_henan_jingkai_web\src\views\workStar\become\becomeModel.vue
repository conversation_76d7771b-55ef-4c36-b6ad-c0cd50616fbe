<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :wrap-class-name="$style['recruit-modal']"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.title || ''}--详情`
      : `编辑${unref(record)?.title || ''}`
    : '新增成为劳模流程';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    const { flowChart } = data.record;
    setFieldsValue({ ...data.record, flowChart: flowChart ? flowChart.split(',') : [] });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    const { flowChart } = values;

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        flowChart: flowChart && flowChart.length > 0 ? flowChart.join(',') : null,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
<style lang="less" module>
.recruit-modal {
  :global {
    .ant-input-number {
      width: 100% !important;
    }
  }
}
</style>
