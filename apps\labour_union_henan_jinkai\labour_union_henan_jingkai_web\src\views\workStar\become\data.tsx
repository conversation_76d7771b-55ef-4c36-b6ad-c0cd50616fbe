import { FormSchema } from '/@/components/Form'
import { BasicColumn } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { uploadApi } from '/@/api/sys/upload'
import { modelTypeFindList } from '@/api/work/index';
import { nextTick } from 'vue';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary()
  return [
    {
      title: '排序号',
      dataIndex: 'sortNumber',
      width: 150,
    },
    {
      title: '标题',
      dataIndex: 'title',
    },
    {
      title: '所属类别',
      dataIndex: 'typeName',
    },
    {
      title: '是否启用',
      dataIndex: 'forbiddenState',
      customRender({ text }) {
        return dictionary.getDictionaryMap.get(`YesOrNo_${text}`)?.dictName || ''
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ]
}

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary()

  return [
    {
      field: 'title',
      label: '标题',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'category',
      label: '所属类别',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          placeholder: '请选择所属类别',
          api: modelTypeFindList,
          resultField: 'data',
          params: {
            pageSize: 10,
            pageNum: 1,
            modelType: 0,
          },
          alwaysLoad: true,
          immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.typeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'typeName', value: 'typeBizId' },
        };
      },
    },
  ]
}

export const modalFormItem = (): FormSchema[] => {
  const dictionary = useDictionary()

  return [
    {
      field: 'title',
      label: '标题',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 20,
      },
    },
    {
      field: 'category',
      label: '所属类别',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          placeholder: '请选择所属类别',
          api: modelTypeFindList,
          resultField: 'data',
          params: {
            pageSize: 10,
            pageNum: 1,
            modelType: 0,
          },
          alwaysLoad: true,
          immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.typeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'typeName', value: 'typeBizId' },
        };
      },
    },
    // {
    //   field: 'category',
    //   label: '所属分类',
    //   required: true,
    //   colProps: { span: 12 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   componentProps: function () {
    //     return {
    //       options: dictionary.getDictionaryOpt.get('modelWorkerSort'),
    //     }
    //   },
    // },
    {
      field: 'sortNumber',
      label: '排序号',
      required: true,
      colProps: { span: 12 },
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'flowChart',
      label: '流程图',
      rulesMessageJoinLabel: true,
      component: 'Upload',
      required: true,
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 44,
        },
        maxNumber: 1,
        maxSize: 100,
        accept: ['image/*'],
      },
    },
  ]
}
