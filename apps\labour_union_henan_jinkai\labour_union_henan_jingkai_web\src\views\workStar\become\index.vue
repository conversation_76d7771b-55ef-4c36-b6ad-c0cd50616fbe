<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/become/add"
          >新增成为劳模流程
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/become/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/become/modify',
              },
              {
                icon:
                  record.forbiddenState === 'n'
                    ? 'material-symbols:lock-open'
                    : 'material-symbols:lock',
                label: record.forbiddenState === 'n' ? '启用' : '禁用',
                type: 'primary',
                onClick: handleConfirm.bind(null, record),
                auth: '/become/disableBth',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                auth: '/become/delete',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <BecomeModel
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { columns, formSchemas } from './data';
import BecomeModel from './becomeModel.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { list, view, deleteLine, saveOrUpdate, enableDisable } from '/@/api/workStar/become';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  authInfo: ['/become/add'],
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  searchInfo: { orderBy: 'sort_number', sortType: 'asc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 480,
    dataIndex: 'action',
    fixed: undefined,
    auth: ['/become/modify', '/become/view','/become/disableBth', '/become/delete'],
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

//新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

//编辑
function handleEdit(record) {
  view({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: false, record: data });
  });
}

//详情
function handleView(record) {
  view({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, { isUpdate: true, disabled: true, record: data });
  });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.title}`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ values, isUpdate }) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
      });
    }
  });
}

function handleConfirm(record) {
  const name = record.forbiddenState === 'n' ? '禁用' : '启用';
  createConfirm({
    iconType: 'warning',
    content: `请确定${name}${record.title}?`,
    onOk: function () {
      enableDisable({
        autoId: record.autoId,
        forbiddenState: record.forbiddenState === 'y' ? 'n' : 'y',
      }).then(({ code, message }) => {
        if (code === 200) {
          reload();
        }
      });
    },
  });
}
</script>
