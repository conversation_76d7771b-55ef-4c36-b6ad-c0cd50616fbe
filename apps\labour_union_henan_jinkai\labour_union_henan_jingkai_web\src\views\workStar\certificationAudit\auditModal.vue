<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { auditModalForm } from './data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const title = computed(() => {
  return `审核--${unref(record)?.userName || ''}`;
});

const form = computed(() => {
  return auditModalForm();
});

const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
  labelWidth: 180,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
console.log(data,4345);

  const { evidentiaryMaterial } = data.record;
  setFieldsValue({
    ...data.record,
    evidentiaryMaterial: evidentiaryMaterial ? evidentiaryMaterial.split(',') : [],
  });

  setModalProps({ confirmLoading: false });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    const { auditStatus, auditInstruction } = values;

    emit('success', {
      values: {
        auditStatus,
        autoId: unref(record)?.autoId,
        auditInstruction,
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
