import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { Tooltip } from 'ant-design-vue';
import { cloneDeep, filter } from 'lodash-es';
import { uploadApi } from '@/api/sys/upload';
import { searchNextUnionForm } from '@/utils/searchNextUnion';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import {modelTypeFindList} from '@/api/work/index'
import { nextTick } from 'vue';
//列表
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '所属工会',
      dataIndex: 'companyName',
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      ellipsis: true,
      width: 120,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '身份证号码',
      dataIndex: 'identityCardNumber',
      ellipsis: true,
     
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      
      customRender: ({ text }) => {
        const education = dictionary.getDictionaryMap.get(`commonAudit_${text}`)?.dictName;
        return <Tooltip title={education}>{education}</Tooltip>;
      },
    },
    {
      title: '审核人姓名',
      dataIndex: 'auditUserName',
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
      width: 160,
    },
  ];
};

//搜索条件
export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'userName',
      label: '姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'identityCardNumber',
      label: '身份证号码',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'auditStatus',
      label: '审核状态',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: filter(
          cloneDeep(dictionary.getDictionaryOpt.get('commonAudit')) as RadioGroupChildOption[],
          v => v.value !=="cancel"
        ),
      },
    },
    ...searchNextUnionForm(),
  ];
};
//审核
export const auditModalForm = (disabled): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'auditStatus',
      label: '审核状态',
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      required: true,
      componentProps: {
        options: filter(
          cloneDeep(dictionary.getDictionaryOpt.get('commonAudit')) as RadioGroupChildOption[],
          v => v.value !== 'wait'&&v.value !=="cancel"
        ),
        
      },
      ifShow:!disabled
    },
    {
      field: 'auditInstruction',
      label: '审核意见',
      // required: function ({ values }) {
      //   if (values.auditStatus == 'refuse' ||  values.auditStatus == 'return') {
      //     return true;
      //   } else {
      //     return false;
      //   }
      // },
      rulesMessageJoinLabel: true,
      component: 'InputTextArea',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 200,
      },
    },
  ];
};
