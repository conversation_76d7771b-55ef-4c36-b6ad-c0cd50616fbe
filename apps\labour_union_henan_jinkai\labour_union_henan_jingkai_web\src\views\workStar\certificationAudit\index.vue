<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/certificationAudit/view',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                disabled:
                  record.auditStatus !== 'wait',// ||userStore.getUserInfo.companyId !== record.companyId
                onClick: handleAudit.bind(null, record),
                auth: '/certificationAudit/audit',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <AddModal
      @register="registerModal"
      :canFullscreen="false"
      width="60%"
    />

    <auditModal
      @register="auditRegisterModal"
      @success="auditHandleSuccess"
      :canFullscreen="false"
      width="60%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { columns, formSchemas } from './data';
import AddModal from './../info/AddModal.vue';
import auditModal from './auditModal.vue';
import { auditCertification } from '@/api/workStar/certificationAudit';
import { list, getDetails ,app} from '@/api/craftsmanInfoAudit';
import { useUserStore } from '@/store/modules/user';
import { useMessage } from '@monorepo-yysz/hooks';

const { createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'modelWorkerId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: {
      span: 3,
    },
  },
  searchInfo: {
    modelType:0
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',

    fixed: undefined,
    auth: ['/certificationAudit/audit', '/certificationAudit/view'],
  },
});

const userStore = useUserStore();

const [registerModal, { openModal }] = useModal();

const [auditRegisterModal, { openModal: auditopenModal, closeModal: auditCloseModal }] = useModal();

//详情
function handleView(record) {
  getDetails({ autoId: record.autoId }).then(({ data }) => {
    openModal(true, {
      isUpdate: true,
      disabled: true,
      isCertification: true,
      record: data,
    });
  });
}

//单个审核
function handleAudit(record) {
  getDetails({ autoId: record.autoId }).then(({ data }) => {
    auditopenModal(true, {
      record: data,
    });
  });
}

function auditHandleSuccess({ values }) {
  auditCertification(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: '审核成功',
      });
      reload();
      auditCloseModal();
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      });
    }
  });
}
</script>
