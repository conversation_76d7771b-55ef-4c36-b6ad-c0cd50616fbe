<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';

defineEmits(['register']);

const record = ref<Recordable>();

const activeKey = ref('success');

const title = computed(() => {
  return `${unref(record)?.userName || ''}--导入详情`;
});

const disabledClass = computed(() => {
  return 'back-transparent';
});

const form = computed(() => {
  return modalFormItem(unref(activeKey));
});

const [registerForm, { resetFields, setFieldsValue }] = useForm({
  labelWidth: 180,
  schemas: form,
  showActionButtonGroup: false,
  disabled: true,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  activeKey.value = data.activeKey;

  if (unref(record)) {
    setFieldsValue({ ...data.record });
  }

  setModalProps({ confirmLoading: false });
});
</script>
