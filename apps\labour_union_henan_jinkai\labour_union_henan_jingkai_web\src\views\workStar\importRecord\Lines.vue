<template>
  <BasicModal
    @register="basicRegisterModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    cancelText="关闭"
    :wrap-class-name="`${$style['video-modal']}`"
  >
    <Tabs
      type="card"
      v-model:active-key="activeKey"
    >
      <Tabs.TabPane
        key="success"
        tab="成功记录"
      >
      </Tabs.TabPane>
      <Tabs.TabPane
        key="fail"
        tab="失败记录"
      >
      </Tabs.TabPane>
    </Tabs>
    <BasicTable
      @register="registerS"
      v-if="activeKey === 'success'"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/modelWorkerInfo/successRecord',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <BasicTable
      @register="registerF"
      v-else
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/modelWorkerInfo/failureRecord',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
  <LineViewModal
    @register="register"
    :can-fullscreen="false"
    width="60%"
  />
</template>

<script lang="ts" setup>
import { failGet, findVoList, failInfo, getInfos } from '/@/api/workStar/modelWorkerInfo';
import { Tabs } from 'ant-design-vue';
import { ref, unref, computed } from 'vue';
import { useTable, BasicTable, TableAction } from '/@/components/Table';
import { columnsLine, formSchemasLine } from './data';
import { useModal, useModalInner, BasicModal } from '/@/components/Modal';
import LineViewModal from './LineViewModal.vue';

const activeKey = ref('success');

const fileName = ref();
const importRecordId = ref();

const title = computed(() => {
  return `${unref(fileName)}导入明细`;
});

const [register, { openModal}] = useModal();

const [basicRegisterModal, {}] = useModalInner(async data => {
  fileName.value = data?.record?.fileName;
  importRecordId.value = data?.record?.importRecordId;
  activeKey.value = 'success';
  await reload();
});

const column = computed(() => {
  return columnsLine(unref(activeKey));
});

const basicProps = {
  rowKey: 'autoId',
  columns: column,
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemasLine(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  beforeFetch: params => {
    params.importRecordId = unref(importRecordId);
    return params;
  },
  isCanResizeParent: false,
  maxHeight: 515,
  canResize: true,
  pagination: true,
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 100,
    dataIndex: 'action',

    fixed: undefined,
    auth: ['/modelWorkerInfo/successRecord', '/modelWorkerInfo/failureRecord'],
  },
};

const [registerS,{reload}] = useTable({ ...basicProps, api: findVoList });

const [registerF] = useTable({ ...basicProps, api: failGet });

async function handleView(record) {
  let data = {};
  if (unref(activeKey) === 'success') {
    
    data = await getInfos({ workerId: record.workerId });
  } else {
    await reload();
    data = await failInfo({ autoId: record.autoId });
  }
  openModal(true, { record: data, activeKey });
}
</script>

<style module lang="less">
.video-modal {
  :global {
    .footer-group {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
