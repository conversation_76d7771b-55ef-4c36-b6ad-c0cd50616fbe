import { Tooltip } from 'ant-design-vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'

export const columns = (): BasicColumn[] => {
  return [
    { title: '文件名', dataIndex: 'fileName' },
    { title: '总条数（条）', dataIndex: 'sumQuantity', width: 140 },
    {
      title: '成功条数（条）',
      dataIndex: 'succeedQuantity',
      customRender({ text }) {
        return (
          <span class={`text-green-500`} title={text || 0}>
            {text || 0}
          </span>
        )
      },
      width: 140,
    },
    {
      title: '失败条数（条）',
      dataIndex: 'failQuantity',
      customRender({ text }) {
        return (
          <span class={`text-red-500`} title={text || 0}>
            {text || 0}
          </span>
        )
      },
      width: 140,
    },
    { title: '导入人', dataIndex: 'createUser', width: 200 },
    {
      title: '导入时间',
      dataIndex: 'createTime',
      width: 160,
    },
  ]
}

export function formSchemas(): FormSchema[] {
  return [
    {
      field: 'fileName',
      component: 'Input',
      label: '文件名',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
  ]
}

export const columnsLine = (activeKey: string): BasicColumn[] => {
  const dictionary = useDictionary()
  if (activeKey === 'success') {
    return [
      {
        title: '所属工会名称',
        dataIndex: 'companyName',
      },
      {
        title: '姓名',
        dataIndex: 'userName',
        width: 240,
      },
      {
        title: '学历',
        dataIndex: 'education',
        customRender: ({ text }) => {
          const education = dictionary.getDictionaryMap.get(`modelEducation_${text}`)?.dictName
          return <Tooltip title={education}>{education}</Tooltip>
        },
        width: 120,
      },
      {
        title: '政治面貌',
        dataIndex: 'politicsState',
        customRender: ({ text }) => {
          const politicsState = dictionary.getDictionaryMap.get(`politicsState_${text}`)?.dictName
          return <Tooltip title={politicsState}>{politicsState}</Tooltip>
        },
        width: 100,
      },
      {
        title: '民族',
        dataIndex: 'nationality',
        customRender: ({ text }) => {
          const nationality = dictionary.getDictionaryMap.get(`nation_${text}`)?.dictName
          return <Tooltip title={nationality}>{nationality}</Tooltip>
        },
        width: 100,
      },
      {
        title: '导入时间',
        dataIndex: 'createTime',
        width: 170,
      },
    ]
  } else {
    return [
      {
        title: '所属工会名称',
        dataIndex: 'companyName',
      },
      {
        title: '姓名',
        dataIndex: 'userName',
        width: 240,
      },
      {
        title: '学历',
        dataIndex: 'education',
        width: 120,
      },
      {
        title: '政治面貌',
        dataIndex: 'politicsState',
        width: 100,
      },
      {
        title: '民族',
        dataIndex: 'nationality',
        width: 100,
      },
      {
        title: '导入时间',
        dataIndex: 'createTime',
        width: 170,
      },
    ]
  }
}

//搜索条件
export const formSchemasLine = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}

export function modalFormItem(activeKey: string): FormSchema[] {
  const dictionary = useDictionary()
  if (activeKey === 'success') {
    return [
      {
        field: 'companyName',
        label: '所属工会名称',
        component: 'ShowSpan',
        colProps: { span: 24 },
      },
      {
        field: 'userName',
        label: '姓名',
        component: 'ShowSpan',
        colProps: { span: 12 },
      },
      {
        field: 'gender',
        label: '性别',
        component: 'Select',
        colProps: { span: 12 },
        componentProps: function () {
          return {
            options: dictionary.getDictionaryOpt.get('gender'),
          }
        },
      },
      {
        field: 'nationality',
        label: '民族',
        component: 'Select',
        required: true,
        colProps: { span: 12 },
        componentProps: function () {
          return {
            options: dictionary.getDictionaryOpt.get('nation'),
          }
        },
      },
      {
        field: 'dateOfBirth',
        label: '出生年月',
        component: 'ShowSpan',
        colProps: { span: 12 },
      },
      {
        field: 'education',
        label: '文化程度',
        component: 'Select',
        colProps: { span: 12 },
        rulesMessageJoinLabel: true,
        componentProps: function () {
          return {
            options: dictionary.getDictionaryOpt.get('modelEducation'),
          }
        },
      },
      {
        field: 'politicsState',
        label: '政治面貌',
        component: 'Select',
        colProps: { span: 12 },
        componentProps: function () {
          return {
            options: dictionary.getDictionaryOpt.get('politicsState'),
          }
        },
      },
      {
        field: 'workUnitName',
        label: '工作单位及职务职称',
        component: 'InputTextArea',
        colProps: { span: 24 },
        componentProps: {
          autoSize: { minRows: 1, maxRows: 5 },
        },
      },
      {
        field: 'whenModelWorker',
        label: '所获最高荣誉称号及年份',
        component: 'InputTextArea',
        colProps: { span: 24 },
        componentProps: {
          autoSize: { minRows: 1, maxRows: 5 },
        },
      },
      {
        field: 'identityCardNumber',
        label: '身份证号码',
        component: 'ShowSpan',
        colProps: { span: 12 },
      },
      {
        field: 'phone',
        label: '联系电话',
        component: 'ShowSpan',
        colProps: { span: 12 },
        rulesMessageJoinLabel: true,
      },
      {
        field: 'modelTypeName',
        label: '所属类别',
        component: 'Select',
        colProps: { span: 12 },
      },
      {
        field: 'remark',
        label: '备注',
        component: 'InputTextArea',
        colProps: { span: 24 },
        componentProps: {
          autoSize: { minRows: 1, maxRows: 8 },
        },
      },
    ]
  } else {
    return [
      {
        field: 'companyName',
        label: '所属工会名称',
        component: 'ShowSpan',
        colProps: { span: 24 },
      },
      {
        field: 'userName',
        label: '姓名',
        component: 'ShowSpan',
        colProps: { span: 12 },
      },
      {
        field: 'gender',
        label: '性别',
        component: 'ShowSpan',
        colProps: { span: 12 },
      },
      {
        field: 'nationality',
        label: '民族',
        component: 'ShowSpan',
        required: true,
        colProps: { span: 12 },
      },
      {
        field: 'stringDateOfBirth',
        label: '出生年月',
        component: 'ShowSpan',
        colProps: { span: 12 },
      },
      {
        field: 'education',
        label: '文化程度',
        component: 'ShowSpan',
        colProps: { span: 12 },
        rulesMessageJoinLabel: true,
      },
      {
        field: 'politicsState',
        label: '政治面貌',
        component: 'ShowSpan',
        colProps: { span: 12 },
      },
      {
        field: 'workUnitName',
        label: '工作单位及职务职称',
        component: 'InputTextArea',
        colProps: { span: 24 },
        componentProps: {
          autoSize: { minRows: 1, maxRows: 5 },
        },
      },
      {
        field: 'whenModelWorker',
        label: '所获最高荣誉称号及年份',
        component: 'InputTextArea',
        colProps: { span: 24 },
        componentProps: {
          autoSize: { minRows: 1, maxRows: 5 },
        },
      },
      {
        field: 'identityCardNumber',
        label: '身份证号码',
        component: 'ShowSpan',
        colProps: { span: 12 },
      },
      {
        field: 'phone',
        label: '联系电话',
        component: 'ShowSpan',
        colProps: { span: 12 },
        rulesMessageJoinLabel: true,
      },
      {
        field: 'modelTypeName',
        label: '所属类别',
        component: 'Select',
        colProps: { span: 12 },
      },
      {
        field: 'remark',
        label: '备注',
        component: 'InputTextArea',
        colProps: { span: 24 },
        componentProps: {
          autoSize: { minRows: 1, maxRows: 8 },
        },
      },
      {
        field: 'failReason',
        label: '失败原因',
        component: 'InputTextArea',
        colProps: { span: 24 },
        componentProps: {
          autoSize: { minRows: 1, maxRows: 8 },
        },
        ifShow({ values }) {
          return !!values.failReason
        },
      },
    ]
  }
}
