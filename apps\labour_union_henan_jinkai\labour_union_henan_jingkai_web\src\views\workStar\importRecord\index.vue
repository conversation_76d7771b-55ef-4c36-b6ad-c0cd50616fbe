<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '记录明细',
                type: 'primary',
                onClick: handleView.bind(null, record),
                auth: '/importRecord/detailed',
              },
              {
                icon: 'clarity:export-line',
                label: '导出失败数据',
                type: 'primary',
                onClick: exportFailureData.bind(null, record),
                auth: '/importRecord/exportFailureData',
              },
            ]"
          >
          </TableAction>
        </template>
      </template>
    </BasicTable>
    <Lines
      @register="linesRegisterModal"
      :can-fullscreen="false"
      width="88%"
    />
  </div>
</template>

<script lang="ts" setup>
import { useTable, TableAction, BasicTable } from '@/components/Table';
import { columns, formSchemas } from './data';
import { exportFail, importLines } from '@/api/workStar/modelWorkerInfo';
import Lines from './Lines.vue';
import { useModal } from '@/components/Modal';
import { downloadByUrl } from '@monorepo-yysz/utils';

const [linesRegisterModal, { openModal: linesOpenModal }] = useModal();

const [registerTable, {}] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: importLines,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 260,
    dataIndex: 'action',

    fixed: undefined,
    auth: ['/importRecord/detailed', '/importRecord/exportFailureData'],
  },
});

function handleView(record) {
  linesOpenModal(true, { record: record });
}

function exportFailureData({ importRecordId, fileName }: Recordable) {
  exportFail({ importRecordId }).then(res => {
    const url = window.URL.createObjectURL(res);

    downloadByUrl({
      url,
      fileName,
    });
  });
}
</script>
