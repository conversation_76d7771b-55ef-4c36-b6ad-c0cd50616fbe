                                                        <template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
    :wrap-class-name="$style['recruit-modal']"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    <template #nameButton="{ model, field }">
        <a-input
          type="primary"
          @click="choiceModel(model, field)"
           :disabled="disabled || isUpdate"
          v-model:value="model[field]"
          placeholder="请选择劳模姓名"
          autocomplete="off"
          readonly
        ></a-input>
      </template>
      <template #button="{ model, field }">
        <a-input
          type="primary"
          @click="choiceUnion(model, field)"
          :disabled="disabled"
          v-model:value="model[field]"
          placeholder="请选择所属工会"
          autocomplete="off"
          readonly
        ></a-input>
      </template>
    </BasicForm>
  </BasicModal>
  <UnionListModal
    @register="registerCommentModal"
    :canFullscreen="false"
    width="60%"
    @success="handleSuccess"
  >
  </UnionListModal>
  <modelListModal  
    @register="registermodelListModal"
    :canFullscreen="false"
    width="60%"
    @success="handleModel"
    >
  </modelListModal>
</template>
<script lang="ts" setup>
import { computed, ref, unref, watch } from 'vue';
import { BasicModal, useModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { modalForm } from './data';
import UnionListModal from './UnionListModal.vue';
import modelListModal from './modelList.vue';

const emit = defineEmits(['success', 'register']);

const isUpdate = ref(true);

const model = ref<Recordable>();

const field = ref('');

const companyName = ref('');
//工会id
const companyId = ref('');
//工会组织分级
const temporaryIssueGrading = ref('');

//区别认证审核页面和劳模信息页面使用
const isCertification = ref(true);

const record = ref<Recordable>();

const disabled = ref(false);

const state = ref();

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.userName || ''}--详情`
    : unref(isUpdate)
      ? `编辑--${unref(record)?.userName || ''}`
      : '新增劳模';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalForm(unref(disabled), unref(isCertification), unref(isUpdate));
});
//所有劳模信息
const [registermodelListModal, { openModal ,closeModal :closeModelModal }] = useModal();
//选择劳模人员列表
function choiceModel(m,f) {
  openModal(true);
  console.log(m,f);
  model.value = m;
  field.value = f;
}
const modelRecord=ref<Recordable>()
function handleModel({record}) {
  modelRecord.value=record
  closeModelModal()
}
//所有工会
const [registerCommentModal, { openModal: openUnionListModal, closeModal }] = useModal();

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  isUpdate.value = !!data?.isUpdate;
  disabled.value = !!data?.disabled;
  isCertification.value = !!data?.isCertification;
  record.value = data.record;
  state.value = data.record?.state || '';

  const { companyName: name, companyId: id, companyClassicIds: ids } = data.record || {};

  companyName.value = name;
  companyId.value = id;
  temporaryIssueGrading.value = ids;

  if (unref(isUpdate)) {
    const { evidentiaryMaterial, personalStyle } = data.record;
    setFieldsValue({
      ...data.record,
      evidentiaryMaterial: evidentiaryMaterial ? evidentiaryMaterial.split(',') : [],
    });
  }

  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });

  setProps({ disabled: unref(disabled) });
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 180,
  schemas: form,
  showActionButtonGroup: false,
});

async function handleSubmit() {
  try {
    const { ...values } = await validate();
    const { evidentiaryMaterial } = values;
    setModalProps({ confirmLoading: true });
    emit('success', {
      values: {
        ...model.value,
        ...values,
        evidentiaryMaterial:
          evidentiaryMaterial && evidentiaryMaterial.length > 0
            ? evidentiaryMaterial.join(',')
            : null,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

//所属工会
function choiceUnion(m, f) {
  openUnionListModal(true);
  model.value = m;
  field.value = f;
}

function handleSuccess({ unionName, unionId, issueGrading }) {
  companyName.value = unionName;
  companyId.value = unionId;
  temporaryIssueGrading.value = issueGrading;
  closeModal();
}

watch(companyName, () => {
  if (model.value) {
    model.value[unref(field)] = unref(companyName);
    model.value['companyId'] = unref(companyId);
  }
});
watch(modelRecord, () => {
  if (modelRecord.value) {
    model.value['companyName'] =companyName.value?companyName.value:unref(modelRecord).companyName;
    model.value[unref(field)] = modelRecord.value.userName;
    model.value['companyId'] =companyId.value?companyId.value:unref(modelRecord).companyId;
    model.value['identityCardNumber'] =unref(modelRecord).identityCardNumber;
    model.value['phone'] =unref(modelRecord).phone;
    model.value['dateOfBirth'] =unref(modelRecord).dateOfBirth;
    model.value['nationality'] =unref(modelRecord).nationality;
    model.value['gender'] =unref(modelRecord).gender;
    model.value['userId'] =unref(modelRecord).userId;
  }
});
</script>

<style lang="less" module>
.recruit-modal {
  :global {
    .ant-input-number {
      width: 100% !important;
    }
  }
}
</style>
