<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" :show-ok-btn="false" :canFullscreen="false">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: 'carbon:task-view',
              label: '选择',
              type: 'default',
              onClick: handleCommentView.bind(null, record),
              // auth: '/difficultEmployees/choice',
            },
          ]" />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '@/components/Modal';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { computed } from 'vue';
import { UnionFormSchemas, Unioncolumns } from './data';
import { useUserStore } from '@/store/modules/user';
import { findUnionList } from '@/api/category';

const userStore = useUserStore();

const emit = defineEmits(['success', 'register']);

const title = computed(() => {
  return `所属区县选择`;
});

const [registerModal, { }] = useModalInner(async () => {
  await clearSelectedRowKeys();
});

const [registerTable, { clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  api: findUnionList,
  columns: Unioncolumns(),
  maxHeight: 435,
  beforeFetch: params => {
    params.pi = params.pageNum;
    params.ps = params.pageSize;
    params.puid = userStore.getUserInfo.companyId;
    return { ...params };
  },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: UnionFormSchemas(),
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',

    fixed: undefined,
    // auth: ['/difficultEmployees/choice']
  },
  immediate: true,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
});

//选择按钮操作
function handleCommentView(record) {
  console.log(record);
  emit('success', { unionName: record.c0100, unionId: record.id, issueGrading: record.c0203 });
}
</script>
