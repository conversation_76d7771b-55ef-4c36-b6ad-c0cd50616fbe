import { Tooltip } from 'ant-design-vue';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { uploadApi } from '@/api/sys/upload';
import { analysisIdCardNumber, getMaxSortNumber } from '@/api/workStar/modelWorkerInfo';
import { searchNextUnionForm } from '@/utils/searchNextUnion';
import { h } from 'vue';
import { Tinymce } from '@/components/Tinymce';
import { validatePhone, validateIdNum } from '@monorepo-yysz/utils';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { modelTypeFindList } from '@/api/work/index';
import { nextTick } from 'vue';
//列表
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    // {
    //   title: '添加工会',
    //   dataIndex: 'addCompanyName',
    //   ellipsis: true,
    //   customRender: ({ text }) => {
    //     return <Tooltip title={text}>{text}</Tooltip>
    //   },
    // },
   
    {
      title: '所属工会',
      dataIndex: 'companyName',
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '劳模姓名',
      dataIndex: 'userName',
      // ellipsis: true,
      // customRender: ({ text }) => {
      //   return <Tooltip title={text}>{text}</Tooltip>;
      // },
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      ellipsis: true,
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '数据来源',
      dataIndex: 'dataSources',
      ellipsis: true,
      customRender: ({ record, text }) => {
        return dictionary.getDictionaryMap.get(`dataSources_${text}`)?.dictName;
      },
    },
    {
      title: '是否在荣誉版单展示',
      dataIndex: 'whetherShow',
      width: 150,
      customRender: ({ record, text }) => {
        return dictionary.getDictionaryMap.get(`YesOrNo_${text}`)?.dictName;
        
      },
    },
    {
      dataIndex: 'modelTypeName',
      title: '所属类别',
      customRender: ({ text }) => {
        return <Tooltip title={text}>{text}</Tooltip>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
    },
  ];
};
//搜索条件
export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '劳模姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      slot: 'userName',
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      slot: 'phone',
    },
    // {
    //   label: '禁用状态',
    //   field: 'forbiddenState',
    //   colProps: { span: 6 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   slot: 'forbiddenState',
    // },
    // {
    //   label: '数据来源',
    //   field: 'dataSources',
    //   colProps: { span: 6 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   slot: 'dataSources',
    // },
    {
      field: 'typeBizId',
      label: '所属类别',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      // slot: 'modelType',
      componentProps: ({ formActionType }) => {
        return {
          placeholder: '请选择所属类别',
          api: modelTypeFindList,
          resultField: 'data',
          params: {
            pageSize: 10,
            pageNum: 1,
            modelType: 0,
          },
          alwaysLoad: true,
          immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.typeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'typeName', value: 'typeBizId' },
        };
      },
    },
    // {
    //   field: 'whetherCertification',
    //   label: '是否已认证',
    //   component: 'Select',
    //   colProps: { span: 6 },
    //   rulesMessageJoinLabel: true,
    //   slot: 'whetherCertification',
    // },
    {
      field: 'whetherShow',
      label: '是否在荣誉版单展示',
      component: 'Select',
      colProps: { span: 6 },
      labelWidth: 160,
      rulesMessageJoinLabel: true,
      slot: 'whetherShow',
    },
    ...searchNextUnionForm(),
    
  ];
};
//新增编辑详情
export const modalForm = (
  disabled: boolean,
  isCertification: boolean,
  isUpdate: boolean
): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: '',
      label: '劳模信息',
      component: 'Divider',
      ifShow() {
        return isCertification;
      },
    },
    {
      field: 'avatar',
      label: '劳模照片',
      required: true,
      component: 'CropperForm',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      // ifShow({ values }) {
      //   return 'y' === values.whetherShow;
      // },
      componentProps: {
        operateType: 82,
        imgSize:251/301
      },
    },
    {
      field: 'userName',
      label: '劳模姓名',
      component: 'Input',
      colProps: { span: 12 },
      required: true,
      // dynamicDisabled: isCertification,
      className: '!w-full',
      rulesMessageJoinLabel: true,
      // componentProps: {
      //   showCount: true,
      //   maxlength: 20,
      // },
      slot: 'nameButton',
      ifShow: !disabled,
    },
    {
      field: 'userName',
      component: 'Input',
      label: '劳模姓名',
      colProps: {
        span: 12,
      },
      required: true,
      dynamicDisabled: true,
      ifShow: disabled,
    },
    {
      field: 'userId',
      label: '姓名Id',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      slot: 'userId',
      show: false,
    },
    {
      field: 'companyName',
      label: '所属工会',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
      slot: 'button',
      ifShow: !disabled,
    },
    {
      field: 'companyId',
      label: '工会Id',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      // slot: 'companyId',
      show: false,
    },
    {
      field: 'companyName',
      label: '所属工会',
      component: 'ShowSpan',
      colProps: { span: 12 },
      ifShow: disabled,
    },
    {
      field: 'identityCardNumber',
      label: '身份证号码',
      component: 'Input',
      colProps: { span: 12 },
      rules: [{ required: true, validator: validateIdNum, trigger: ['change', 'blur'] }],
      rulesMessageJoinLabel: true,
      componentProps: function ({ formModel }) {
        return {
          showCount: true,
          maxlength: 18,
          onChange: e => {
            if (e.target?.value) {
              analysisIdCardNumber({ identityCardNumber: e.target.value }).then(res => {
                const { gender, dateOfBirth } = (res?.data || {}) as Recordable;
                formModel['gender'] = gender;
                formModel['dateOfBirth'] = dateOfBirth;
              });
            }
          },
        };
      },
    },
    {
      field: 'gender',
      label: '性别',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          options: dictionary.getDictionaryOpt.get('gender'),
        };
      },
    },
    {
      field: 'dateOfBirth',
      label: '出生年月',
      component: 'DatePicker',
      required: true,
      componentProps: {
        valueFormat: `YYYY-MM-DD`,
        format: `YYYY-MM-DD`,
        placeholder: '请选择出生年月',
      },
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      className: '!w-full',
    },
    {
      field: 'phone',
      label: '联系电话',
      component: 'Input',
      colProps: { span: 12 },
      rules: [{ required: true, validator: validatePhone, trigger: ['change', 'blur'] }],
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 11,
      },
    },
    {
      field: 'nationality',
      label: '民族',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          options: dictionary.getDictionaryOpt.get('nation'),
        };
      },
    },
    {
      field: 'education',
      label: '文化程度',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          options: dictionary.getDictionaryOpt.get('modelEducation'),
        };
      },
    },
    {
      field: 'politicsState',
      label: '政治面貌',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          options: dictionary.getDictionaryOpt.get('politicsState'),
        };
      },
    },
    {
      field: 'typeBizId',
      label: '所属类别',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          placeholder: '请选择所属类别',
          api: modelTypeFindList,
          resultField: 'data',
          params: {
            pageSize: 10,
            pageNum: 1,
            modelType: 0,
          },
          alwaysLoad: true,
          immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.typeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'typeName', value: 'typeBizId' },
        };
      },
    },
    {
      field: 'workUnitName',
      label: '工作单位及职务职称',
      component: 'Input',
      colProps: { span: 12 },
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 80
      },
    },
    {
      field: 'whenModelWorker',
      label: '所获最高荣誉称号及年份',
      component: 'Input',
      required: true,
      componentProps: {
        showCount: true,
        maxlength: 80
      },
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      className: '!w-full',
    },
    {
      field: 'personalStyle',
      label: '个人摘要',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 200
      },
    },
    // {
    //   field: 'workUnitName',
    //   label: '工作单位及职务职称',
    //   component: 'InputTextArea',
    //   colProps: { span: 12 },
    //   required: true,
    //   rulesMessageJoinLabel: true,
    //   componentProps: {
    //     showCount: true,
    //     maxlength: 80,
    //     autoSize: { minRows: 1, maxRows: 5 },
    //   },
    // },
    // {
    //   field: 'whenModelWorker',
    //   label: '所获最高荣誉称号及年份',
    //   component: 'InputTextArea',
    //   required: true,
    //   componentProps: {
    //     showCount: true,
    //     maxlength: 80,
    //     autoSize: { minRows: 1, maxRows: 5 },
    //   },
    //   colProps: { span: 12 },
    //   rulesMessageJoinLabel: true,
    //   className: '!w-full',
    // },
    {
      field: 'whetherShow',
      label: '荣誉榜单展示',
      component: 'RadioGroup',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      defaultValue: 'n',
      componentProps: function ({ formModel }) {
        return {
          options: dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[],
          // onChange: e => {
          //   //新增时才进行操作
          //   if (!isUpdate) {
          //     if ('y' === e.target.value) {
          //       getMaxSortNumber().then(res => {
          //         if (200 === res.code) {
          //           formModel['sortNumber'] = res.data;
          //         }
          //       });
          //     } else {
          //       formModel['personalStyle'] = undefined;
          //       formModel['sortNumber'] = undefined;
          //       formModel['personalProfile'] = undefined;
          //     }
          //   }
          // },
        };
      },
    },
    
    // {
    //   field: 'personalStyle',
    //   label: '个人风采',
    //   colProps: { span: 12 },
    //   component: 'Upload',
    //   ifShow({ values }) {
    //     return 'y' === values.whetherShow;
    //   },
    //   componentProps: {
    //     maxSize: 10,
    //     maxNumber: 3,
    //     api: uploadApi,
    //     accept: ['image/*'],
    //     uploadParams: {
    //       operateType: 82,
    //     }, 
    //   },
    //   rulesMessageJoinLabel: true,
    // },
    
    {
      field: 'personalProfile',
      component: 'Input',
      label: '个人简介',
      required: true,
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return 'y' === values.whetherShow;
      },
      render: ({ model, field, disabled }) => {
        return h(Tinymce, {
          value: model[field],
          onChange: (value: string) => {
            model[field] = value;
          },
          showImageUpload: false,
          operateType: 82,
          options: {
            readonly: disabled,
          },
        });
      },
    },
    {
      field: 'certificateNumber',
      label: '证书编号',
      component: 'Input',
      colProps: { span: 12 },
      dynamicDisabled: true,
      ifShow({ values }) {
        return disabled && values.certificateNumber && values.certificateNumber !== null;
      },
    },
    {
      field: 'evidentiaryMaterial',
      label: '证明材料',
      component: 'Upload',
      colProps: { span: 12 },
      dynamicDisabled: true,
      ifShow({ values }) {
        return disabled && values.evidentiaryMaterial && values.evidentiaryMaterial.length > 0;
      },
      componentProps: {
        api: uploadApi,
      },
    },

    // {
    //   field: 'forbiddenState',
    //   label: '禁用状态',
    //   component: 'Select',
    //   required: true,
    //   colProps: { span: 12 },
    //   rulesMessageJoinLabel: true,
    //   ifShow({ values }) {
    //     return disabled && values.dataSources && !!!isCertification;
    //   },
    //   componentProps: function () {
    //     return {
    //       options: [
    //         { label: '启用', value: 'y' },
    //         { label: '禁用', value: 'n' },
    //       ],
    //     };
    //   },
    // },
    // {
    //   field: 'dataSources',
    //   label: '数据来源',
    //   component: 'Select',
    //   required: true,
    //   colProps: { span: 12 },
    //   rulesMessageJoinLabel: true,
    //   ifShow({ values }) {
    //     return disabled && values.dataSources && !!!isCertification;
    //   },
    //   componentProps: function () {
    //     return {
    //       options: dictionary.getDictionaryOpt.get('dataSources'),
    //     };
    //   },
    // },
    {
      field: '',
      label: '审核信息',
      component: 'Divider',
      ifShow() {
        return isCertification;
      },
    },
    {
      field: 'auditStatus',
      label: '审核状态',
      component: 'Select',
      required: true,
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      ifShow({ values }) {
        return disabled && values.dataSources && isCertification;
      },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('commonAudit'),
        };
      },
    },
    {
      field: 'auditUserName',
      label: '审核人',
      component: 'Input',
      colProps: { span: 12 },
      ifShow() {
        return disabled && isCertification;
      },
    },
    {
      field: 'auditInstruction',
      label: '审核意见',
      ifShow() {
        return disabled && isCertification;
      },
      rulesMessageJoinLabel: true,
      colProps: { span: 24 },
      component: 'InputTextArea',
      componentProps: {
        showCount: true,
        maxlength: 200,
        autoSize: { minRows: 1, maxRows: 5 },
      },
    },
    // {
    //   field: 'remark',
    //   label: '备注',
    //   component: 'InputTextArea',
    //   colProps: { span: 24 },
    //   rulesMessageJoinLabel: true,
    //   required: true,
    //   componentProps: {
    //     showCount: true,
    //     maxlength: 200,
    //     autoSize: { minRows: 1, maxRows: 5 },
    //   },
    // },
  ];
};

//选择所属工会弹框筛选条件
export const UnionFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'un',
      label: '工会名称',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

//筛选工会列表
export const Unioncolumns = (): BasicColumn[] => {
  return [
    {
      title: '工会名称',
      dataIndex: 'c0100',
    },
  ];
};
//劳模列表
export const modelColumns = (): BasicColumn[] => {
  return [
    {
      title: '名称',
      dataIndex: 'a0100',
    },
    {
      title: '联系电话',
      dataIndex: 'a0115',
    },
    {
      title: '工会名称',
      dataIndex: 'c0100',
    },
  ];
};

//选择劳模弹框筛选条件
export const modelSchemas = (): FormSchema[] => {
  return [
    {
      field: 'p',
      label: '联系电话',
      colProps: { span: 8 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    // {
    //   field: 'idno',
    //   label: '证件号码',
    //   colProps: { span: 8 },
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    // },
  ];
};
