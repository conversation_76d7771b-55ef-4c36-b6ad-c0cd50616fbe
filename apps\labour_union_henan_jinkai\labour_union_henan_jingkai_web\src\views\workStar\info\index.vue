<template>
  <Spin :spinning="spinning">
    <div :class="$style.lm">
      <BasicTable @register="registerTable">
        <template #form-userName="{ model, field }">
          <Input
            v-model:value="model[field]"
            autocomplete="off"
            placeholder="请输入姓名"
            @change="changeUserName"
          />
        </template>
        <template #form-phone="{ model, field }">
          <Input
            v-model:value="model[field]"
            autocomplete="off"
            placeholder="请输入联系电话"
            @change="changePhone"
          />
        </template>
        <template #form-identityCardNumber="{ model, field }">
          <Input
            v-model:value="model[field]"
            placeholder="请输入身份证号码"
            autocomplete="off"
            @change="changeIdentityCardNumber"
          />
        </template>
        <template #form-dataSources="{ model, field }">
          <Select
            v-model:value="model[field]"
            placeholder="请选择数据来源"
            @change="changeDataSources"
            :options="dictionary.getDictionaryOpt.get('dataSources')"
          />
        </template>
        <template #form-modelWorkerSort="{ model, field }">
          <Select
            v-model:value="model[field]"
            placeholder="请选择所属类别"
            @change="changeModelWorkerSort"
            :options="dictionary.getDictionaryOpt.get('modelWorkerSort')"
          />
        </template>
        <template #form-whetherCertification="{ model, field }">
          <Select
            v-model:value="model[field]"
            placeholder="请选择是否已认证"
            @change="changeWhetherCertification"
            :options="dictionary.getDictionaryOpt.get('YesOrNo')"
          />
        </template>
        <template #form-whetherShow="{ model, field }">
          <Select
            v-model:value="model[field]"
            placeholder="请选择是否展示在荣誉版单"
            @change="changeWhetherShow"
            :options="dictionary.getDictionaryOpt.get('YesOrNo')"
          />
        </template>
        <template #toolbar>
          <a-button
            type="primary"
            @click="handleClick"
            auth="/modelWorkerInfo/add"
          >
            新增劳模</a-button
          >
          <a-button
            type="primary"
            @click="handleDown"
            auth="/modelWorkerInfo/download"
            >下载导入劳模模板</a-button
          >
          <Upload
            name="multipartFile"
            accept=".xlsx,.xls"
            @change="handleImport"
            :before-upload="beforeUpload"
            :action="action"
            :headers="{ token: userStore.getToken }"
            v-if="ifImport"
          >
            <a-button
              type="primary"
              auth="/modelWorkerInfo/import"
              >导入劳模信息</a-button
            >
          </Upload>
          <!-- <a-button
            type="primary"
            @click="handleDownInfo"
            auth="/modelWorkerInfo/export"
            >导出劳模信息</a-button
          > -->
        </template>
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :actions="[
                {
                  icon: 'carbon:task-view',
                  label: '详情',
                  type: 'default',
                  onClick: handleView.bind(null, record),
                  auth: '/modelWorkerInfo/view',
                },
                {
                  icon: 'fa6-solid:pen-to-square',
                  label: '编辑',
                  type: 'primary',
                  onClick: handleEdit.bind(null, record),
                  auth: '/modelWorkerInfo/modify',
                  disabled:
                    userStore.getUserInfo.companyId !== record.companyId &&
                    userStore.getUserInfo.companyId !== record.addCompanyId,
                },
                {
                  icon: 'fluent:delete-16-filled',
                  label: '删除',
                  danger:true,
                  type: 'primary',
                  onClick: handleDelete.bind(null, record),
                },
              ]"
            >
            </TableAction>
          </template>
        </template>
      </BasicTable>

      <AddModal
        @register="register"
        @success="handleSuccess"
        :canFullscreen="false"
        width="50%"
      >
      </AddModal>
    </div>
  </Spin>
</template>

<script lang="ts" setup>
import { BasicTable, TableAction, useTable } from '@/components/Table';
import AddModal from './AddModal.vue';
import { columns, formSchemas } from './data';
import {
  findVoList,
  saveOrUpdate,
  getById,
  updateLogicDeleteById,
  exportFail,
  exportModelWorker,
  getImportTemplateURL,
} from '@/api/workStar/modelWorkerInfo';
import { useModal } from '@/components/Modal';
import { computed, createVNode, onMounted, ref, unref } from 'vue';
import { message, Modal, UploadProps, Spin } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { Upload } from 'ant-design-vue';
import { useGlobSetting } from '@/hooks/setting';
import { useUserStore } from '@/store/modules/user';
import { usePermission } from '@/hooks/web/usePermission';
import dayjs from 'dayjs';
import { useDictionary } from '@/store/modules/dictionary';
import { Select, Input } from 'ant-design-vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { downloadByUrl } from '@monorepo-yysz/utils';

const emits = defineEmits(['register']);
const dictionary = useDictionary();
const { createErrorModal, createSuccessModal, createConfirm } = useMessage();

const { uploadUrl } = useGlobSetting();

const userStore = useUserStore();

const { hasPermission } = usePermission();

//loading加载
const spinning = ref<boolean>(false);

//新增编辑详情使用弹窗注册
const [register, { openModal, closeModal }] = useModal();

const action = ref(`${uploadUrl}/h5/modelWorkerInfo/importModelWorker`);

const ifImport = computed(() => {
  return hasPermission('/modelWorkerInfo/import');
});

const search = ref();

const [registerTable, { reload }] = useTable({
  rowKey: 'modelWorkerId',
  columns: columns(),
  authInfo: [
    '/modelWorkerInfo/add',
    '/modelWorkerInfo/import',
    '/modelWorkerInfo/download',
    '/modelWorkerInfo/export',
  ],
  searchInfo: {
    modelType: 0,
  },
  showIndexColumn: false,
  api: findVoList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: {
      span: 3,
    },
  },
  beforeFetch: params => {
    search.value = params;
    return params;
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 250,
    dataIndex: 'action',
    fixed: undefined,
    auth: [
      '/modelWorkerInfo/view',
      '/modelWorkerInfo/ToEnableThe',
      '/modelWorkerInfo/disable',
      '/modelWorkerInfo/modify',
    ],
  },
});

function changeUserName(e) {
  search.value.userName = e.target.value;
}
function changePhone(e) {
  search.value.Phone = e.target.value;
}
function changeIdentityCardNumber(e) {
  search.value.identityCardNumber = e.target.value;
}
function changeForbiddenState(val) {
  search.value.forbiddenState = val;
}
function changeDataSources(val) {
  search.value.dataSources = val;
}
function changeModelWorkerSort(val) {
  search.value.modelWorkerSort = val;
}

function changeWhetherCertification(val) {
  search.value.whetherCertification = val;
}
function changeWhetherShow(val) {
  search.value.whetherShow = val;
}

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
    isCertification: false,
  });
}
//编辑
function handleEdit(record) {
  getById({ workerId: record.workerId }).then(res => {
    const { code, data, message: msg } = res;
    if (code === 200) {
      openModal(true, { isUpdate: true, isCertification: false, record: data ,disabled: false,});
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}
//删除
function handleDelete(record, title, value) {
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定删除${record.userName}吗?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          updateLogicDeleteById(record.workerId).then(res => {
            const { code, message: mes } = res;
            if (code === 200) {
              message.success(`删除成功`);
            } else {
              message.error(`删除失败!${mes}`);
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}
//详情
function handleView(record) {
  getById({ workerId: record.workerId }).then(res => {
    const { code, data, message: msg } = res;
    if (code === 200) {
      openModal(true, {
        record: data,
        isUpdate: true,
        disabled: true,
        isCertification: false,
      });
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}

function handleImport({ file }) {
  if (file.status === 'done') {
    spinning.value = false;
    const { data, message, code } = file?.response;
    if (code !== 200) {
      createErrorModal({ content: `导入失败!${message}` });
      return;
    }

    const { failQuantity, succeedQuantity, fileName, importRecordId } = data;
    if (failQuantity === 0) {
      createSuccessModal({ content: `导入成功,成功条数：${succeedQuantity}条` });
      reload();
    } else {
      reload();
      createErrorModal({ content: `导入成功,成功条数：${succeedQuantity}条,失败条数：${failQuantity}条` });
      // createConfirm({
      //   iconType: 'error',
      //   content: `<p>存在导入失败数据, 是否下载失败文件？</p><p><span class="text-green-500">成功条数：${succeedQuantity}</span>; <span class="text-red-500">失败条数：${failQuantity}</span>。</p>`,
      //   okText: '下载导入失败数据',
      //   onOk: async function () {
      //     exportFail({ importRecordId }).then(res => {
      //       const url = window.URL.createObjectURL(res);
      //       downloadByUrl({
      //         url,
      //         fileName,
      //       });
      //     });
      //   },
      // });
    }
  }
}

const beforeUpload: UploadProps['beforeUpload'] = file => {
  const { name } = file;
  const fileName = name?.split('.') || [''];
  const isExcel = ['xls', 'xlsx', 'csv'].includes(fileName[fileName.length - 1]);
  if (!isExcel) {
    createErrorModal({ content: '只能上传Excel表格' });
  }
  spinning.value = true;
  return isExcel || Upload.LIST_IGNORE;
};

function handleDownInfo() {
  spinning.value = true;
  exportModelWorker(unref(search)).then(res => {
    spinning.value = false;
    const url = window.URL.createObjectURL(res.data);
    const day = dayjs().format('YYYY-MM-DD HH:mm');
    downloadByUrl({
      url,
      fileName: '劳模信息导出' + day + '.xlsx',
    });
  });
}

function handleDown() {
  getImportTemplateURL().then(res => {
    if (res) {
      const url = window.URL.createObjectURL(res);
      const day = dayjs().format('YYYY-MM-DD HH:mm');
      downloadByUrl({
        url,
        fileName: '劳模模板' + day + '.xlsx',
      });
    }
  });
}

//新增或编辑
function handleSuccess({ values, isUpdate }) {
  values.modelType=0;
  if (isUpdate) {
    saveOrUpdate(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: `编辑成功` });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `编辑失败!${message}` });
      }
    });
  } else {
    
    saveOrUpdate(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: `新增成功` });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `新增失败!${message}` });
      }
    });
  }
}

onMounted(async () => {
  emits('register');
});
</script>

<style lang="less" module>
.lm {
  :global {
    background-color: #fff;

    .ant-upload-list {
      display: none;
    }

    .ant-form {
      @apply px-6px;
    }
  }
}
</style>
