<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '选择',
                type: 'default',
                onClick: handleModel.bind(null, record),
                ifShow: record.a0115 !== null,
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useTable, BasicTable, TableAction } from '/@/components/Table';
import { modelColumns, modelSchemas } from './data';
import { userPagedAll } from '/@/api/workStar/whiteList';

const emit = defineEmits(['register', 'success']);

const title = computed(() => {
  return '用户信息选择';
});

const [registerModal, {}] = useModalInner(async () => {
  await clearSelectedRowKeys();
});

const [registerTable, { clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  api: userPagedAll,
  columns: modelColumns(),
  maxHeight: 430,
  beforeFetch: params => {
    params.pi = params.pageNum;
    params.ps = params.pageSize;
    params.uid = '6650f8e054af46e7a415be50597a99d5';
    params.p = params.p ? params.p : undefined;
    params.idno = params.idno ? params.idno : undefined;
    return { ...params };
  },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: modelSchemas(),
  },
  afterFetch: data => {
    const userData = data.data;
    return userData && userData.length > 0 ? userData : [];
  },
  fetchSetting: {
    totalField: 'recordCount',
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
    // auth: ['/difficultEmployees/choice']
  },
  immediate: true,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
  indexColumnProps: { width: 90 },
});

//选择按钮操作
function handleModel(record) {
  emit('success', {
    record: {
      companyName: record.c0100,
      userName: record.a0100,
      identityCardNumber: record.a0125,
      companyId: record.a0137,
      dateOfBirth: record.a0104,
      phone: record.a0115,
      nationality: record.a0108,
      userId: record.id,
      gender: record.a0102 == 1 ? 'male' : 'female',
    },
  });
}
</script>
