import { BasicColumn, FormSchema } from '@/components/Table';
export function typeColumns(): BasicColumn[] {
  return [
    {
      dataIndex: 'sort',
      title: '序号',
      width: 150,
    },
    {
      dataIndex: 'typeName',
      title: '类型名称',
    },
    {
      dataIndex: 'modelType',
      title: '类型',
      customRender({ text }) {
        const title = text == 0 ? '劳模' : '工匠'; //类型0劳模类型1工匠类型
        return <span title={title}>{title}</span>;
      },
    },
  ];
}
export function typeFormItem(isUpdate: boolean): FormSchema[] {
  return [
    {
      field: 'sort',
      label: '排序',
      colProps: { span: 24 },
      component: 'InputNumber',
      className: '!w-full',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'typeName',
      label: '类型名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'modelType',
      label: '类型',
      required: true,
      colProps: { span: 24 },
      component: 'RadioGroup',
      defaultValue: 0,
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '劳模', value: 0 },
            { label: '工匠', value: 1 },
          ],
        };
      },
    },
  ];
}

export function searchSchemas(): FormSchema[] {
  return [
    {
      field: 'modelType',
      label: '类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: [
            { label: '劳模', value: 0 },
            { label: '工匠', value: 1 },
          ],
        };
      },
    },
  ];
}
