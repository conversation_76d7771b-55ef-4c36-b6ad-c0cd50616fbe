# Whether to open mock
VITE_USE_MOCK = false

# public path
VITE_PUBLIC_PATH = /

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VITE_BUILD_COMPRESS = 'gzip'


# Basic interface address SPA
VITE_GLOB_API_URL = /basic-api

# File upload address， optional
# It can be forwarded by nginx or write the actual address directly /upload
VITE_GLOB_UPLOAD_URL = /basic-api
# 低版本兼容
VITE_USE_LEGACY = true
# Interface prefix
VITE_GLOB_API_URL_PREFIX =
# 压缩
VITE_USE_ZIP = true
VITE_USE_NAME = ncsfpt

