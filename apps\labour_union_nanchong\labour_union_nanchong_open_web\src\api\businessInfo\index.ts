import { BasicResponse } from '@monorepo-yysz/types';
import { openHttp } from '/@/utils/http/axios';

//查询商户
export const findCompanyInfo = params => {
  return openHttp.get<Recordable>({ url: '/openCompanyInfo/getByCompanyId', params });
};

//修改商户
export const updateCompanyMainInfo = params => {
  return openHttp.post<BasicResponse>(
    { url: '/openCompanyInfo/updateCompanyMainInfo', params },
    { isTransformResponse: false }
  );
};

//查询商户资质
export const getOpenCompanyMoreInfo = params => {
  return openHttp.get<Recordable>({ url: '/openCompanyInfo/getOpenCompanyMoreInfo', params });
};

//修改商户资质
export const updateCompanyMoreInfoByCompanyId = params => {
  return openHttp.post<BasicResponse>(
    { url: '/openCompanyInfo/updateCompanyMoreInfoByCompanyId', params },
    { isTransformResponse: false }
  );
};

//查询商户交易
export const getCompanyTransConfig = params => {
  return openHttp.get<Recordable>({ url: '/openCompanyInfo/getCompanyTransConfig', params });
};

//修改商户交易
export const customUpdateConfigByCompanyId = params => {
  return openHttp.post<BasicResponse>(
    { url: '/openCompanyInfo/customUpdateConfigByCompanyId', params },
    { isTransformResponse: false }
  );
};

//特殊获取一级商家和二级商家 角色
export const specialGetMerchantRole = params => {
  return openHttp.get<Recordable>({ url: '/openCompanyInfo/specialGetMerchantRole', params });
};