import { dataCenterHttp, defHttp, openHttp } from '@/utils/http/axios';
import { LoginParams, GetUserInfoModel } from './model/userModel';
import { RouteItem } from './model/menuModel';
import { ErrorMessageMode } from '#/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum Api {
  Login = '/openBusiness/openLogin',
  Logout = '/dataCenterBusiness/logout',
  GetUserInfo = '/dataCenterBusiness/manageLogin',
  GetPermCode = '/dataCenterBusiness/getCurrentMenuList',
  TestRetry = '/testRetry',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return openHttp.post<GetUserInfoModel>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    }
  );
}

/**
 * 修改自己的密码
 * @param params userid
 * @returns
 */
export function updatePwdByUserId(params) {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: '/accountInfo/updateCurrentPwd',
      params,
    },
    { isTransformResponse: false }
  );
}

/**
 * @description: getUserInfo
 */
export function getUserInfo(token) {
  return dataCenterHttp.get<GetUserInfoModel>(
    {
      url: Api.GetUserInfo,
      params: {
        token,
      },
    },
    { errorMessageMode: 'none' }
  );
}

export function getPermCode() {
  return dataCenterHttp.get<RouteItem[]>({ url: Api.GetPermCode });
}

export function doLogout() {
  return dataCenterHttp.post({ url: Api.Logout });
}

export function testRetry() {
  return defHttp.get(
    { url: Api.TestRetry },
    {
      retryRequest: {
        isOpenRetry: true,
        count: 5,
        waitTime: 1000,
      },
    }
  );
}
