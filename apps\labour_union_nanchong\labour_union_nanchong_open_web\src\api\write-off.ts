import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum API {
  findRecordList = '/activityInfo/h5/coupon/findRecordList',
  exportRecord = '/activityInfo/coupon/exportRecord',
  findListByMerchantId = '/couponInfo/findListByMerchantId',
  findActivityListByCoupon = '/activityInfo/findActivityListByCoupon'
}

// 列表
export const list = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    { url: API.findRecordList, params },
    {
      isTransformResponse: false,
    }
  );
};

// 列表
export const findListByMerchantId = (params: Recordable) => {
    return h5Http.get<BasicResponse>(
        { url: API.findListByMerchantId, params },
        {
            isTransformResponse: false,
        }
    );
};

// 活动列表(商户票券绑定)
export const findActivityListByCoupon = (params: Recordable) => {
    return h5Http.get<BasicResponse>(
        { url: API.findActivityListByCoupon, params },
        {
            isTransformResponse: false,
        }
    );
};

export const exportRecord = params =>{
    return h5Http.post<any>(
        { url: API.exportRecord, params, responseType: 'blob' },
        {
            isTransformResponse: false,
        }
    );
}
