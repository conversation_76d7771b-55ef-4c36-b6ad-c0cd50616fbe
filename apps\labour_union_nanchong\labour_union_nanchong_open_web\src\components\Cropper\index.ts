import { withInstall } from '@monorepo-yysz/utils';
import cropperImage from './src/Cropper.vue';
import avatarCropper from './src/CropperAvatar.vue';
import copperModal from './src/CropperModal.vue';
import cropperForm from './src/CropperForm.vue';

export * from './src/typing';
export const CropperImage = withInstall(cropperImage);
export const CropperAvatar = withInstall(avatarCropper);
export const CropperModal = withInstall(copperModal);
export const CropperForm = withInstall(cropperForm);
