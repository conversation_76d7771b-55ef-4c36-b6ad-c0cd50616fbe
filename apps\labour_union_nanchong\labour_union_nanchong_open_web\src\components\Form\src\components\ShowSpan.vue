<template>
  <span
    v-bind="$attrs"
    class="!pl-[11px] inline-block w-11/12 !truncate"
    :title="val"
    >{{ val }}</span
  >
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'ShowSpan',
  setup(_, { attrs }) {
    const val = computed(() => {
      const { value } = attrs;
      return value as string;
    });

    return {
      val,
    };
  },
});
</script>
