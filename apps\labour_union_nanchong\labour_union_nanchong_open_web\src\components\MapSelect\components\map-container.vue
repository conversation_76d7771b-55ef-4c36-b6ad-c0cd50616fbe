<template>
  <div
    id="container"
    :class="$style['map-container']"
    style="height: 100%; width: 100%"
  />
</template>

<script lang="ts" setup>
import { byLngLat } from '@/api/sys/map';
import { nextTick, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
  lnglat: { type: String, default: '106.089957,30.800315' },
  disabled: { type: Boolean, default: false },
});

const emit = defineEmits(['get-location']);

let map;
let marker;

function initMap() {
  map = new BMapGL.Map('container'); // 创建地图实例
  const lnglat = props.lnglat.split(',');

  let point = new BMapGL.Point(lnglat[0], lnglat[1]); // 创建点坐标

  const icon = new BMapGL.Icon('/resource/img/pointer.png', new BMapGL.Size(30, 30));
  icon.setAnchor(0);

  marker = new BMapGL.Marker(point, { icon }); // 创建标注

  map.addOverlay(marker);

  map.centerAndZoom(point, 13);

  map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放

  map.addEventListener('click', clickMap);

  // 拖拽
  props.disabled ? marker?.disableDragging() : marker?.enableDragging();

  marker.addEventListener('dragend', dragendMap);

  queryLocation({ lng: lnglat[0], lat: lnglat[1] });
}

function dragendMap(data) {
  clickMap({ ...data, latlng: data.latLng });
}

async function clickMap(data) {
  if (props.disabled) return;

  const { latlng } = data;
  const TLt = new BMapGL.Point(latlng.lng, latlng.lat);

  marker.setPosition(TLt);
  map?.panTo(TLt);
  await queryLocation(latlng);
}

async function queryLocation(latlng) {
  const { result } =
    (await byLngLat({
      location: `${latlng.lat},${latlng.lng}`,
    })) || {};

  const { formatted_address, business } = result;

  emit('get-location', {
    location: `${latlng.lng},${latlng.lat}`,
    address: `${formatted_address}${business ? `(${business})` : ''}`,
  });
}

watch(
  () => props.lnglat,
  () => {
    if (props.lnglat) {
      const lnglat = props.lnglat.split(',');

      const TLt = new BMapGL.Point(lnglat[0], lnglat[1]);
      map?.panTo(TLt);
      marker?.setPosition(TLt);
    }
  },
  { deep: true }
);

watch(
  () => props.disabled,
  () => {
    props.disabled ? marker?.disableDragging() : marker?.enableDragging();
  },
  { deep: true }
);

onUnmounted(() => {
  map.removeEventListener('click', clickMap);
  map = null;
});

onMounted(() => {
  nextTick(() => {
    initMap();
  });
});
</script>

<style lang="less" module>
.map-container {
  :global {
    .BMap_cpyCtrl,
    .anchorBL {
      display: none;
    }
  }
}
</style>
