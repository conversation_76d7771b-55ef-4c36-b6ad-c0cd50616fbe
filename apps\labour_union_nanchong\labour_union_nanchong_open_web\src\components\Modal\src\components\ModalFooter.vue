<template>
  <div>
    <slot name="insertFooter"></slot>
    <a-button
      v-bind="cancelButtonProps"
      @click="handleCancel"
      v-if="showCancelBtn"
    >
      {{ cancelText }}
    </a-button>
    <slot name="centerFooter"></slot>
    <a-button
      :type="okType"
      @click="handleOk"
      :loading="confirmLoading"
      v-bind="okButtonProps"
      v-if="showOkBtn"
    >
      {{ okText }}
    </a-button>
    <slot name="appendFooter"></slot>

    <a-button
      :type="otherType"
      @click="handleOther"
      :loading="confirmLoading"
      v-bind="otherButtonProps"
      v-if="showOtherBtn"
    >
      {{ otherText }}
    </a-button>
    <a-button
      :type="viewType"
      @click="handleView"
      :loading="confirmLoading"
      v-bind="viewButtonProps"
      v-if="showViewBtn"
    >
      {{ viewText }}
    </a-button>
  </div>
</template>
<script lang="ts" setup>
import { basicProps } from '../props';

defineOptions({ name: 'BasicModalFooter' });

defineProps(basicProps);

const emit = defineEmits(['ok', 'cancel', 'other', 'view']);

function handleOk(e: Event) {
  emit('ok', e);
}

function handleCancel(e: Event) {
  emit('cancel', e);
}

function handleOther(e: Event) {
  emit('other', e);
}

function handleView(e: Event) {
  emit('view', e);
}
</script>
