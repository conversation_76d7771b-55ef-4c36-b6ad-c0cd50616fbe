<template>
  <BasicTitle
    :helpMessage="helpMessage"
    class="!text-slate-200 truncate"
    :title="title"
  >
    {{ title }}
  </BasicTitle>
</template>
<script lang="ts" setup>
import type { PropType } from 'vue';
import { BasicTitle } from '@/components/Basic';

defineOptions({ name: 'BasicModalHeader' });

defineProps({
  helpMessage: {
    type: [String, Array] as PropType<string | string[]>,
  },
  title: { type: String },
});
</script>
