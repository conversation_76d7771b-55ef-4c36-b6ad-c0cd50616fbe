<template>
  <BasicModal
    :title="title"
    v-bind="$attrs"
    @register="register"
    :can-fullscreen="false"
    @ok="handleSuccess"
    :destroyOnClose="true"
    @visible-change="visibleChange"
  >
    <Form class="p-4 enter-x" :model="formData" ref="formRef" :rules="getFormRules">
      <FormItem name="oldPassword" class="enter-x" v-if="type=='userHeard'">
        <InputPassword
          size="large"
          allowClear
          v-model:value="formData.oldPassword"
          placeholder="请输入旧密码"
          :maxlength="20"
        />
      </FormItem>
      <FormItem name="pwd" class="enter-x">
        <InputPassword
          size="large"
          allowClear
          v-model:value="formData.pwd"
          placeholder="请输入密码"
        />
      </FormItem>
      <FormItem name="rePassword" class="enter-x">
        <InputPassword
          size="large"
          allowClear
          visibilityToggle
          v-model:value="formData.rePassword"
          placeholder="确认密码"
        />
      </FormItem>
    </Form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal'
import { Form, Input } from 'ant-design-vue'
import { computed, reactive, ref, unref, onMounted } from 'vue'
import { useRules, useFormVal } from './usePwd'

const emit = defineEmits(['register', 'success'])

const formRef = ref()
const FormItem = Form.Item
const InputPassword = Input.Password

const record = ref<Recordable>()

const type = ref()

const formData = reactive({
  oldPassword: '',
  pwd: '',
  rePassword: '',
})

// onMounted(() => {
//   formData.value = { oldPassword: '', pwd: '', rePassword: '' }
// })

const title = computed(() => {
  return `修改${unref(record)?.account || ''}密码`
})

const { getFormRules } = useRules(formData)

const { validForm, reset } = useFormVal(formRef)

const [register] = useModalInner(async data => {
  await reset()

  record.value = data?.record
   type.value = data?.type
})

async function handleSuccess() {
  const value = await validForm()
  emit('success', { values: { ...unref(record), ...value } })
}

async function visibleChange(show) {
  if (!show) {
    await reset()
  }
}
</script>
