<script lang="tsx">
import type { PropType, CSSProperties } from 'vue';
import { computed, defineComponent, unref, toRef, ref } from 'vue';
import { BasicMenu } from '@/components/Menu';
import { SimpleMenu } from '@/components/SimpleMenu';
import { AppLogo, AppSearchModal } from '@/components/Application';
import { MenuModeEnum, MenuSplitTyeEnum } from '@monorepo-yysz/enums';
import { useMenuSetting } from '@/hooks/setting/useMenuSetting';
import { ScrollContainer } from '@/components/Container';
import { useGo } from '@/hooks/web/usePage';
import { useSplitMenu } from './useLayoutMenu';
import { isHttpUrl, openWindow, propTypes } from '@monorepo-yysz/utils';
import { useRootSetting } from '@/hooks/setting/useRootSetting';
import { useDesign, useAppInject } from '@monorepo-yysz/hooks';
import { SearchOutlined } from '@ant-design/icons-vue';
// import { useRouter } from 'vue-router';
// import { Links } from '@/enums/pageEnum';

export default defineComponent({
  name: 'LayoutMenu',
  props: {
    theme: propTypes.oneOf(['light', 'dark']),
    splitType: {
      type: Number as PropType<MenuSplitTyeEnum>,
      default: MenuSplitTyeEnum.NONE,
    },
    isHorizontal: propTypes.bool,
    // menu Mode
    menuMode: {
      type: [String] as PropType<MenuModeEnum | null>,
      default: '',
    },
  },
  setup(props) {
    const go = useGo();

    const {
      getMenuMode,
      getMenuType,
      getMenuTheme,
      getCollapsed,
      getCollapsedShowTitle,
      getAccordion,
      getIsHorizontal,
      getIsSidebarType,
      getSplit,
    } = useMenuSetting();
    const { getShowLogo } = useRootSetting();

    // const router = useRouter();

    const { prefixCls } = useDesign('layout-menu');

    const { menusRef } = useSplitMenu(toRef(props, 'splitType'));

    const { getIsMobile } = useAppInject();

    const showModal = ref(false);

    const getComputedMenuMode = computed(() =>
      unref(getIsMobile) ? MenuModeEnum.INLINE : props.menuMode || unref(getMenuMode)
    );

    const getComputedMenuTheme = computed(() => props.theme || unref(getMenuTheme));

    const getIsShowLogo = computed(() => unref(getShowLogo) && unref(getIsSidebarType));

    const getUseScroll = computed(() => {
      return (
        !unref(getIsHorizontal) &&
        (unref(getIsSidebarType) ||
          props.splitType === MenuSplitTyeEnum.LEFT ||
          props.splitType === MenuSplitTyeEnum.NONE)
      );
    });

    const getWrapperStyle = computed((): CSSProperties => {
      return {
        height: `calc(100% - ${unref(getIsShowLogo) ? '65px' : '0px'})`,
      };
    });

    const getLogoClass = computed(() => {
      return [
        `${prefixCls}-logo`,
        unref(getComputedMenuTheme),
        {
          [`${prefixCls}--mobile`]: unref(getIsMobile),
        },
      ];
    });

    const getCommonProps = computed(() => {
      const menus = unref(menusRef);
      return {
        menus,
        beforeClickFn: beforeMenuClickFn,
        items: menus,
        theme: unref(getComputedMenuTheme),
        accordion: unref(getAccordion),
        collapse: unref(getCollapsed),
        collapsedShowTitle: unref(getCollapsedShowTitle),
        onMenuClick: handleMenuClick,
      };
    });
    /**
     * click menu
     * @param menu
     */

    function handleMenuClick(path: string) {
      // if (Links.includes(path)) {
      //   const r = router.resolve({
      //     path: path,
      //   });
      //   openWindow(r.href);
      // } else {
      //   go(path);
      // }
      go(path);
    }

    /**
     * before click menu
     * @param menu
     */
    async function beforeMenuClickFn(path: string) {
      if (!isHttpUrl(path)) {
        return true;
      }
      openWindow(path);
      return false;
    }

    function renderHeader() {
      if (!unref(getIsShowLogo) && !unref(getIsMobile)) return null;

      return (
        <AppLogo
          showTitle={!unref(getCollapsed)}
          class={unref(getLogoClass)}
          theme={unref(getComputedMenuTheme)}
        />
      );
    }

    function renderMenu() {
      const { menus, ...menuProps } = unref(getCommonProps);
      if (!menus || !menus.length) return null;
      return !props.isHorizontal ? (
        <SimpleMenu
          {...menuProps}
          isSplitMenu={unref(getSplit)}
          items={menus}
        />
      ) : (
        <BasicMenu
          {...(menuProps as any)}
          isHorizontal={props.isHorizontal}
          type={unref(getMenuType)}
          showLogo={unref(getIsShowLogo)}
          mode={unref(getComputedMenuMode as any)}
          items={menus}
        />
      );
    }

    function changeModal(show: boolean) {
      showModal.value = show;
    }

    return () => {
      return (
        <div class={`mx-5px`}>
          {renderHeader()}
          {unref(getUseScroll) ? (
            <>
              {
                <>
                  <div
                    class={`${prefixCls}--menu-search my-5px`}
                    onClick={changeModal.bind(null, true)}
                  >
                    <a-input
                      disabled={false}
                      class={`!cursor-pointer `}
                      placeholder={`搜索菜单`}
                    >
                      {{ prefix: () => <SearchOutlined /> }}
                    </a-input>
                  </div>
                  <AppSearchModal
                    onClose={changeModal.bind(null, false)}
                    open={unref(showModal)}
                  />
                </>
              }
              <ScrollContainer
                style={unref(getWrapperStyle)}
                class={`${prefixCls}--scroll-menu`}
              >
                {() => renderMenu()}
              </ScrollContainer>
            </>
          ) : (
            renderMenu()
          )}
        </div>
      );
    };
  },
});
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-layout-menu';
@logo-prefix-cls: ~'@{namespace}-app-logo';

.@{prefix-cls} {
  &-logo {
    height: @header-height;
    padding: 10px 4px 10px 10px;

    img {
      width: @logo-width;
      height: @logo-width;
    }
  }

  &--scroll-menu {
    .scrollbar__bar {
      display: none;
    }
  }

  &--mobile {
    .@{logo-prefix-cls} {
      &__title {
        opacity: 1;
      }
    }
  }

  &--menu-search {
    cursor: pointer !important;
    padding-left: 10px;
    padding-right: 10px;

    .ant-input-affix-wrapper {
      background-color: transparent;
      border-radius: 13px;
      border: 2px solid @menu-selected;

      input {
        cursor: pointer !important;
        border-left: 1px solid @menu-selected;
        background-color: transparent;

        &::-webkit-input-placeholder {
          color: @white;
          padding-left: 5px;
        }
      }

      .ant-input-prefix {
        span {
          color: @menu-selected !important;
        }
      }
    }
  }
}
</style>
