// by den<PERSON><PERSON><PERSON>
/**
 * 脱敏手机号码
 *
 * @param content 待脱敏的手机号内容
 * @param fillChar 脱敏填充字符，默认为 "*"
 * @returns 脱敏后的手机号字符串
 */
export function des_phone(content:string, fillChar:string = "*"):string {
    if (!content) {
      return "";
    }
  
    // 非字符串转换为字符串
    content = content.toString();
  
    if (content.length < 11) {
      return content;
    }
  
    let index = 1;
    let result = "";
  
    for (let char of content) {
      if (index < 4 || index > content.length - 4) {
        result += char;
      } else {
        result += fillChar;
      }
      index++;
    }
    return result;
}

/**
 * 通过身份证号码获取生日
 *
 * @param idNumber 身份证号码
 * @returns 生日
 */
export function getBirthdate(idNumber:string) {
    const regExp = /^(\\d{6})(\\d{8})(\\d{4})(\\d{1}|\\d{3}[\\dx])$/;
    const matches = idNumber.match(regExp);
    if (matches) {
      const year = matches[1].substring(0, 4);
      const month = matches[1].substring(4, 6);
      const day = matches[1].substring(6, 8);
      return year + '-' + month + '-' + day;
    }
    return '';
  }