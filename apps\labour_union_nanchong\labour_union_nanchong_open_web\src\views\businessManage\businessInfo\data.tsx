import { uploadApi } from '@/api/sys/upload';
import { FormSchema } from '/@/components/Form';
import { useDictionary } from '/@/store/modules/dictionary';
import cascaderOptions, { DivisionUtil } from '@pansy/china-division';
import { nextTick } from 'vue';
import { list} from '@/api/merchants/type';

const dictionary = useDictionary();

export const modalFormBasic = (): FormSchema[] => {
  const divisionUtil = new DivisionUtil(cascaderOptions);

  const cityData = divisionUtil.getSourceData();

  return [
    {
      field: '',
      label: '商户主体信息',
      component: 'Divider',
    },
    {
      field: 'companyName',
      label: '商户名称',
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyIcon',
      label: '普惠商户图标',
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'contractName',
      label: '联系人姓名',
      component: 'Input',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'contractPhone',
      label: '联系人电话',
      component: 'Input',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'areaCode',
      label: '所属地区',
      colProps: { span: 12 },
      component: 'TreeSelect',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          treeData: cityData,
        };
      },
    },
    {
      field: 'typeId',
      label: '所属分类',
      required: true,
      component: 'ApiSelect',
      colProps: { span: 12 },
      itemProps: {
        autoLink: true,
      },
      componentProps: ({ formActionType }) => {
        return {
          placeholder: '请选择类型',
          api: list,
          resultField: 'data',
          params: {
            pageSize: 0,
          },
          alwaysLoad: true,
          immediate: true,
          onChange: () => {
            const { clearValidate } = formActionType;
            nextTick(() => clearValidate());
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.typeName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'typeName', value: 'autoId' },
        };
      },
    },
    {
      field: 'address',
      label: '经营地点',
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'introduce',
      label: '商户简介',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
    },
    // {
    //   field: 'introduce',
    //   label: '商户收款码',
    //   component: 'CropperForm',
    //   rulesMessageJoinLabel: true,
    // },
  ];
};

export const modalFormQualifications = (): FormSchema[] => {
  return [
    {
      field: '',
      label: '商户资质信息',
      component: 'Divider',
    },
    {
      field: 'identityType',
      label: '商户主体证件类型',
      colProps: { span: 12 },
      component: 'Select',
      defaultValue: 'idCard',
      show: false,
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('identityType'),
        };
      },
    },
    {
      field: 'identityNumber',
      label: '法人身份证号',
      component: 'Input',
      rulesMessageJoinLabel: true,
      componentProps: {
        maxlength: 20,
        showCount:true,
      },
    },
    {
      field: 'identityImgFront',
      label: '法人身份证(正)',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'identityImgBack',
      label: '法人身份证(背)',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'licenseImg',
      label: '营业执照照片',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'openingImg',
      label: '开户许可证件',
      colProps: { span: 12 },
      component: 'CropperForm',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'publicityImg',
      label: '详情宣传图',
      component: 'Upload',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: {
        api: uploadApi,
        maxNumber: 3,
        uploadParams: {
          operateType: 65,
        },
      },
    },
    {
      field: 'qualificationImg',
      label: '资质证明',
      colProps: { span: 12 },
      component: 'Upload',
      rulesMessageJoinLabel: true,
      componentProps: {
        api: uploadApi,
        maxNumber: 3,
        uploadParams: {
          operateType: 65,
        },
      },
    },
  ];
};

export const modalFormPay = (): FormSchema[] => {
  return [
    {
      field: 'accountNickname',
      label: '商户交易配置',
      component: 'Divider',
    },
    {
      field: '',
      label: '开户户名',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      // componentProps: {
      //   disabled:true
      // },
    },
    {
      field: 'incomeAccountNumber',
      label: '收款银行账号',
      colProps: { span: 24 },
      component: 'Input',
      // componentProps: {
      //   disabled:true
      // },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'transPlatformBusinessId',
      label: '交易平台业务Id',
      colProps: { span: 24 },
      dynamicDisabled:true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      // componentProps: {
      //   disabled:true
      // },
    },
  ];
};
