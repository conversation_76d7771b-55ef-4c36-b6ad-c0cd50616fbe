<template>
  <div class="flex items-center" :class="$style['upload-simple']">
    <Upload
      v-model:file-list="fileList"
      name="file"
      list-type="picture-card"
      class="avatar-uploader"
      :show-upload-list="false"
      :action="uploadUrl"
      :before-upload="beforeUpload"
      :accept="`.jpg,.png,.jpeg,.gif,.webp`"
      :headers="{ token: userStore.getToken }"
      @change="info => handleChange(info, index)"
      :data="{
        operateType: operateType || 0,
        bucketName: bucket_name,
      }"
      v-bind="getAttrs"
      :disabled="disabled"
      v-for="(_, index) in fixPic"
    >
      <img
        v-if="url || someFile[index]"
        :src="url || someFile[index]"
        alt=""
        class="!w-full !h-full"
      />
      <div v-else>
        <loading-outlined v-if="loading"></loading-outlined>
        <plus-outlined v-else></plus-outlined>
        <div class="ant-upload-text">上传</div>
      </div>
    </Upload>
    <div v-if="url || someFile.length > 0">
      <Button shape="circle" @click="() => setVisible(true)" title="预览">
        <template #icon>
          <SearchOutlined />
        </template>
      </Button>
      <Image
        :style="{ display: 'none' }"
        :preview="{
          visible,
          onVisibleChange: setVisible,
        }"
        :src="url || someFile[0]"
      />
      <div class="hidden">
        <ImagePreviewGroup
          v-if="someFile"
          :preview="{ visible, onVisibleChange: vis => (visible = vis) }"
        >
          <Image v-for="item in someFile" :src="item" />
        </ImagePreviewGroup>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { PlusOutlined, LoadingOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { message, Upload, Image, Button, Modal } from 'ant-design-vue'
import { defineComponent, ref, computed, unref } from 'vue'
import type { UploadChangeParam, UploadProps } from 'ant-design-vue'
import { useGlobSetting } from '/@/hooks/setting'
import { isString, isArray, map } from 'lodash-es'
import { useUserStore } from '/@/store/modules/user'

const ImagePreviewGroup = Image.PreviewGroup

function getBase64(img: Blob, callback: (base64Url: string) => void) {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result as string))
  reader.readAsDataURL(img)
}
export default defineComponent({
  components: {
    LoadingOutlined,
    PlusOutlined,
    Upload,
    Image,
    Button,
    SearchOutlined,
    ImagePreviewGroup,
  },
  props: ['url', 'operateType', 'someFile', 'fix', 'disabled'],
  emits: ['change'],
  setup(props, { emit, attrs }) {
    const { uploadUrl, bucket_name } = useGlobSetting()

    const fileList = ref([])

    const loading = ref<boolean>(false)

    const userStore = useUserStore()

    const url = computed(() => {
      return props.url
    })

    const disabled = computed(() => {
      return !!props.disabled
    })

    const someFile = computed(() => {
      if (isString(props.someFile)) {
        return map(props.someFile.split(','), v => userStore.getPrefix + v)
      } else if (isArray(props.someFile)) {
        return map(props.someFile, v => userStore.getPrefix + v)
      } else if (unref(url)) {
        return [userStore.getPrefix + unref(url)]
      } else {
        return []
      }
    })

    const fixPic = computed(() => {
      return unref(disabled) ? unref(someFile).length : props.fix || 1
    })

    const getAttrs = computed(() => {
      return {
        ...attrs,
      }
    })

    const visible = ref<boolean>(false)

    const setVisible = (value): void => {
      visible.value = value
    }

    const handleChange = (info: UploadChangeParam, index) => {
      if (info.file.status === 'uploading') {
        loading.value = true
        return
      }
      if (info.file.status === 'done') {
        // Get this url from response in real world.
        getBase64(info.file.originFileObj as Blob, () => {
          loading.value = false
        })
        if (info.file.response.code === 200) {
          const noPrefix = info.file.response.data[0]
          const url = noPrefix ? noPrefix : ''
          emit('change', { filePath: url, index: index })
        } else {
          Modal.error({
            title: '提示',
            content: info.file.response.message || '上传失败',
          })
        }
      }
      if (info.file.status === 'error') {
        loading.value = false
        message.error('上传失败')
      }
    }

    //@ts-ignore
    const beforeUpload = (file: UploadProps['fileList'][number]) => {
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        message.error('请上传不超过2MB的图片!')
      }
      return isLt2M
    }

    return {
      fileList,
      loading,
      handleChange,
      beforeUpload,
      uploadUrl: uploadUrl + '/file/minio/uploadFile',
      getAttrs,
      url,
      visible,
      setVisible,
      operateType: props.operateType,
      someFile,
      fixPic,
      disabled,
      userStore,
      bucket_name,
    }
  },
})
</script>
<style lang="less" module>
.upload-simple {
  :global {
    @apply !w-full;

    .avatar-uploader {
      .ant-upload {
        @apply !w-128px !h-128px;
      }
    }

    .ant-upload-select-picture-card i {
      font-size: 32px;
      color: #999;
    }

    .ant-upload-select-picture-card .ant-upload-text {
      margin-top: 8px;
      color: #666;
    }
  }
}
</style>
