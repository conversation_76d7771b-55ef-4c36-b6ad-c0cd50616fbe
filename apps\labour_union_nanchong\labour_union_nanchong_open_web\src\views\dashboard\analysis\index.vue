<template>
  <div :class="$style.dashboard">
    <div class="flex justify-center items-center flex-col w-full h-full">
      <span class="text-2xl">您好，{{ userName }}!</span>
      <span class="text-3xl font-bold">欢迎登录{{ title }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useGlobSetting } from '@/hooks/setting';
import { useUserStore } from '@/store/modules/user';
import { computed } from 'vue';

const { title } = useGlobSetting();

const userStore = useUserStore();

const userName = computed(() => userStore.getUserInfo.nickname || '用户');
</script>

<style lang="less" module>
.dashboard {
  :global {
    height: calc(100vh - 65px);

    background-image: url('@/assets/images/login/login-back.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}
</style>
