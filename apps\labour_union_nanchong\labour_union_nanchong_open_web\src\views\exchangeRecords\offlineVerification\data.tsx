import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useUserStore } from '/@/store/modules/user';
import { useDictionary } from '/@/store/modules/dictionary';
import { Image, Input } from 'ant-design-vue';
import { userInfoSearch } from '@/api/productManagement/integralExchangeRecord';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '兑换人',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '兑换时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '商品名',
      dataIndex: 'productName',
    },
    {
      title: '商品图片',
      dataIndex: 'productCoverImg',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '规格名',
      dataIndex: 'productSubName',
    },
    {
      title: '规格封面',
      dataIndex: 'productSubImg',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    // {
    //   title: '提货方式',
    //   dataIndex: 'integralPayment',
    //   customRender({ text }) {
    //     const name = dictionary.getDictionaryMap.get(`integralPayment_${text}`)?.dictName || '';
    //     return <span title={name}>{name}</span>;
    //   },
    // },
    {
      title: '状态',
      dataIndex: '',
      customRender({ record }) {
        let name = '';
        if ('1' === record?.integralPayment) {
          name =
            dictionary.getDictionaryMap.get(`transOrderState_${record?.deliveryStatus}`)
              ?.dictName || '';
        } else {
          name = dictionary.getDictionaryMap.get(`qrCodeState_${record?.state}`)?.dictName || '';
        }
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '核销人姓名',
      dataIndex: 'writeOffUserName',
    },
    {
      title: '核销人手机号',
      dataIndex: 'writeOffUserPhone',
    },
  ];
};

export const modalColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '',
      dataIndex: '',
    },
    {
      title: '',
      dataIndex: '',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: 'userId',
      label: '兑换人',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          api: userInfoSearch,
          resultField: 'data',
          params: { integralPayment: '2' },
          alwaysLoad: true,
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.userName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'userName', value: 'userId' },
        };
      },
    },
    // {
    //   field: 'productSubId',
    //   label: '规格名',
    //   component: 'ApiSelect',
    //   colProps: { span: 6 },
    //   rulesMessageJoinLabel: true,
    //   componentProps: ({ formActionType }) => {
    //     return {
    //       api: getSpecifications,
    //       resultField: 'data',
    //       params: { productId: productId ? productId : '', systemQueryType: 'manage' },
    //       alwaysLoad: true,
    //       immediate: true,
    //       showSearch: true,
    //       filterOption: (input: string, option: any) => {
    //         return option.productSubName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    //       },
    //       fieldNames: { label: 'productSubName', value: 'productSubId' },
    //     };
    //   },
    // },

    {
      field: 'state',
      label: '状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('qrCodeState'),
        };
      },
    },
  ];
};

export const modalFormItem = (disabled, isUpdate): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();
  return [
    {
      field: '',
      label: '兑换信息',
      component: 'Divider',
    },
    {
      field: 'userName',
      label: '兑换人',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'createTime',
      label: '兑换时间',
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: !disabled || isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'productName',
      label: '商品名',
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: !disabled || isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'productSubName',
      label: '规格名',
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: !disabled || isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'productCoverImg',
      label: '商品封面图',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'CropperForm',
    },
    {
      field: 'productSubImg',
      label: '规格封面',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'CropperForm',
    },
    {
      field: 'state',
      label: '状态',
      dynamicDisabled: !disabled || isUpdate,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('qrCodeState'),
        };
      },
    },
    {
      field: '',
      label: '核销信息',
      component: 'Divider',
    },
    {
      field: 'writeOffUserName',
      label: '核销人姓名',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 20,
        showCount: true,
      },
    },
    {
      field: 'writeOffUserPhone',
      label: '核销人手机号码',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 18,
        showCount: true,
      },
    },
  ];
};

export const columnSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: '',
      label: '',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get(''),
        };
      },
    },
  ];
};
