<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              // {
              //   icon: 'fa6-solid:pen-to-square',
              //   label: '编辑',
              //   type: 'primary',
              //   onClick: handleEdit.bind(null, record),
              // },
              // {
              //   icon: 'fluent:delete-16-filled',
              //   label: '删除',
              //   type: 'primary',
              //   danger: true,
              //   onClick: handleDelete.bind(null, record),
              // },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <OfflineVerificationModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import OfflineVerificationModal from './offlineVerificationModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  list,
  view,
  deleteLine,
  deliveryGoods,
} from '@/api/productManagement/integralExchangeRecord';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  beforeFetch(params) {
    params.integralPayment = '2';
    return params;
  },
  searchInfo: { orderBy: 'create_time', sortType: 'desc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

//编辑
function handleEdit(record) {
  view({ recordId: record?.recordId }).then(({ data }) => {
    openModal(true, {
      isUpdate: true,
      disabled: false,
      record: { ...data, userName: record?.userName },
    });
  });
}

//详情
function handleView(record) {
  view({ recordId: record?.recordId }).then(({ data }) => {
    openModal(true, {
      isUpdate: true,
      disabled: true,
      record: { ...data, userName: record?.userName },
    });
  });
}

// 删除
function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.title}`,
    onOk: function () {
      deleteLine({ ...record }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ values, isUpdate }) {
  // saveOrUpdate(values).then(({ code, message }) => {
  //   if (code === 200) {
  //     createSuccessModal({
  //       content: `${isUpdate ? '编辑' : '新增'}成功`,
  //     });
  //     reload();
  //     closeModal();
  //   } else {
  //     createErrorModal({
  //       content: `${isUpdate ? '编辑' : '新增'}失败! ${message}`,
  //     });
  //   }
  // });
}
</script>
