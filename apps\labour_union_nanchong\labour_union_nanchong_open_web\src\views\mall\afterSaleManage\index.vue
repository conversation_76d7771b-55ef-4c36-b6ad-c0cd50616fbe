<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="[
            {
              icon: 'carbon:task-view',
              label: '详情',
              type: 'default',
              onClick: handleView.bind(null, record)
            },
            {
              icon: 'mynaui:send',
              label: '审核',
              type: 'primary',
              onClick: handleAudit.bind(null, record),
              disabled: 'wait' !== record.serviceState,
            },
            {
              icon: 'hugeicons:mail-receive-01',
              label: '确认收货',
              type: 'primary',
              onClick: handleReceiveBackTransport.bind(null, record),
              disabled: 'receive' !== record.serviceState,
            },
          ]" />
        </template>
      </template>
      <template #expandedRowRender="{ expanded, record }">
        <div class="expanded-table-wrapper">
          <BasicTable :columns="childrenColumns()" :dataSource="record?.detailVOList" :pagination="false"
            :useSearchForm="false" ref="tableRef" rowKey="autoId" v-if="expanded" :showIndexColumn="false"
            :bordered="true" :maxHeight="400" :canResize="false" size="small">
          </BasicTable>
        </div>
      </template>
    </BasicTable>

    <OnlineShippingModal @register="registerModal" :can-fullscreen="false" width="50%" />
    <AfterSaleAuditModal @register="AfterSaleAuditRegisterModal" @success="AfterSaleAuditHandleSuccess"
      :can-fullscreen="false" width="50%" />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas, childrenColumns } from './data';
import OnlineShippingModal from './onlineShippingModal.vue';
import AfterSaleAuditModal from './afterSaleAuditModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { findCustomSaleServiceList, auditSaleAfter, receiveBackTransport } from '@/api/productManagement/afterSale';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: findCustomSaleServiceList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  beforeFetch: p => {
    p.integralPayment = '1'
    return p
  },
  searchInfo: { orderBy: 'create_time', sortType: 'desc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 300,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal }] = useModal();
const [AfterSaleAuditRegisterModal, { openModal: AfterSaleAuditOpenModal, closeModal: AfterSaleAuditCloseModal }] = useModal();

//审核
function handleAudit(record) {
  AfterSaleAuditOpenModal(true, {
    isUpdate: true,
    disabled: false,
    record: record,
  });
}

//确认收货
function handleReceiveBackTransport(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确定是否进行【确认收货】?`,
    onOk: function () {
      receiveBackTransport({ todoValueList: [record?.serviceGroupId] }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({
            content: `确认收货成功`,
          });
          reload()
        } else {
          createErrorModal({
            content: `确认收货失败! ${message}`,
          });
        }
      });
    },
  });
}

//详情
function handleView(record) {
  openModal(true, {
    isUpdate: true,
    disabled: true,
    record: { ...record },
  });
}

function AfterSaleAuditHandleSuccess({ values, isUpdate }) {
  const { operateType, remark, serviceGroupId } = values
  auditSaleAfter({ operateType, remark, todoValueList: [serviceGroupId] }).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `审核成功`,
      });
      reload();
      AfterSaleAuditCloseModal();
    } else {
      createErrorModal({
        content: `审核失败! ${message}`,
      });
    }
  });
}
</script>

<style lang="less" scoped>
.expanded-table-wrapper {
  :deep(.ant-table-wrapper) {
    overflow-x: hidden;

    .ant-table {
      width: 100% !important;
      table-layout: fixed;
    }
  }
}
</style>