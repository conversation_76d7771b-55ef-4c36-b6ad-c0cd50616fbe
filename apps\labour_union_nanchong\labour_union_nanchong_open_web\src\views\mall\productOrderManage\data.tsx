import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useUserStore } from '/@/store/modules/user';
import { useDictionary } from '/@/store/modules/dictionary';
import { Image, Input } from 'ant-design-vue';
import { cloneDeep, filter, startsWith } from 'lodash-es';
export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '订单信息',
      dataIndex: 'orderInfo',
      width: 400,
      customRender({ record }) {
        return (
          <div style={{ textAlign: 'left' }}>
            <p>
              <span style={{ fontWeight: 'bold' }}>下单时间: </span>&nbsp;{record.createTime}
            </p>
            <p>
              <span style={{ fontWeight: 'bold' }}>订单编号: </span>&nbsp;{record.orderId}
            </p>
            {/* <div style={{ display: 'flex', alignItems: 'center' }}>
              <Image
                src={userStore.getPrefix + record.productSubImg}
                width={50}
                height={50}
              />
              <div style={{ marginLeft: '20px' }}>
                <p>{record.productName}</p>
                <p style={{ fontSize: 12, color: '#999' }}>{record.productSubName}</p>
                <p>数量: {record.currentProductCount}</p>
              </div>
            </div> */}
          </div>
        );
      },
    },
    {
      title: '收货信息',
      dataIndex: 'productSubName',
      customRender({ record }) {
        return (
          <div style={{ textAlign: 'left' }}>
            <p>
              <span style={{ fontWeight: 'bold' }}>收货人姓名: </span>&nbsp;
              {record.snapshotVo?.receiveSnapshot?.receiverName}
            </p>
            <p>
              <span style={{ fontWeight: 'bold' }}>收货人电话: </span>&nbsp;
              {record.snapshotVo?.receiveSnapshot?.receiverPhone}
            </p>
            {/* <p>{record.snapshotVo?.receiveSnapshot?.detailArea}</p> */}
          </div>
        );
      },
    },
    {
      title: '支付信息',
      dataIndex: 'productSubImg',
      width: 200,
      customRender: ({ record }) => {
        return (
          <div style={{ textAlign: 'left' }}>
            <p>
              <b>支付金额￥: </b>
              {record.payAmount-record?.couponDiscountAmount}
            </p>
            {/* <p>--------------------</p> */}
            <p>
              <b>支付方式: </b>
              {dictionary.getDictionaryMap.get(`paymentType_${record?.paymentType}`)?.dictName ||
                ''}
            </p>
          </div>
        );
      },
    },
    {
      title: '订单留言',
      dataIndex: 'productSubName',
    },
    {
      title: '订单状态',
      dataIndex: 'orderState',
      customRender({ record }) {
        let name =
          dictionary.getDictionaryMap.get(`transOrderState_${record?.orderState}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: 'orderId',
      label: '订单编号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'orderState',
      label: '订单状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('transOrderState'),
        };
      },
    },
  ];
};

export const modalFormItem = (disabled, isUpdate): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: '',
      label: '订单信息',
      component: 'Divider',
    },
    {
      field: 'orderId',
      label: '订单编号',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 20,
        showCount: true,
      },
    },
    {
      field: 'createTime',
      label: '下单时间',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 18,
        showCount: true,
      },
    },
    {
      field: 'paymentTime',
      label: '付款时间',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 50,
        showCount: true,
      },
    },
    {
      field: 'deliverTime',
      label: '发货时间',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 50,
        showCount: true,
      },
    },
    {
      field: 'receiveTime',
      label: '收货时间',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 300,
        showCount: true,
      },
    },
    {
      field: 'shippingAddress',
      label: '订单留言',
      colProps: { span: 24 },
      component: 'InputTextArea',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 300,
        showCount: true,
      },
    },
    {
      field: '',
      label: '商品信息',
      component: 'Divider',
    },
    {
      field: 'companyName',
      label: '商户名称',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },

    {
      field: 'orderState',
      label: '订单状态',
      dynamicDisabled: !disabled || isUpdate,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('transOrderState'),
        };
      },
    },
    {
      field: 'discountAmount',
      label: '优惠金额(原/现价差)',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
      labelWidth:140
    },
    {
      field: 'payAmount',
      label: '订单总金额',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
      {
      field: 'couponDiscountAmount',
      label: '优惠金额(票劵)',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'finalAmount',
      label: '实付金额',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'orderListDivider',
      label: '购买商品/规格信息',
      component: 'Divider',
    },
    {
      field: 'orderList',
      label: '',
      component: 'Input',
      slot: 'orderList',
      colProps: { span: 24 },
    },
    {
      field: '',
      label: '收货信息',
      component: 'Divider',
    },
    {
      field: 'snapshotVo.receiveSnapshot.receiverName',
      label: '收货人姓名',
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: !disabled || isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'snapshotVo.receiveSnapshot.receiverPhone',
      label: '收货人电话',
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: !disabled || isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'snapshotVo.receiveSnapshot.detailArea',
      label: '收件人地址',
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: !disabled || isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'snapshotVo.receiveSnapshot.detailAddress',
      label: '详细地址',
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: !disabled || isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '发货信息',
      component: 'Divider',
    },
    {
      field: 'transportName',
      label: '物流名称',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'transportNumber',
      label: '物流单号',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const orderListColumns = (): BasicColumn[] => {
  const userStore = useUserStore();
  const dictionary = useDictionary();
  return [
    {
      title: '商品名称',
      customRender({ record }) {
        return <span>{record.productName}</span>;
      },
      key: 'productName',
      width: '30%',
    },
    {
      title: '商品规格',
      customRender({ record }) {
        return <span>{record.productSubName}</span>;
      },
      key: 'productSubName',
      width: '20%',
    },
    {
      title: '商品规格封面',
      dataIndex: '',
      key: 'productSubImg',
      width: '15%',
      customRender: ({ record }) => {
        let text = record.productSubImg;
        return (
          <Image
            src={startsWith(text, 'http') ? text : userStore.getPrefix + text}
            width={50}
            height={50}
          />
        );
      },
    },
    {
      title: '运费(元)',
      customRender({ record }) {
        return <span>{record.transportPrice}</span>;
      },
      key: 'transportPrice',
      width: '15%',
    },
    {
      title: '购买数量(件)',
      customRender({ record }) {
        return <span>{record.currentProductCount}</span>;
      },
      key: 'currentProductCount',
      width: '20%',
    },
  ];
};

export const columnSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: '',
      label: '',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '',
      required: true,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get(''),
        };
      },
    },
  ];
};

export const deliveryModalFormItem = (disabled, isUpdate): FormSchema[] => {
  const dictionary = useDictionary();
  return [

    {
      field: '',
      label: '订单信息',
      component: 'Divider',
    },
    {
      field: 'orderId',
      label: '订单编号',
      colProps: { span: 12 },
      component: 'Input',
  
      rulesMessageJoinLabel: true,
          dynamicDisabled: !disabled || isUpdate,
    },
    {
      field: 'createTime',
      label: '下单时间',
      colProps: { span: 12 },
      component: 'Input',
    
      rulesMessageJoinLabel: true,
          dynamicDisabled: !disabled || isUpdate,
    },
    {
      field: 'paymentTime',
      label: '付款时间',
      colProps: { span: 12 },
      component: 'Input',
 
      rulesMessageJoinLabel: true,
          dynamicDisabled: !disabled || isUpdate,
   
    },
   
    {
      field: 'shippingAddress',
      label: '订单留言',
      colProps: { span: 24 },
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
          dynamicDisabled: !disabled || isUpdate,
    },
    {
      field: '',
      label: '商品信息',
      component: 'Divider',
    },
    {
      field: 'companyName',
      label: '商户名称',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },

    {
      field: 'orderState',
      label: '订单状态',
      dynamicDisabled: !disabled || isUpdate,
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('transOrderState'),
        };
      },
    },
  {
      field: 'discountAmount',
      label: '优惠金额(原/现价差)',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
      labelWidth:140
    },
    {
      field: 'payAmount',
      label: '订单总金额',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
      {
      field: 'couponDiscountAmount',
      label: '优惠金额(票劵)',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'finalAmount',
      label: '实付金额',
      colProps: { span: 12 },
      dynamicDisabled: !disabled || isUpdate,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'orderListDivider',
      label: '购买商品/规格信息',
      component: 'Divider',
    },
    {
      field: 'orderList',
      label: '',
      component: 'Input',
      slot: 'orderList',
      colProps: { span: 24 },
    },
    {
      field: '',
      label: '收货信息',
      component: 'Divider',
    },
    {
      field: 'snapshotVo.receiveSnapshot.receiverName',
      label: '收货人姓名',
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: !disabled || isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'snapshotVo.receiveSnapshot.receiverPhone',
      label: '收货人电话',
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: !disabled || isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'snapshotVo.receiveSnapshot.detailArea',
      label: '收件人地址',
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: !disabled || isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'snapshotVo.receiveSnapshot.detailAddress',
      label: '详细地址',
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: !disabled || isUpdate,
      rulesMessageJoinLabel: true,
    },
    {
      field: '',
      label: '发货信息',
      component: 'Divider',
    },
    {
      field: 'transportName',
      label: '物流名称',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: true,
    },
    {
      field: 'transportNumber',
      label: '物流单号',
      colProps: { span: 24 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      required: true,
    },
  ];
};

export const childrenColumns = (): BasicColumn[] => {
  const userStore = useUserStore();
  const dictionary = useDictionary();
  return [
    {
      title: '商品名称',
      dataIndex: 'productName',
      width: '25%',
      ellipsis: true,
    },
    {
      title: '商品规格名称',
      dataIndex: 'productSubName',
      width: '25%',
      ellipsis: true,
    },
    {
      title: '商品规格封面',
      dataIndex: 'productSubImg',
      width: '20%',
      customRender: ({ text }) => {
        return (
          <Image
            src={startsWith(text, 'http') ? text : userStore.getPrefix + text}
            width={50}
            height={50}
          />
        );
      },
    },
    {
      title: '运费(元)',
      dataIndex: 'transportPrice',
      width: '15%',
      ellipsis: true,
    },
    {
      title: '购买数量(件)',
      dataIndex: 'currentProductCount',
      width: '15%',
      ellipsis: true,
    },
  ];
};
