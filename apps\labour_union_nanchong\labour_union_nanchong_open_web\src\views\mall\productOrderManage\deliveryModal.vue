<template>
  <BasicModal @register="registerModal" v-bind="$attrs" :title="title" @ok="handleSubmit">
    <BasicForm @register="registerForm" :class="disabledClass">
      <template #orderList>
        <!-- 订单商品列表 -->
        <div class="order-list">
          <a-table :columns="columns" :dataSource="orderData" :pagination="false" size="small" :bordered="true">
          </a-table>
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { deliveryModalFormItem,orderListColumns  } from './data';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.orderId || ''}--详情`
      : `${unref(record)?.orderId || ''}--发货`
    : '新增xx';
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return deliveryModalFormItem(unref(disabled), unref(isUpdate));
});

const columns = computed(() => {
  return orderListColumns();
});

const orderData = ref([]);

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  console.log(data.record, "data");

  await resetFields();

  record.value = data.record;
  orderData.value = getDataSource(data.record) || [];

  disabled.value = !!data.disabled;

  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
   const { payAmount, couponDiscountAmount } = data.record
    setFieldsValue({
      ...data.record,
      transportName: undefined,
      transportNumber: undefined,
      finalAmount : (payAmount - couponDiscountAmount).toFixed(2)
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

function getDataSource(record) {
  const temList = record.snapshotVo.transProductSnapshot.productInfoList;
  interface PriceListInfo {
    [key: string]: any;
  }
  return temList.flatMap(item => item.priceListInfo) as PriceListInfo[];
}

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();

    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
