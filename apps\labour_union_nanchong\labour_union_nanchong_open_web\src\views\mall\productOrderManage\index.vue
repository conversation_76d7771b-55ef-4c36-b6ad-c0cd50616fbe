<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'mynaui:send',
                label: '发货',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                disabled: 'deliver' !== record.orderState,
              },
            ]"
          />
        </template>
      </template>
      <template #expandedRowRender="{ expanded, record }">
        <div class="expanded-table-wrapper">
          <BasicTable
            :columns="childrenColumns()"
            :dataSource="getDataSource(record)"
            :pagination="false"
            :useSearchForm="false"
            ref="tableRef"
            rowKey="autoId"
            v-if="expanded"
            :showIndexColumn="false"
            :bordered="true"
            :maxHeight="400"
            :canResize="false"
            size="small"
          >
            <template #action="{ record2 }">
              <TableAction
                :actions="[
                  {
                    icon: 'carbon:task-view',
                    label: '详情',
                    type: 'default',
                    onClick: handleView.bind(null, record2),
                  },
                ]"
              />
            </template>
          </BasicTable>
        </div>
      </template>
    </BasicTable>
    <OrderModal
      @register="registerModal"
      :can-fullscreen="false"
      width="50%"
    />
    <!--   @success="handleSuccess" -->
    <DeliveryModal
      @register="registerDeliveryModal"
      @success="handleDeliverySuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref} from 'vue';
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas, childrenColumns } from './data';
import OrderModal from './orderModal.vue';
import DeliveryModal from './deliveryModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import {
  findOrderVoList,
  deleteLine,
  orderDeliveryHandle,
} from '@/api/productManagement/orderRecord';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const tableRef = ref();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: findOrderVoList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  searchInfo: { orderBy: 'create_time', sortType: 'desc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 180,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal }] = useModal();

const [registerDeliveryModal, { openModal: openDeliveryModal, closeModal: closeDeliveryModal }] =
  useModal();

//编辑
function handleEdit(record) {
  openDeliveryModal(true, {
    isUpdate: true,
    disabled: false,
    record: record,
  });
}

function getDataSource(record) {
  const temList = record.snapshotVo.transProductSnapshot.productInfoList;
  let orderDetailsListData = [];
  for (let i = 0; i < temList.length; i++) {
    for (let j = 0; j < temList[i].priceListInfo.length; j++) {
      orderDetailsListData.push(temList[i].priceListInfo[j]);
    }
  }
  return orderDetailsListData;
}

//详情
function handleView(record) {
  openModal(true, {
    isUpdate: true,
    disabled: true,
    record: record,
  });
}

function handleDeliverySuccess({ values, isUpdate }) {
  orderDeliveryHandle(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `发货成功`,
      });
      reload();
      closeDeliveryModal();
    } else {
      createErrorModal({
        content: `发货失败! ${message}`,
      });
    }
  });
}
</script>

<style lang="less" scoped>
.expanded-table-wrapper {
  :deep(.ant-table-wrapper) {
    overflow-x: hidden;
    
    .ant-table {
      width: 100% !important;
      table-layout: fixed;
    }
  }
}
</style>
