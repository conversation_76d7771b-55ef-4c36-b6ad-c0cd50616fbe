<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" :show-ok-btn="canSubmit" :canFullscreen="false"
    @ok="handleSubmitSpeciForm">
    <BasicTable @register="registerTable">
      <template v-if="!isMain" #toolbar>
        <a-button type="primary" @click="handleMoreUp">
          批量上架</a-button>
        <a-button type="primary" :danger ="true"  @click="handleMoreDown">
          批量下架</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="[
          {
            icon: 'carbon:task-view',
            label: '上架',
            ifShow: record.saleState == 'down',
            type: 'primary',
            onClick: handleUpSpecification.bind(null, record, 'up'),
            // auth: '/difficultEmployees/choice',
          },
          {
            icon: 'carbon:task-view',
            label: '下架',
            ifShow: record.saleState == 'up',
            type:'primary',
            danger: true,
            onClick: handleUpSpecification.bind(null, record, 'down'),
            // auth: '/difficultEmployees/choice',
          },
        ]" />
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '@/components/Modal';
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { computed, createVNode, ref, unref } from 'vue';
import { getSpecifications, upAndDownProducts } from '@/api/productManagement';
import {
  querySpecifications,
  querySpecificationsColumns,
} from '@/views/mall/products/productManagement';
import { message, Modal } from 'ant-design-vue';
import { CloseCircleFilled } from '@ant-design/icons-vue';

const emit = defineEmits(['success', 'register']);
const isUpOrDown = ref();
const productId = ref('');
const isMain = ref();
const isShow = ref();
const canSubmit = ref();
const title = computed(() => {
  return `规格商品上架提示`;
});

const [registerModal, { closeModal: closeModal }] = useModalInner(async data => {
  await clearSelectedRowKeys();
  if (data.record) {
    productId.value = data.record.productId;
    isMain.value = data.isMain;
    isShow.value = data.isShow;
    canSubmit.value = data.canSubmit;
  }
  reload();
});

const rowSelection = computed(() => {
  return unref(isMain) ? undefined : {
    type: 'checkbox'
  };
});

const [
  registerTable,
  { reload, getSelectRows, setSelectedRowKeys, getDataSource, clearSelectedRowKeys },
] = useTable({
  rowKey: 'autoId',
  api: getSpecifications,
  columns: querySpecificationsColumns(),
  beforeFetch: params => {
    params.productId = productId.value;
    params.systemQueryType = 'manage';
    return params;
  },
  formConfig: {
    labelWidth: 100,
    autoSubmitOnEnter: true,
    schemas: querySpecifications(),
  },
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
  },
  rowSelection: rowSelection,
  maxHeight: 350,
  immediate: false,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: false,
  clickToRowSelect: false,
});

//批量提交主商品时候操作
function handleSubmitSpeciForm() {
  const temProductSubIdList = [];
  const rows = getSelectRows();
  if (!rows || rows.length === 0) {
    Modal.warning({
      title: '提示',
      icon: createVNode(CloseCircleFilled),
      content: '请选择至少一种规格上架(下架)！',
      okText: '确认',
      closable: true,
    });
    return false;
  } else {
    for (let i = 0; i < rows.length; i++) {
      temProductSubIdList.push(rows[i].productSubId);
    }
    const upParams = {};
    if (isMain.value === true) {
      upParams.operateProductType = 'integral';
      upParams.operateType = 'up';
    } else {
      upParams.operateProductType = 'inclusiveSub';
      if (isUpOrDown.value === 'up') {
        upParams.operateType = 'up';
      } else {
        upParams.operateType = 'down';
      }
    }
    upParams.productId = productId.value;
    upParams.productSubIdList = temProductSubIdList;
     upAndDownProducts(upParams).then(res => {
    if (res.code === 200) {
      message.success(upParams.operateType === 'up' ? '商品上架成功' : '商品下架成功');
      reload();
    clearSelectedRowKeys();
    } else {
      message.error(upParams.operateType === 'up' ? '商品上架失败' : '商品下架失败');
    }
  });
    
  }
}

//商品规格单独上下架操作
function handleUpSpecification(record, arg1) {
  const upParams = {};
  const temList = [];
  temList.push(record.productSubId);
  upParams.operateProductType = 'inclusiveSub';
  upParams.operateType = arg1;
  upParams.productId = productId.value;
  upParams.productSubIdList = temList;
  upAndDownProducts(upParams).then(res => {
    if (res.code === 200) {
      message.success(upParams.operateType === 'up' ? '商品上架成功' : '商品下架成功');
      reload();
    } else {
      message.error(upParams.operateType === 'up' ? '商品上架失败' : '商品下架失败');
    }
  });
}

//批量上架
function handleMoreUp() {
  const rows = getSelectRows();

  // 检查商品是否是上架状态
  const hasDownProducts = rows.some((row) => row.saleState === 'up');
  console.log(hasDownProducts,'hasDownProducts');
  
  if (hasDownProducts) {
    Modal.warning({
      title: '提示', 
      icon: createVNode(CloseCircleFilled),
      content: '选中的商品规格中有已上架的商品规格！',
      okText: '确认',
      closable: true,
    });
    return false;
  }

  isUpOrDown.value = 'up';
  handleSubmitSpeciForm();
}

//批量下架
function handleMoreDown() {
const rows = getSelectRows();

  // 检查商品是否是上架状态
  const hasDownProducts = rows.some((row) => row.saleState === 'down');
  console.log(hasDownProducts,'hasDownProducts');
  
  if (hasDownProducts) {
    Modal.warning({
      title: '提示', 
      icon: createVNode(CloseCircleFilled),
      content: '选中的商品规格中有已下架的商品规格！',
      okText: '确认',
      closable: true,
    });
    return false;
  }

  isUpOrDown.value = 'down';
  handleSubmitSpeciForm();
}
</script>
