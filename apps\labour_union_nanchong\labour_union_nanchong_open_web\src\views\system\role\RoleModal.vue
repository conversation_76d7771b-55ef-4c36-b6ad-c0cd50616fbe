<template>
  <BasicModal @register="registerModal" v-bind="$attrs" :title="title" @ok="handleSubmit">
    <BasicForm @register="registerForm" :class="disabledClass">
      <template #menuIds="">
        <BasicTree :checkedKeys="checkedValue" :treeData="treeData" :checkable="true" :toolbar="true"
          @check="handleCheck" :fieldNames="{ children: 'children', title: 'title', key: 'autoId' }" :search="true"
          expandOnSearch ref="treeRef" />
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';
import { BasicTree, KeyType, TreeActionType } from '@/components/Tree';
import { TreeDataItem } from 'ant-design-vue/es/tree/Tree';
import { getMenuList } from '@/api/sys/menu';

import { map, join, split } from 'lodash-es';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref<boolean>(false);

const isUpdate = ref<boolean>(false);

const treeRef = ref<Nullable<TreeActionType>>(null);
const checkedValue = ref<KeyType[]>([]);
const treeData = ref<TreeDataItem[]>([]);
const halfCheckedKeys = ref<KeyType[]>([]);



const title = computed(() => {
  return unref(isUpdate)
    ? unref(disabled)
      ? `${unref(record)?.roleName || ''}--详情`
      : `编辑${unref(record)?.roleName || ''}`
    : '新增角色';
});

//定义tree
function getTree() {
  const tree = unref(treeRef);
  if (!tree) {
    throw new Error('tree is null!');
  }
  return tree;
}

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem();
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  checkedValue.value = [];
  treeData.value = [];


  record.value = data.record;
  disabled.value = !!data.disabled;
  isUpdate.value = !!data.isUpdate;

  treeData.value = (await getMenuList({})) as unknown as TreeDataItem[];
  console.log(treeData, 'treeData');

  if (unref(isUpdate)) {
    //编辑回显值
    const keys = data.record.menuIds ? map(split(data.record.menuIds, ','), v => Number(v)) : [];
    checkedValue.value = keys;
    console.log(keys, 'keys');
    halfCheckedKeys.value = data.record.halfCheckedKeys
      ? map(split(data.record.halfCheckedKeys, ','), v => Number(v))
      : [];
    console.log(halfCheckedKeys.value, 'halfCheckedKeys.value');
    setFieldsValue({
      ...data.record,
      // menuIds: split(data.record.menuIds, ','),
      menuIds: [],
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });

  setTimeout(() => {
    // getTree().expandAll(true)
    getTree().setExpandedKeys(unref(halfCheckedKeys));
    setModalProps({
      confirmLoading: false,
    });
  }, 500);
});

function handleCheck(checkedKeys, e) {
  checkedValue.value = checkedKeys;
  halfCheckedKeys.value = e.halfCheckedKeys;
  console.log(e, 'e.halfCheckedKeys');

}

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    const checkeds = getTree().getCheckedKeys() as any;

    let checked: string[] | number[] = [];
    if (!!checkeds?.checked) {
      checked = unref(checkeds)?.checked;
    } else if (checkeds?.length > 0) {
      checked = unref(checkeds);
    }

    emit('success', {
      values: {
        ...unref(record),
        ...values,
        // menuIds: join(values.menuIds, ','),
        menuIds: join(checked, ','),
        halfCheckedKeys: join(unref(halfCheckedKeys), ','),
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
