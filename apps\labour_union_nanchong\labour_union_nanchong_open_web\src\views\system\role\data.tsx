import { getMenuList } from '@/api/sys/menu';
import { ApiBasicTree, FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import { sortMenuData } from '@/store/modules/permission';

const dictionary = useDictionary();

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '角色名称',
      dataIndex: 'roleName',
    },
    {
      title: '角色类型',
      dataIndex: 'roleType',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`openRoleType_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '角色状态',
      dataIndex: 'state',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`state_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'roleName',
      label: '角色名称',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'roleName',
      label: '角色名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'menuIds',
      label: '角色菜单',
      component: 'ApiTree',
      slot: 'menuIds',
      // itemProps: {
      //   autoLink: true,
      // },
      // render({ field, model, disabled }) {
      //   return (
      //     <ApiBasicTree
      //       checkValue={model[field]}
      //       toolbar={true}
      //       disabled={disabled}
      //       expandOnSearch={true}
      //       fieldNames={{ children: 'children', title: 'title', key: 'treeId' }}
      //       search={true}
      //       checkable={true}
      //       showLine={true}
      //       placeholderSearch="请输入菜单名称"
      //       api={getMenuList}
      //       checkStrictly={true}
      //       onCheck={e => {
      //         model[field] = e.checked;
      //       }}
      //       afterFetch={res => {
      //         return sortMenuData(res || []);
      //       }}
      //     />
      //   );
      // },
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      componentProps: {
        autoSize: { minRows: 1, maxRows: 8 },
      },
    },
  ];
};
