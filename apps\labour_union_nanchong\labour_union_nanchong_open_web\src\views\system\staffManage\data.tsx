import { list } from '@/api/system/role';
import { threeList } from '@/api/system/staff';
import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import { specialGetMerchantRole } from '/@/api/businessInfo/index';
import { getSecondLevelCompanyInfos } from '@/api/merchants/index';
import { useUserStore } from '@/store/modules/user';

const dictionary = useDictionary();
const userStore = useUserStore();

export const columns = (): BasicColumn[] => {
  return [
   {
      title: '商户名称',
      dataIndex: 'companyName',
      ifShow:userStore?.getUserInfo?.isOneLevel
    },
    {
      title: '账号',
      dataIndex: 'account',
    },
    {
      title: '姓名',
      dataIndex: 'nickname',
    },
    // {
    //   title: '手机号',
    //   dataIndex: 'phone',
    // },
    {
      title: '帐号类型',
      dataIndex: 'accountType',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`accountType_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '帐号状态',
      dataIndex: 'state',
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`state_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '角色',
      dataIndex: 'roleNameList',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'account',
      label: '账号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'nickname',
      label: '姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
     {
      field: 'queryRoleId',
      label: '角色',
      component: 'ApiSelect',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formModel }) => {
        return {
          api: specialGetMerchantRole,
          resultField: 'data',
          params: {
            nextLevelFlag:formModel['nextLevelFlag']
          },
          alwaysLoad: true,
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.roleName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'roleName', value: 'autoId' },
        };
      },
    },
     {
      field: 'nextLevelFlag',
      component: 'Checkbox',
      label: '包含下级',
      colProps: { span: 6 },
      defaultValue: true,
      ifShow:userStore?.getUserInfo?.isOneLevel
    },
      {
      field: 'queryCompanyId',
      label: '二级商户',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => userStore?.getUserInfo?.isOneLevel&&values.nextLevelFlag,
      componentProps: ({}) => {
        return {
          api: getSecondLevelCompanyInfos,
          // params: {
          //   pageSize: 0,
          // },
          resultField: 'data',
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'companyName', value: 'companyId' },
        };
      },
    },
  ];
};

export const modalFormItem = (companyId,accountType): FormSchema[] => {
  return [
    {
      field: 'account',
      label: '账号',
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'nickname',
      label: '姓名',
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'confirmRoleIds',
      label: '选择角色',
      required: true,
      component: 'ApiSelect',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      // ifShow: userStore?.getUserInfo?.companyId === companyId || 'merchantManage' === accountType ,
      componentProps: () => {
        return {
          api: list,
          params: {
            pageSize: 0,
          },
          mode: 'multiple',
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.roleName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'roleName', value: 'autoId' },
        };
      },
    },
  ];
};


export const accountModalFormItem = (): FormSchema[] => {
  return [
    {
      field: 'companyId',
      label: '选择商户',
      required: false,
      component: 'ApiTreeSelect',
      rulesMessageJoinLabel: true,
      componentProps: function ({ formModel }) {
        return {
          api: threeList,
          fieldNames: { label: 'companyName', value: 'companyId', children: 'children' },
          showSearch: true,
          immediate: true,
          alwaysLoad: true,
          treeNodeFilterProp: 'companyName',
          getPopupContainer: () => document.body,
        };
      },
    },
  ];
};