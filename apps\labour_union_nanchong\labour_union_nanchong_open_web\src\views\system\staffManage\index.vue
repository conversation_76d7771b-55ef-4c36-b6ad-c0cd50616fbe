<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
        >
          新增员工
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-tag
            v-if="record.account === userStore.getUserInfo.account"
            color="green"
            >当前角色</a-tag
          >
          <TableAction
            v-else
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                ifShow:userStore.getUserInfo.companyId===record.companyId&&record.isOneLevelCorrelation
              },
              {
                icon: 'carbon:password',
                label: '重置密码',
                type: 'primary',
                onClick: handlePwd.bind(null, record),
                ifShow:record.isOneLevelCorrelation
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                onClick: handleDelete.bind(null, record),
                ifShow:userStore.getUserInfo.companyId===record.companyId&&record.isOneLevelCorrelation
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '变更商户归属',
                type: 'primary',
                onClick: handleUpdate.bind(null, record),
                ifShow:userStore.getUserInfo.accountType==='openRoot' && record.accountType==='merchantStaff'
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <StaffModal
      @register="registerModal"
      @success="handleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
    <AccountCompanyModal
      @register="accountRegisterModal"
      @success="accountHandleSuccess"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import { useModal } from '@/components/Modal';
import { columns, formSchemas } from './data';
import StaffModal from './StaffModal.vue';
import AccountCompanyModal from './AccountCompanyModal.vue';
import { list, deleteLine, updateApi, saveApi, roleRelation, resetPassword, updateAccountCompany } from '@/api/system/staff';
import { useMessage } from '@monorepo-yysz/hooks';
import { map } from 'lodash-es';
import { useUserStore } from '@/store/modules/user';

const userStore = useUserStore();

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    submitOnChange: true,
    actionColOptions: { span: 3 },
  },
  searchInfo: { orderBy: 'create_time', sortType: 'desc' },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 400,
    dataIndex: 'action',
    fixed: undefined,
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

const [accountRegisterModal, { openModal:openUpdateModal, closeModal:closeUpdateModal }] = useModal();

// 新增
function handleClick() {
  openModal(true, { isUpdate: false, disabled: false });
}

// 重置密码
function handlePwd(record: any) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要重置${record.account}的密码？`,
    onOk: function () {
      resetPassword({
        todoValueList:[record.autoId]
      }).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `重置成功！` });
          reload();
        } else {
          createErrorModal({ content: `重置失败！${message}。` });
        }
      });
    },
  });
}

// 编辑
function handleEdit(record: Recordable<any>) {
  roleRelation({ bindingValue: record.userId }).then(data => {
    openModal(true, {
      isUpdate: true,
      disabled: false,
      record: { ...record, confirmRoleIds: map(data || [], v => v.roleId) },
    });
  });
}

// 变更归属
function handleUpdate(record: Recordable<any>) {
  openUpdateModal(true, {
      isUpdate: true,
      disabled: false,
      record: { ...record},
    });
}

// 详情
function handleView(record: Recordable<any>) {
  roleRelation({ bindingValue: record.userId }).then(data => {
    openModal(true, {
      isUpdate: true,
      disabled: true,
      record: { ...record, confirmRoleIds: map(data || [], v => v.roleId) },
    });
  });
}

// 删除
function handleDelete(record: Recordable<any>) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除${record.account || '当前数据'}？`,
    onOk: function () {
      deleteLine(record.autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功！` });
          reload();
        } else {
          createErrorModal({ content: `删除失败！${message}。` });
        }
      });
    },
  });
}

// 新增修改
function handleSuccess({ values, isUpdate }: Recordable<any>) {
  //TODO 该处根据业务选择api（该模式就替换saveOrUodate）或saveOrUodate（该模式删除api相关代码）。
  const api = isUpdate ? updateApi : saveApi;
  api(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '编辑' : '新增'}成功！`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '编辑' : '新增'}失败！${message}。`,
      });
    }
  });
}


// 修改商户归属
function accountHandleSuccess({ values}: Recordable<any>) {
  //TODO 该处根据业务选择api（该模式就替换saveOrUodate）或saveOrUodate（该模式删除api相关代码）。
  updateAccountCompany(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `编辑成功！`,
      });
      reload();
      closeUpdateModal();
    } else {
      createErrorModal({
        content: `编辑失败！${message}。`,
      });
    }
  });
}
</script>
