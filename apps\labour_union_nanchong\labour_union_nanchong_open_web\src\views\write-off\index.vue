<template>
  <div>
    <BasicTable @register="registerTable" :class="$style['write-off']">
      <template #toolbar>
        <a-button
            type="primary"
            @click="handleDowen"
            :loading="spinning"
        >导出</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleView.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <WriteOffModal
      @register="registerModal"
      :can-fullscreen="false"
      width="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import {exportColumn, recordColumns, recordFormSchemas} from './data';
import WriteOffModal from './RecordModal.vue';
import {exportRecord, list} from '@/api/write-off';
import { useMessage } from '@monorepo-yysz/hooks';
import {computed, ref, unref} from 'vue';
import dayjs from "dayjs";
import {downloadByUrl} from "@monorepo-yysz/utils";
import {useUserStore} from "@/store/modules/user";

const { createErrorModal } = useMessage();

const schemas = computed(() => {
  return recordFormSchemas();
});
const exportParams = ref<Recordable>({});
const [registerTable] = useTable({
  rowKey: 'autoId',
  columns: recordColumns(),
  showIndexColumn: false,
  api: list,
  beforeFetch(params){

    const { receiveDateRange, destroyDateRange, ...p } = params;
    p.orderBy = 'check_time'
    p.sortType = 'desc'
    //p.checkCompanyId = useUserStore().getUserInfo.companyId
    if (receiveDateRange?.length) {
      p.receiveStartTime = receiveDateRange[0] + ' 00:00:00';
      p.receiveEndTime = receiveDateRange[1] + ' 23:59:59';
    }
    if (destroyDateRange?.length) {
      p.destroyStartTime = destroyDateRange[0] + ' 00:00:00';
      p.destroyEndTime = destroyDateRange[1] + ' 23:59:59';
    }
    exportParams.value = p;
    return p
  },
  formConfig: {
    labelWidth: 120,
    schemas: schemas,
    autoSubmitOnEnter: true,
    submitOnChange: true,
  },
  searchInfo: {},
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 120,
    dataIndex: 'action',
    fixed: undefined,
  },
});

const [registerModal, { openModal }] = useModal();

const spinning = ref(false)
function handleDowen() {
  // const {  couponId,activityId } = unref(exportParams);
  // if ( !couponId && !activityId) {
  //   createErrorModal({ content: '请选择指定活动或者票券进行导出' });
  //   return;
  // }
  spinning.value = true;
  exportRecord({ ...unref(exportParams),state:'y', exportColumn }).then(res => {
    const url = window.URL.createObjectURL(res);
    const fileName = `领取记录-${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`;
    downloadByUrl({
      url,
      fileName,
    });
    spinning.value = false;
  });
}
// 详情
function handleView(record: Recordable<any>) {
  openModal(true, {
    record,
  })
}
</script>
<style lang="less" module>
.write-off{
  :global {
    .ant-select-selection-item{
     // width: 120px;
    }
  }
}
</style>
