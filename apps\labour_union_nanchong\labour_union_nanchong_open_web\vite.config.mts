import { defineApplicationConfig } from '@monorepo-yysz/vite-config';
import { generateModifyVars } from './build/modifyVars';
import path from 'path';
export default defineApplicationConfig({
  overrides: {
    optimizeDeps: {
      include: [
        'qrcode',
        '@iconify/iconify',
        'ant-design-vue/es/locale/zh_CN',
        'ant-design-vue/es/locale/en_US',
        'lottie-web',
        'sortablejs',
        '@zxcvbn-ts/core',
        'cropperjs',
      ],
    },
    build: {
      outDir: 'ncsfpt',
    },
    server: {
      host: true,
      port: 3701,
      proxy: {
        '/basic-api': {
          target: 'http://**************:28000',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/basic-api`), ''),
          // only https
          // secure: false
        },
        '/upload': {
          target: 'http://**************:28000',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/upload`), ''),
        },
        '/map-api': {
          target: 'https://api.map.baidu.com',
          changeOrigin: true,
          ws: true,
          rewrite: path => path.replace(new RegExp(`^/map-api`), ''),
        },
      },
      open: false, // 项目启动后，自动打开
      warmup: {
        clientFiles: ['./index.html', './src/{views,components}/*'],
      },
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          modifyVars: generateModifyVars(),
        },
      },
    },
  },
});
