import { BasicResponse } from '@monorepo-yysz/types';
import { defHttp } from '/@/utils/http/axios';

enum ConvenienceApplication {
  findList = '/findAppList', //查看列表
  batchAudit = '/audit', //审核
  getById = '/getById', //详情
}

function getApi(url?: string) {
  if (!url) {
    return '/manage/joinUnionAuditRecord';
  }
  return '/manage/joinUnionAuditRecord' + url;
}

//查询列表
export const list = params => {
  return defHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//审核
export const app = params => {
  return defHttp.post<BasicResponse>(
    {
      url: getApi(ConvenienceApplication.batchAudit),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//根据主键查询详情
export const getById = params => {
  return defHttp.get<BasicResponse>(
    {
      url: getApi(ConvenienceApplication.getById),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
