import { aliHttp, h5Http } from '/@/utils/http/axios';
// 首页数据统计
export const aliFirstPage = (params: any) => {
  return aliHttp.get({
    url: '/getH5FirstPage',
    params,
  });
};
// table列表 ALI排名展示
export const aliTableList = (params: any) => {
  return aliHttp.get({
    url: '/getTableList',
    params,
  });
};
// ALI数据展示-指数排名
export const aliDataShow = (params: any) => {
  return aliHttp.get({
    url: '/getTwoThreePageInfo',
    params,
  });
};
//获取工作平台历史趋势
export const getHistoryTrend = (params: any) => {
  return aliHttp.get({
    url: '/getALIHistory',
    params,
  });
};
export const aliDataSummary = (params: any) => {
  return h5Http.get({
    url: '/aliDataSummary/currentMonth',
    params,
  });
};
// 导出
export const exportData = (params: any) => {
  return aliHttp.get(
    {
      url: '/downAliTable?dateRange=' + params.dateRange,
      responseType: 'blob',
    },
    { isTransformResponse: false }
  );
};
