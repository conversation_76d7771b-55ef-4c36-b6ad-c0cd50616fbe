import { openHttp, h5Http } from '/@/utils/http/axios';
// 商家入驻后活跃度情况分析
export function getActiveAnalysis() {
  return openHttp.get({
    url: '/openDataSummary/settledActivelyAnalyse',
  });
}
//区域优惠券发放情况
export function getCouponAnalysis() {
  return h5Http.get({
    url: '/dataSummary/couponEmitAnalysisByArea',
  });
}
// 区域优惠券近半年发放情况
export function getCouponAnalysisSixMonth(params: any) {
  return h5Http.get({
    url: '/dataSummary/couponEmitAnalysisByHalf',
    params,
  });
}
// 商家入驻情况
export function getSettledAnalysis() {
  return openHttp.get({
    url: '/openDataSummary/merchantSettledAnalyse',
  });
}
// 商家的类型分析
export function getMerchantTypeAnalysis() {
  return openHttp.get({
    url: '/openDataSummary/merchantType',
  });
}
// 活动区域数据分析
export function getActivityAreaAnalysis() {
  return h5Http.get({
    url: '/dataSummary/activityInfoAreaAnalyse',
  });
}
// 活动半年数据分析
export function getActivitySixMonthAnalysis(params: any) {
  return h5Http.get({
    url: '/dataSummary/activityInfoHalf',
    params,
  });
}
// 普惠商品销量排行榜
export function getSalesRanking() {
  return openHttp.get({
    url: '/openDataSummary/productList',
  });
}
