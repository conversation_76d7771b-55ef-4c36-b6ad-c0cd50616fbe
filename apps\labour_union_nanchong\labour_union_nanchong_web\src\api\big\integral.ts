import { dataCenterHttp, h5Http, openHttp } from '/@/utils/http/axios';
// 近半年增长分析
export function getGrowthAnalysis(params?: Recordable) {
  return dataCenterHttp.get({
    url: '/customIntegral/halfYearSummary',
    params,
  });
}

// 积分增长来源分析
export function getIntegralSourceAnalysis() {
  return dataCenterHttp.get({
    url: '/customIntegral/integralSource',
  });
}

// 积分段人数分析
export function getIntegralSegmentAnalysis() {
  return dataCenterHttp.get({
    url: '/customIntegral/integralSegmentationCountPc',
  });
}
// 积分阶段各个区县的积分人数
export function getIntegralSegmentDistrictAnalysis(levelCode: string) {
  return dataCenterHttp.get({
    url: '/customIntegral/areaNumber?levelCode=' + levelCode,
  });
}

//总积分
export function getTotalIntegral() {
  return dataCenterHttp.get({
    url: '/customIntegral/dataSummary',
  });
}

// 查询积分增长来源分析
export const addIntegralResource = (params?: Recordable) => {
  return dataCenterHttp.get<Recordable>({
    url: '/customIntegral/addIntegralResource',
    params,
  });
};
// 查询各区域积分数分析
export const dataSummaryByAreaName = (params?: Recordable) => {
  return dataCenterHttp.get<Recordable>({
    url: '/customIntegral/dataSummaryByAreaName',
    params,
  });
};
// 查询积分排行前50名
export const getIntegralTop50Rank = (params?: Recordable) => {
  return dataCenterHttp.get<Recordable>({
    url: '/customIntegral/getIntegralTop50Rank',
    params,
  });
};
// 近半年积分活动分析
export const getIntegralActivityAnalysis = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/integralActivity',
    params,
  });
};
//近7日积分抽奖数据分析
export const getIntegralLotteryData = (params?: Recordable) => {
  return h5Http.get<Recordable>({
    url: '/dataSummary/pointsLotterySevenDays',
    params,
  });
};

// 近7日打卡数据分析
export const getSignData = (params?: Recordable) => {
  return dataCenterHttp.get<Recordable>({
    url: '/dataCenterBusiness/userSignSevenDays',
    params,
  });
};

// 积分商品排行榜
export const getIntegralGoodsRank = (params?: Recordable) => {
  return openHttp.get<Recordable>({
    url: '/openDataSummary/salesList',
    // params,
  });
};
