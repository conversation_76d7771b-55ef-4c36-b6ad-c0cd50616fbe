import { h5Http } from '/@/utils/http/axios';

// 阵地服务分析页--阵地类型统计--改过
export const positionTypeList = params => {
  return h5Http.get({
    url: '/dataSummary/countPositionType',
    params,
  });
};
// 各区域服务征地分布分析
export const countPositionRegion = params => {
  return h5Http.get({
    url: '/dataSummary/countPositionRegion',
    params,
  });
};
// 阵地所属工会下拉
export const belongUninonSelect = () => {
  return h5Http.get({
    url: '/dataSummary/getAffiliationUnion',
  });
};
// 获取服务类型下拉
export const getServiceType = () => {
  return h5Http.get({
    url: '/dataSummary/getServiceType',
  });
};
// 阵地列表
export const getPositionList = params => {
  return h5Http.get({
    url: '/dataSummary/getPositionTabulation',
    params,
  });
};
// 阵地详情
export const getPositionDetail = params => {
  return h5Http.get({
    url: '/dataSummary/getPositionDetails',
    params,
  });
};
