import { BasicResponse } from '@monorepo-yysz/types';
import { openHttp } from '@/utils/http/axios';

enum API {
  findList = '/findVoList',
  noBindingProduct = '/noBindingProduct',
  saveApi = '/addBirthDayProduct',
  enableOrDisableProduct = '/enableOrDisableProduct',
  removeBirthdayProduct = '/removeBirthdayProduct',
  enableOrDisable = '/enableOrDisable'
}

function getApi(url?: string) {
  if (!url) {
    return '/birthdayProduct';
  }
  return '/birthdayProduct' + url;
}

// 列表
export const list = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(API.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

export const noBindingProduct = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(API.noBindingProduct), params },
    {
      isTransformResponse: false,
    }
  );
};

// 新增
export const saveApi = (params: Recordable) => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(API.saveApi),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const enableOrDisableProduct = (params: Recordable) => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(API.enableOrDisableProduct),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const deleteLine = params => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(API.removeBirthdayProduct),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};


export const enableOrDisable = (params: Recordable) => {
  return openHttp.post<BasicResponse>(
    {
      url: getApi(API.enableOrDisable),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};