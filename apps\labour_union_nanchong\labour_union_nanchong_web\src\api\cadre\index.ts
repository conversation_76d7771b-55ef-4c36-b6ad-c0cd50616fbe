import { dataCenterHttp } from '@/utils/http/axios';

enum Cadre {
  paged = '/cadrePaged', //获取干部信息（分页）
  all = '/cadreAll', //获取干部信息（不分页）
  pagedAccount = '/cadrePagedAccount', //获取干部信息（分页，带干部账号id）
  find = '/cadreFind', //查询单个干部信息
  pagedAll = '/cadrePagedAll', //获取干部信息（分页，包含下级）
  depts = '/deptRetrieveDeptPaged',
}

function getApi(url?: string) {
  if (!url) {
    return '/unionBasicData';
  }
  return '/unionBasicData' + url;
}

export const all = params => {
  return dataCenterHttp.get(
    {
      url: getApi(Cadre.all),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const paged = params => {
  return dataCenterHttp.get(
    {
      url: getApi(Cadre.paged),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const pagedAccount = params => {
  return dataCenterHttp.get(
    {
      url: getApi(Cadre.pagedAccount),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const find = (params, isTransformResponse?: boolean) => {
  return dataCenterHttp.get(
    {
      url: getApi(Cadre.find),
      params,
    },
    {
      isTransformResponse,
    }
  );
};

export const pagedAll = params => {
  return dataCenterHttp.get(
    {
      url: getApi(Cadre.pagedAll),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//部门
export const depts = params => {
  return dataCenterHttp.get<Recordable>({
    url: getApi(Cadre.depts),
    params,
  });
};
