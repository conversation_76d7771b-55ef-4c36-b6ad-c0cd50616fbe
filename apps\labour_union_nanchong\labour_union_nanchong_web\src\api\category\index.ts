import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http, dataCenterHttp } from '/@/utils/http/axios';

enum Category {
  getOneColumn = '/h5CategoryInfo',
  setCategorySort = '/setCategorySort',
  getReferToCategory = '/getReferToCategory',
  getList = '/getParentCategoryList', //父级栏目
  getTree = '/rootGetTree', //tree
  getMaxSortNumber = '/getMaxSortNumber',
  getCategoryNamePinyin = '/getCategoryNamePinyin',
  getUnionList = '/getUnionPagedAll',
}

function getApi(url?: string) {
  if (!url) {
    return '/h5CategoryInfo';
  }
  return '/h5CategoryInfo' + url;
}

//列表
export const listTree = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Category.getTree), params },
    {
      isTransformResponse: false,
    }
  );
};

//获取最大排序号
export const maxSortNumber = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(Category.getMaxSortNumber),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 栏目名称转栏目编码（中文转拼音）
export const getCategoryNamePinyin = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(Category.getCategoryNamePinyin),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//列表
export const listTreeBind = params => {
  return h5Http.get<Recordable[]>({ url: getApi(Category.getTree), params });
};

//add
export const addCategory = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(), params },
    {
      isTransformResponse: false,
    }
  );
};

//delete
export const deleteCategory = params => {
  return h5Http.delete<BasicResponse>(
    { url: getApi(), params },
    {
      isTransformResponse: false,
    }
  );
};

//update
export const updateCategory = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(), params },
    {
      isTransformResponse: false,
    }
  );
};
//批量设置栏目排序号
export const setCategorySort = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Category.setCategorySort), params },
    {
      isTransformResponse: false,
    }
  );
};

//查询参照栏目列表
export const getReferToCategory = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Category.getReferToCategory), params },
    {
      isTransformResponse: false,
    }
  );
};

//getOneColumn
export const getOneColumn = params => {
  return h5Http.get<BasicResponse>(
    { url: Category.getOneColumn, params },
    {
      isTransformResponse: false,
    }
  );
};

//getList
export const getList = (params?: any) => {
  return h5Http.get<Recordable[]>({ url: getApi(Category.getList), params });
};

//tree
export const getUnionTree = (params?: any) => {
  return h5Http.get<Recordable[]>({ url: '/h5CategoryInfo/getUnionTree', params });
};

export const findUnionList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Category.getUnionList), params },
    {
      isTransformResponse: false,
    }
  );
};
