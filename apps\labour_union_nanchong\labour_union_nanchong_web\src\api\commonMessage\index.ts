import { BasicResponse } from '@monorepo-yysz/types';
import { manageHttp } from '/@/utils/http/axios';

enum MessageTemplate {
  findList = '/findVoList',
  delete = '/removeById',
  saveOrUpdate = '/saveOrUpdate', //新增/修改消息模板
}

function getApi(url?: string) {
  if (!url) {
    return '/messageTemplate';
  }
  return '/messageTemplate' + url;
}

//列表
export const list = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(MessageTemplate.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getApi(MessageTemplate.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteMessageTemplate = id => {
  return manageHttp.delete<BasicResponse>(
    {
      url: getApi(MessageTemplate.delete) + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};
