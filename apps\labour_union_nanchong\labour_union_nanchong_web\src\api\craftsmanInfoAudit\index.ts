import { BasicResponse } from '@monorepo-yysz/types';
import { manageHttp,h5Http } from '/@/utils/http/axios';

enum ConvenienceApplication {
  findList = '/findVoList', //查看列表
  getDetails = '/getVoByDto', //详情
  app = '/auditModelWorkerCertification', //审核
}

function getApi(url?: string) {
  if (!url) {
    return '/modelWorkerAuditRecord';
  }
  return '/modelWorkerAuditRecord' + url;
}

//查询列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//根据主键查询详情
export const getDetails = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.getDetails), params },
    {
      isTransformResponse: false,
    }
  );
};

//审核
export const app = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(ConvenienceApplication.app),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
