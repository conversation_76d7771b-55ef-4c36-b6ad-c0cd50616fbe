import { BasicResponse } from '@monorepo-yysz/types';
import { manageHttp } from '/@/utils/http/axios';

enum ConvenienceApplication {
  findList = '/findVoList', //查看列表
  getDetails = '/getVoByDto', //详情
  logicDelete = '/updateLogicDeleteById', //逻辑删除
  updateInfo = '/updateCraftsmanInfo', //修改信息
  adminInsert = '/adminInsert', //管理员预设新增工匠工作室
}

function getApi(url?: string) {
  if (!url) {
    return '/craftsmanInfo';
  }
  return '/craftsmanInfo' + url;
}

//查询列表
export const list = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//查询详情
export const getDetails = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(ConvenienceApplication.getDetails), params },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const getDelete = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getApi(ConvenienceApplication.logicDelete),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//修改信息
export const updateInfo = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getApi(ConvenienceApplication.updateInfo),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//管理员预设新增工匠
export const adminInsert = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getApi(ConvenienceApplication.adminInsert),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
