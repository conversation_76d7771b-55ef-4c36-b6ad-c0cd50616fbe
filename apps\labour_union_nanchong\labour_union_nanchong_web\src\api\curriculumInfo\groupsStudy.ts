import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

//兴趣小组列表
export const groupInfoFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/groupsStudy/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//详情
export const getVoByDto = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/groupsStudy/getVoByDto',
      params,
    },
    { isTransformResponse: false }
  );
};
//新增
export const saveOrUpdateByDTO = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/groupsStudy/saveOrUpdateByDTO',
      params,
    },
    { isTransformResponse: false }
  );
};
//审核小组
export const auditGroups = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/groupsStudy/auditGroups',
      params,
    },
    { isTransformResponse: false }
  );
};

//删除学习小组
export const updateLogicDeleteById = params => {
  return h5Http.delete<BasicResponse>(
      {
      url: '/groupsStudy/updateLogicDeleteById?groupId='+params,
      params,
      },
      { isTransformResponse: false }
  );
};
//删除学习小组成员
export const deleteInsertRecord = params => {
    return h5Http.delete<BasicResponse>(
        {
        url: '/groupsInsertRecord?autoId='+params,
        params,
        },
        { isTransformResponse: false }
    );
};
//小组成员审核列表
export const groupsInsertRecord = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/groupsInsertRecord/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};

//审核小组成员
export const auditInsertGroups = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/groupsInsertRecord/auditInsertGroups',
      params,
    },
    { isTransformResponse: false }
  );
};