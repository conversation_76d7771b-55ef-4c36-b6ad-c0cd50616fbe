import { h5Http } from '/@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum OBJ {
  findList = '/findList',
  view = '/getVoById',
  auditApply = '/auditApply',
}

function getApi(url?: string) {
  if (!url) {
    return '/employmentCompanyApplyRecordPengAn';
  }
  return '/employmentCompanyApplyRecordPengAn' + url;
}

//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//审核入驻
export const auditApply = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(OBJ.auditApply),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//view
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = (autoId: number[] | number) => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};
