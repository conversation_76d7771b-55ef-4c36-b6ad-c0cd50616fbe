import { h5Http } from '/@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

//发布招聘信息
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/employmentRecruit/unionSaveOrUpdate',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取企业招聘信息列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/employmentPublicMethod/unionFindVoList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取招聘信息投递的简历列表
export const findEmploymentUserRecruitRelationVoList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/employmentPublicMethod/findEmploymentUserRecruitRelationVoList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取招聘信息投递的评价列表
export const RecruitFindVoList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/employmentRecruitEvaluatePengAn/findVoList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: '/employmentPublicMethod/unionRemoveById' + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取行业信息
export const position = params => {
  return h5Http.get<BasicResponse>({
    url: '/employmentPosition/findVoList',
    params,
  });
};

//信息
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/employmentPublicMethod/getEmploymentRecruitByEmploymentRecruitId',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// (发布&&撤销发布)
export const release = params => {
  return h5Http.post<BasicResponse>(
    { url: '/employmentRecruit/release', params },
    {
      isTransformResponse: false,
    }
  );
};

export const getParentEmploymentPositionList = params => {
  return h5Http.get(
    {
      url: '/employmentPosition/getParentEmploymentPositionList',
      params,
    },
    {
      isTransformResponse: true,
    }
  );
};
