import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum hazardInfo {
  findList = '/findVoList',
  auditHazard = '/auditHazard'
}

function getApi(url?: string) {
  if (!url) {
    return '/hazardReport';
  }
  return '/hazardReport' + url;
}

//列表查询
export const list = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(hazardInfo.findList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};


//删除
export const deleteLine = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const auditHazard = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(hazardInfo.auditHazard),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
