import { h5Http } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum GroupLabel {
  base = "/interestGroupLabel",
  //分页查询
  findList = '/findList',
  getMaxNum = '/getMaxNum',
}

function getApi(url?: string) {
  if (!url) {
    return GroupLabel.base;
  }
  return GroupLabel.base + url;
}

//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(GroupLabel.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//序号最大值
export const getMaxNum = params => {
  return h5Http.get<BasicResponse>(
      { url: getApi(GroupLabel.getMaxNum), params },
  );
};

export const deleteLine = autoId => {
  return h5Http.delete<BasicResponse>(
      {url: `${getApi()}?autoId=${autoId}`},
      {
        isTransformResponse: false,
      }
  )
}

