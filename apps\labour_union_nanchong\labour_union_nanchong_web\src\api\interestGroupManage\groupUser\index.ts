import { h5Http } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum ApiObj {
  base = "/interestGroupUserInfo",
  //分页查询
  findList = '/findVoList',
  groupUserList = '/groupUserList',
  deleteGroupUser = '/interestGroupUserRelation',
  updateState = '/interestGroupUserRelation/updateState'
}

function getApi(url?: string) {
  if (!url) {
    return ApiObj.base;
  }
  return ApiObj.base + url;
}

//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(ApiObj.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

export const groupUserList = params => {
  return h5Http.get<BasicResponse>(
      { url: getApi(ApiObj.groupUserList), params },
      {
        isTransformResponse: false,
      }
  );
};

export const deleteLine = params => {
    return h5Http.delete<BasicResponse>(
        { url: ApiObj.deleteGroupUser, params },
        {
            isTransformResponse: false,
        }
    );
};

export const updateState = params => {
    return h5Http.post<BasicResponse>(
        { url: ApiObj.updateState, params },
        {
            isTransformResponse: false,
        }
    );
};
