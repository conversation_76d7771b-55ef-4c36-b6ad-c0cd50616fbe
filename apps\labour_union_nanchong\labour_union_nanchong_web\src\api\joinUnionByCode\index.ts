import { BasicResponse } from '@monorepo-yysz/types';
import { manageHttp } from '/@/utils/http/axios';

enum CommonApplication {
  findList = '/findList',
  saveOrUpdate = '/saveOrUpdate',
  removeById = '/removeById',
  ImageForQrcCode = '/getEnterUnionQrCode',
}
function getApi(url?: string) {
  if (!url) {
    return '/scanOfUnion';
  }
  return '/scanOfUnion' + url;
}

function getManagerApi(url?: string) {
  if (!url) {
    return '/scanOfUnionAdmin';
  }
  return '/scanOfUnionAdmin' + url;
}

//查询码上入会列表
export const findListForCode = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(CommonApplication.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增或修改/启用禁用
export const saveOrUpdate = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getApi(CommonApplication.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const removed = id => {
  return manageHttp.delete<BasicResponse>(
    { url: getApi(CommonApplication.removeById) + `?autoId=${id}` },
    {
      isTransformResponse: false,
    }
  );
};

//生成二维码返回
export const viewQrCode = params => {
  return manageHttp.get<BasicResponse>(
    {
      url: getApi(CommonApplication.ImageForQrcCode),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//查询对应工会的管理员
export const findManagerList = params => {
  return manageHttp.get<BasicResponse>(
    { url: getManagerApi(CommonApplication.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//工会绑定对应管理员
export const bindManagerForUnion = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getManagerApi(CommonApplication.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除绑定管理员
export const removeManagerById = id => {
  return manageHttp.delete<BasicResponse>(
    { url: getManagerApi(CommonApplication.removeById) + `?autoId=${id}` },
    {
      isTransformResponse: false,
    }
  );
};

//新增或修改/启用禁用管理员
export const saveOrUpdateManager = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getManagerApi(CommonApplication.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
