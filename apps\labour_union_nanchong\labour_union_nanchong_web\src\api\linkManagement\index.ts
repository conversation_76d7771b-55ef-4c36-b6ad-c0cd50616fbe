import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

//列表
export const liveInfoFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/liveInfo/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//新增或更新
export const saveOrUpdateByDTO = params => {
    return h5Http.post<BasicResponse>(
      {
        url: '/liveInfo/saveOrUpdateByDTO',
        params,
      },
      { isTransformResponse: false }
    );
  };

//删除
export const deleteLiveInfo = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: '/liveInfo?autoId=' + id,
    },
    { isTransformResponse: false }
  );
};

//列表详情
export const getVoByDto = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/liveInfo/getVoByDto',
      params,
    },
    { isTransformResponse: false }
  );
};


//启用禁用
export const publishStatus = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/liveInfo/publishStatus',
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

//开播关播
export const openStatus = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/liveInfo/isOpenStatus',
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

