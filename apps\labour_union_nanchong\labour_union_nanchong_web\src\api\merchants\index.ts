import { BasicResponse } from '@monorepo-yysz/types';
import { openHttp,dataCenterHttp } from '/@/utils/http/axios';

enum OBJ {
  insert = '/initOpenCustomer', //工会直接新增商家
  update = '/updateCompanyMoreInfoByCompanyId', //修改商家
  findList = '/findVoList',
  view = '/getByCompanyId',
  removeGiftCompany = '/removeGiftCompany',
  chooseGiftCompany = '/chooseGiftCompany',
  findChildList = '/findChildList',
  deleteCompanyByCompanyId = '/deleteCompanyByCompanyId',
  queryExistBusinessByCompanyId = '/queryExistBusinessByCompanyId',
}

function getApi(url?: string) {
  if (!url) {
    return '/openCompanyInfo';
  }
  return '/openCompanyInfo' + url;
}

// 列表
export const list = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 子级列表
export const findChildList = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    { url: getApi(OBJ.findChildList), params },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return openHttp.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//工会新增商户
export const insertMerchant = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(OBJ.insert), params },
    {
      isTransformResponse: false,
    }
  );
};

//修改商户主体信息(资质信息)
export const updateMerchant = params => {
  return openHttp.post<BasicResponse>(
    { url: getApi(OBJ.update), params },
    {
      isTransformResponse: false,
    }
  );
};

//选择一岁一礼商家
export const chooseGiftCompany = params => {
  return openHttp.post<BasicResponse>(
      { url: getApi(OBJ.chooseGiftCompany), params },
      {
        isTransformResponse: false,
      }
  );
};


//移除一岁一礼商家
export const removeGiftCompany = params => {
  return openHttp.post<BasicResponse>(
      { url: getApi(OBJ.removeGiftCompany), params },
      {
        isTransformResponse: false,
      }
  );
};

//下架商户
export const deleteCompanyByCompanyId = params => {
  return openHttp.delete<BasicResponse>(
      { url: getApi(OBJ.deleteCompanyByCompanyId)+ `?companyId=${params}`},
      {
        isTransformResponse: false,
      }
  );
};

//查询商户是否关联数据
export const queryExistBusinessByCompanyId = params => {
  return openHttp.get<BasicResponse>(
      { url: getApi(OBJ.queryExistBusinessByCompanyId)+ `?companyId=${params}`},
      {
        isTransformResponse: false,
      }
  );
};

// 重置密码
export const resetPassword = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: '/accountInfo/resetPassword',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};