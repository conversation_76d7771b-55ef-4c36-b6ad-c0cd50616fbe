import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

enum NewsSpecial {
  getNewsSpecialList = '/manageFindList',
  getSpecialInfoByAutoId = '/getSpecialInfoByAutoId',
  addSpecialCategoryCorrelation = '/addSpecialCategoryCorrelation',
  getMaxSortNumber = '/getMaxSortNumber',
  getRegularSpecial = '/getRegularSpecial',
  getReferToSpecial = '/getReferToSpecial',
  setSpecialSort = '/setSpecialSort',
  setRegularSpecial = '/setRegularSpecial',
}

function getApi(url?: string) {
  if (!url) {
    return '/h5SpecialInfo';
  }
  return '/h5SpecialInfo' + url;
}

//列表
export const getNewsSpecialList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(NewsSpecial.getNewsSpecialList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//获取最大排序号
export const maxSortNumber = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(NewsSpecial.getMaxSortNumber),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//修改
export const amend = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//新增
export const addNewsSpecial = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteNewsSpecial = params => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//详情
export const getView = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(NewsSpecial.getSpecialInfoByAutoId),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//查询是否有固定专题
export const getRegularSpecial = () => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(NewsSpecial.getRegularSpecial),
    },
    {
      isTransformResponse: false,
    }
  );
};

//查询参照专题列表
export const getReferToSpecial = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(NewsSpecial.getReferToSpecial),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//批量设置专题排序号
export const setSpecialSort = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(NewsSpecial.setSpecialSort),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//设置固定专题
export const setRegularSpecial = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(NewsSpecial.setRegularSpecial),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//栏目关系
export const newsSpecialCategory = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(NewsSpecial.addSpecialCategoryCorrelation),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
