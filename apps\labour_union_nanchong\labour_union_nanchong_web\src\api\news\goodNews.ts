import { h5Http } from '/@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum OBJ {
  findVoList = '/findVoList',
  levelFindVoList = '/levelFindVoList',
  view = '/',
  newsGoodInfoToNewsInfoVO = '/newsGoodInfoToNewsInfoVO',
  saveOrUpdate = '/saveOrUpdateByDTO',
  newsGoodAudit = '/newsGoodAudit',
  substrateReportNews = '/substrateReportNews',
  countyLevelReportNews = '/countyLevelReportNews',
  getUpperType = '/getUpperType',
}

function getApi(url?: string) {
  if (!url) {
    return '/h5newsGoodInfo';
  }
  return '/h5newsGoodInfo' + url;
}

//基层工会优文上报查询列表接口
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(OBJ.findVoList), params },
    {
      isTransformResponse: false,
    }
  );
};
//市级和区县级工会优文上报查询列表接口
export const levelFindVoList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(OBJ.levelFindVoList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(OBJ.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
//审核
export const newsGoodAudit = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(OBJ.newsGoodAudit),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//基层工会上报
export const substrateReportNews = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(OBJ.substrateReportNews),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//区县级工会上报
export const countyLevelReportNews = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(OBJ.countyLevelReportNews),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//判断当前登录工会 上级审核工会是市级还是区县级
export const getUpperType = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(OBJ.getUpperType),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//view
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(OBJ.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
//将优文上报对象 装成新闻vo对象
export const newsGoodInfoToNewsInfoVO = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(OBJ.newsGoodInfoToNewsInfoVO),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = (autoId: number[] | number) => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};
