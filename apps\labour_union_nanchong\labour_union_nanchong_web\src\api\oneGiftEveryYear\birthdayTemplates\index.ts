import { BasicResponse } from '@monorepo-yysz/types';
import {  h5Http } from '@/utils/http/axios';

enum LABEL {
    saveOrUpdate = '/saveOrUpdateByDTO',
    findVoList = '/findVoList',
    changeState = '/changeState',
}
function getApi(url?: string) {
    if (!url) {
        return '';
    }
    return '/birthdayTemplates' + url;
}
// 列表
export const findVoList = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(LABEL.findVoList), params },
        {
            isTransformResponse: false,
        }
    );
}
// 新增或修改
export const saveOrUpdate = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(LABEL.saveOrUpdate), params },
        {
            isTransformResponse: false,
        }
    );
}

// 修改公开状态
export const changeState = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(LABEL.changeState), params },
        {
            isTransformResponse: false,
        }
    );
}