import { BasicResponse } from '@monorepo-yysz/types';
import {  h5Http } from '@/utils/http/axios';

enum LABEL {
    findVoList = '/findVoList',
    saveOrUpdateByDTO = '/saveOrUpdateByDTO',
}
function getApi(url?: string) {
    if (!url) {
        return '/shortVideoDescription';
    }
    return '/shortVideoDescription' + url;
}
// 列表
export const findVoList = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(LABEL.findVoList), params },
        {
            isTransformResponse: false,
        }
    );
}
// 查询详情
export const getById = id => {
    return h5Http.get<BasicResponse>(
        { url: getApi()+ `?autoId=${id}` },
        {
            isTransformResponse: false,
        }
    );
}

// 新增或修改短视频说明
export const saveOrUpdateByDTO = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(LABEL.saveOrUpdateByDTO), params },
        {
            isTransformResponse: false,
        }
    );
}

//删除
export const deleteById = id => {
    return h5Http.delete<BasicResponse>(
        {
            url: getApi()+ `?autoId=${id}`,
        },
        {
            isTransformResponse: false,
        }
    );
};