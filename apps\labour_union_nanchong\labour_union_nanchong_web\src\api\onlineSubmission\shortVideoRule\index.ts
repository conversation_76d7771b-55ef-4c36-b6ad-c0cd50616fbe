import { BasicResponse } from '@monorepo-yysz/types';
import {  h5Http } from '@/utils/http/axios';

enum LABEL {
    findVoList = '/findVoList',
    changeState = '/changeState',
    changeValue = '/changeValue',
}
function getApi(url?: string) {
    if (!url) {
        return '/shortVideoRule';
    }
    return '/shortVideoRule' + url;
}
// 列表
export const findVoList = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(LABEL.findVoList), params },
        {
            isTransformResponse: false,
        }
    );
}

// 更改短视频规则启用状态
export const changeState = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(LABEL.changeState), params },
        {
            isTransformResponse: false,
        }
    );
}

// 更改短视频规则值
export const changeValue = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(LABEL.changeValue), params },
        {
            isTransformResponse: false,
        }
    );
}