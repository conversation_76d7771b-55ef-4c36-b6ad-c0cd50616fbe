import { BasicResponse } from '@monorepo-yysz/types';
import { defHttp } from '@/utils/http/axios';

// 绑定活动
export const bindActivity = params => {
  return defHttp.post<BasicResponse>(
    { url: '/points-activity-notice/bindActivity', params },
    { isTransformResponse: false }
  );
};

//  积分活动下发通知
export const saveOrUpdate = params => {
  return defHttp.post<BasicResponse>(
    { url: '/points-activity-notice/saveOrUpdate', params },
    { isTransformResponse: false }
  );
};

//  新增活动意见收集
export const add = params => {
  return defHttp.post<BasicResponse>(
    { url: '/opinion-collection/add', params },
    { isTransformResponse: false }
  );
};

//   获取意见反馈列表
export const collectionFindList = params => {
  return defHttp.get<BasicResponse>(
    { url: '/opinion-collection/findList', params },
    { isTransformResponse: false }
  );
};

// 获取积分活动通知列表;
export const noticeFindList = params => {
  return defHttp.get<BasicResponse>(
    { url: '/points-activity-notice/findList', params },
    { isTransformResponse: false }
  );
};

// 获取活动;
export const findByActivityCategory = params => {
  return defHttp.get<BasicResponse>(
    { url: '/activity/findByActivityCategory', params },
    { isTransformResponse: false }
  );
};
