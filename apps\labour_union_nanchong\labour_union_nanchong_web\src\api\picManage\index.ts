import { BasicResponse } from '@monorepo-yysz/types';
import { manageHttp } from '/@/utils/http/axios';

enum Banner {
  findList = '/findList',
  delete = '/removeById',
  getDetails = '/getById',
  saveOrUpdate = '/saveOrUpdate',
}

function getApi(url?: string) {
  if (!url) {
    return '/bannerPic';
  }
  return '/bannerPic' + url;
}

//列表
export const list = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(Banner.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return manageHttp.post<BasicResponse>(
    {
      url: getApi(Banner.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//删除
export const deleteLine = id => {
  return manageHttp.delete<BasicResponse>(
    {
      url: getApi(Banner.delete) + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};

//详情
export const getDetails = params => {
  return manageHttp.get<BasicResponse>(
    { url: getApi(Banner.getDetails), params },
    {
      isTransformResponse: false,
    }
  );
};
