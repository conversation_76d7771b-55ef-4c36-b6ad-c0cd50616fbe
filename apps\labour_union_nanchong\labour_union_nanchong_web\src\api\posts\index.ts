import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum Topic {
  base = '/postsTopics',
  findList = `${Topic.base}/findList`,
}

enum PostsRecord {
  base = '/postsRecord',
  findList = `${PostsRecord.base}/findList`,
  audit = `${PostsRecord.base}/audit`,
}

enum Comments {
  base = '/postsCommentRecord',
  findList = `${Comments.base}/findList`,
  audit = `${Comments.base}/audit`,
}

//话题列表
export const topicList = params => {
  return h5Http.get<BasicResponse>(
    { url: Topic.findList, params },
    {
      isTransformResponse: false,
    }
  );
};
//话题新增修改
export const saveOrUpdateTopic = params => {
  return h5Http.post<BasicResponse>(
    {
      url: Topic.base,
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
//话题删除
export const deleteTopic = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: Topic.base + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};

//话题内容列表
export const postsRecordList = params => {
  return h5Http.get<BasicResponse>(
      { url: PostsRecord.findList, params },
      {
        isTransformResponse: false,
      }
  );
};
//话题内容审核
export const auditPostsRecord = params => {
  return h5Http.post<BasicResponse>(
      {
        url: PostsRecord.audit,
        params,
      },
      {
        isTransformResponse: false,
      }
  );
};
//话题内容删除
export const deletePostsRecord = id => {
  return h5Http.delete<BasicResponse>(
      {
        url: PostsRecord.base + '?autoId=' + id,
      },
      {
        isTransformResponse: false,
      }
  );
};


//评论列表
export const postsCommentList = params => {
  return h5Http.get<BasicResponse>(
      { url: Comments.findList, params },
      {
        isTransformResponse: false,
      }
  );
};
//话题内容审核
export const auditPostsComment = params => {
  return h5Http.post<BasicResponse>(
      {
        url: Comments.audit,
        params,
      },
      {
        isTransformResponse: false,
      }
  );
};
//评论删除
export const deletePostsComment = id => {
  return h5Http.delete<BasicResponse>(
      {
        url: Comments.base + '?autoId=' + id,
      },
      {
        isTransformResponse: false,
      }
  );
};

