import { BasicResponse } from '@monorepo-yysz/types';
import { manageHttp } from '/@/utils/http/axios';

enum CorporateAudit {
  list = '/archiveProjectRecords/findList',
  audit = '/auditProjectDeclareRecords/addProject',
}

//项目申报审核记录列表
export const projectArchiveInfoList = params => {
  return manageHttp.get<BasicResponse>(
    { url: CorporateAudit.list, params },
    {
      isTransformResponse: false,
    }
  );
};
