import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum Competition {
  view = '/getOneCompetitionInfo',
  list = '/getCompetitionInfoList',
  release = '/release',
  getAuditCompetitionInfoList = '/getAuditCompetitionInfoList',
  batchAudit = '/batchAudit',
}

function getApi(url?: string) {
  if (!url) {
    return '/competitionInfo';
  }
  return '/competitionInfo' + url;
}

//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Competition.list), params },
    {
      isTransformResponse: false,
    }
  );
};

//申请审核获取所属竞赛主题名称下拉框的值
export const applyAuditGetCompetitionInfoList = params => {
  return h5Http.get<BasicResponse>(
    { url: '/competitionApplyAudit/applyAuditGetCompetitionInfoList', params },
    {
      isTransformResponse: false,
    }
  );
};

//一键批复,查询职工申请项目申报竞赛,审核通过且没有上传批复文件
export const getPassCompetitionApplyAuditList = params => {
  return h5Http.get<BasicResponse>(
    { url: '/competitionApplyAudit/getPassCompetitionApplyAuditList', params },
    {
      isTransformResponse: false,
    }
  );
};

//
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(), params },
    {
      isTransformResponse: false,
    }
  );
};

//一键批复提交接口
export const oneClickApproval = params => {
  return h5Http.post<BasicResponse>(
    { url: '/competitionApplyAudit/oneClickApproval', params },
    {
      isTransformResponse: false,
    }
  );
};

//
export const view = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Competition.view), params },
    {
      isTransformResponse: false,
    }
  );
};

export const deleteLine = params => {
  return h5Http.delete<BasicResponse>(
    { url: '/competitionApplyAudit/delCompetitionInfo', params },
    {
      isTransformResponse: false,
    }
  );
};

export const release = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Competition.release), params },
    {
      isTransformResponse: false,
    }
  );
};

//项目审核列表
export const auditList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Competition.getAuditCompetitionInfoList), params },
    {
      isTransformResponse: false,
    }
  );
};
//项目审核
export const batchAudit = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Competition.batchAudit), params },
    {
      isTransformResponse: false,
    }
  );
};

//申请审核列表
export const applyAuditList = params => {
  return h5Http.get<BasicResponse>(
    { url: '/competitionApplyAudit/getCompetitionApplyAuditList', params },
    {
      isTransformResponse: false,
    }
  );
};

//导出职工申请劳动竞赛数据
export const exportCompetitionApplyAudit = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/competitionApplyAudit/exportCompetitionApplyAudit',
      params,
      responseType: 'blob',
      timeout: 600 * 1000,
    },
    {
      isTransformResponse: false,
    }
  );
};

//申请审核
export const ApplyAudit = params => {
  return h5Http.post<BasicResponse>(
    { url: '/competitionApplyAudit/auditCompetitionApplyAudit', params },
    {
      isTransformResponse: false,
    }
  );
};
