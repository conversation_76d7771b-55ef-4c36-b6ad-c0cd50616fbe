import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http, dataCenterHttp } from '/@/utils/http/axios';

enum Competition {
  view = '/getWhiteListDetails',
  list = '/whiteListFindList',
  saveOrUpdate = '/saveOrUpdateWhiteList',
  deleteWhiteList = '/deleteWhiteList',
}

function getApi(url?: string) {
  if (!url) {
    return '/competitionWhiteList';
  }
  return '/competitionWhiteList' + url;
}

//列表
export const list = params => {
  return h5Http.get<any>(
    { url: getApi(Competition.list), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增或编辑区县白名单人员
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(Competition.saveOrUpdate), params },
    {
      isTransformResponse: false,
    }
  );
};

//删除报名单人员
export const deleteWhiteList = params => {
  return h5Http.delete(
    { url: getApi(Competition.deleteWhiteList), params },
    {
      isTransformResponse: false,
    }
  );
};

//根据主键id获取[区县白名单]详情
export const view = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(Competition.view), params },
    {
      isTransformResponse: false,
    }
  );
};

//获取会员信息（分页，含下级）
export const userPagedAll = params => {
  return dataCenterHttp.get(
    { url: '/unionBasicData/userPagedAll', params },
    {
      isTransformResponse: false,
    }
  );
};
