import { BasicResponse } from '@monorepo-yysz/types';
import {  h5Http } from '@/utils/http/axios';

enum CATEGORY {
    saveOrUpdate = '/saveOrUpdateByDTO',
    findVoList = '/findVoList',
    removeUrl = '/removeByCategoryId',
    updatePublish = '/updatePublish',
    getMaxSortNumber = '/getMaxSortNumber',
}
function getApi(url?: string) {
    if (!url) {
      return '';
    }
    return '/singleCategory' + url;
}
// 列表
export const categoryList = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(CATEGORY.findVoList), params },
        {
          isTransformResponse: false,
        }
    );
}
// 新增或修改
export const categorySaveOrUpdate = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(CATEGORY.saveOrUpdate), params },
        {
          isTransformResponse: false,
        }
    );
}
// 删除
export const categoryRemove = (id:string) => {
    return h5Http.delete<BasicResponse>(
        {
          url: getApi(CATEGORY.removeUrl) + '?categoryId=' + id,
        },
        {
          isTransformResponse: false,
        }
    );
}
// 发布或下架
export const publishControl = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(CATEGORY.updatePublish), params },
        {
          isTransformResponse: false,
        }
    );
}


//获取最大排序号
export const maxSortNumber = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(CATEGORY.getMaxSortNumber),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};