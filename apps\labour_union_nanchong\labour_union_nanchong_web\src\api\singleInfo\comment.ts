import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

enum COMMENT {
    commentAudit = '/commentAudit',
    findVoList = '/findVoList',
    removeUrl = '/removeById',
}
function getApi(url?: string) {
    if (!url) {
      return '';
    }
    return '/singlePostComment' + url;
}
// 列表
export const commentList = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(COMMENT.findVoList), params },
        {
          isTransformResponse: false,
        }
    );
}
// 审核评论
export const commentCheck = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(COMMENT.commentAudit), params },
        {
          isTransformResponse: false,
        }
    );
}
// 删除
export const commentRemove = (id:string) => {
    return h5Http.delete<BasicResponse>(
        {
          url: getApi(COMMENT.removeUrl) + '?autoId=' + id,
        },
        {
          isTransformResponse: false,
        }
    );
}