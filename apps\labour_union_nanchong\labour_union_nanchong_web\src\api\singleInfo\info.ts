import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

enum SINGLEINFO {
    findVoList = '/findVoList',//职工列表
    detailUrl = '/findDetail',//职工详情
    saveOrUpdate = '/saveOrUpdateByDTO',//职工新增或修改
    removeUrl = '/removeBySingleId',//职工删除
    blackUrl = '/updateBlackStatus',//系统黑名单操作
    checkListUrl = '/findRecordAuditList',//职工信息审核列表
    checkDetailUrl = '/findAuditDetail',//职工信息审核详情
    checkStatusUrl = '/singleAudit',//职工信息审核操作
}
function getApi(url?: string) {
    if (!url) {
      return '';
    }
    return '/singleInfo' + url;
}
// 职工列表
export const singleList = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(SINGLEINFO.findVoList), params },
        {
          isTransformResponse: false,
        }
    );
}

// 职工详情

export const singleDetail = (singleUserId:string) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(SINGLEINFO.detailUrl)+'?singleUserId='+singleUserId },
        {
          isTransformResponse: false,
        }
    );
}

// 新增或修改职工信息
export const singleSaveOrUpdate = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(SINGLEINFO.saveOrUpdate), params },
        {
          isTransformResponse: false,
        }
    );
}
// 删除
export const singleRemove = (singleId:string) => {
    return h5Http.delete<BasicResponse>(
        {
          url: getApi(SINGLEINFO.removeUrl) + '?singleId=' + singleId,
        },
        {
          isTransformResponse: false,
        }
    );
}

// 系统黑名单操作
export const updateBlackStatus = (params:any) => {
    return h5Http.post<BasicResponse>(
        {
          url: getApi(SINGLEINFO.blackUrl),
          params
        },
        {
          isTransformResponse: false,
        }
    );
}

// ==== 审核模块 ====

// 职工信息审核列表
export const checkList = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(SINGLEINFO.checkListUrl), params },
        {isTransformResponse: false,}    
    )
}
// 职工信息审核详情
export const checkDetail = (singleUserId:string) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(SINGLEINFO.checkDetailUrl)+'?singleUserId='+singleUserId },
        {isTransformResponse: false,}    
    )
}
// 职工信息审核操作
export const checkStatus = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(SINGLEINFO.checkStatusUrl), params },
        {isTransformResponse: false,}    
    )
}
// ==== end ====


