import { BasicResponse } from '@monorepo-yysz/types';
import {  h5Http } from '@/utils/http/axios';

enum LABEL {
    saveOrUpdate = '/saveOrUpdateByDTO',
    findVoList = '/findVoList',
    removeUrl = '/removeById',
}
function getApi(url?: string) {
    if (!url) {
      return '';
    }
    return '/singleLabel' + url;
}
// 列表
export const labelList = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(LABEL.findVoList), params },
        {
          isTransformResponse: false,
        }
    );
}
// 新增或修改
export const labelSaveOrUpdate = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(LABEL.saveOrUpdate), params },
        {
          isTransformResponse: false,
        }
    );
}
// 删除
export const labelRemove = (id:string) => {
    return h5Http.delete<BasicResponse>(
        {
          url: getApi(LABEL.removeUrl) + '?autoId=' + id,
        },
        {
          isTransformResponse: false,
        }
    );
}