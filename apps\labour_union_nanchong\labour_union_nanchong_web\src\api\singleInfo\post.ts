import { BasicResponse } from '@monorepo-yysz/types';
import {  h5Http } from '@/utils/http/axios';


enum FIRENDS {
    postAudit = '/postAudit',
    findVoList = '/findVoList',
    removeUrl = '/removeByPostId',
}
function getApi(url?: string) {
    if (!url) {
      return '';
    }
    return '/singlePost' + url;
}
// 列表
export const firendCircleList = (params:any) => {
    return h5Http.get<BasicResponse>(
        { url: getApi(FIRENDS.findVoList), params },
        {
          isTransformResponse: false,
        }
    );
}
// 审核
export const firendCircleAudit = (params:any) => {
    return h5Http.post<BasicResponse>(
        { url: getApi(FIRENDS.postAudit), params },
        {
          isTransformResponse: false,
        }
    );
}
// 删除
export const firendCircleRemove = (id:string) => {
    return h5Http.delete<BasicResponse>(
        {
          url: getApi(FIRENDS.removeUrl) + '?postId=' + id,
        },
        {
          isTransformResponse: false,
        }
    );
}