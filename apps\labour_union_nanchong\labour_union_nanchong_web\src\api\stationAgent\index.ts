import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';
//专家列表
export const expertFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/psychologicalExpert/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//专家列表新增编辑
export const saveOrUpdateByDTO = params => {
    return h5Http.post<BasicResponse>(
        {
        url: '/psychologicalExpert/saveOrUpdateByDTO',
        params,
        },
        { isTransformResponse: false }
    );
};
//专家列表删除
export const expertDelete = params => {
    return h5Http.delete<BasicResponse>(
        {
        url: '/psychologicalExpert?autoId='+params,
        params,
        },
        { isTransformResponse: false }
    );
};
  //专家列表详情
export const getDetails = params => {
    return h5Http.get<BasicResponse>(
        {
        url: '/psychologicalExpert/getVoByDto',
        params,
        },
        { isTransformResponse: false }
    );
};
//专家对话人员列表
export const psychologicalUser = params => {
    return h5Http.get<BasicResponse>(
        {
        url: '/psychologicalUser/findVoList',
        params,
        },
        { isTransformResponse: false }
    );
};
//专家对话详情
export const psychologicaDialogue = params => {
    return h5Http.get<BasicResponse>(
        {
        url: '/psychologicaDialogue/findVoList',
        params,
        },
        { isTransformResponse: false }
    );
};

//改变专家状态
export const changeState = params => {
    return h5Http.post<BasicResponse>(
        {
            url: '/psychologicalExpert/changeState',
            params,
        },
        { isTransformResponse: false }
    );
};
