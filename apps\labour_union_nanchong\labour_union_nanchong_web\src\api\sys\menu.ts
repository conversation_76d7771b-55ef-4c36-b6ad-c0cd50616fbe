import { dataCenterHttp } from '@/utils/http/axios';
import { getMenuListResultModel } from './model/menuModel';
import { BasicResponse } from '@monorepo-yysz/types';

enum Api {
  GetMenuList = '/dataCenterBusiness/getCurrentMenuTreeList',
  saveOrUpdate = '/menuInfo/saveOrUpdate',
  remove = '/menuInfo/removeById',
}

/**
 * @description: Get user menu based on id
 */

export const getMenuList = (params?: Recordable) => {
  return dataCenterHttp.get<getMenuListResultModel>({ url: Api.GetMenuList, params });
};

export const deleteLine = id => {
  return dataCenterHttp.delete<BasicResponse>(
    {
      url: Api.remove + '?autoId=' + id,
    },
    { isTransformResponse: false }
  );
};

// 新增
export const menuSaveByDTO = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: Api.saveOrUpdate,
      params,
    },
    { isTransformResponse: false }
  );
};

// 修改
export const menuUpdateByDTO = (params: Recordable) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: Api.saveOrUpdate,
      params,
    },
    { isTransformResponse: false }
  );
};
