import { dataCenterHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

export interface Dictionary {
  groupCode?: string;
  pageSize?: 0;
}

export interface DictionaryModal {
  autoId?: number;
  groupCode?: string;
  groupName?: string;
  dictCode?: string;
  dictName?: string;
  dictColor?: string;
  remark?: string;
}

export const queryDictionary = (params?: Dictionary) => {
  return dataCenterHttp.get<DictionaryModal[]>({
    url: '/dictionaryInfo/findVoList',
    params: {
      ...params,
      pageSize: 0,
    },
  });
};

export const addDictionary = (params?: Dictionary) => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: '/dictionaryInfo',
      params: { ...params, updateFlag: 'y', displayFlag: 'y' },
    },
    {
      isTransformResponse: false,
    }
  );
};

export const delDictionary = (autoId?: number) => {
  return dataCenterHttp.delete<BasicResponse>(
    {
      url: '/dictionaryInfo?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};
