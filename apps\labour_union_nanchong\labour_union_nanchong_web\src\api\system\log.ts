import { dataCenterHttp, manageHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

export const operateLogFindList = params => {
  return manageHttp.get<BasicResponse>(
    {
      url: '/operateLog/operateLogSearch',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const view = params => {
  return manageHttp.get<BasicResponse>(
    {
      url: '/common/getOperatorDetailLogByAutoId',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//查询系统日志
export const systemLogFindList = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: '/dataCenterBusiness/findSystemLogList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
