import { BasicResponse } from '@monorepo-yysz/types';
import { dataCenterHttp } from '/@/utils/http/axios';

enum Role {
  findList = '/roleInfo/findList',
  addRoleAndPerms = '/roleInfo',
  updateRoleAndPerms = '/roleInfo',
  distributeUserByRole = '/dataCenterBusiness/distributeRole',
  roleBindOfficer = '/dataCenterBusiness/roleBindOfficer',
  getUserIdsByRoleId = '/dataCenterBusiness/getBindingValueByRoleRelation',
  delete = '/roleInfo',
  //未提供
  getUnionAllCadreByUnionId = '/unionCustomData/getUnionAllCadreByUnionId',
  getUnionAllDeptByUnionId = '/unionCustomData/getUnionAllDeptByUnionId',
}

interface Trade {
  level1: [];
  level2: [];
  level3: [];
}

function getApi(url?: string) {
  if (!url) {
    return '';
  }
  return '' + url;
}

export const getRoleList = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(Role.findList),
      params,
    },
    { isTransformResponse: false }
  );
};

export const addRole = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(Role.addRoleAndPerms),
      params,
    },
    { isTransformResponse: false }
  );
};

export const updateRole = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(Role.updateRoleAndPerms),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const deleteRole = id => {
  return dataCenterHttp.delete<BasicResponse>(
    {
      url: getApi(Role.delete) + `?autoId=${id}`,
    },
    {
      isTransformResponse: false,
    }
  );
};

//绑定关系
export const distributeUserByRole = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(Role.distributeUserByRole),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//干部绑定关系
export const roleBindOfficer = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(Role.roleBindOfficer),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const getUserIdsByRoleId = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: getApi(Role.getUserIdsByRoleId),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const roleNewsAdd = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(Role.updateRoleAndPerms),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//工会干部查询
export const getUnionAllCadreByUnionId = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: Role.getUnionAllCadreByUnionId,
      params,
    },
    { isTransformResponse: false }
  );
};
//工会部门查询
export const getUnionAllDeptByUnionId = params => {
  return dataCenterHttp.get<BasicResponse>(
    {
      url: Role.getUnionAllDeptByUnionId,
      params,
    },
    { isTransformResponse: false }
  );
};

//管理员
export const unionNextAdminSelect = () => {
  return dataCenterHttp.get<Trade>({
    url: '/unionCustomData/unionNextAdminSelect',
  });
};

//分配管理员角色
export const distributeAdmin = params => {
  return dataCenterHttp.post<BasicResponse>(
    {
      url: getApi(Role.distributeUserByRole),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
