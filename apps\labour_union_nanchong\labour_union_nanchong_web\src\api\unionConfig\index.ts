import { defHttp } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum CommonApplication {
  findList = '/findVoList',
  getUnionList = '/getUnionPagedAll', //获取工会数据
}
function getApi(url?: string) {
  if (!url) {
    return '/manage/areaOfIndustryOfUnion';
  }
  return '/manage/areaOfIndustryOfUnion' + url;
}

export const findUnionList = params => {
  return defHttp.get<BasicResponse>(
    { url: getApi(CommonApplication.getUnionList), params },
    {
      isTransformResponse: false,
    }
  );
};

export const list = params => {
  return defHttp.get<BasicResponse>(
    { url: getApi(CommonApplication.findList), params },
    {
      isTransformResponse: false,
    }
  );
};
//新增或修改
export const saveOrUpdate = params => {
  return defHttp.post<BasicResponse>(
    {
      url: '/manage/areaOfIndustryOfUnion',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
//删除
export const removed = id => {
  return defHttp.delete<BasicResponse>(
    { url: '/manage/areaOfIndustryOfUnion' + `?autoId=${id}` },
    {
      isTransformResponse: false,
    }
  );
};
