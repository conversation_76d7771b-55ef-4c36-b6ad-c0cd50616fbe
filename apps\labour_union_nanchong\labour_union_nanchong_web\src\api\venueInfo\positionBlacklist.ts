import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum PositionBlacklistEnums {
  findList = '/findVenueBlacklistList',
  save = '/saveVenueBlacklist',
  details = '/getVenueBlacklistVOByDto',
  delete = '/removeVenueBlacklistById',
}

function getApi(url?: string) {
  if (!url) {
    return '/venueInfo';
  }
  return '/venueInfo' + url;
}

//阵地黑名单列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(PositionBlacklistEnums.findList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//阵地黑名单新增
export const save = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(PositionBlacklistEnums.save),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//阵地黑名单详情
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(PositionBlacklistEnums.details),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//阵地黑名单删除
export const deleteLine = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(PositionBlacklistEnums.delete) + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};
