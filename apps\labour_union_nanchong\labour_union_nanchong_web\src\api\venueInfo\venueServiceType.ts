import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum VenueInfo {
  findList = '/findVenueServiceTypeVoList',
  saveOrUpdate = '/saveOrUpdateServiceType',
  details = '/getVenueServiceTypeVoByDto',
  delete = '/deleteServiceTypeByAutoId',
}

function getApi(url?: string) {
  if (!url) {
    return '/venueInfo';
  }
  return '/venueInfo' + url;
}

//服务类型列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.findList),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//服务类型新增或修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(VenueInfo.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//服务类型详情
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.details),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//服务类型删除
export const deleteLine = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(VenueInfo.delete) + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    }
  );
};
