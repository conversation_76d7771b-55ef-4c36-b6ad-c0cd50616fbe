import { h5Http } from '/@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum AuditAct {
  findPrizeRecordList = '/findPrizeRecordList',
  auditPrize = '/auditPrize',
  disposePrize = '/disposePrize',
  lotteryDraw = '/lotteryDraw',
  exportPrizeRecord = '/exportPrizeRecord',
  findLuckRecordList = '/findLuckRecordList',
  exportLuckRecord = '/exportLuckRecord',
}

function getApi(url?: string) {
  if (!url) {
    return '/walkingInfo';
  }
  return '/walkingInfo' + url;
}

//获取兑换记录
export const findPrizeRecordList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(AuditAct.findPrizeRecordList), params },
    {
      isTransformResponse: false,
    }
  );
};

//获取抽奖记录
export const findLuckRecordList = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(AuditAct.findLuckRecordList), params },
    {
      isTransformResponse: false,
    }
  );
};

//单个或批量审核
export const auditPrize = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(AuditAct.auditPrize),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//物品发放
export const disposePrize = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(AuditAct.disposePrize),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//开奖
export const lotteryDraw = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(AuditAct.lotteryDraw),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

//导出
export const exportPrizeRecord = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(AuditAct.exportPrizeRecord),
      params,
      responseType: 'blob',
    },
    { isReturnNativeResponse: true }
  );
};

//抽奖记录导出
export const exportLuckRecord = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(AuditAct.exportLuckRecord),
      params,
      responseType: 'blob',
    },
    { isReturnNativeResponse: true }
  );
};
