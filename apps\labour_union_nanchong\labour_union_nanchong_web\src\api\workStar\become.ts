import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios'

enum VenueInfo {
  findList = '/findVoList',
  getMaxSortNumber = '/getMaxSortNumber',
  setModelWorkerBecomeSort = '/setModelWorkerBecomeSort',
  saveOrUpdateByDTO='/saveOrUpdateByDTO',
  enableDisable = '/setEnableDisable'
}

function getApi(url?: string) {
  if (!url) {
    return '/modelWorkerBecome'
  }
  return '/modelWorkerBecome' + url
}

//服务类型列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.findList),
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

//查询最大排序号
export const getMaxSortNumber = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(VenueInfo.getMaxSortNumber),
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

//服务类型新增或修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(VenueInfo.saveOrUpdateByDTO),
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

//设置劳模流程排序号
export const setModelWorkerBecomeSort = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(VenueInfo.setModelWorkerBecomeSort),
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

//服务类型详情
export const view = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(),
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

//服务类型删除
export const deleteLine = id => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi() + '?autoId=' + id,
    },
    {
      isTransformResponse: false,
    },
  )
}

//服务类型禁用
export const enableDisable = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(VenueInfo.enableDisable),
      params,
    },
    {
      isTransformResponse: false,
    },
  )
}

