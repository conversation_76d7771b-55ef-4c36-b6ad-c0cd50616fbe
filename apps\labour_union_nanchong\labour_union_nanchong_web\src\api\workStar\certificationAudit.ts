import { h5Http } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum modelWorker {
  auditCertification = '/auditModelWorkerCertification',
}

function getApi(url?: string) {
  if (!url) {
    return '/modelWorkerAuditRecord';
  }
  return '/modelWorkerAuditRecord' + url;
}

//审核劳模认证
export const auditCertification = params => {
  return h5Http.post<BasicResponse>(
    { url: getApi(modelWorker.auditCertification), params },
    {
      isTransformResponse: false,
    }
  );
};
