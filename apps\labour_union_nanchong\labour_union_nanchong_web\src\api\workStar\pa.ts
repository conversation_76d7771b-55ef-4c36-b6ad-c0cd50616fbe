import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '/@/utils/http/axios';

enum API {
  findList = '/findVoList',
  view = '/getVoByDto',
  saveOrUpdate = '/saveOrUpdateByDTO',
  deleteById = '/deleteById',
  downloadWorkerTemplate = '/downloadWorkerTemplate',
}

function getApi(url?: string) {
  if (!url) {
    return '/modelCraftsmanInfoPengAn';
  }
  return '/modelCraftsmanInfoPengAn' + url;
}

// 列表
export const list = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    { url: getApi(API.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

// 新增修改
export const saveOrUpdate = (params: Recordable) => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(API.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// view
export const view = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(API.view),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

// 删除
export const deleteLine = (autoId: number[] | number) => {
  return h5Http.delete<BasicResponse>(
    {
      url: getApi(API.deleteById) + '?autoId=' + autoId,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const downloadWorkerTemplate = () => {
  return h5Http.get<BasicResponse>(
    { url: getApi(API.downloadWorkerTemplate), responseType: 'blob' },
    {
      isTransformResponse: false,
    }
  );
};

export const importRecordPengAn = params => {
  return h5Http.get<BasicResponse>(
    { url: '/modelCraftsmanImportRecordPengAn/findVoList', params },
    {
      isTransformResponse: false,
    }
  );
};

export const importFailPengAn = params => {
  return h5Http.get<BasicResponse>(
    { url: '/modelCraftsmanImportFailPengAn/findVoList', params },
    {
      isTransformResponse: false,
    }
  );
};

export const failInfo = params => {
  return h5Http.get<Recordable>({ url: '/modelCraftsmanImportFailPengAn/getByEntity', params });
};

export const auditRecordPengAn = params => {
  return h5Http.get<BasicResponse>({ url: '/modelCraftsmanAuditRecordPengAn/findVoList', params });
};

export const auditModelWorkerCertification = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/modelCraftsmanAuditRecordPengAn/auditModelWorkerCertification',
      params,
    },
    { isTransformResponse: false }
  );
};

export const messagePengAn = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    {
      url: '/modelCraftsmanMessagePengAn/findVoList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const messageDetailPengAn = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    {
      url: '/modelCraftsmanMessageDetailPengAn/findVoList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const companyDemandListPengAn = (params: Recordable) => {
  return h5Http.get<BasicResponse>(
    {
      url: '/modelCraftsmanCompanyDemandAuditPengAn/findVoList',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};

export const companyDemandAuditPengAn = (params: Recordable) => {
  return h5Http.post<BasicResponse>(
    {
      url: '/modelCraftsmanCompanyDemandAuditPengAn/auditDemand',
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};
