import { BasicResponse } from '@monorepo-yysz/types';
import { h5Http } from '@/utils/http/axios';

//劳模类型列表
export const modelTypeFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/modelType/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//新增或更新栏目类型
export const modelType = params => {
    return h5Http.post<BasicResponse>(
      {
        url: '/modelType/saveOrUpdateByDTO',
        params,
      },
      { isTransformResponse: false }
    );
  };

//删除
export const deleteModelType = params => {
  return h5Http.delete<BasicResponse>(
    {
      url: '/modelType?autoId=' + params,
      params,
    },
    { isTransformResponse: false }
  );
};
//留言列表
export const questionFindList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/question/findVoList',
      params,
    },
    { isTransformResponse: false }
  );
};
//留言列表详情
export const questionVoByDto = params => {
  return h5Http.get<BasicResponse>(
    {
      url: '/question/getVoByDto',
      params,
    },
    { isTransformResponse: false }
  );
};
//改变留言状态
export const changePublicityState = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/question/changePublicityState',
      params,
    },
    { isTransformResponse: false }
  );
};
//删除
export const deleteLine = params => {
  return h5Http.delete<BasicResponse>(
    {
      url: '/question?autoId='+params,
      params,
    },
    { isTransformResponse: false }
  );
};
//回复留言
export const saveOrUpdateByDTO = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/questionReply/saveOrUpdateByDTO',
      params,
    },
    { isTransformResponse: false }
  );
};

//审核
export const auditSave = params => {
  return h5Http.post<BasicResponse>(
    {
      url: '/question/audit',
      params,
    },
    { isTransformResponse: false }
  );
};
