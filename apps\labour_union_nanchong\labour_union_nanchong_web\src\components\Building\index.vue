<template>
  <div class="w-full h-full p-2">
    <div class="flex-col dashboard-bg">
      <div
        class="w-200px h-200px"
        id="building"
      />
      <div
        class="text-[#98BEF7] text-[20px]"
        :style="{ fontFamily: 'Source Han Sans CN' }"
      >
        正在对接实施中.....
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import lottie from 'lottie-web';
import { nextTick, onMounted } from 'vue';
import { useUserStore } from '@/store/modules/user';

const userStore = useUserStore();

onMounted(() => {
  nextTick(() => {
    userStore.getPrefix &&
      lottie.loadAnimation({
        container: document.getElementById(`building`) as any, // 包含动画的dom元素
        renderer: 'svg', // 渲染出来的是什么格式
        loop: true, // 循环播放
        autoplay: true, // 自动播放
        path: `${userStore.getPrefix + '/union-nanchong/lottie/setting.json'}`, // 动画json的路径
      });
  });
});
</script>

<style lang="less" scoped>
.dashboard-bg {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #03183b;
}
</style>
