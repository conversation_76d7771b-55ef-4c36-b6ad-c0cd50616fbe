<template>
  <Menu.Item
    :key="item.path"
    class="!ps-5 !pe-5"
  >
    <a-badge
      :count="menuUnhandledCount?.[item.name]"
      class="!text-[unset]"
      :numberStyle="{
        padding: '0 5px',
        height: '15px',
        lineHeight: '15px',
        transform: 'translate(100%,-50%)',
      }"
    >
      <MenuItemContent
        v-bind="$props"
        :item="item"
      />
    </a-badge>
  </Menu.Item>
</template>
<script lang="ts" setup>
import { Menu } from 'ant-design-vue';
import { itemProps } from '../props';
import MenuItemContent from './MenuItemContent.vue';
import { useBadge } from '@/hooks/web/useBadge';

defineOptions({ name: 'BasicMenuItem' });

defineProps(itemProps);

const { menuUnhandledCount } = useBadge();
</script>
