<template>
  <div>
    <!-- 页面加载状态 -->
    <div
      v-if="loading"
      class="flex items-center justify-center min-h-80vh loading-container"
    >
      <a-spin
        size="large"
        :tip="loadingTip"
        class="loading-spin"
      >
        <div class="w-full h-40 flex flex-col items-center justify-center space-y-4">
          <div class="text-center">
            <h3 class="text-lg font-medium text-gray-700 mb-2">{{ loadingTitle }}</h3>
            <p class="text-sm text-gray-500">{{ loadingDescription }}</p>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 错误状态提示 -->
    <div
      v-else-if="hasError"
      class="flex flex-col items-center justify-center min-h-80vh error-container"
    >
      <a-empty
        :description="errorDescription"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      >
        <template #description>
          <div class="space-y-2">
            <p class="text-gray-500">{{ errorDescription }}</p>
            <p
              v-if="errorDetail"
              class="text-sm text-gray-400"
            >
              {{ errorDetail }}
            </p>
          </div>
        </template>
        <div class="space-x-2">
          <a-button
            v-if="showBackButton"
            type="primary"
            @click="handleGoBack"
          >
            {{ backButtonText }}
          </a-button>
          <a-button
            v-if="showRetryButton"
            @click="handleRetry"
            :loading="retryLoading"
          >
            {{ retryButtonText }}
          </a-button>
        </div>
      </a-empty>
    </div>

    <!-- 正常内容 -->
    <div v-else>
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { Empty } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { useMessage } from '@monorepo-yysz/hooks';
import { PageEnum } from '@/enums/pageEnum';

interface Props {
  // 加载状态
  loading?: boolean;
  loadingTip?: string;
  loadingTitle?: string;
  loadingDescription?: string;

  // 错误状态
  hasError?: boolean;
  errorDescription?: string;
  errorDetail?: string;

  // 按钮配置
  showBackButton?: boolean;
  backButtonText?: string;
  backRoute?: string;

  showRetryButton?: boolean;
  retryButtonText?: string;

  // 自定义处理函数
  customBackHandler?: () => void;
  customRetryHandler?: () => Promise<void> | void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  loadingTip: '正在加载...',
  loadingTitle: '初始化页面中',
  loadingDescription: '正在验证信息和权限...',

  hasError: false,
  errorDescription: '页面加载失败',
  errorDetail: '',

  showBackButton: true,
  backButtonText: '返回首页',
  backRoute: PageEnum.BASE_HOME_COPY,

  showRetryButton: true,
  retryButtonText: '重新加载',
});

const emit = defineEmits<{
  back: [];
  retry: [];
}>();

const { push } = useRouter();
const { createMessage } = useMessage();

const retryLoading = ref(false);

// 返回处理
const handleGoBack = () => {
  if (props.customBackHandler) {
    props.customBackHandler();
  } else {
    push(props.backRoute);
  }
  emit('back');
};

// 重试处理
const handleRetry = async () => {
  if (props.customRetryHandler) {
    try {
      retryLoading.value = true;
      await props.customRetryHandler();
    } catch (error) {
      createMessage.error(`${error || ''}，重试失败，请稍后再试`);
    } finally {
      retryLoading.value = false;
    }
  }
  emit('retry');
};
</script>

<style scoped>
.min-h-80vh {
  min-height: 80vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  background: #fafafa;
  border-radius: 8px;
  margin: 2rem;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  background: #fafafa;
  border-radius: 8px;
  margin: 2rem;
}

.error-container .ant-empty-description {
  color: #666;
  margin-bottom: 1.5rem;
}

.loading-spin {
  font-size: 16px;
  color: #1890ff;
}

.loading-spin :deep(.ant-spin-text) {
  color: #666;
  font-size: 14px;
  margin-top: 12px;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-container,
  .error-container {
    margin: 1rem;
    padding: 2rem 1rem;
  }

  .min-h-80vh {
    min-height: 60vh;
  }
}
</style>
