import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from '@monorepo-yysz/hooks';

export interface PageStatusOptions {
  // 初始加载配置
  initialLoading?: boolean;
  loadingTip?: string;
  loadingTitle?: string;
  loadingDescription?: string;

  // 错误配置
  defaultErrorDescription?: string;

  // 按钮配置
  backRoute?: string;
  backButtonText?: string;
  retryButtonText?: string;

  // 验证函数
  validateData?: (data: any) => boolean;

  // 自定义处理函数
  customBackHandler?: () => void;
  customRetryHandler?: () => Promise<void>;
}

export function usePageStatus(options: PageStatusOptions = {}) {
  const {
    initialLoading = true,
    loadingTip = '正在加载栏目信息...',
    loadingTitle = '初始化页面中',
    loadingDescription = '正在验证栏目信息和权限...',
    defaultErrorDescription = '页面加载失败',
    backRoute = '/',
    backButtonText = '返回首页',
    retryButtonText = '重新加载',
    validateData = data => !!data,
    customBackHandler,
    customRetryHandler,
  } = options;

  const { push } = useRouter();
  const { createErrorModal, createMessage } = useMessage();

  // 状态管理
  const pageLoading = ref(initialLoading);
  const hasError = ref(false);
  const errorDescription = ref(defaultErrorDescription);
  const errorDetail = ref('');
  const pageData = ref<any>(null);

  // 计算属性
  const isReady = computed(() => !pageLoading.value && !hasError.value && pageData.value);

  /**
   * 设置加载状态
   */
  const setLoading = (loading: boolean) => {
    pageLoading.value = loading;
  };

  /**
   * 设置错误状态
   */
  const setError = (description: string, detail?: string) => {
    hasError.value = true;
    errorDescription.value = description;
    errorDetail.value = detail || '';
    pageLoading.value = false;
  };

  /**
   * 清除错误状态
   */
  const clearError = () => {
    hasError.value = false;
    errorDescription.value = defaultErrorDescription;
    errorDetail.value = '';
  };

  /**
   * 设置页面数据
   */
  const setPageData = (data: any) => {
    pageData.value = data;
    if (validateData(data)) {
      clearError();
    } else {
      setError('数据验证失败', '获取的数据不符合要求');
    }
    pageLoading.value = false;
  };

  /**
   * 初始化页面数据
   */
  const initPageData = async (
    dataFetcher: () => Promise<any>,
    errorHandler?: (error: any) => void
  ) => {
    try {
      setLoading(true);
      clearError();

      const data = await dataFetcher();
      setPageData(data);

      if (!validateData(data)) {
        createErrorModal({
          title: '数据验证失败',
          content: '获取的数据不符合要求，请联系管理员。',
          okText: backButtonText,
          onOk: () => {
            if (customBackHandler) {
              customBackHandler();
            } else {
              push(backRoute);
            }
          },
        });
      }
    } catch (error) {
      console.error('页面初始化失败:', error);
      setError('加载失败', '页面初始化失败，请刷新页面重试或联系管理员');

      if (errorHandler) {
        errorHandler(error);
      } else {
        createErrorModal({
          title: '加载失败',
          content: '页面初始化失败，请刷新页面重试或联系管理员。',
          okText: '确定',
        });
      }
    }
  };

  /**
   * 重新加载数据
   */
  const reloadPageData = async (dataFetcher: () => Promise<any>) => {
    try {
      setLoading(true);
      clearError();

      const data = await dataFetcher();
      setPageData(data);

      if (validateData(data)) {
        createMessage.success('页面重新加载成功');
      }
    } catch (error) {
      console.error('重新加载失败:', error);
      setError('重新加载失败', '请稍后再试');
      createMessage.error(`${error || ''}，重新加载失败，请稍后再试`);
    }
  };

  /**
   * 重置状态
   */
  const resetStatus = () => {
    setLoading(initialLoading);
    clearError();
    pageData.value = null;
  };

  return {
    // 状态
    pageLoading,
    hasError,
    errorDescription,
    errorDetail,
    pageData,
    isReady,

    // 配置
    loadingTip,
    loadingTitle,
    loadingDescription,
    backRoute,
    backButtonText,
    retryButtonText,
    customBackHandler,
    customRetryHandler,

    // 方法
    setLoading,
    setError,
    clearError,
    setPageData,
    initPageData,
    reloadPageData,
    resetStatus,
  };
}
