<template>
  <div class="w-full flex justify-center items-center absolute bottom-8 text-[#FFFFFF]">
    <div class="relative">
      <div v-if="VITE_GLOB_APP_MAIN_ONE">
        <label>主办单位：</label>
        <a
          class="cursor-pointer text-[#B4D8FD]"
          :href="VITE_GLOB_APP_MAIN_ONE_URL"
          target="_blank"
          >{{ VITE_GLOB_APP_MAIN_ONE }}</a
        >、
        <a
          class="cursor-pointer text-[#B4D8FD]"
          :href="VITE_GLOB_APP_MAIN_TWO_URL"
          target="_blank"
          >{{ VITE_GLOB_APP_MAIN_TWO }}</a
        >
        <br />
      </div>
      <div
        class="text-[#fff] bg-[rgba(72,153,255,0.5)] text-[16px] font-semibold py-[8px] px-[34px] rounded-[15px]"
      >
        <!-- 推荐分辨率为 1920*1080 推荐使用 谷歌浏览器 -->
        技术支持：<a
          class="cursor-pointer text-[#fff] text-[16px]"
          target="_blank"
          :href="VITE_GLOB_APP_SUPPORT_URL"
          >{{ VITE_GLOB_APP_SUPPORT }}</a
        >
      </div>
    </div>
    <div class="pl-6"><label> </label></div>
  </div>
</template>

<script lang="ts" setup>
import { getAppEnvConfig } from '@/utils/env';

const {
  VITE_GLOB_APP_MAIN_ONE,
  VITE_GLOB_APP_MAIN_TWO,
  VITE_GLOB_APP_SUPPORT,
  VITE_GLOB_APP_SUPPORT_URL,
  VITE_GLOB_APP_MAIN_ONE_URL,
  VITE_GLOB_APP_MAIN_TWO_URL,
} = getAppEnvConfig();
</script>
