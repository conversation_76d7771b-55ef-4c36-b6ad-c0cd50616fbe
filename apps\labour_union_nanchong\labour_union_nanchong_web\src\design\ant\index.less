@import './pagination.less';
@import './input.less';
@import './btn.less';
@import './popconfirm.less';

.ant-image-preview-root {
  img {
    display: unset;
  }
}

.ant-back-top {
  right: 20px;
  bottom: 20px;
}

.collapse-container__body {
  > .ant-descriptions {
    margin-left: 6px;
  }
}

.ant-image-preview-operations {
  background-color: rgb(0 0 0 / 30%);
}

.ant-popover {
  &-content {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }
}

// =================================
// ==============modal message======
// =================================
.modal-icon-warning {
  color: @warning-color !important;
}

.modal-icon-success {
  color: @success-color !important;
}

.modal-icon-error {
  color: @error-color !important;
}

.modal-icon-info {
  color: @primary-color !important;
}

.ant-checkbox-checked .ant-checkbox-inner::after,
.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
  border-top: 0 !important;
  border-left: 0 !important;
}

.ant-form-item-control-input-content {
  > div {
    > div {
      max-width: 100%;
    }
  }
}

.ant-col {
  width: 100%;
}

.ant-modal-header {
  background-color: @primary-color !important;
  color: #fff !important;

  .ant-modal-title {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    /* stylelint-disable-next-line order/order */
    @apply !text-light-200 w-2/3 truncate;
  }
}

.ant-table {
  .ant-table-header {
    .ant-table-thead > tr > th {
      background-color: @form-table-title-color;
      border-color: #d4d4d4 !important;
    }
  }

  .ant-table-body {
    .ant-table-row-expand-icon {
      border: 1px solid #151515 !important;
    }

    .ant-table-row-expand-icon-expanded {
      &::before {
        top: 4px !important;
        right: 3px !important;
        left: 3px !important;
        height: 6px !important;
        clip-path: polygon(0% 0%, 50% 100%, 100% 0%) !important;
      }

      &::after {
        top: 0 !important;
        right: 0 !important;
        left: 0 !important;
        height: 0 !important;
      }
    }

    .ant-table-row-expand-icon-collapsed {
      &::before {
        top: 4px !important;
        right: 3px !important;
        left: 3px !important;
        height: 6px !important;
        clip-path: polygon(100% 0%, 0% 50%, 100% 100%) !important;
      }

      &::after {
        top: 0 !important;
        right: 0 !important;
        left: 0 !important;
        height: 0 !important;
      }
    }

    .ant-table-tbody > tr.ant-table-row:hover > td {
      background: @table-hover-color;
    }
  }
}
