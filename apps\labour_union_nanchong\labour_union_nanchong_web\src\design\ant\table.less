@prefix-cls: ~'@{namespace}-basic-table';

// fix table unnecessary scrollbar
.@{prefix-cls} {
  .hide-scrollbar-y {
    .ant-spin-nested-loading {
      .ant-spin-container {
        .ant-table {
          .ant-table-content {
            .ant-table-scroll {
              .ant-table-hide-scrollbar {
                overflow-y: auto !important;
              }

              .ant-table-body {
                overflow-y: auto !important;
              }
            }

            .ant-table-fixed-right {
              .ant-table-body-outer {
                .ant-table-body-inner {
                  overflow-y: auto !important;
                }
              }
            }

            .ant-table-fixed-left {
              .ant-table-body-outer {
                .ant-table-body-inner {
                  overflow-y: auto !important;
                }
              }
            }
          }
        }
      }
    }
  }

  .hide-scrollbar-x {
    .ant-spin-nested-loading {
      .ant-spin-container {
        .ant-table {
          .ant-table-content {
            .ant-table-scroll {
              .ant-table-body {
                overflow: auto !important;
              }
            }

            .ant-table-fixed-right {
              .ant-table-body-outer {
                .ant-table-body-inner {
                  overflow-x: auto !important;
                }
              }
            }

            .ant-table-fixed-left {
              .ant-table-body-outer {
                .ant-table-body-inner {
                  overflow-x: auto !important;
                }
              }
            }
          }
        }
      }
    }
  }
}
