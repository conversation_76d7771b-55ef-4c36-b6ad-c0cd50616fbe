import { concat, map } from 'lodash-es';
import './loading.css';
import { useGlobSetting } from '@/hooks/setting';
import { useUserStore } from '@/store/modules/user';

export function useLoading(state: Boolean) {
  const userStore = useUserStore();
  const { title } = useGlobSetting();

  const app = document.querySelector('#app');

  if (state) {
    const dom = document.createElement('div');
    const login = document.querySelector('#login-basic');

    //隐藏首页
    login && login.classList.add('hidden');

    dom.setAttribute('class', 'loading-container');

    /**
 * 
 <span>页</span><span>面</span><span>正</span><span>在</span><span>加</span><span>载</span><span>中</span><span>,</span><span>请</span><span>稍</span><span>候</span>
 */
    const text = concat(
      map(userStore.getCompanyInfo?.companyName || title, v => `<span>${v}</span>`),
      ['<span>欢</span>', '<span>迎</span>', '<span>您</span>']
    ).join('');

    dom.innerHTML = `<div class='loading loading-1'>${text}<span>.</span><span>.</span><span>.</span></div>`;
    app?.appendChild(dom);
  } else {
    const dom = document.querySelector('.loading-container');
    dom && app?.removeChild(dom as Element);
  }
}
