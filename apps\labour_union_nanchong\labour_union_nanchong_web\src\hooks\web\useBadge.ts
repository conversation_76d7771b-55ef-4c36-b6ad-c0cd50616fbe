import { useUserStore } from '@/store/modules/user';
import { ref, watch } from 'vue';

/**
 * 对应菜单标徽
 * @returns menuUnhandledCount
 */
export const useBadge = () => {
  const userStore = useUserStore();

  const menuUnhandledCount = ref();

  watch(
    () => userStore.getMenuBadge,
    () => {
      menuUnhandledCount.value = userStore.getMenuBadge;
    },
    { deep: true, immediate: true }
  );
  return { menuUnhandledCount };
};
