<template>
  <Layout.Header
    :class="getHeaderClass"
    :style="{
      backgroundImage: `url(${bg_top})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
    }"
  >
    <!-- left start -->
    <div :class="`${prefixCls}-left`">
      <!-- logo -->
      <AppLogo
        v-if="getShowHeaderLogo || getIsMobile"
        :class="`${prefixCls}-logo`"
        :theme="getHeaderTheme"
        :style="getLogoWidth"
      />
      <LayoutTrigger
        v-if="
          (getShowContent && getShowHeaderTrigger && !getSplit && !getIsMixSidebar) || getIsMobile
        "
        :theme="getHeaderTheme"
        :sider="false"
      />
      <LayoutBreadcrumb
        v-if="getShowContent && getShowBread"
        :theme="getHeaderTheme"
      />
    </div>
    <!-- left end -->

    <!-- menu start -->
    <div
      v-if="getShowTopMenu && !getIsMobile"
      :class="`${prefixCls}-menu`"
    >
      <LayoutMenu
        :isHorizontal="true"
        :theme="getHeaderTheme"
        :splitType="getSplitType"
        :menuMode="getMenuMode"
      />
    </div>
    <!-- menu-end -->
    <!-- action  -->
    <div :class="`${prefixCls}-action`">
      <div
        class="text-16px"
        v-if="false"
        >{{ titleHeader }}</div
      >

      <AppSearch
        v-if="getShowSearch"
        :class="`${prefixCls}-action__item `"
      />

      <ErrorAction
        v-if="getUseErrorHandle"
        :class="`${prefixCls}-action__item error-action`"
      />

      <Notify
        v-if="getShowNotice"
        :class="`${prefixCls}-action__item notify-item`"
      />

      <FullScreen
        v-if="getShowFullScreen"
        :class="`${prefixCls}-action__item fullscreen-item`"
      />

      <AppLocalePicker
        v-if="getShowLocalePicker"
        :reload="true"
        :showText="false"
        :class="`${prefixCls}-action__item`"
      />

      <UserDropDown :theme="getHeaderTheme" />

      <SettingDrawer
        v-if="getShowSetting"
        :class="`${prefixCls}-action__item`"
      />
    </div>
  </Layout.Header>
</template>
<script lang="ts" setup>
import { Layout } from 'ant-design-vue';
import { computed, unref } from 'vue';
import { AppLocalePicker, AppLogo, AppSearch } from '@/components/Application';
import { SettingButtonPositionEnum } from '@/enums/appEnum';
import { MenuModeEnum, MenuSplitTyeEnum } from '@monorepo-yysz/enums';
import { useHeaderSetting } from '@/hooks/setting/useHeaderSetting';
import { useMenuSetting } from '@/hooks/setting/useMenuSetting';
import { useRootSetting } from '@/hooks/setting/useRootSetting';
import { useDesign, useAppInject } from '@monorepo-yysz/hooks';
import { useLocale } from '@/locales/useLocale';
import { createAsyncComponent } from '@monorepo-yysz/ui';
import { propTypes } from '@monorepo-yysz/utils';
import LayoutMenu from '../menu/index.vue';
import LayoutTrigger from '../trigger/index.vue';
import { ErrorAction, FullScreen, LayoutBreadcrumb, Notify, UserDropDown } from './components';
import bg_top from '@/assets/images/dashboard/top-bg.png';
import { useGlobSetting } from '@/hooks/setting';
import { useUserStore } from '@/store/modules/user';

const SettingDrawer = createAsyncComponent(() => import('@/layouts/default/setting/index.vue'), {
  loading: true,
});
defineOptions({ name: 'LayoutHeader' });

const props = defineProps({
  fixed: propTypes.bool,
});
const { prefixCls } = useDesign('layout-header');
const {
  getShowTopMenu,
  getShowHeaderTrigger,
  getSplit,
  getIsMixMode,
  getMenuWidth,
  getIsMixSidebar,
} = useMenuSetting();
const { getUseErrorHandle, getShowSettingButton, getSettingButtonPosition } = useRootSetting();

const { title } = useGlobSetting();

const {
  getHeaderTheme,
  getShowFullScreen,
  getShowNotice,
  getShowContent,
  getShowBread,
  getShowHeaderLogo,
  getShowHeader,
  getShowSearch,
} = useHeaderSetting();

const { getShowLocalePicker } = useLocale();

const { getIsMobile } = useAppInject();

const userStore = useUserStore();

const getHeaderClass = computed(() => {
  const theme = unref(getHeaderTheme);
  return [
    prefixCls,
    {
      [`${prefixCls}--fixed`]: props.fixed,
      [`${prefixCls}--mobile`]: unref(getIsMobile),
      [`${prefixCls}--${theme}`]: theme,
    },
  ];
});

const titleHeader = computed(() => {
  return userStore.getCompanyInfo?.companyName || title;
});

const getShowSetting = computed(() => {
  if (!unref(getShowSettingButton)) {
    return false;
  }
  const settingButtonPosition = unref(getSettingButtonPosition);

  if (settingButtonPosition === SettingButtonPositionEnum.AUTO) {
    return unref(getShowHeader);
  }
  return settingButtonPosition === SettingButtonPositionEnum.HEADER;
});

const getLogoWidth = computed(() => {
  if (!unref(getIsMixMode) || unref(getIsMobile)) {
    return {};
  }
  const width = unref(getMenuWidth) < 180 ? 180 : unref(getMenuWidth);
  return { width: `${width}px` };
});

const getSplitType = computed(() => {
  return unref(getSplit) ? MenuSplitTyeEnum.TOP : MenuSplitTyeEnum.NONE;
});

const getMenuMode = computed(() => {
  return unref(getSplit) ? MenuModeEnum.HORIZONTAL : null;
});
</script>
<style lang="less">
@import url('./index.less');
</style>
