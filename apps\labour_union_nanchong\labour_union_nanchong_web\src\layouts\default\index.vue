<template>
  <Layout
    :class="prefixCls"
    v-bind="lockEvents"
  >
    <LayoutFeatures />
    <LayoutHeader
      fixed
      v-if="getShowFullHeaderRef"
    />
    <Layout :class="[layoutClass, `${prefixCls}-out`]">
      {{ currentRoute.meta.path }}
      <LayoutSideBar v-if="(getShowSidebar && ifShowSider) || getIsMobile" />
      <Layout :class="`${prefixCls}-main`">
        <LayoutMultipleHeader />
        <LayoutContent />
        <LayoutFooter />
      </Layout>
    </Layout>
  </Layout>
</template>

<script lang="ts" setup>
import { computed, unref } from 'vue';
import { Layout } from 'ant-design-vue';
import { createAsyncComponent } from '@monorepo-yysz/ui';
import LayoutHeader from './header/index.vue';
import LayoutContent from './content/index.vue';
import LayoutSideBar from './sider/index.vue';
import LayoutMultipleHeader from './header/MultipleHeader.vue';
import { useHeaderSetting } from '@/hooks/setting/useHeaderSetting';
import { useMenuSetting } from '@/hooks/setting/useMenuSetting';
import { useDesign, useAppInject } from '@monorepo-yysz/hooks';
import { useLockPage } from '@/hooks/web/useLockPage';
import { useMultipleTabSetting } from '@/hooks/setting/useMultipleTabSetting';
import { useRouter } from 'vue-router';
import { PageEnum } from '@/enums/pageEnum';

const { currentRoute } = useRouter();

const LayoutFeatures = createAsyncComponent(() => import('@/layouts/default/feature/index.vue'));
const LayoutFooter = createAsyncComponent(() => import('@/layouts/default/footer/index.vue'));

defineOptions({ name: 'DefaultLayout' });

const { prefixCls } = useDesign('default-layout');
const { getIsMobile } = useAppInject();
const { getShowFullHeaderRef } = useHeaderSetting();
const { getShowSidebar, getIsMixSidebar, getShowMenu } = useMenuSetting();
const { getAutoCollapse } = useMultipleTabSetting();

// Create a lock screen monitor
const lockEvents = useLockPage();

const layoutClass = computed(() => {
  let cls: string[] = ['ant-layout'];
  if (unref(getIsMixSidebar) || unref(getShowMenu)) {
    cls.push('ant-layout-has-sider');
  }

  if (!unref(getShowMenu) && unref(getAutoCollapse)) {
    cls.push('ant-layout-auto-collapse-tabs');
  }

  return cls;
});

const ifShowSider = computed(() => unref(currentRoute).path !== PageEnum.BASE_HOME_COPY);
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-default-layout';

.@{prefix-cls} {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;
  background-color: @content-bg;

  > .ant-layout {
    min-height: 100%;
  }

  &-main {
    width: 100%;
    // margin-left: 1px;
  }
}

.@{prefix-cls}-out {
  &.ant-layout-has-sider {
    .@{prefix-cls} {
      &-main {
        // margin-left: 1px;
      }
    }
  }
}
</style>
