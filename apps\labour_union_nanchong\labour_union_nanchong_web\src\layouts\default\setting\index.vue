<template>
  <div @click="openDrawer(true)">
    <Icon icon="ion:settings-outline" />
    <SettingDrawer @register="register" />
  </div>
</template>
<script lang="ts" setup>
import SettingDrawer from './SettingDrawer';
import { Icon } from '@monorepo-yysz/ui';

import { useDrawer } from '@/components/Drawer';

defineOptions({ name: 'Setting<PERSON>utton' });

const [register, { openDrawer }] = useDrawer();
</script>
