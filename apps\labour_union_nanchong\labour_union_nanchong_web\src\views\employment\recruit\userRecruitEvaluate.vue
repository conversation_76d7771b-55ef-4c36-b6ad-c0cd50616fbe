<template>
  <BasicModal
    @register="registerTableModal"
    v-bind="$attrs"
    :title="title"
    maskTransitionName="fade"
    transitionName="fade"
    :showOkBtn="false"
  >
    <BasicTable @register="registerTable" />
    <UserCurriculumVitaeModal
      @register="registerModal"
      :can-fullscreen="false"
      width="50%"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { BasicTable, useTable } from '/@/components/Table';
import { recruitEvaluateModalColumns, recruitEvaluateColumnSchemas } from './data';
import UserCurriculumVitaeModal from './userCurriculumVitaeModal.vue';
import { RecruitFindVoList } from '@/api/employment/recruit';
import { useModal } from '@/components/Modal';

defineEmits(['register']);

const record = ref<Recordable>();

const title = computed(() => {
  return `${unref(record)?.employmentPositionName}--职工评价`;
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: recruitEvaluateModalColumns(),
  showIndexColumn: false,
  api: RecruitFindVoList,
  formConfig: {
    labelWidth: 120,
    schemas: recruitEvaluateColumnSchemas(),
    autoSubmitOnEnter: true,
  },
  beforeFetch(params) {
    params.employmentRecruitId = unref(record)?.employmentRecruitId;
    return params;
  },
  searchInfo: {},
  maxHeight: 420,
  useSearchForm: true,
  immediate: false,
  bordered: true,
});

const [registerModal, { openModal }] = useModal();

const [registerTableModal, { setModalProps }] = useModalInner(async data => {
  record.value = data.record;
  setModalProps({ confirmLoading: false });
  reload();
});
</script>
